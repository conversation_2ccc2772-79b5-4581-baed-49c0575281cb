package com.lenskart.scm.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.scm.constants.ScmConstants;
import com.lenskart.scm.model.ScmOrderContext;

public class OrderSenseiDbUtils {
    public static String getLatestDoOrderId(ScmOrderContext scmOrderContext)  {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                ScmConstants.ORDER_SENSEI_DB,
                OrderSenseiQueries.DO_ORDER_ID).getFirst().get("id").toString();
    }
}
