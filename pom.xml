<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.lenskart</groupId>
    <artifactId>be-automation</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <modules>
        <module>commons</module>
        <module>juno</module>
        <module>pos</module>
        <module>scm</module>
        <module>nexs</module>
        <module>cs</module>
        <module>cosmos</module>
        <module>example</module>
        <module>e2e</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <rest-assured.version>5.5.2</rest-assured.version>
        <jackson.version>2.15.2</jackson.version>
        <testng.version>7.8.0</testng.version>
        <lombok.version>1.18.30</lombok.version>
        <mysql.version>8.0.33</mysql.version>
        <hikaricp.version>5.0.1</hikaricp.version>
        <commons-dbcp2.version>2.9.0</commons-dbcp2.version>
        <extentreports.version>5.1.1</extentreports.version>
        <slf4j.version>2.0.9</slf4j.version>
        <logback.version>1.5.18</logback.version>
        <commons-io.version>2.15.0</commons-io.version>
        <snakeyaml.version>2.0</snakeyaml.version>
        <jsch.version>0.1.55</jsch.version>
        <mongodb.version>4.11.1</mongodb.version>
        <json.version>20250107</json.version>
        <jedis.version>5.1.0</jedis.version>
        <elasticsearch.version>7.17.28</elasticsearch.version>
        <juno-schema.version>15.50</juno-schema.version>
        <oshi.version>6.6.0</oshi.version>
        <awaitility.version>4.3.0</awaitility.version>
        <surefire.version>3.2.2</surefire.version>
        <aspectjweaver.version>1.9.6</aspectjweaver.version>
        <!-- Skip tests by default during install, but allow override from command line -->
        <skipTests>true</skipTests>
    </properties>

    <!-- Dependency Management -->
    <dependencyManagement>
        <dependencies>
            <!-- Force the use of a specific version of aspectjweaver to avoid corrupted jar issues -->
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectjweaver.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- Repository configuration for internal dependencies -->
    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <layout>default</layout>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>archiva.internal</id>
            <name>Lenskart Internal Repository</name>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>

        <repository>
            <id>archiva.nexs.releases</id>
            <name>Lenskart NEXS Releases Repository</name>
            <url>http://archiva-new.prod.internal:8080/repository/internal/nexs-releases</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <!-- Plugin repository configuration for internal plugins -->
    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Central Plugin Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <layout>default</layout>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <updatePolicy>never</updatePolicy>
            </releases>
        </pluginRepository>

        <pluginRepository>
            <id>archiva.internal.plugins</id>
            <name>Lenskart Internal Plugin Repository</name>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <!-- Distribution management for deploying artifacts -->
    <distributionManagement>
        <repository>
            <id>archiva.internal.releases</id>
            <name>Lenskart Internal Releases</name>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
        </repository>
        <snapshotRepository>
            <id>archiva.internal.snapshots</id>
            <name>Lenskart Internal Snapshots</name>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <!-- Maven Surefire Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.2</version>
                <!-- Configuration for Surefire is defined in each module -->
                <configuration>
                    <!-- Empty configuration as each module will define its own suiteXmlFiles -->
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>