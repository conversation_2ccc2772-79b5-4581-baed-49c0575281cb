package com.lenskart.e2e.helper.nexs;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.database.EMSDbUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsOrderCompletionHelper;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.helpers.ems.EmsTriggerExceptionHelper;
import com.lenskart.nexs.helpers.orderqc.OrderQcHoldAndFailHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.util.OrderAdopterUtil;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.List;
import java.util.Map;

@Slf4j
@SuperBuilder
public class QCFailHelper extends BaseHelper<Object, Object> implements ServiceHelper {
    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;
    NexsOrderContext nexsOrderContext;




    @Override
    public ServiceHelper init() {

        /* Juno Helper to create order */
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }

        /* Scm helper to move the order to scm states */
        OrderAdopterUtil.syncOrder(String.valueOf(orderContext.getOrderId()));

        nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
                        .build())
                .incrementId(String.valueOf(orderContext.getOrderId()))
                .build();
        nexsOrderContext.setIsLensQcFail(true);
        nexsOrderContext.setIsQcHold(true);
        nexsOrderContext.setIsQcFail(true);
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .sequentialTransition(true)
                .build();
        stateManager.test();

        nexsOrderContext.setIsQcHold(true);
        OrderQcHoldAndFailHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();

        nexsOrderContext.setIsQcHold(false);
        nexsOrderContext.setIsQcFail(true);
        OrderQcHoldAndFailHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();

        boolean success = AwaitUtils.pollUntil(
                () -> {
                    List<Map<String, Object>> EMSDetails = EMSDbUtils.getDetails(nexsOrderContext);
                    String emsId = EMSDetails.getFirst().get("id").toString();
                    nexsOrderContext.setExceptionId(emsId);
                    EmsTriggerExceptionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                    String currentStatus = EMSDetails.getFirst().get("status").toString();
                    log.info("Polling EMS status: {}", currentStatus);
                    nexsOrderContext.setEmsStatus(currentStatus);
                    return "FINISHED".equals(currentStatus);
                },
                "Wait for order to move to in_picking",
                Duration.ofSeconds(60),
                Duration.ofSeconds(6),
                true
        );
         nexsOrderContext.setIsFrameQcFailed(true);
        if (success && (WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals("IN_PICKING"))) {
            NexsOrderCompletionHelper.builder()
                    .nexsOrderContext(nexsOrderContext)
                    .build()
                    .test();
        } else {
            throw new RuntimeException("Order is either reassigned or not in IN_PICKING state");
        }
        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
