package com.lenskart.e2e.helper.nexs;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.database.EMSDbUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsOrderCompletionHelper;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.helpers.ems.EmsTriggerExceptionHelper;
import com.lenskart.nexs.helpers.orderqc.OrderQcHoldAndFailHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.util.OrderAdopterUtil;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.List;
import java.util.Map;

@Slf4j
@SuperBuilder
public class QCFailHelper extends BaseHelper<Object, Object> implements ServiceHelper {
    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;
    NexsOrderContext nexsOrderContext;




    @Override
    public ServiceHelper init() {

        /* Juno Helper to create order */
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }

        /* Scm helper to move the order to scm states */
        OrderAdopterUtil.syncOrder(String.valueOf(orderContext.getOrderId()));

        nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
                        .build())
                .incrementId(String.valueOf(orderContext.getOrderId()))
                .build();
        nexsOrderContext.setIsQcHold(true);
        nexsOrderContext.setIsQcFail(true);
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .sequentialTransition(true)
                .build();
        stateManager.test();

        nexsOrderContext.setIsQcHold(true);
        OrderQcHoldAndFailHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();

        nexsOrderContext.setIsQcHold(false);
        nexsOrderContext.setIsQcFail(true);
        OrderQcHoldAndFailHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();

        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
