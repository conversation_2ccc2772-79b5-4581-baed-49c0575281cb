package com.lenskart.e2e.helper.nexs;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.PickingDbutils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.picking.*;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.util.OrderAdopterUtil;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

import static com.lenskart.nexs.constants.Constants.SINGAPORE_WAREHOUSE_FACILITY;
@SuperBuilder
public class SupervisorPickingHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsOrderContext nexsOrderContext;
    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;

    @Override
    public ExecutionHelper init() {
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }

        /* Scm helper to move the order to scm states */
        OrderAdopterUtil.syncOrder(String.valueOf(orderContext.getOrderId()));
        nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
                        .build())
                .incrementId(String.valueOf(orderContext.getOrderId()))
                .build();
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .sequentialTransition(true)
                .build();
        stateManager.test();
        nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder().build()).build();
        nexsOrderContext.setFacilityCode(SINGAPORE_WAREHOUSE_FACILITY);
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        return this;
    }


    @Override
    public ExecutionHelper orchestrateFlow() {

        //create summary
        CreateSummaryHelper createSummaryHelper = CreateSummaryHelper.builder().nexsOrderContext(nexsOrderContext).location(Constants.SUNGLASS).category(Constants.FR0_CATEGORY).build();
        createSummaryHelper.test();

        String summary_id = RestUtils.getValueFromResponse(createSummaryHelper.getResponse(), "data.PICKINGSUMMARY.id").toString();
        nexsOrderContext.setSummary_id(summary_id);

        //get summary details
        GetPickingDetailsHelper getPickingDetailsHelper = GetPickingDetailsHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build();
        getPickingDetailsHelper.test();

        Object responseObj = RestUtils.getValueFromResponse(getPickingDetailsHelper.getResponse(), "data.id");
        List<String> picklistIds;

        if (responseObj instanceof List<?>) {
            picklistIds = ((List<?>) responseObj).stream()
                    .map(Object::toString)
                    .toList();
        } else {
            picklistIds = new ArrayList<>();
        }
        for (String item : picklistIds) {
            nexsOrderContext.setPicklistId(item);
            if(PickingDbutils.getPickingDetails(nexsOrderContext).equals(Constants.PICKLIST_CREATED)){
                nexsOrderContext.setPicklistId(item);
                String productId = PickingDbutils.getProductIdByPicklistId(nexsOrderContext);
                nexsOrderContext.setProductId(productId);
                break;
            }

        }

        //skip item
        SkipItemHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        //close picking summary
        ClosePickingSummaryHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        //mark item permanently not found
        MarkItemPermanentlyNotFoundHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        String id_list = PickingDbutils.getOrderItemId(nexsOrderContext).toString();
        nexsOrderContext.setWmsOrderId(id_list);

        //pick skipped item
        PickSkippedItemHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
