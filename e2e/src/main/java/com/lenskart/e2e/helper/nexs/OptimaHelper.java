package com.lenskart.e2e.helper.nexs;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.constants.ScmConstants;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.database.ScmQueries;
import com.lenskart.scm.helpers.optima.AssignFullfillerHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.util.OrderAdopterUtil;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

@Slf4j
@SuperBuilder
public class OptimaHelper extends BaseHelper<Object, Object> implements ServiceHelper {
    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;
    NexsOrderContext nexsOrderContext;
    ScmOrderContext scmOrderContext;

    @Override
    public ServiceHelper init() {

        String wmsOrderCode = WMSDbUtils.getLatestOrder().getFirst().get("wms_order_code").toString();
        scmOrderContext =  ScmOrderContext.builder().unicomOrderCode(wmsOrderCode).build();
        scmOrderContext.setUnicomOrderCode(wmsOrderCode);

        AssignFullfillerHelper.builder().scmOrderContext(scmOrderContext).build().test();

        return this;
    }

    @Override
    public ServiceHelper process() {

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }



}