package com.lenskart.e2e.helper.pos;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.validators.CancellationReasonRefundMethodsValidator;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.pos.validator.OrderValidator;
import com.lenskart.scm.util.OrderAdopterUtil;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class PosE2EHelper extends BaseHelper<Object, Object> implements ServiceHelper {

    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;
    CsOrderContext csOrderContext;




    @Override
    public ServiceHelper init() {
        /* Juno Helper to create order */
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }

        /* Scm helper to move the order to scm states */
//        OrderAdopterUtil.syncOrder(String.valueOf(orderContext.getOrderId()));
//
//        NexsOrderContext nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
//                        .build())
//                .incrementId(String.valueOf(orderContext.getOrderId()))
//                .build();
//
//        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
//                .orderContext(orderContext)
//                .nexsOrderContext(nexsOrderContext)
//                .sequentialTransition(true)
//                .build();
//
//        stateManager.test();
        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {

        OrderValidator validator = OrderValidator.builder().orderContext(orderContext).build();
            validator.validateNode();
            validator.validateDBEntities();
            return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
