package com.lenskart.e2e.helper.cs;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.cs.helpers.CrmPushPrescriptionOrdersHelper;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.validators.PFUStatusValidator;
import com.lenskart.juno.helpers.*;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.juno.helpers.CreatePrescriptionHelper;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.juno.helpers.MedibuddyApprovalRejectionHelper;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;


@SuperBuilder
@Slf4j
public class CsPFUE2EHelpers extends BaseHelper<Object, Object> implements ServiceHelper {

    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;
    CsOrderContext csOrderContext;
    CsOrderContext.PFUContext pfuContext;
    ScmOrderContext scmOrderContext;


    @SneakyThrows
    @Override
    public ServiceHelper init() {

        /* Juno Helper to create order */
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }

        AwaitUtils.sleepSeconds(10);

        PFUStatusValidator pfuStatusValidator=PFUStatusValidator.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .status(pfuContext.getInitialOrderState())
                .build();
        pfuStatusValidator.validateDBEntities();

        orderContext.setIsUpdatePrescriptionWhileOrderProcessingInWH(true);

        if (pfuContext.getPrescriptionUpdatedBy().equalsIgnoreCase("juno")) {
            CreatePrescriptionHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        } else if (pfuContext.getPrescriptionUpdatedBy().equalsIgnoreCase("OPENAI")) {
            CrmPushPrescriptionOrdersHelper.builder()
                    .orderContext(orderContext)
                    .pfuContext(pfuContext)
                    .build()
                    .test();
        }

        if(pfuContext.isInsuranceOrder()) {
            orderContext.setIsMedibuddyApprovalRequired(pfuContext.isMedibuddyApproveStatus());
            MedibuddyApprovalRejectionHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        }

        if(pfuContext.isPaymentCompleted()) {
            PlaywrightRePaymentHelper.builder()
                    .orderContext(orderContext)
                    .mobileNumber(Countries.IN.getDefaultPhoneNumber())
                    .build()
                    .test();
        }

        AwaitUtils.sleepSeconds(10);

        pfuStatusValidator=PFUStatusValidator.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .status(pfuContext.getFinalOrderState())
                .build();
        pfuStatusValidator.validateDBEntities();

        if(pfuContext.getPrescriptionAdditionCount()>1){
            if (pfuContext.getPrescriptionUpdatedBy().equalsIgnoreCase("juno")) {
                CreatePrescriptionHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();
            } else if (pfuContext.getPrescriptionUpdatedBy().equalsIgnoreCase("bot")) {
                CrmPushPrescriptionOrdersHelper.builder()
                        .orderContext(orderContext)
                        .pfuContext(pfuContext)
                        .build()
                        .test();
            }
            pfuStatusValidator.validateDBEntities();
        }
        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
