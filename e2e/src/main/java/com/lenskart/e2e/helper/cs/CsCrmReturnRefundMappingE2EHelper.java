package com.lenskart.e2e.helper.cs;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.cs.helpers.CancellationHelper;
import com.lenskart.cs.helpers.CrmReturnRefundMappingHelper;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class CsCrmReturnRefundMappingE2EHelper extends BaseHelper<Object, Object> implements ServiceHelper {

    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;
    CsOrderContext csOrderContext;

    @Override
    public ServiceHelper init() {
        /* Juno Helper to create order */
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }
        AwaitUtils.sleepSeconds(15);

        CancellationHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

        AwaitUtils.sleepSeconds(15);
        CrmReturnRefundMappingHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();


        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}
