package com.lenskart.e2e.helper.nexs;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.api.ImsService;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.PickingDbutils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.picking.*;
import com.lenskart.nexs.helpers.state.TrayMakingHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.util.OrderAdopterUtil;
import lombok.experimental.SuperBuilder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SuperBuilder
public class LensPickingHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsOrderContext nexsOrderContext;
    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;
    String nexs_order_id, barcode,lens_summary_id;
    LensSummaryCompleteHelper lensSummaryCompleteHelper;
    LensScanHelper lensScanHelper;
    List<String> pickingIds;

    @Override
    public ExecutionHelper init() {
        pickingIds = new ArrayList<>();
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }

        /* Scm helper to move the order to scm states */
        OrderAdopterUtil.syncOrder(String.valueOf(orderContext.getOrderId()));
        nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
                        .build())
                .incrementId(String.valueOf(orderContext.getOrderId()))
                .build();
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .sequentialTransition(true)
                .build();
        stateManager.test();
        nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder().build()).build();
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        return this;
    }


    @Override
    public ExecutionHelper
    orchestrateFlow() {

        //create summary
        CreateSummaryHelper createSummaryHelper = CreateSummaryHelper.builder().nexsOrderContext(nexsOrderContext).location(Constants.EYEFRAME).category(Constants.FR1_CATEGORY).build();
        createSummaryHelper.test();
        String summary_id = RestUtils.getValueFromResponse(createSummaryHelper.getResponse(), "data.PICKINGSUMMARY.id").toString();
        nexsOrderContext.setSummary_id(summary_id);


        //get summary details
        GetPickingDetailsHelper getPickingDetailsHelper = GetPickingDetailsHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build();
        getPickingDetailsHelper.test();

        Object responseObj = RestUtils.getValueFromResponse(getPickingDetailsHelper.getResponse(), "data.id");
        List<String> picklistIds;

        if (responseObj instanceof List<?>) {
            picklistIds = ((List<?>) responseObj).stream()
                    .map(Object::toString)
                    .toList();
        } else {
            picklistIds = new ArrayList<>();
        }
        for (String item : picklistIds) {
            nexsOrderContext.setPicklistId(item);
            if (PickingDbutils.getPickingDetails(nexsOrderContext).equals(Constants.PICKLIST_CREATED)) {
                nexsOrderContext.setPicklistId(item);
                String productId = PickingDbutils.getProductIdByPicklistId(nexsOrderContext);
                nexsOrderContext.setProductId(productId);
                String shipmentId = PickingDbutils.getShipmentId(nexsOrderContext);
                nexsOrderContext.setShippingId(shipmentId);
                break;
            }
        }

        //scan barcode
        PickingScanBarcodeHelper pickingScanBarcodeHelper = PickingScanBarcodeHelper.builder().nexsOrderContext(nexsOrderContext).build();
        pickingScanBarcodeHelper.test();
        String fitting_id = PickingDbutils.getFittingId(nexsOrderContext);
        nexsOrderContext.setFittingId(fitting_id);
        List<Map<String, Object>> nexs_order_ids = WMSDbUtils.getNexsOrderId(nexsOrderContext);
        if (!nexs_order_ids.isEmpty() && nexs_order_ids.getFirst().containsKey(Constants.NEXS_ORDER_ID)) {
            nexs_order_id = nexs_order_ids.getFirst().get("nexs_order_id").toString();
        }
        nexsOrderContext.setNexsOrderId(nexs_order_id);

        //close picking summary
        ClosePickingSummaryHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        nexsOrderContext.setIsFramePickingEnabled(true);
        nexsOrderContext.setOrderProcessingType(Constants.FR1_PROCESSING_TYPE);
        nexsOrderContext.setBarcodes(Collections.singletonList(pickingScanBarcodeHelper.getBarcode()));
        nexsOrderContext.setBarcode(pickingScanBarcodeHelper.getBarcode());

        //TRAY MAKING FOR FRAME
        TrayMakingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        PickingViewHelper pickingViewHelper = PickingViewHelper.builder().nexsOrderContext(nexsOrderContext).build();
        pickingViewHelper.test();
        nexsOrderContext.setFittingId(RestUtils.getValueFromResponse(pickingViewHelper.getResponse(), "data.fittingId").toString());
        nexsOrderContext.setTrayId(RestUtils.getValueFromResponse(pickingViewHelper.getResponse(), "data.trayId").toString());

        PickingDetailsHelper pickingDetailsHelper = PickingDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build();
        pickingDetailsHelper.test();
        List<String> pickingDetailsIds = pickingDetailsHelper.getResponse().jsonPath().getList("data.lensDetailsList.pickingDetailsId", String.class);
        for (String pickingDetailsId : pickingDetailsIds) {
            nexsOrderContext.setPicklistId(pickingDetailsId);
            pickingIds.add(pickingDetailsId);
            String productId = PickingDbutils.getProductIdByPicklistId(nexsOrderContext);
            nexsOrderContext.setProductId(productId);
            barcode = ImsService.fetchBarcodeForGivenPid(Constants.AVAILABLE, Constants.GOOD, true, nexsOrderContext.getFacilityCode(), nexsOrderContext.getProductId(), Constants.AVAILABLE);
            nexsOrderContext.setBarcode(barcode);
            LensScanHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            LensSummaryCompleteHelper.builder().nexsOrderContext(nexsOrderContext).build();
        }
        nexsOrderContext.setPickingDetailsIds(pickingDetailsIds);
        nexsOrderContext.setPickingSummaryId(PickingDbutils.getLensSummaryId(nexsOrderContext));

        LensSummaryCompleteHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
