package com.lenskart.e2e.helper.nexs;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.PickingDbutils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.helpers.picking.CreateWaveHelper;
import com.lenskart.nexs.helpers.picking.FetchWaveDetailsHelper;
import com.lenskart.nexs.helpers.state.AddverbPickingHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.util.OrderAdopterUtil;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@SuperBuilder
public class AddverbE2EHelper extends NexsBaseHelper implements ExecutionHelper {
    OrderContext orderContext;
    JSONObject payload;
    Response response;
    NexsOrderContext nexsOrderContext;
    String shippingPackageId;
    String nexs_order_id;
    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;

    @Override
    public ExecutionHelper init() {
        /* Juno Helper to create order */
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }

        /* Scm helper to move the order to scm states */
        OrderAdopterUtil.syncOrder(String.valueOf(orderContext.getOrderId()));
        nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
                        .build())
                .incrementId(String.valueOf(orderContext.getOrderId()))
                .build();
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .sequentialTransition(true)
                .build();
        stateManager.test();
        nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder().build()).build();
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
       //create a wave
        CreateWaveHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        //fetch wave details
        FetchWaveDetailsHelper fetchWaveDetailsHelper = FetchWaveDetailsHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build();
        fetchWaveDetailsHelper.test();
        boolean success = AwaitUtils.pollUntil(
                () -> {
                    // Run the helper and check status
                    fetchWaveDetailsHelper.test();
                    return "DONE".equals(nexsOrderContext.getStatus());

                },
                "status to be DONE",
                Duration.ofSeconds(120),
                Duration.ofSeconds(10),
                true
        );
        if (!success) {
            throw new RuntimeException("Wave status is not DONE");
        }

        //pick addverb order with addverb api
       List<Map<String, Object>> shippingPackageIds= PickingDbutils.getShippingPackageIdsByGroupId(nexsOrderContext);
        if (!shippingPackageIds.isEmpty() && shippingPackageIds.getFirst().containsKey("shipment_id")) {
            // Extract just the shipment_id value as a string
            shippingPackageId = shippingPackageIds.getFirst().get("shipment_id").toString();
        }
        nexsOrderContext.setShippingId(shippingPackageId);
        nexsOrderContext.setFittingId(nexsOrderContext.getGroupId());
        List<Map<String, Object>> nexs_order_ids= WMSDbUtils.getNexsOrderId(nexsOrderContext);
        if (!nexs_order_ids.isEmpty() && nexs_order_ids.getFirst().containsKey("nexs_order_id")) {
            nexs_order_id = nexs_order_ids.getFirst().get("nexs_order_id").toString();
        }
        nexsOrderContext.setShippingId(shippingPackageId);
        nexsOrderContext.setNexsOrderId(nexs_order_id);
        nexsOrderContext.setFacilityCode(Constants.BHIWADI_WAREHOUSE_FACILITY);
        if(Objects.isNull((RestUtils.getValueFromResponse(fetchWaveDetailsHelper.getResponse(),"wave.message")))) {
            AddverbPickingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        }else{
            log.info("There are no ASRS eligible orders");
        }
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }

}
