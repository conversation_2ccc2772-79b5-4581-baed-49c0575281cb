package com.lenskart.e2e.helper.cs;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.cs.helpers.CsAwaitedRTOHelper;
import com.lenskart.cs.helpers.TrackingMiddlewareHelper;
import com.lenskart.cs.helpers.TrackingMiddlewareUIHelper;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.validator.E2EValidator;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.util.OrderAdopterUtil;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@SuperBuilder
@Slf4j
public class CsTrackingMiddlewareHelper extends BaseHelper<Object, Object> implements ServiceHelper {
    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;
    CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails;
    List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList;
    ScmOrderContext scmOrderContext;


    @SneakyThrows
    @Override
    public ServiceHelper init() {

        /* Juno Helper to create order*/

        if (orderContext.isPosOrder()) {

            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }

        //Scm helper to move the order to scm states
        OrderAdopterUtil.syncOrder(String.valueOf(orderContext.getOrderId()));

        NexsOrderContext nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
                        .build())
                .incrementId(String.valueOf(orderContext.getOrderId()))
                .build();

        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .sequentialTransition(true)
                .build();

        stateManager.test();

        List<Map<String, Object>> orderStatus = ScmDbUtils.getAWBNumber(String.valueOf(orderContext.getOrderId()));

        ScmDbUtils.updateCarrierCode(nexsOrderContext.getShippingProviderCode(),String.valueOf(orderContext.getOrderId()));

        TrackingMiddlewareHelper trackingMiddlewareHelper =
                TrackingMiddlewareHelper.builder()
                        .orderContext(orderContext)
                        .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                        .trackingMiddlewareDetails(trackingMiddlewareDetails)
                        .awbNo(orderStatus.getFirst().get("tracking_no").toString())
                        .build();

        trackingMiddlewareHelper.test();

        log.info("Successfully completed processing of events through middleware.");

       TrackingMiddlewareUIHelper trackingMiddlewareUIHelper =
                TrackingMiddlewareUIHelper.builder()
                        .orderContext(orderContext)
                        .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                        .trackingMiddlewareDetails(trackingMiddlewareDetails)
                        .awbNo(orderStatus.getFirst().get("tracking_no").toString())
                        .build();

        trackingMiddlewareUIHelper.test();

        log.info("Successfully processed pushed events received in the UI API response via middleware.");

        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        E2EValidator validator = E2EValidator
                .builder()
                .orderContext(orderContext)
                .build();




        // juno validator
        // nexs validator
        // cse validator
        // pos validator


        validator.validateNode();
        validator.validateDBEntities();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
