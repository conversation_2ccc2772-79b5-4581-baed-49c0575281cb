package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import org.testng.annotations.DataProvider;

import java.util.List;
import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class JunoE2EUSDataProvider {

    private OrderContext getBaseOrderContext() {
        return OrderContext.builder()
                .phoneNumber(Countries.US.getDefaultPhoneNumber())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.US)
                        .pinCode(Countries.US.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                .build();
    }

    @DataProvider(name = "usEyeglassPowerOrder")
    public Object[][] usEyeglassPowerOrder() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(US_EYEGLASSES.getProductId())
//                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(US_EYEGLASSES.getProductId())
//                                        .powerType(BIFOCAL)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(US_EYEGLASSES.getProductId())
//                                        .powerType(ZERO_POWER)
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "usEyeglassWithoutPower")
    public Object[][] usEyeglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(US_EYEGLASSES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "usSunglassWithPower")
    public Object[][] usSunglassWithPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(US_SUNGLASSES.getProductId())
//                                .powerType(SUNGLASSES)
                                .build())
                }
        };
    }

    @DataProvider(name = "usSunglassWithoutPower")
    public Object[][] usSunglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(US_SUNGLASSES.getProductId())
                                .build())
                }
        };
    }



    @DataProvider(name = "usEyeSunglassOrder")
    public Object[][] usEyeSunglassOrder() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(US_EYEGLASSES.getProductId())
//                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(US_EYEGLASSES.getProductId())
//                                        .powerType(BIFOCAL)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(US_SUNGLASSES.getProductId())
                                        .build()
                        ),

                }
        };
    }

    //No pid mapped currently
    @DataProvider(name = "usAccessories")
    public Object[][] usAccessories() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(US_ACCESSORIES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "usEyeglassPowerOrderWithGV")
    public Object[][] usEyeglassPowerOrderWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(US_EYEGLASSES.getProductId())
//                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(US_SUNGLASSES.getProductId())
//                                        .powerType(SUNGLASSES)
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_US_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }


    @DataProvider(name = "usEyeSunglassOrderWithGV")
    public Object[][] usEyeSunglassOrderWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(US_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(US_SUNGLASSES.getProductId())
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_US_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "useyeglassSingleVisionPowerwithWallet")
    public Object[][] useyeglassSingleVisionPowerwithWallet() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(US_EYEGLASSES.getProductId())
//                                .powerType(SINGLE_VISION)
                                .build())

                }
        };
    }
}