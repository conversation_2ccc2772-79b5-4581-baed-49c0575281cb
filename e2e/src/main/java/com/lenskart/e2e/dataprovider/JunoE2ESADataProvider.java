package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.commons.utils.GenericUtils;
import org.testng.annotations.DataProvider;
import java.util.List;
import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class JunoE2ESADataProvider {

    private OrderContext getBaseOrderContext() {
        return OrderContext.builder()
                .phoneNumber(Countries.SA.getDefaultPhoneNumber())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.SA)
                        .pinCode(Countries.SA.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                .build();
    }


    @DataProvider(name = "saEyeglassPowerOrder")
    public Object[][] saEyeglassPowerOrder() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(SA_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SA_EYEGLASSES.getProductId())
                                        .powerType(BIFOCAL)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SA_EYEGLASSES.getProductId())
                                        .powerType(ZERO_POWER)
                                        .build()
                        ),
                }
        };
    }

    @DataProvider(name = "saEyeglassWithoutPower")
    public Object[][] saEyeglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SA_EYEGLASSES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "saSunglassWithPower")
    public Object[][] saSunglassWithPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SA_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build())
                }
        };
    }

    @DataProvider(name = "saSunglassWithoutPower")
    public Object[][] saSunglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SA_SUNGLASSES.getProductId())
                                .build())
                }
        };
    }



    @DataProvider(name = "saEyeSunContactLensOrder")
    public Object[][] saEyeSunContactLensOrder() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(SA_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SA_EYEGLASSES.getProductId())
                                        .powerType(BIFOCAL)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SA_SUNGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SA_CONTACT_LENS.getProductId())
                                        .build()
                        ),

                }
        };
    }

    //No pid mapped currently so not added Testcase for accessories
    @DataProvider(name = "saAccessories")
    public Object[][] saAccessories() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SA_ACCESSORIES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "saEyeglassPowerOrderWithGV")
    public Object[][] saEyeglassPowerOrderWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                        OrderContext.ProductList.builder()
                                .productId(SA_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SA_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()
                                ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_SA_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }


    @DataProvider(name = "saEyeSunCLOrderWithGV")
    public Object[][] saEyeSunCLOrderWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(SA_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SA_SUNGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SA_CONTACT_LENS.getProductId())
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_SA_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "saeyeglassSingleVisionPowerwithWallet")
    public Object[][] saeyeglassSingleVisionPowerwithWallet() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SA_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build()),

                        true

                }
        };
    }
}