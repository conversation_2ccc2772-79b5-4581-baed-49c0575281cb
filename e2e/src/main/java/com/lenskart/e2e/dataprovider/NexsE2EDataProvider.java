package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.oms.dto.DistributorCustomerAddressDetailsDto;
import com.lenskart.oms.dto.DistributorCustomerDetailsDto;
import com.lenskart.oms.enums.AddressType;
import com.lenskart.pos.model.POS;
import org.apache.commons.lang3.RandomStringUtils;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class NexsE2EDataProvider {

    private static final OrderContext baseOrderContext = OrderContext.builder()
            .phoneNumber(Countries.IN.getDefaultPhoneNumber())
            .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                    .country(Countries.IN)
                    .pinCode(Countries.IN.getDefaultPinCode())
                    .build())
            .paymentMethod(PaymentMethod.COD)
            .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
            .build();

    private static final OrderContext baseOrderContextSG = OrderContext.builder()
            .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                    .country(Countries.SG)
                    .pinCode(Countries.SG.getDefaultPinCode())
                    .build())
            .paymentMethod(PaymentMethod.OFFLINE_CASH)
            .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
            .build();

    private static final OrderContext baseOrderContextAE = OrderContext.builder()
            .phoneNumber(Countries.AE.getDefaultPhoneNumber())
            .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                    .country(Countries.AE)
                    .pinCode(Countries.AE.getDefaultPinCode())
                    .build())
            .paymentMethod(PaymentMethod.OFFLINE_CASH)
            .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
            .build();


    @DataProvider(name = "eyeglassWithPower")
    public Object[][] eyeglassWithPower() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "eyeglassWithPowerForImsCidPickingChecks")
    public Object[][] eyeglassWithPowerForImsCidPickingChecks() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.PICKED)
                                .build())
                }
        };
    }

    @DataProvider(name = "eyeglassWithPowerSingeVision")
    public Object[][] eyeglassWithPowerSingeVision() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .isPrescriptionRequired(true)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "eyeglassWithPowerBifocal")
    public Object[][] eyeglassWithPowerBifocal() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(BIFOCAL)
                                .isPrescriptionRequired(true)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "sunglassesWithPower")
    public Object[][] sunglassesWithPower() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .isPrescriptionRequired(true)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "eyeglassFrameOnly")
    public Object[][] eyeglassFrameOnly() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "sunglasses")
    public Object[][] sunglasses() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "FrameOnlySG")
    public Object[][] sgEyeFrameOnly() {
        return new Object[][]{
                {
                        baseOrderContextSG,
                        List.of(OrderContext.ProductList.builder()
                                .productId(SG_EYEGLASSES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "FrameOnlyAE")
    public Object[][] aeEyeFrameOnly() {
        return new Object[][]{
                {
                        baseOrderContextAE,
                        List.of(OrderContext.ProductList.builder()
                                .productId(AE_EYEGLASSES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "accessories")
    public Object[][] accessories() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_ACCESSORIES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "contactLens")
    public Object[][] contactLens() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .isPrescriptionRequired(true)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "contactLensZeroPower")
    public Object[][] contactLensZeroPower() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS_ZERO_POWER.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "fr0_Loyalty")
    public Object[][] fr0_Loyalty() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("2" + GenericUtils.genrateRandomNumericString(9))
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .finalState(NexsOrderState.AWB_CREATED)
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_LOYALTY.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "nexsCancellationContext")
    public Object[][] nexsCancellationContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.FULL_CANCELLATION)
                        .cancellationReason("test order")
                        .cancelledBy("<EMAIL>")
                        .cancellationSource(Client.VSM.getDisplayName())
                        .refundMethod(RefundMethod.SOURCE.getDisplayName())
                        .cancellationReasonID(204).build()

                }
        };
    }

    @DataProvider(name = "addverb")
    public Object[][] addverbContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "nexsOrderReassignmnet")
    public Object[][] nexsReturnContext() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .finalState(NexsOrderState.IN_PICKING)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "qcFail")
    public Object[][] nexsQcFail() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .finalState(NexsOrderState.IN_QC)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "picking")
    public Object[][] nexsPicking() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("121004")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "pickingSG")
    public Object[][] nexsPickingSG() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("86153734")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_EYEGLASSES_2.getProductId())
                                                .finalState(NexsOrderState.IN_PICKING)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode(Countries.SG.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "FR1QcFail")
    public Object[][] nexsQcFailFR1() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES_1.getProductId()).powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.IN_QC)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "nexsOrderAutoJit")
    public Object[][] nexsAutoJitContext() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(BIFOCAL)
                                .isPrescriptionRequired(true)
                                .build())

                }
        };
    }

    @DataProvider(name = "nexsUpdatePower")
    public Object[][] nexsUpdatePowerContext() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .isPrescriptionRequired(true)
                                                .finalState(NexsOrderState.IN_PICKING)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()

                }
        };

    }

    @DataProvider(name = "nexsOrderManualJit")
    public Object[][] nexsManualJiContext() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .isPrescriptionRequired(true)
                                .build())

                }
        };
    }

    @DataProvider(name = "singaporeManualJitOrderProcess")
    public Object[][] singaporeManualJitOrderProcessContext() {
        return new Object[][]{
                {
                        baseOrderContextSG,
                        List.of(OrderContext.ProductList.builder()
                                //hardcoding pid due to preprod inventory picture at juno end
                                .productId("131511")
                                .powerType(SINGLE_VISION)
                                .isPrescriptionRequired(true)
                                .build())

                }
        };
    }

    @DataProvider(name = "dubaiManualJitOrderProcess")
    public Object[][] dubaiManualJitOrderProcessContext() {
        return new Object[][]{
                {
                        baseOrderContextAE,
                        List.of(OrderContext.ProductList.builder()
                                //hardcoding pid due to preprod inventory picture at juno end
                                .productId("146024")
                                .powerType(SINGLE_VISION)
                                .isPrescriptionRequired(true)
                                .build())

                }
        };
    }

    @DataProvider(name = "NonNDDPincode")
    public Object[][] nonNDDPincode() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .finalState(NexsOrderState.IN_PICKING)
                                .build())
                }
        };
    }

    @DataProvider(name = "NDDPincode")
    public Object[][] nddPincode() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.IN_PICKING)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("400001")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "bulkOrder")
    public Object[][] bulkOrder() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .itemType(ItemType.BULK_ORDER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_ADMIN_STORE.getStoreId())
                                        .build())
                                .headers(OrderContext.Headers.builder().client(Client.POS_WEB).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "storeEyeglassZeroPowerOrder")
    public Object[][] storeEyeglassZeroPowerOrder() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "storeSunglassesWithPowerOrder")
    public Object[][] storeSunglassesWithPowerOrder() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "storeSingleVisionOrder")
    public Object[][] storeSingleVisionOrder() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "storeContactLensOrder")
    public Object[][] storeContactLensOrder() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_CONTACT_LENS.getProductId())
                                                .itemType(ItemType.CONTACT_LENS)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "storeOtcOrder")
    public Object[][] storeOtcOrder() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .itemType(ItemType.OTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
        };
    }

    @DataProvider(name = "storeBulkOrder")
    public Object[][] storeBulkOrder() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .itemType(ItemType.BULK_ORDER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_ADMIN_STORE.getStoreId())
                                        .build())
                                .headers(OrderContext.Headers.builder().client(Client.POS_WEB).build())
                                .build()
                }
        };
    }


    @DataProvider(name = "storeEyeglassOrderForConsolidation")
    public Object[][] storeEyeglassOrderForConsolidation() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.INVOICED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "createDOCustomer")
    public Object[][] createDOCustomer() {
        DistributorCustomerDetailsDto distributorCustomerDetailsDto;
        DistributorCustomerAddressDetailsDto billingAddress;
        DistributorCustomerAddressDetailsDto shippingAddress;

        billingAddress = new DistributorCustomerAddressDetailsDto();
        billingAddress.setAddressLine1("test");
        billingAddress.setAddressLine2("test");
        billingAddress.setCity("Bangalore");
        billingAddress.setCountry("IN");
        billingAddress.setPincode(560038);
        billingAddress.setState("Karnataka");
        billingAddress.setCreatedBy("test");
        billingAddress.setUpdatedBy("test");
        billingAddress.setPhone("**********");
        billingAddress.setAddressType(AddressType.BILLING);

        shippingAddress = new DistributorCustomerAddressDetailsDto();
        shippingAddress.setAddressLine1("test");
        shippingAddress.setAddressLine2("test");
        shippingAddress.setCity("Bangalore");
        shippingAddress.setCountry("IN");
        shippingAddress.setPincode(560038);
        shippingAddress.setState("Karnataka");
        shippingAddress.setCreatedBy("test");
        shippingAddress.setUpdatedBy("test");
        shippingAddress.setPhone("**********");
        shippingAddress.setAddressType(AddressType.SHIPPING);

        distributorCustomerDetailsDto = new DistributorCustomerDetailsDto();
        distributorCustomerDetailsDto.setName("test" + System.currentTimeMillis());
        distributorCustomerDetailsDto.setEmail("test" + System.currentTimeMillis() + "@test.com");
        distributorCustomerDetailsDto.setMobile("**********");
        distributorCustomerDetailsDto.setCustomerEnabled(true);
        distributorCustomerDetailsDto.setCreatedBy("test");
        distributorCustomerDetailsDto.setUpdatedBy("test");
        distributorCustomerDetailsDto.setGstin("");
        distributorCustomerDetailsDto.setCode("te-" + System.currentTimeMillis() + RandomStringUtils.randomNumeric(3));
        distributorCustomerDetailsDto.setCustomerAddressDetails(List.of(billingAddress, shippingAddress));

        return new Object[][]{
                {distributorCustomerDetailsDto}
        };
    }

    @DataProvider(name = "fofoOtcOrder")
    public Object[][] fofoStoreOtcOrder() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .itemType(ItemType.OTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }

        };
    }
}

