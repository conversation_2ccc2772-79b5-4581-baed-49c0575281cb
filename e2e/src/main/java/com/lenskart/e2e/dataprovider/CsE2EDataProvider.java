package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.constants.Constants;
import com.lenskart.commons.model.*;
import com.lenskart.commons.utils.DateUtils;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.model.POS;
import com.lenskart.scm.model.ScmOrderContext;
import org.testng.annotations.DataProvider;
import java.util.List;
import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.IN_EYEGLASSES;
import static com.lenskart.commons.model.ProductId.IN_SUNGLASSES;
import static com.lenskart.commons.model.ProductId.*;


public class CsE2EDataProvider {

    @DataProvider(name = "rtoContextCODOrder")
    public Object[][] rtoContextCODOrder() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").build()).build()

                }
        };
    }

    @DataProvider(name = "updatePowerMedibuddyOrderByBotApprovePayment")
    public Object[][] updatePowerMedibuddyOrderByBotApprovePayment() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .GiftVoucherList(OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.MEDIBUDDY_GIFTCODE)
                                .isGiftVoucherApplicable(true)
                                .build())
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("OPENAI")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(true)
                                .isInsuranceOrder(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerMedibuddyOrderByJunoApprovePayment")
    public Object[][] updatePowerMedibuddyOrderByJunoApprovePayment() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .GiftVoucherList(OrderContext.GiftVoucherList.builder()
                                        .giftVoucher(GiftVoucher.MEDIBUDDY_GIFTCODE)
                                        .isGiftVoucherApplicable(true)
                                        .build())
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("juno")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(true)
                                .isInsuranceOrder(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerMedibuddyOrderByBotRejectPayment")
    public Object[][] updatePowerMedibuddyOrderByBotRejectPayment() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .GiftVoucherList(OrderContext.GiftVoucherList.builder()
                                        .giftVoucher(GiftVoucher.MEDIBUDDY_GIFTCODE)
                                        .isGiftVoucherApplicable(true)
                                        .build())
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("OPENAI")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.CANCELED.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerMedibuddyOrderByJunoRejectPayment")
    public Object[][] updatePowerMedibuddyOrderByJunoRejectPayment() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .GiftVoucherList(OrderContext.GiftVoucherList.builder()
                                        .giftVoucher(GiftVoucher.MEDIBUDDY_GIFTCODE)
                                        .isGiftVoucherApplicable(true)
                                        .build())
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("juno")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.CANCELED.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerWEBDTCByBotCOD")
    public Object[][] updatePowerWEBDTCByBotCOD() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("OPENAI")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .initialOrderState(OrderState.PROCESSING_POWER_FOLLOWUP.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING_POWER_FOLLOWUP_VERIFY.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerWEBDTCByBotPending")
    public Object[][] updatePowerWEBDTCByBotPending() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.NET_BANKING)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("OPENAI")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .isPaymentCompleted(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING_POWER_FOLLOWUP_VERIFY.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerWEBDTCByBotPrepaid")
    public Object[][] updatePowerWEBDTCByBotPrepaid() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("OPENAI")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .initialOrderState(OrderState.PROCESSING_POWER_FOLLOWUP.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING_POWER_FOLLOWUP_VERIFY.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerWEBDTCByJunoCOD")
    public Object[][] updatePowerWEBDTCByJunoCOD() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("juno")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .initialOrderState(OrderState.PROCESSING_POWER_FOLLOWUP.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerWEBDTCByJunoPending")
    public Object[][] updatePowerWEBDTCByJunoPending() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.NET_BANKING)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("juno")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .isPaymentCompleted(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerWEBDTCByJunoPrepaid")
    public Object[][] updatePowerWEBDTCByJunoPrepaid() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.NET_BANKING)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("juno")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .initialOrderState(OrderState.PROCESSING_POWER_FOLLOWUP.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerWEBDTCByJunoTwicePending")
    public Object[][] updatePowerWEBDTCByJunoTwicePending() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.NET_BANKING)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("juno")
                                .prescriptionAdditionCount(2)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .isPaymentCompleted(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerWEBDTCByBotTwicePending")
    public Object[][] updatePowerWEBDTCByBotTwicePending() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.NET_BANKING)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("OPENAI")
                                .prescriptionAdditionCount(2)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .isPaymentCompleted(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING_POWER_FOLLOWUP_VERIFY.getDisplayName())
                                .build()

                }
        };
    }


    @DataProvider(name = "updatePowerWEBDTCByJunoInitialPowerGivenCOD")
    public Object[][] updatePowerWEBDTCByJunoInitialPowerGivenCOD() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("juno")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .initialOrderState(OrderState.PROCESSING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING.getDisplayName())
                                .build()

                }
        };
    }


    @DataProvider(name = "updatePowerWEBDTCByBotInitialPowerGivenCOD")
    public Object[][] updatePowerWEBDTCByBotInitialPowerGivenCOD() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("OPENAI")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .initialOrderState(OrderState.PROCESSING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING_POWER_FOLLOWUP_VERIFY.getDisplayName())
                                .build()

                }
        };
    }


    @DataProvider(name = "updatePowerWEBDTCByBotInitialPowerGivenPending")
    public Object[][] updatePowerWEBDTCByBotInitialPowerGivenPending() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.NET_BANKING)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("OPENAI")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .isPaymentCompleted(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING_POWER_FOLLOWUP_VERIFY.getDisplayName())
                                .build()

                }
        };
    }


    @DataProvider(name = "updatePowerWEBDTCByJunoInitialPowerGivenPending")
    public Object[][] updatePowerWEBDTCByJunoInitialPowerGivenPending() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.NET_BANKING)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("juno")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .isPaymentCompleted(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING.getDisplayName())
                                .build()

                }
        };


    }

    @DataProvider(name = "updatePowerWEBDTCByJunoPendingNoPower")
    public Object[][] updatePowerWEBDTCByJunoPendingNoPower() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.NET_BANKING)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(false)
                                .isPaymentCompleted(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING_POWER_FOLLOWUP.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerMedibuddyOrderByBotApprovePaymentCOCOLensOnly")
    public Object[][] updatePowerMedibuddyOrderByBotApprovePaymentCOCOLensOnly() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_LENS_ONLY_1.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.LENS_ONLY_LOCAL_FITTING_IN_COCO)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("OPENAI")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(true)
                                .isInsuranceOrder(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING_POWER_FOLLOWUP_VERIFY.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerMedibuddyOrderByBotRejectPaymentCOCOLensOnly")
    public Object[][] updatePowerMedibuddyOrderByBotRejectPaymentCOCOLensOnly() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_LENS_ONLY_1.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.LENS_ONLY_LOCAL_FITTING_IN_COCO)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("OPENAI")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.CANCELED.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerMedibuddyOrderByJunoApprovePaymentCOCOLensOnly")
    public Object[][] updatePowerMedibuddyOrderByJunoApprovePaymentCOCOLensOnly() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_LENS_ONLY_1.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.LENS_ONLY_LOCAL_FITTING_IN_COCO)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("juno")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(true)
                                .isInsuranceOrder(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.PROCESSING.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "updatePowerMedibuddyOrderByJunoRejectPaymentCOCOLensOnly")
    public Object[][] updatePowerMedibuddyOrderByJunoRejectPaymentCOCOLensOnly() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_LENS_ONLY_1.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.LENS_ONLY_LOCAL_FITTING_IN_COCO)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(),
                        CsOrderContext.PFUContext.builder()
                                .prescriptionUpdatedBy("juno")
                                .prescriptionAdditionCount(1)
                                .medibuddyApproveStatus(false)
                                .isInsuranceOrder(true)
                                .initialOrderState(OrderState.PENDING.getDisplayName())
                                .finalOrderState(OrderState.CANCELED.getDisplayName())
                                .build()

                }
        };
    }

    @DataProvider(name = "rtoContextPrepaidOrder")
    public Object[][] rtoContextPrepaidOrder() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").build()).build()

                }
        };
    }

    @DataProvider(name = "rtoContextPrepaidQCStatusFail")
    public Object[][] rtoContextPrepaidQCStatusFail() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Fail").build()).build()

                }
        };
    }

    @DataProvider(name = "rtoContextCODWithGoldMembership")
    public Object[][] rtoContextCODWithGoldMembership() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_LOYALTY.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build()).build(),CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").build()).build()
                }
        };
    }
    @DataProvider(name = "directReceivingContextWEBDTC")
    public Object[][] directReceivingContextWEBDTC() {
        return new Object[][]{

                //Happy flow of direct receiving
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(ProductId.IN_EYEGLASSES.getProductId()).identifierType("online").psuedoGatepass("").returnType("online").rtoItems("false").productBadRecall(false).qcFailParams(null).qcStatus("QC Pass").build()

                }
        };

    }

    @DataProvider(name = "directReceivingContextWEBDTCItemMissing")
    public Object[][] directReceivingContextWEBDTCItemMissing() {
        return new Object[][]{

                //Case where item is missing while receiving
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).identifierType("online").psuedoGatepass("").returnType("online").rtoItems("false").productBadRecall(false).qcFailParams(null).notReceivedYN(true).qcStatus("QC Pass").build()

                }
        };

    }

    @DataProvider(name = "directReceivingContextCOCODTC")
    public Object[][] directReceivingContextCOCODTC() {
        return new Object[][]{

                //Happy flow of direct receiving for COCODTC
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).identifierType("online").psuedoGatepass("").returnType("online").rtoItems("false").productBadRecall(false).qcFailParams(null).qcStatus("QC Pass").build()

                }
        };

    }

    @DataProvider(name = "directReceivingContextCOCOB2B")
    public Object[][] directReceivingContextCOCOB2B() {
        return new Object[][]{

                //Direct receiving for COCOB2B
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.AE.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(AE_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.AE)
                                        .pinCode("570024")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.AE)
                                        .storeId(POS.AE_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(),CsOrderContext.builder().productIDToBeReturned(ProductId.AE_EYEGLASSES.getProductId()).identifierType("online").psuedoGatepass("").returnType("online").rtoItems("false").productBadRecall(false).qcFailParams(null).qcStatus("QC Pass").build()
                }
        };

    }

    @DataProvider(name = "directReceivingContextSGWEBB2B")
    public Object[][] directReceivingContextSGWEBB2B() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.SG.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_EYEGLASSES.getProductId())
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode(Countries.SG.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(SG_EYEGLASSES.getProductId()).identifierType("online").psuedoGatepass("").returnType("online").rtoItems("false").productBadRecall(false).qcFailParams(null).notReceivedYN(true).qcStatus("QC Pass").build()

                }
        };

    }

    @DataProvider(name = "directReceivingContextSGCOCODTC")
    public Object[][] directReceivingContextSGCOCODTC() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.SG.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode(Countries.SG.getDefaultPinCode())
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SG)
                                        .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(),CsOrderContext.builder().productIDToBeReturned(SG_SUNGLASSES.getProductId()).identifierType("online").psuedoGatepass("").returnType("online").rtoItems("false").productBadRecall(false).qcFailParams(null).notReceivedYN(true).qcStatus("QC Pass").build()
                }
        };

    }

    @DataProvider(name = "directReceivingContextFOFOB2B")
    public Object[][] directReceivingContextFOFOB2B() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(),CsOrderContext.builder().productIDToBeReturned(IN_SUNGLASSES.getProductId()).identifierType("online").psuedoGatepass("").returnType("online").rtoItems("false").productBadRecall(false).qcFailParams(null).notReceivedYN(true).qcStatus("QC Pass").build()
                }
        };

    }

    @DataProvider(name = "emptyBoxReceiving")
    public Object[][] emptyBoxReceiving() {
        return new Object[][]{
                //Happy flow of empty box receiving
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(ProductId.IN_EYEGLASSES.getProductId()).returnType("online").build()
                }
        };

    }

    //Verify return/refund from vsm for webdtc with multiple payment methods. Add combination for payments: COD + SC
    @DataProvider(name = "returnContextMultiplePayment")
    public Object[][] returnContextMultiplePayment() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .productLists(List.of(OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .finalState(NexsOrderState.DISPATCHED)
                                        .build()))
                                .StoreCreditList(OrderContext.StoreCreditList.builder()
                                        .storeCode(StoreCredit.STORECODE_IN)
                                        .isStoreCreditApplicable(true)
                                        .build())
                                .GiftVoucherList(OrderContext.GiftVoucherList.builder()
                                        .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                        .isGiftVoucherApplicable(true)
                                        .build())
                                .build()
                        ,CsOrderContext.builder().productIDToBeReturned(IN_SUNGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").status("PENDING").build()).refundMethod(RefundMethod.SOURCE.getDisplayName()).needApproval(false).returnType("online").rtoItems("false").doRefund(true).source(Client.DESKTOP.getPlatform().toString()).qcStatus("QC Pass").build()
                }
        };
    }

    //initiate  return from vsm for cocodtc and take refund
    @DataProvider(name = "returnContextCOCODTC")
    public Object[][] returnContextCOCODTC() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").status("PENDING").status("").putawayNo("").build()).refundMethod(RefundMethod.SOURCE.getDisplayName()).returnType("online").rtoItems("false").needApproval(false).doRefund(true).source(Client.VSM.getDisplayName()).qcStatus("QC Pass").build()
                }
        };
    }
    //Verify receiving of increment id on receiving panel for nav channel  - otc
    @DataProvider(name = "returnContextCOCOOTC")
    public Object[][] returnContextCOCOOTC() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .itemType(ItemType.OTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(),CsOrderContext.builder().productIDToBeReturned(IN_SUNGLASSES_POS.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").status("PENDING").putawayNo("").build()).refundMethod(RefundMethod.SOURCE.getDisplayName()).needApproval(false).doRefund(true).returnType("online").rtoItems("false").source(Client.VSM.getDisplayName()).qcStatus("QC Pass").build()
                }
        };
    }

    //Verify receiving of increment id on receiving panel for nav channel  - LocalFitting
    @DataProvider(name = "returnContextLocalFitting")
    public Object[][] returnContextLocalFitting() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.LOCAL_FITTING)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(),CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").status("PENDING").putawayNo("").build()).refundMethod(RefundMethod.SOURCE.getDisplayName()).needApproval(false).returnType("online").rtoItems("false").doRefund(true).source(Client.VSM.getDisplayName()).qcStatus("QC Pass").build()
                }
        };
    }

    //initiate return  from msite for cocodtc and take refund
    @DataProvider(name = "returnContextCOCODTCReturnWeb")
    public Object[][] returnContextCOCODTCReturnWeb() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").status("PENDING").build()).refundMethod(RefundMethod.SOURCE.getDisplayName()).needApproval(false).returnType("online").rtoItems("false").doRefund(true).source(Client.DESKTOP.getPlatform().toString()).qcStatus("QC Pass").build()
                }
        };
    }
    //"Verify return/refund from vsm for webdtc for COD Order
    @DataProvider(name = "returnContextWebDTCCOD")
    public Object[][] returnContextWebDTCCOD() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build()).build()
                        , CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").status("PENDING").build()).refundMethod(RefundMethod.SOURCE.getDisplayName()).needApproval(false).doRefund(true).returnType("online").rtoItems("false").source(Client.VSM.getDisplayName()).qcStatus("QC Pass").build()
                }
        };
    }


    @DataProvider(name = "returnContextWEBDTCAndGold")
    public Object[][] returnContextWEBDTCAndGold() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_LOYALTY.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build()).build()
                        ,CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").status("PENDING").build()).refundMethod(RefundMethod.SOURCE.getDisplayName()).needApproval(false).doRefund(true).returnType("online").rtoItems("false").source(Client.VSM.getDisplayName()).qcStatus("QC Pass").build()
                }
        };
    }
    //Initiate return as refund from vsm for webdtc and take refund
    @DataProvider(name = "returnContextWebDTCPrepaidReturnVSM")
    public Object[][] returnContextWebDTCPrepaidReturnVSM() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(),
                        CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").status("PENDING").build()).refundMethod(RefundMethod.SOURCE.getDisplayName()).needApproval(false).doRefund(true).returnType("online").rtoItems("false").source(Client.VSM.getDisplayName()).qcStatus("QC Pass").build()

                }
        };
    }

    //Verify return from msite for webdtc and take refund
    @DataProvider(name = "returnContextWEBDTCReturnWeb")
    public Object[][] returnContextWEBDTCReturnWeb() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build()).build()
                        ,CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").status("PENDING").build()).refundMethod(RefundMethod.SOURCE.getDisplayName()).needApproval(false).doRefund(true).returnType("online").rtoItems("false").source(Client.DESKTOP.getPlatform().toString()).qcStatus("QC Pass").build()
                }
        };
    }

    @DataProvider(name = "returnCancellationContext")
    public Object[][] returnCancellationContext() {
        return new Object[][]{


                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build()).build()
                        ,CsOrderContext.builder().productIDToBeReturned(IN_EYEGLASSES.getProductId()).receivingGatePass(CsOrderContext.ReceivingGatePass.builder().qcStatus("Pass").status("PENDING").build()).refundMethod(RefundMethod.SOURCE.getDisplayName()).needApproval(false).doRefund(true).source(Client.VSM.getDisplayName()).comment("cancelling return as requested by Ops team").returnStatus(OrderState.CANCELLED.getDisplayName()).build()
                }
        };
    }

    @DataProvider(name = "returnExchangeContext")
    public Object[][] returnExchangeContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder()
                        .productIDToBeReturned(IN_EYEGLASSES.getProductId())
                        .receivingGatePass(CsOrderContext.ReceivingGatePass.builder()
                                .qcStatus("Pass").build())
                        .refundMethodRequest("exchange same frame same lens")
                        .refundMethod(RefundMethod.EXCHANGE.getDisplayName())
                        .source(Client.VSM.getDisplayName())
                        .needApproval(true)
                        .doRefund(false)
                        .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder()
                        .productIDToBeReturned(IN_EYEGLASSES.getProductId())
                        .receivingGatePass(CsOrderContext.ReceivingGatePass.builder()
                                .qcStatus("Pass").build())
                        .refundMethodRequest("exchange different frame different lens")
                        .refundMethod(RefundMethod.EXCHANGE.getDisplayName())
                        .source(Client.VSM.getDisplayName())
                        .needApproval(true)
                        .doRefund(false)
                        .build()
                }
        };
    }

    @DataProvider(name = "cancellationContextPartial")
    public Object[][] cancellationContextPartialCancellation() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build(), OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES_1.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.PARTIAL_CANCELLATION)
                        .cancellationReason("test order")
                        .cancelledBy(Users.ANJU.getEmail())
                        .cancellationSource(Client.VSM.getDisplayName())
                        .testType(TestType.POSITIVE)
                        .refundMethod(RefundMethod.STORE_CREDIT.getDisplayName())
                        .productIDToBeCancelled(ProductId.IN_EYEGLASSES.getProductId())
                        .isCancellable(true)
                        .message("order cancelled and refund under processing.")
                        .code("accepted")
                        .client(Client.VSM.getDisplayName())
                        .cancelledOrderShipmentStatus(OrderState.CANCELED.getDisplayName())
                        .cancellationReasonID(204).build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build(), OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES_1.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.PARTIAL_CANCELLATION)
                        .cancellationReason("test order")
                        .cancelledBy(Users.ANJU.getEmail())
                        .cancellationSource(Client.VSM.getDisplayName())
                        .testType(TestType.POSITIVE)
                        .message("order cancelled and refund under processing.")
                        .code("accepted")
                        .refundMethod(RefundMethod.SOURCE.getDisplayName())
                        .productIDToBeCancelled(ProductId.IN_EYEGLASSES.getProductId())
                        .isCancellable(true)
                        .client(Client.VSM.getDisplayName())
                        .cancelledOrderShipmentStatus(OrderState.CANCELED.getDisplayName())
                        .cancellationReasonID(204).build()
                }
        };
    }

    @DataProvider(name = "cancellationContextPartialForNexs")
    public Object[][] cancellationContextPartialForNexs() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build(), OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES_1.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.PARTIAL_CANCELLATION)
                        .cancellationReason("test order")
                        .skipCancellationReasonsRefundMethod(false)
                        .cancelledBy(Users.ANJU.getEmail())
                        .cancellationSource(Client.VSM.getDisplayName())
                        .testType(TestType.POSITIVE)
                        .refundMethod(RefundMethod.STORE_CREDIT.getDisplayName())
                        .productIDToBeCancelled(ProductId.IN_EYEGLASSES.getProductId())
                        .isCancellable(true)
                        .message("order cancelled and refund under processing.")
                        .code("accepted")
                        .client(Client.VSM.getDisplayName())
                        .cancelledOrderShipmentStatus(OrderState.ORDER_NOT_CONFIRMED.getDisplayName())
                        .cancellationReasonID(204).build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build(), OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES_1.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.PARTIAL_CANCELLATION)
                        .cancellationReason("test order")
                        .cancelledBy(Users.ANJU.getEmail())
                        .cancellationSource(Client.VSM.getDisplayName())
                        .testType(TestType.POSITIVE)
                        .skipCancellationReasonsRefundMethod(false)
                        .message("order cancelled and refund under processing.")
                        .code("accepted")
                        .refundMethod(RefundMethod.SOURCE.getDisplayName())
                        .productIDToBeCancelled(ProductId.IN_EYEGLASSES.getProductId())
                        .isCancellable(true)
                        .client(Client.VSM.getDisplayName())
                        .cancelledOrderShipmentStatus(OrderState.CANCELED.getDisplayName())
                        .cancellationReasonID(204).build()
                }
        };
    }

    @DataProvider(name = "cancellationContextFullCancellationForNexS")
    public Object[][] cancellationContextFullCancellationForNexS() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.FULL_CANCELLATION)
                        .cancellationReason("test order")
                        .skipCancellationReasonsRefundMethod(false)
                        .testType(TestType.POSITIVE)
                        .cancelledBy(Users.ANJU.getEmail())
                        .cancellationSource(Client.VSM.getDisplayName())
                        .refundMethod(RefundMethod.SOURCE.getDisplayName())
                        .productIDToBeCancelled(ProductId.IN_EYEGLASSES.getProductId())
                        .cancelledOrderShipmentStatus(OrderState.CANCELLED.getDisplayName())
                        .isCancellable(true)
                        .client(Client.VSM.getDisplayName())
                        .cancellationReasonID(204).build()

                },
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.FULL_CANCELLATION)
                        .cancellationReason("test order")
                        .skipCancellationReasonsRefundMethod(false)
                        .testType(TestType.POSITIVE)
                        .cancelledBy(Users.ANJU.getEmail())
                        .cancellationSource(Client.VSM.getDisplayName())
                        .refundMethod(RefundMethod.SOURCE.getDisplayName())
                        .productIDToBeCancelled(ProductId.IN_EYEGLASSES.getProductId())
                        .cancelledOrderShipmentStatus(OrderState.ORDER_NOT_CONFIRMED.getDisplayName())
                        .isCancellable(true)
                        .client(Client.VSM.getDisplayName())
                        .cancellationReasonID(204).build()

                }
        };
    }

    @DataProvider(name = "cancellationContextFull")
    public Object[][] cancellationContextFullCancellation() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.AE.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(AE_EYEGLASSES.getProductId())
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.AE)
                                        .pinCode(Countries.AE.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.FULL_CANCELLATION)
                        .cancellationReason("test order")
                        .testType(TestType.NEGATIVE)
                        .cancelledBy(Users.ANJU.getEmail())
                        .cancellationSource(Client.VSM.getDisplayName())
                        .refundMethod(RefundMethod.STORE_CREDIT.getDisplayName())
                        .productIDToBeCancelled(ProductId.AE_EYEGLASSES.getProductId())
                        .cancelledOrderShipmentStatus(OrderState.PROCESSING.getDisplayName())
                        .isCancellable(true)
                        .message("Payment method is not correct in the request parameters")
                        .code("Not Acceptable")
                        .client(Client.VSM.getDisplayName())
                        .cancellationReasonID(204).build()

                },

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.FULL_CANCELLATION)
                        .cancellationReason("test order")
                        .cancelledBy(Users.ANJU.getEmail())
                        .testType(TestType.POSITIVE)
                        .cancellationSource(Client.VSM.getDisplayName())
                        .refundMethod(RefundMethod.SOURCE.getDisplayName())
                        .productIDToBeCancelled(ProductId.IN_EYEGLASSES.getProductId())
                        .cancelledOrderShipmentStatus(OrderState.CANCELED.getDisplayName())
                        .isCancellable(true)
                        .message("order cancelled and refund under processing.")
                        .code("accepted")
                        .client(Client.VSM.getDisplayName())
                        .cancellationReasonID(204).build()

                },
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.FULL_CANCELLATION)
                        .cancellationReason("test order")
                        .cancelledBy(Users.ANJU.getEmail())
                        .cancellationSource(Client.VSM.getDisplayName())
                        .refundMethod(RefundMethod.SOURCE.getDisplayName())
                        .productIDToBeCancelled(ProductId.IN_EYEGLASSES.getProductId())
                        .testType(TestType.POSITIVE)
                        .message("order cancelled and refund under processing.")
                        .code("accepted")
                        .productIDToBeCancelled(ProductId.IN_EYEGLASSES.getProductId())
                        .cancelledOrderShipmentStatus(OrderState.ORDER_NOT_CONFIRMED.getDisplayName())
                        .isCancellable(true)
                        .client(Client.VSM.getDisplayName())
                        .cancellationReasonID(204).build()

                },
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.SG.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode(Countries.SG.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.FULL_CANCELLATION)
                        .cancellationReason("test order")
                        .cancelledBy(Users.ANJU.getEmail())
                        .cancellationSource(Client.VSM.getDisplayName())
                        .refundMethod(RefundMethod.STORE_CREDIT.getDisplayName())
                        .productIDToBeCancelled(SG_EYEGLASSES.getProductId())
                        .testType(TestType.POSITIVE)
                        .message("order cancelled and refund under processing.")
                        .code("accepted")
                        .cancelledOrderShipmentStatus(OrderState.ORDER_NOT_CONFIRMED.getDisplayName())
                        .isCancellable(true)
                        .client(Client.VSM.getDisplayName())
                        .cancellationReasonID(204).build()

                }
        };
    }

    @DataProvider(name = "awaitedRTOContextPrepaidOrder")
    public Object[][] awaitedRTOContextPrepaidOrder() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), ScmOrderContext.builder().code("710").codeDescription("In Transit to Sender").city("Aligarh").version("v2-track-carrier-feed").eddSource("tracking_middleware").build()

                }
        };
    }

    @DataProvider(name = "awaitedRTOContextCODOrder")
    public Object[][] awaitedRTOContextCODOrder() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), ScmOrderContext.builder().code("710").codeDescription("In Transit to Sender").city("Aligarh").version("v2-track-carrier-feed").eddSource("tracking_middleware").build()

                }
        };
    }

    @DataProvider(name = "getFastRefundDetailsCashFree")
    public Object[][] verifyFastRefundCashFree() {

        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .finalState(NexsOrderState.IN_PICKING)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
                                .build(),
                        NexsOrderContext.builder()
                                .build(),
                        CsOrderContext.builder()
                                .fastRefunduserId(Users.ANJU.getEmail())
                                .refundIntiation(Constants.REFUND_INITIATION)
                                .client(Client.VSM.getDisplayName())
                                .refundMethod(RefundMethod.CASHFREE.getDisplayName())
                                .remarks(Constants.REMARKS_PREPROD_AUTOMATION)
                                .build()
                }
        };
    }

    @DataProvider(name = "getFastRefundDetailsStoreCredit")
    public Object[][] verifyFastRefundStoreCredit() {

        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .finalState(NexsOrderState.IN_PICKING)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
                                .build(),
                        NexsOrderContext.builder()
                                .build(),
                        CsOrderContext.builder()
                                .fastRefunduserId(Users.ANJU.getEmail())
                                .refundIntiation(Constants.REFUND_INITIATION)
                                .client(Client.VSM.getDisplayName())
                                .refundMethod(RefundMethod.STORE_CREDIT.getDisplayName())
                                .remarks(Constants.REMARKS_PREPROD_AUTOMATION)
                                .build()
                }
        };
    }

    @DataProvider(name = "getFastRefundDetailsSource")
    public Object[][] verifyFastRefundSource() {

        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .finalState(NexsOrderState.IN_PICKING)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
                                .build(),
                        NexsOrderContext.builder()
                                .build(),
                        CsOrderContext.builder()
                                .fastRefunduserId(Users.ANJU.getEmail())
                                .refundIntiation(Constants.REFUND_INITIATION)
                                .client(Client.VSM.getDisplayName())
                                .refundMethod(RefundMethod.SOURCE.getDisplayName())
                                .remarks(Constants.REMARKS_PREPROD_AUTOMATION)
                                .build()
                }
        };
    }

    // Data provider for BlueDart delivered event flow handled by Middleware
    @DataProvider(name = "verifyBlueDartDeliveredEvents")
    public Object[][] verifyBlueDartDeliveredEvents() {
        // Represents a list of scan events for the same AWB number (one shipment)
        // The order of events simulates a full delivery lifecycle (Pickup → In Transit → Out For Delivery → Delivered)
        List<CsOrderContext.TrackingMiddlewareDetails> detailsList = List.of(
                // 1. Pickup event
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .origin("Gurgaon")
                        .destination("Jhansi")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1000")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("Online shipment booked")
                        .scanCode("030")
                        .scanGroupType("S")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1100")
                        .scannedLocation("Gurgaon")
                        .scanType("PU")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("1")
                        .receivedBy("")
                        .build(),

                // 2. In Transit event
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .origin("Gurgaon")
                        .destination("Jhansi")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1200")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("SHIPMENT OUTSCAN")
                        .scanCode("015")
                        .scanGroupType("S")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1300")
                        .scannedLocation("Gurgaon")
                        .scanType("UD")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("1")
                        .receivedBy("")
                        .build(),

                // 3. Out For Delivery scan
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .origin("Gurgaon")
                        .destination("Jhansi")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1400")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("SHIPMENT OUTSCAN")
                        .scanCode("002")
                        .scanGroupType("S")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1500")
                        .scannedLocation("Gurgaon")
                        .scanType("UD")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("1")
                        .receivedBy("")
                        .build(),

                // 4. Delivery Event
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .origin("Gurgaon")
                        .destination("Jhansi")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1600")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("SHIPMENT DELIVERED")
                        .scanCode("000")
                        .scanGroupType("T")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1700")
                        .scannedLocation("Jhansi")
                        .scanType("DL")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("1")
                        .receivedBy("Akshay Srivastav")
                        .build()
        );

        // Order and courier context used in the test
        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("570024")
                        .build()
                )
                .paymentMethod(PaymentMethod.CREDIT_CARD)
                .headers(OrderContext.Headers.builder()
                        .client(Client.DESKTOP)
                        .build()
                )
                .build();

        CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails =
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierName(MiddlewareCouriers.BLUEDART.getCourierName())
                        .identifierType("awb")
                        .build();

        return new Object[][] {
                {detailsList, orderContext, trackingMiddlewareDetails}
        };
    }

    // 🔁 Data Provider for Middleware service - RTO scan flow via BlueDart
    @DataProvider(name = "verifyBlueDartRTOEvents")
    public Object[][] verifyBlueDartRTOEvents() {
        List<CsOrderContext.TrackingMiddlewareDetails> detailsList = List.of(
                //Event 1: Online Shipment Booked
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .estimatedDeliveryDate("")
                        .origin("Gurgaon")
                        .originAreaCode("GR")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1000")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("Online shipment booked")
                        .scanCode("030")
                        .scanGroupType("S")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1100")
                        .scannedLocation("Gurgaon")
                        .scanType("PU")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("1")
                        .receivedBy("")
                        .build(),

                //Event 2: InTransit
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .estimatedDeliveryDate("")
                        .origin("Gurgaon")
                        .originAreaCode("GR")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1200")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("SHIPMENT OUTSCAN")
                        .scanCode("015")
                        .scanGroupType("S")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1300")
                        .scannedLocation("Gurgaon")
                        .scanType("UD")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("1")
                        .receivedBy("")
                        .build(),

                //Event 3: OFD (Out for Delivery)
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .estimatedDeliveryDate("")
                        .origin("Gurgaon")
                        .originAreaCode("GR")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1400")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("SHIPMENT OUTSCAN")
                        .scanCode("002")
                        .scanGroupType("S")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1500")
                        .scannedLocation("Gurgaon")
                        .scanType("UD")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("1")
                        .receivedBy("")
                        .build(),

                //Event 4: Delivery Failed
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .estimatedDeliveryDate("")
                        .origin("Gurgaon")
                        .originAreaCode("GR")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1600")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("CONSIGNEE REFUSED TO ACCEPT")
                        .scanCode("011")
                        .scanGroupType("T")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1700")
                        .scannedLocation("Jhansi")
                        .scanType("UD")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("P")
                        .receivedBy(null)
                        .build(),

                //Event 5: RTO INITIATED
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .estimatedDeliveryDate("")
                        .origin("Gurgaon")
                        .originAreaCode("GR")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1600")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("RTO (SHIPPER REQUEST)")
                        .scanCode("074")
                        .scanGroupType("RT")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1700")
                        .scannedLocation("Jhansi")
                        .scanType("RT")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("P")
                        .receivedBy(null)
                        .build(),

                //Event 6: RTO INTRANSIT
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .estimatedDeliveryDate("")
                        .origin("Gurgaon")
                        .originAreaCode("GR")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1600")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("SHIPMENT INSCAN")
                        .scanCode("001")
                        .scanGroupType("RS")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1700")
                        .scannedLocation("Jhansi")
                        .scanType("UD")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("P")
                        .receivedBy(null)
                        .build(),

                //Event 7: RTO DELIVERED
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .senderID("Bluedart")
                        .receiverID("LENSKART")
                        .estimatedDeliveryDate("")
                        .origin("Gurgaon")
                        .originAreaCode("GR")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .pickUpTime("1600")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51467497")
                        .prodCode("A")
                        .subProductCode("P")
                        .weight("1.1")
                        .feature("ForwardShipment")
                        .customerCode("123456")
                        .scan("SHIPMENT DELIVERED")
                        .scanCode("000")
                        .scanGroupType("RT")
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy"))
                        .scanTime("1700")
                        .scannedLocation("Jhansi")
                        .scanType("DL")
                        .comments("1930747875")
                        .scannedLocationCode("GR")
                        .scannedLocationCity("Gurgaon")
                        .scannedLocationStateCode("HR")
                        .statusTimeZone("IST")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .sorryCardNumber("123")
                        .reachedDestinationLocation("N")
                        .secureCode("P")
                        .receivedBy(null)
                        .build()
        );

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("570024")
                        .build()
                )
                .paymentMethod(PaymentMethod.CREDIT_CARD)
                .headers(OrderContext.Headers.builder()
                        .client(Client.DESKTOP)
                        .build()
                )
                .build();

        CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails =
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierName(MiddlewareCouriers.BLUEDART.getCourierName())
                        .identifierType("awb")
                        .build();

        return new Object[][]{
                {detailsList, orderContext, trackingMiddlewareDetails}
        };
    }

    // ✅ Data provider for validating full delivery flow via CRITICALOG through /middleware/push-scans API
    // 🧪 This includes: PICKED → IN_TRANSIT → OUT_FOR_DELIVERY → DELIVERED
    @DataProvider(name = "verifyCriticalLogDeliveredEvents")
    public Object[][] verifyCriticalLogDeliveredEvents() {
        List<CsOrderContext.TrackingMiddlewareDetails> detailsList = List.of(

                // Event 1: PICKED
                // 📌 Shipment picked up from origin (Gurgaon)
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy")+" 10:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate(null)
                        .refNo(null)
                        .scanCode(MiddlewareEvents.CRITICALLOG_PICKED.getEventId())
                        .scanDate(DateUtils.getFirstDayOfCurrentMonth().toString())
                        .currentLocation("Gurgaon")
                        .comments(MiddlewareEvents.CRITICALLOG_PICKED.getEventName())
                        .currentCity("Gurgaon")
                        .currentState("Haryana")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy(null)
                        .statusTimeZone("IST")
                        .build(),

                // Event 2: IN_TRANSIT
                // 📌 Shipment is moving from origin hub (Gurgaon) to next node
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy")+" 12:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate(null)
                        .refNo(null)
                        .scanCode(MiddlewareEvents.CRITICALLOG_IN_TRANSIT.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd")+" 13:00:00")
                        .currentLocation("Gurgaon")
                        .comments(MiddlewareEvents.CRITICALLOG_IN_TRANSIT.getEventName())
                        .currentCity("Gurgaon")
                        .currentState("Haryana")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy(null)
                        .statusTimeZone("IST")
                        .build(),

                // Event 3: OUT FOR DELIVERY
                // 📌 Shipment has reached destination hub and is on the way to customer
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Noida")
                        .originAreaCode("NDA")
                        .destination("Faridabad")
                        .destinationAreaCode("FAC")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy")+" 14:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate(null)
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.CRITICALLOG_OUT_FOR_DELIVERY.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd")+" 15:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.CRITICALLOG_OUT_FOR_DELIVERY.getEventName())
                        .currentCity("Gurgaon")
                        .currentState("Haryana")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy(null)
                        .statusTimeZone("IST")
                        .build(),

                // Event 4: DELIVERED
                // 📌 Shipment successfully delivered to end customer (Jhansi)
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Noida")
                        .originAreaCode("NDA")
                        .destination("Faridabad")
                        .destinationAreaCode("FAC")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy")+" 16:00:0")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate(null)
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.CRITICALLOG_DELIVERED.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd")+" 18:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.CRITICALLOG_DELIVERED.getEventName())
                        .currentCity("Jhansi")
                        .currentState("Uttar Pradesh")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("Akshay Srivastav")
                        .statusTimeZone("IST")
                        .build()
        );

        // 🔧 Order-level test metadata
        // 🧾 Simulates a customer order that went through this full scan flow
        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("570024")
                        .build()
                )
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build()
                )
                .build();

        // 🚚 Tracking middleware setup to map courier and AWB data for test
        CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails =
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierName(MiddlewareCouriers.CRITICALOG.getCourierName())
                        .identifierType("awb")
                        .build();

        // 📤 Returns test data to be consumed by the test method
        return new Object[][] {
                {detailsList, orderContext, trackingMiddlewareDetails}
        };
    }

    // Data provider for RTO events from CRITICALLOG courier handled by the Middleware service
    @DataProvider(name = "verifyCriticalLogRTOEvents")
    public Object[][] verifyCriticalLogRTOEvents() {
        List<CsOrderContext.TrackingMiddlewareDetails> detailsList = List.of(

                // 1️⃣ ONLINE SHIPMENT BOOKED (Picked Up)
                // Represents the first scan where the shipment is picked up from the origin
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 10:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate(null)
                        .refNo(null)
                        .scanCode(MiddlewareEvents.CRITICALLOG_PICKED.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 11:00:00")
                        .currentLocation("Gurgaon")
                        .comments(MiddlewareEvents.CRITICALLOG_PICKED.getEventName())
                        .currentCity("Gurgaon")
                        .currentState("Haryana")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy(null)
                        .statusTimeZone("IST")
                        .build(),

                // 2️⃣ INTRANSIT
                // Indicates the shipment has left the origin hub and is in transit to the destination
                // Purpose: Ensures tracking shows progress between pickup and delivery attempt
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 12:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate(null)
                        .refNo(null)
                        .scanCode(MiddlewareEvents.CRITICALLOG_IN_TRANSIT.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 13:00:00")
                        .currentLocation("Gurgaon")
                        .comments(MiddlewareEvents.CRITICALLOG_IN_TRANSIT.getEventName())
                        .currentCity("Gurgaon")
                        .currentState("Haryana")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy(null)
                        .statusTimeZone("IST")
                        .build(),

                // 3️⃣ OUT FOR DELIVERY (OFD)
                // Confirms that the shipment reached the destination hub and is dispatched for delivery
                // Purpose: Helps confirm that the courier attempted to deliver the product
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Noida")
                        .originAreaCode("NDA")
                        .destination("Faridabad")
                        .destinationAreaCode("FAC")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 14:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate(null)
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.CRITICALLOG_OUT_FOR_DELIVERY.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + " 15:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.CRITICALLOG_OUT_FOR_DELIVERY.getEventName())
                        .currentCity("Gurgaon")
                        .currentState("Haryana")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("")
                        .statusTimeZone("IST")
                        .build(),

                // 4️⃣ DELIVERY FAILED (Undelivered Attempted)
                // Marks the shipment as undelivered after delivery attempt fails (e.g., customer unavailable)
                // Purpose: Critical for RTO logic to be triggered based on undelivered status
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Noida")
                        .originAreaCode("NDA")
                        .destination("Faridabad")
                        .destinationAreaCode("FAC")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 16:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate(null)
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.CRITICALLOG_ATTEMPTED_UNDELIVERED.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + " 18:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.CRITICALLOG_ATTEMPTED_UNDELIVERED.getEventName())
                        .currentCity("Jhansi")
                        .currentState("Uttar Pradesh")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("")
                        .statusTimeZone("IST")
                        .build(),

                // 5️⃣ RTO INITIATED
                // Indicates the courier has initiated Return-To-Origin (RTO) flow
                // Purpose: Ensures backend recognizes return journey starting after failure to deliver
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Noida")
                        .originAreaCode("NDA")
                        .destination("Faridabad")
                        .destinationAreaCode("FAC")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 16:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate(null)
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.CRITICALLOG_RTO.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + " 18:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.CRITICALLOG_RTO.getEventName())
                        .currentCity("Jhansi")
                        .currentState("Uttar Pradesh")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("")
                        .statusTimeZone("IST")
                        .build(),

                // 6️⃣ RTO DELIVERED
                // Final scan that confirms the product has reached the origin (merchant) location
                // Purpose: Validates full RTO flow completion in system and database
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Noida")
                        .originAreaCode("NDA")
                        .destination("Faridabad")
                        .destinationAreaCode("FAC")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 16:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate(null)
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.CRITICALLOG_RTO_DELIVERED.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + " 18:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.CRITICALLOG_DELIVERED.getEventName())
                        .currentCity("Jhansi")
                        .currentState("Uttar Pradesh")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("")
                        .statusTimeZone("IST")
                        .build()
        );

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("570024")
                        .build()
                )
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build()
                )
                .build();

        CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails =
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierName(MiddlewareCouriers.CRITICALOG.getCourierName())
                        .identifierType("awb")
                        .build();

        return new Object[][] {
                {detailsList, orderContext, trackingMiddlewareDetails}
        };
    }

    // Data provider for DELIVERED events from PurpleDrone courier handled by the Middleware service
    @DataProvider(name = "verifyPurpleDroneDeliveredEvents")
    public Object[][] verifyPurpleDroneDeliveredEvents() {
        List<CsOrderContext.TrackingMiddlewareDetails> detailsList = List.of(

                // PICKED_UP Event: Shipment is picked by the courier from origin
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierComment(MiddlewareEvents.PURPLEDRONE_PICKED_UP.getEventName())
                        .statusTime("1000")
                        .userId("akshay")
                        .estimatedDeliveryDate("2025-07-30")
                        .statusDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd"))
                        .password("testNarvar")
                        .currentLocation("Gurugram")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .statusCode(MiddlewareEvents.PURPLEDRONE_PICKED_UP.getEventId())
                        .currentCity("Gurugram")
                        .currentState("Haryana")
                        .comments("The shipment is on its way to the destination hub")
                        .build(),

                // IN_TRANSIT Event: Shipment is moving between courier hubs
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierComment(MiddlewareEvents.PURPLEDRONE_IN_TRANSIT.getEventName())
                        .statusTime("1200")
                        .userId("testuser")
                        .estimatedDeliveryDate("2025-07-20")
                        .statusDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd"))
                        .password("testNarvar")
                        .currentLocation("Gurugram")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .statusCode(MiddlewareEvents.PURPLEDRONE_IN_TRANSIT.getEventId())
                        .currentCity("Gurugram")
                        .currentState("Haryana")
                        .comments("The shipment is on its way to the destination hub")
                        .build(),

                // OUT_FOR_DELIVERY Event: Shipment is dispatched from final hub to customer
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierComment(MiddlewareEvents.PURPLEDRONE_OUT_FOR_DELIVERY.getEventName())
                        .statusTime("1400")
                        .userId("testuser")
                        .estimatedDeliveryDate("2025-07-20")
                        .statusDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd"))
                        .password("testNarvar")
                        .currentLocation("Gurugram")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .statusCode(MiddlewareEvents.PURPLEDRONE_OUT_FOR_DELIVERY.getEventId())
                        .currentCity("Gurugram")
                        .currentState("Haryana")
                        .comments("The shipment is out for delivery")
                        .build(),

                // DELIVERED Event: Shipment has been successfully delivered to the customer
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierComment(MiddlewareEvents.PURPLEDRONE_DELIVERED.getEventName())
                        .statusTime("1600")
                        .userId("testuser")
                        .estimatedDeliveryDate("2025-07-20")
                        .statusDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd"))
                        .password("testNarvar")
                        .currentLocation("Jhansi")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .statusCode(MiddlewareEvents.PURPLEDRONE_DELIVERED.getEventId())
                        .currentCity("Jhansi")
                        .currentState("Uttar Pradesh")
                        .comments("The shipment has been delivered")
                        .build()
        );

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("570024")
                        .build()
                )
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build()
                )
                .build();

        CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails =
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierName(MiddlewareCouriers.PURPLEDRONE.getCourierName())
                        .identifierType("awb")
                        .build();

        return new Object[][] {
                {detailsList, orderContext, trackingMiddlewareDetails}
        };
    }

    // Data provider for RTO events from PurpleDrone courier handled by the Middleware service
    @DataProvider(name = "verifyPurpleDroneRTOEvents")
    public Object[][] verifyPurpleDroneRTOEvents() {
        List<CsOrderContext.TrackingMiddlewareDetails> detailsList = List.of(
                // Event 1: Shipment picked up from the origin
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierComment(MiddlewareEvents.PURPLEDRONE_PICKED_UP.getEventName())
                        .statusTime("1000")
                        .userId("akshay")
                        .estimatedDeliveryDate("2025-07-30")
                        .statusDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd"))
                        .password("testNarvar")
                        .currentLocation("Gurugram")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .statusCode(MiddlewareEvents.PURPLEDRONE_PICKED_UP.getEventId())
                        .currentCity("Gurugram")
                        .currentState("Haryana")
                        .comments("The shipment is on its way to the destination hub")
                        .build(),

                // Event 2: Shipment is in transit to the destination
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierComment(MiddlewareEvents.PURPLEDRONE_IN_TRANSIT.getEventName())
                        .statusTime("1200")
                        .userId("testuser")
                        .estimatedDeliveryDate("2025-07-20")
                        .statusDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd"))
                        .password("testNarvar")
                        .currentLocation("Gurugram")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .statusCode(MiddlewareEvents.PURPLEDRONE_IN_TRANSIT.getEventId())
                        .currentCity("Gurugram")
                        .currentState("Haryana")
                        .comments("The shipment is on its way to the destination hub")
                        .build(),

                // Event 3: Shipment is out for delivery at the destination
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierComment(MiddlewareEvents.PURPLEDRONE_OUT_FOR_DELIVERY.getEventName())
                        .statusTime("1400")
                        .userId("testuser")
                        .estimatedDeliveryDate("2025-07-20")
                        .statusDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd"))
                        .password("testNarvar")
                        .currentLocation("Gurugram")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .statusCode(MiddlewareEvents.PURPLEDRONE_OUT_FOR_DELIVERY.getEventId())
                        .currentCity("Gurugram")
                        .currentState("Haryana")
                        .comments("The shipment is out for delivery")
                        .build(),

                // Event 4: Delivery attempt failed (e.g., customer not available)
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierComment(MiddlewareEvents.PURPLEDRONE_UnDelivered.getEventName())
                        .statusTime("1600")
                        .userId("testuser")
                        .estimatedDeliveryDate("2025-07-20")
                        .statusDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd"))
                        .password("testNarvar")
                        .currentLocation("Jhansi")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .statusCode(MiddlewareEvents.PURPLEDRONE_UnDelivered.getEventId())
                        .currentCity("Jhansi")
                        .currentState("Uttar Pradesh")
                        .comments("The delivery attempt failed")
                        .build(),

                // Event 5: RTO has been initiated after failed delivery
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierComment(MiddlewareEvents.PURPLEDRONE_RTO_INITIATED.getEventName())
                        .statusTime("1700")
                        .userId("testuser")
                        .estimatedDeliveryDate("2025-07-20")
                        .statusDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd"))
                        .password("testNarvar")
                        .currentLocation("Jhansi")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .statusCode(MiddlewareEvents.PURPLEDRONE_RTO_INITIATED.getEventId())
                        .currentCity("Jhansi")
                        .currentState("Uttar Pradesh")
                        .comments("RTO initiated after failed delivery")
                        .build(),

                // Event 6: RTO shipment successfully delivered back to origin
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierComment(MiddlewareEvents.PURPLEDRONE_RTO_DELIVERED.getEventName())
                        .statusTime("1700")
                        .userId("testuser")
                        .estimatedDeliveryDate("2025-07-20")
                        .statusDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd"))
                        .password("testNarvar")
                        .currentLocation("Jhansi")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .statusCode(MiddlewareEvents.PURPLEDRONE_RTO_DELIVERED.getEventId())
                        .currentCity("Gurugram")
                        .currentState("Haryana")
                        .comments("RTO shipment delivered back to origin")
                        .build()
        );

        // Construct the order context and header info
        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("570024")
                        .build()
                )
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build()
                )
                .build();

        // Add courier identification details for tracking
        CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails =
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierName(MiddlewareCouriers.PURPLEDRONE.getCourierName())
                        .identifierType("awb")
                        .build();

        return new Object[][] {
                {detailsList, orderContext, trackingMiddlewareDetails}
        };
    }

    // Data provider for DELIVERED events from PicoExpress courier handled by the Middleware service
    @DataProvider(name = "verifyPicoExpressDeliveredEvents")
    public Object[][] verifyPicoExpressDeliveredEvents() {
        List<CsOrderContext.TrackingMiddlewareDetails> detailsList = List.of(
                // PICOXPRESS_PICKED
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy")+" 09:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.PICOXPRESS_PICKED.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd")+" 10:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.PICOXPRESS_PICKED.getEventName())
                        .currentCity("New Delhi")
                        .currentState("Delhi")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("")
                        .statusTimeZone("IST")
                        .build(),

                // PICOXPRESS_IN_TRANSIT
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy")+" 11:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.PICOXPRESS_IN_TRANSIT.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd")+" 12:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.PICOXPRESS_IN_TRANSIT.getEventName())
                        .currentCity("New Delhi")
                        .currentState("Delhi")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("")
                        .statusTimeZone("IST")
                        .build(),

                // PICOXPRESS_OUT_FOR_DELIVERY
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy")+" 13:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.PICOXPRESS_OUT_FOR_DELIVERY.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd")+" 14:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.PICOXPRESS_OUT_FOR_DELIVERY.getEventName())
                        .currentCity("New Delhi")
                        .currentState("Delhi")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("")
                        .statusTimeZone("IST")
                        .build(),

                // PICOXPRESS_DELIVERED
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy")+" 15:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.PICOXPRESS_DELIVERED.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd")+" 18:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.PICOXPRESS_DELIVERED.getEventName())
                        .currentCity("New Delhi")
                        .currentState("Delhi")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("John Doe")
                        .statusTimeZone("IST")
                        .build()
        );

        // Setup test order context
        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("570024")
                        .build()
                )
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build()
                )
                .build();

        // Tracking middleware metadata for API call
        CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails =
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierName(MiddlewareCouriers.PICOXPRESS.getCourierName())
                        .identifierType("awb")
                        .build();

        return new Object[][] {
                {detailsList, orderContext, trackingMiddlewareDetails}
        };
    }

    // Data provider for RTO events from PicoExpress courier handled by the Middleware service
    @DataProvider(name = "verifyPicoExpressRTOEvents")
    public Object[][] verifyPicoExpressRTOEvents() {
        List<CsOrderContext.TrackingMiddlewareDetails> detailsList = List.of(
                // Picked Up
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 09:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.PICOXPRESS_PICKED.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + " 10:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.PICOXPRESS_PICKED.getEventName())
                        .currentCity("New Delhi")
                        .currentState("Delhi")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("")
                        .statusTimeZone("IST")
                        .build(),

                // InTransit
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 11:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.PICOXPRESS_IN_TRANSIT.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + " 12:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.PICOXPRESS_IN_TRANSIT.getEventName())
                        .currentCity("New Delhi")
                        .currentState("Delhi")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("")
                        .statusTimeZone("IST")
                        .build(),

                // Out for Delivery
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 13:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.PICOXPRESS_OUT_FOR_DELIVERY.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + " 14:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.PICOXPRESS_OUT_FOR_DELIVERY.getEventName())
                        .currentCity("New Delhi")
                        .currentState("Delhi")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("")
                        .statusTimeZone("IST")
                        .build(),

                // Delivery Failed
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 15:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.PICOXPRESS_ATTEMPTED_UNDELIVERED.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + " 16:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.PICOXPRESS_ATTEMPTED_UNDELIVERED.getEventName())
                        .currentCity("New Delhi")
                        .currentState("Delhi")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("John Doe")
                        .statusTimeZone("IST")
                        .build(),

                // RTO Initiated
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 17:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.PICOXPRESS_RTO.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + " 18:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.PICOXPRESS_RTO.getEventName())
                        .currentCity("New Delhi")
                        .currentState("Delhi")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("John Doe")
                        .statusTimeZone("IST")
                        .build(),

                // RTO Delivered
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .origin("Gurgaon")
                        .originAreaCode("GGN")
                        .destination("Jhansi")
                        .destinationAreaCode("JHS")
                        .pickUpDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"dd_MM_yyyy") + " 19:00:00")
                        .shipmentMode(MiddlewareEvents.FORWARD_SHIPMENT_MODE.getEventId())
                        .dynamicExpectedDeliveryDate("")
                        .refNo("51387948")
                        .scanCode(MiddlewareEvents.PICOXPRESS_RTO_DELIVERED.getEventId())
                        .scanDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + " 20:00:00")
                        .currentLocation("Saket")
                        .comments(MiddlewareEvents.PICOXPRESS_RTO_DELIVERED.getEventName())
                        .currentCity("New Delhi")
                        .currentState("Delhi")
                        .statusLatitude("28.584669")
                        .statusLongitude("77.352013")
                        .receivedBy("John Doe")
                        .statusTimeZone("IST")
                        .build()
        );

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("570024")
                        .build()
                )
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build()
                )
                .build();

        CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails =
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierName(MiddlewareCouriers.PICOXPRESS.getCourierName())
                        .identifierType("awb")
                        .build();

        return new Object[][] {
                {detailsList, orderContext, trackingMiddlewareDetails}
        };
    }

    // Data provider for Delivered events from DOT courier handled by the Middleware service
    @DataProvider(name = "verifyDotDeliveredEvents")
    public Object[][] verifyDotDeliveredEvents() {
        List<CsOrderContext.TrackingMiddlewareDetails> detailsList = List.of(

                // DOT_PICKED
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .orderUpdateEventType("ORDER_STATUS_UPDATE")
                        .orderType("DROP")
                        .date("2025-07-04")
                        .slotStart("2025-07-04T02:30:00.000+0000")
                        .slotEnd("2025-07-04T14:30:00.000+0000")
                        .channel("TRACK_IQ")
                        .orderStatus("EXECUTING")
                        .orderSubStatus(MiddlewareEvents.DOT_PICKED.getEventId())
                        .actorId("dot-demo/MV018Mahesh")
                        .triggerTime(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T12:20:50.750+0000")
                        .teamId("Mayur_Vihar")
                        .homebaseId("MV-1-001")
                        .homebaseEta(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:47:09.883+0000")
                        .homebaseEtd(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:49:09.883+0000")
                        .homebaseCompleteOtp("4954")
                        .customerCompleteOtp("9753")
                        .cancellationOtp("9753")
                        .customerReturnOtp("2292")
                        .planIteration(1)
                        .tourId(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "-SHIPLOG-09598")
                        .sequence(1)
                        .riderId("MV018Mahesh")
                        .riderName("Mahesh Kumar")
                        .riderNumber("+917982559575")
                        .transporterId("default-transporter")
                        .transporterName("Default transporter")
                        .vehicleModelId("SUPER_CARRY")
                        .vehicleModelName("SUPER CARRY")
                        .vehicleId("bike")
                        .vehicleName("bike")
                        .tourStartTime("2025-07-03T18:30:00.000+0000")
                        .isSortedForTour(false)
                        .initialEta("2025-07-04T17:17:58.883+0000")
                        .initialEtd("2025-07-04T17:22:58.883+0000")
                        .currentEta("2025-07-04T16:49:09.883+0000")
                        .slaStatus("ON_TIME")
                        .refId("d9e5d13d255040c3ab05f0fcfb18844f")
                        .amount(3683.0)
                        .currency("INR")
                        .paymentStatus("PAID")
                        .timestamp("2025-07-04T12:20:52.731+0000")
                        .build(),

                // DOT_OUT_FOR_DELIVERY
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .orderUpdateEventType("ORDER_STATUS_UPDATE")
                        .orderType("DROP")
                        .date("2025-07-04")
                        .slotStart("2025-07-04T02:30:00.000+0000")
                        .slotEnd("2025-07-04T14:30:00.000+0000")
                        .channel("TRACK_IQ")
                        .orderStatus("EXECUTING")
                        .orderSubStatus(MiddlewareEvents.DOT_OUT_FOR_DELIVERY.getEventId())
                        .actorId("dot-demo/MV018Mahesh")
                        .triggerTime(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T12:20:50.750+0000")
                        .teamId("Mayur_Vihar")
                        .homebaseId("MV-1-001")
                        .homebaseEta(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:47:09.883+0000")
                        .homebaseEtd(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:49:09.883+0000")
                        .homebaseCompleteOtp("4954")
                        .customerCompleteOtp("9753")
                        .cancellationOtp("9753")
                        .customerReturnOtp("2292")
                        .planIteration(1)
                        .tourId(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "-SHIPLOG-09598")
                        .sequence(1)
                        .riderId("MV018Mahesh")
                        .riderName("Mahesh Kumar")
                        .riderNumber("+917982559575")
                        .transporterId("default-transporter")
                        .transporterName("Default transporter")
                        .vehicleModelId("SUPER_CARRY")
                        .vehicleModelName("SUPER CARRY")
                        .vehicleId("bike")
                        .vehicleName("bike")
                        .tourStartTime("2025-07-03T18:30:00.000+0000")
                        .isSortedForTour(false)
                        .initialEta("2025-07-04T17:17:58.883+0000")
                        .initialEtd("2025-07-04T17:22:58.883+0000")
                        .currentEta("2025-07-04T16:49:09.883+0000")
                        .slaStatus("ON_TIME")
                        .refId("d9e5d13d255040c3ab05f0fcfb18844f")
                        .amount(3683.0)
                        .currency("INR")
                        .paymentStatus("PAID")
                        .timestamp("2025-07-04T12:20:52.731+0000")
                        .build(),

                // DOT_DELIVERED
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .orderUpdateEventType("ORDER_STATUS_UPDATE")
                        .orderType("DROP")
                        .date("2025-07-04")
                        .slotStart("2025-07-04T02:30:00.000+0000")
                        .slotEnd("2025-07-04T14:30:00.000+0000")
                        .channel("TRACK_IQ")
                        .orderStatus(MiddlewareEvents.DOT_DELIVERED.getEventId())
                        .actorId("dot-demo/MV018Mahesh")
                        .triggerTime(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T12:20:50.750+0000")
                        .teamId("Mayur_Vihar")
                        .homebaseId("MV-1-001")
                        .homebaseEta(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:47:09.883+0000")
                        .homebaseEtd(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:49:09.883+0000")
                        .homebaseCompleteOtp("4954")
                        .customerCompleteOtp("9753")
                        .cancellationOtp("9753")
                        .customerReturnOtp("2292")
                        .planIteration(1)
                        .tourId(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "-SHIPLOG-09598")
                        .sequence(1)
                        .riderId("MV018Mahesh")
                        .riderName("Mahesh Kumar")
                        .riderNumber("+917982559575")
                        .transporterId("default-transporter")
                        .transporterName("Default transporter")
                        .vehicleModelId("SUPER_CARRY")
                        .vehicleModelName("SUPER CARRY")
                        .vehicleId("bike")
                        .vehicleName("bike")
                        .tourStartTime("2025-07-03T18:30:00.000+0000")
                        .isSortedForTour(false)
                        .initialEta("2025-07-04T17:17:58.883+0000")
                        .initialEtd("2025-07-04T17:22:58.883+0000")
                        .currentEta("2025-07-04T16:49:09.883+0000")
                        .slaStatus("ON_TIME")
                        .refId("d9e5d13d255040c3ab05f0fcfb18844f")
                        .amount(3683.0)
                        .currency("INR")
                        .paymentStatus("PAID")
                        .timestamp("2025-07-04T12:20:52.731+0000")
                        .build()
        );

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("570024")
                        .build()
                )
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build()
                )
                .build();

        CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails =
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierName(MiddlewareCouriers.DOT.getCourierName())
                        .identifierType("awb")
                        .build();

        return new Object[][] {
                {detailsList, orderContext, trackingMiddlewareDetails}
        };
    }

    // Data provider for RTO event flow of DOT courier handled by the Middleware service
    @DataProvider(name = "verifyDotRTOEvents")
    public Object[][] verifyDotRTOEvents() {

        // Define the scan event sequence for DOT RTO flow
        List<CsOrderContext.TrackingMiddlewareDetails> detailsList = List.of(
                // 1. Picked Up event
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .orderUpdateEventType("ORDER_STATUS_UPDATE")
                        .orderType("DROP")
                        .date("2025-07-04")
                        .slotStart("2025-07-04T02:30:00.000+0000")
                        .slotEnd("2025-07-04T14:30:00.000+0000")
                        .channel("TRACK_IQ")
                        .orderStatus("EXECUTING")
                        .orderSubStatus(MiddlewareEvents.DOT_PICKED.getEventId())
                        .actorId("dot-demo/MV018Mahesh")
                        .triggerTime(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T12:20:50.750+0000")
                        .teamId("Mayur_Vihar")
                        .homebaseId("MV-1-001")
                        .homebaseEta(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:47:09.883+0000")
                        .homebaseEtd(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:49:09.883+0000")
                        .homebaseCompleteOtp("4954")
                        .customerCompleteOtp("9753")
                        .cancellationOtp("9753")
                        .customerReturnOtp("2292")
                        .planIteration(1)
                        .tourId(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "-SHIPLOG-09598")
                        .sequence(1)
                        .riderId("MV018Mahesh")
                        .riderName("Mahesh Kumar")
                        .riderNumber("+917982559575")
                        .transporterId("default-transporter")
                        .transporterName("Default transporter")
                        .vehicleModelId("SUPER_CARRY")
                        .vehicleModelName("SUPER CARRY")
                        .vehicleId("bike")
                        .vehicleName("bike")
                        .tourStartTime("2025-07-03T18:30:00.000+0000")
                        .isSortedForTour(false)
                        .initialEta("2025-07-04T17:17:58.883+0000")
                        .initialEtd("2025-07-04T17:22:58.883+0000")
                        .currentEta("2025-07-04T16:49:09.883+0000")
                        .slaStatus("ON_TIME")
                        .refId("d9e5d13d255040c3ab05f0fcfb18844f")
                        .amount(3683.0)
                        .currency("INR")
                        .paymentStatus("PAID")
                        .timestamp("2025-07-04T12:20:52.731+0000")
                        .build(),

                // 2. Out for Delivery event
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .orderUpdateEventType("ORDER_STATUS_UPDATE")
                        .orderType("DROP")
                        .date("2025-07-04")
                        .slotStart("2025-07-04T02:30:00.000+0000")
                        .slotEnd("2025-07-04T14:30:00.000+0000")
                        .channel("TRACK_IQ")
                        .orderStatus("EXECUTING")
                        .orderSubStatus(MiddlewareEvents.DOT_OUT_FOR_DELIVERY.getEventId())
                        .actorId("dot-demo/MV018Mahesh")
                        .triggerTime(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T12:20:50.750+0000")
                        .teamId("Mayur_Vihar")
                        .homebaseId("MV-1-001")
                        .homebaseEta(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:47:09.883+0000")
                        .homebaseEtd(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:49:09.883+0000")
                        .homebaseCompleteOtp("4954")
                        .customerCompleteOtp("9753")
                        .cancellationOtp("9753")
                        .customerReturnOtp("2292")
                        .planIteration(1)
                        .tourId(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "-SHIPLOG-09598")
                        .sequence(1)
                        .riderId("MV018Mahesh")
                        .riderName("Mahesh Kumar")
                        .riderNumber("+917982559575")
                        .transporterId("default-transporter")
                        .transporterName("Default transporter")
                        .vehicleModelId("SUPER_CARRY")
                        .vehicleModelName("SUPER CARRY")
                        .vehicleId("bike")
                        .vehicleName("bike")
                        .tourStartTime("2025-07-03T18:30:00.000+0000")
                        .isSortedForTour(false)
                        .initialEta("2025-07-04T17:17:58.883+0000")
                        .initialEtd("2025-07-04T17:22:58.883+0000")
                        .currentEta("2025-07-04T16:49:09.883+0000")
                        .slaStatus("ON_TIME")
                        .refId("d9e5d13d255040c3ab05f0fcfb18844f")
                        .amount(3683.0)
                        .currency("INR")
                        .paymentStatus("PAID")
                        .timestamp("2025-07-04T12:20:52.731+0000")
                        .build(),

                // 3. Delivery Failed (Undelivered) event
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .orderUpdateEventType("ORDER_STATUS_UPDATE")
                        .orderType("DROP")
                        .date("2025-07-04")
                        .slotStart("2025-07-04T02:30:00.000+0000")
                        .slotEnd("2025-07-04T14:30:00.000+0000")
                        .channel("TRACK_IQ")
                        .orderStatus(MiddlewareEvents.DOT_UnDeliveredD.getEventId())
                        .actorId("dot-demo/MV018Mahesh")
                        .triggerTime(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T12:20:50.750+0000")
                        .teamId("Mayur_Vihar")
                        .homebaseId("MV-1-001")
                        .homebaseEta(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:47:09.883+0000")
                        .homebaseEtd(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:49:09.883+0000")
                        .homebaseCompleteOtp("4954")
                        .customerCompleteOtp("9753")
                        .cancellationOtp("9753")
                        .customerReturnOtp("2292")
                        .planIteration(1)
                        .tourId(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "-SHIPLOG-09598")
                        .sequence(1)
                        .riderId("MV018Mahesh")
                        .riderName("Mahesh Kumar")
                        .riderNumber("+917982559575")
                        .transporterId("default-transporter")
                        .transporterName("Default transporter")
                        .vehicleModelId("SUPER_CARRY")
                        .vehicleModelName("SUPER CARRY")
                        .vehicleId("bike")
                        .vehicleName("bike")
                        .tourStartTime("2025-07-03T18:30:00.000+0000")
                        .isSortedForTour(false)
                        .initialEta("2025-07-04T17:17:58.883+0000")
                        .initialEtd("2025-07-04T17:22:58.883+0000")
                        .currentEta("2025-07-04T16:49:09.883+0000")
                        .slaStatus("ON_TIME")
                        .refId("d9e5d13d255040c3ab05f0fcfb18844f")
                        .amount(3683.0)
                        .currency("INR")
                        .paymentStatus("PAID")
                        .timestamp("2025-07-04T12:20:52.731+0000")
                        .build(),

                // 4. RTO Initiated event
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .orderUpdateEventType("ORDER_STATUS_UPDATE")
                        .orderType("DROP")
                        .date("2025-07-04")
                        .slotStart("2025-07-04T02:30:00.000+0000")
                        .slotEnd("2025-07-04T14:30:00.000+0000")
                        .channel("TRACK_IQ")
                        .orderStatus(MiddlewareEvents.DOT_RTO.getEventId())
                        .actorId("dot-demo/MV018Mahesh")
                        .triggerTime(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T12:20:50.750+0000")
                        .teamId("Mayur_Vihar")
                        .homebaseId("MV-1-001")
                        .homebaseEta(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:47:09.883+0000")
                        .homebaseEtd(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "T16:49:09.883+0000")
                        .homebaseCompleteOtp("4954")
                        .customerCompleteOtp("9753")
                        .cancellationOtp("9753")
                        .customerReturnOtp("2292")
                        .planIteration(1)
                        .tourId(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth(),"yyyy_MM_dd") + "-SHIPLOG-09598")
                        .sequence(1)
                        .riderId("MV018Mahesh")
                        .riderName("Mahesh Kumar")
                        .riderNumber("+917982559575")
                        .transporterId("default-transporter")
                        .transporterName("Default transporter")
                        .vehicleModelId("SUPER_CARRY")
                        .vehicleModelName("SUPER CARRY")
                        .vehicleId("bike")
                        .vehicleName("bike")
                        .tourStartTime("2025-07-03T18:30:00.000+0000")
                        .isSortedForTour(false)
                        .initialEta("2025-07-04T17:17:58.883+0000")
                        .initialEtd("2025-07-04T17:22:58.883+0000")
                        .currentEta("2025-07-04T16:49:09.883+0000")
                        .slaStatus("ON_TIME")
                        .refId("d9e5d13d255040c3ab05f0fcfb18844f")
                        .amount(3683.0)
                        .currency("INR")
                        .paymentStatus("PAID")
                        .timestamp("2025-07-04T12:20:52.731+0000")
                        .build()
        );

        // Order context containing customer and order-level metadata
        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("570024")
                        .build()
                )
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build()
                )
                .build();

        // Middleware tracking context for test metadata
        CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails =
                CsOrderContext.TrackingMiddlewareDetails.builder()
                        .courierName(MiddlewareCouriers.DOT.getCourierName())
                        .identifierType("awb")
                        .build();

        return new Object[][] {
                {detailsList, orderContext, trackingMiddlewareDetails}
        };
    }

    @DataProvider(name = "crmCancellationProvider")
    public Object[][] crmCancellationProvider() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(),
                        CsOrderContext.builder()
                                .comment("order_cancel")
                                .refundMethod("source")
                                .cancellationReason("Delivery time too long")
                                .cancellationReasonID(784)
                                .build()
                }
        };
    }

    @DataProvider(name = "crmReturnRefundMappingProvider")
    public Object[][] crmReturnRefundMappingProvider() {
        return new Object[][]{
                {

                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType(CancellationType.FULL_CANCELLATION)
                        .refundMethod(RefundMethod.SOURCE.getDisplayName())
                        .testType(TestType.POSITIVE)
                        .cancellationReason("test_order")
                        .cancellationReasonID(204)
                        .cancelledBy(Users.ANJU.getEmail())
                        .cancellationSource(Client.VSM.getDisplayName())
                        .productIDToBeCancelled(IN_EYEGLASSES.getProductId())
                        .cancelledOrderShipmentStatus(OrderState.CANCELED.getDisplayName())
                        .isCancellable(true)
                        .message("order cancelled and refund under processing.")
                        .code("accepted")
                        .build()
                }
        };
    }
}