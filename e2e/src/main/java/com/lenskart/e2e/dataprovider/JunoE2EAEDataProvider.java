package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import org.testng.annotations.DataProvider;

import java.util.List;
import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class JunoE2EAEDataProvider {

    private OrderContext getBaseOrderContext() {
        return OrderContext.builder()
                .phoneNumber(Countries.AE.getDefaultPhoneNumber())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.AE)
                        .pinCode(Countries.AE.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                .build();
    }

    @DataProvider(name = "aeEyeglassPowerOrder")
    public Object[][] aeEyeglassPowerOrder() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(AE_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(AE_EYEGLASSES.getProductId())
                                        .powerType(BIFOCAL)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(AE_EYEGLASSES.getProductId())
                                        .powerType(ZERO_POWER)
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "aeEyeglassWithoutPower")
    public Object[][] aeEyeglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(AE_EYEGLASSES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "aeSunglassWithPower")
    public Object[][] aeSunglassWithPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(AE_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build())
                }
        };
    }

    @DataProvider(name = "aeSunglassWithoutPower")
    public Object[][] aeSunglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(AE_SUNGLASSES.getProductId())
                                .build())
                }
        };
    }



    @DataProvider(name = "aeEyeSunContactLensOrder")
    public Object[][] aeEyeSunContactLensOrder() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(AE_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(AE_EYEGLASSES.getProductId())
                                        .powerType(BIFOCAL)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(AE_SUNGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(AE_CONTACT_LENS.getProductId())
                                        .build()
                        ),

                }
        };
    }

    //No pid mapped currently so not added Testcase for accessories
    @DataProvider(name = "aeAccessories")
    public Object[][] aeAccessories() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(AE_ACCESSORIES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "aeEyeglassPowerOrderWithGV")
    public Object[][] aeEyeglassPowerOrderWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(AE_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(AE_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_AE_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }


    @DataProvider(name = "aeEyeSunCLOrderWithGV")
    public Object[][] aeEyeSunCLOrderWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(AE_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(AE_SUNGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(AE_CONTACT_LENS.getProductId())
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_AE_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "aeeyeglassSingleVisionPowerwithWallet")
    public Object[][] aeeyeglassSingleVisionPowerwithWallet() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(AE_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build()),

                        true

                }
        };
    }
}