package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.commons.utils.GenericUtils;
import org.testng.annotations.DataProvider;

import java.util.List;
import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class JunoE2ETHDataProvider {

    private OrderContext getBaseOrderContext() {
        return OrderContext.builder()
                .phoneNumber(Countries.TH.getDefaultPhoneNumber())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.TH)
                        .pinCode(Countries.TH.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                .build();
    }

    @DataProvider(name = "thEyeglassSingleVisionPower")
    public Object[][] thEyeglassSingleVisionPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(TH_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build())
                }
        };
    }

    @DataProvider(name = "thEyeglassBifocalPower")
    public Object[][] thEyeglassBifocalPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(TH_EYEGLASSES.getProductId())
                                .powerType(BIFOCAL)
                                .build())
                }
        };
    }


    @DataProvider(name = "thEyeglassWithoutPower")
    public Object[][] thEyeglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(TH_EYEGLASSES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "thSunglassWithoutPower")
    public Object[][] thSunglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(TH_SUNGLASSES.getProductId())
                                .build())
                }
        };
    }



    @DataProvider(name = "thEyeglassAndSunglassWithGV")
    public Object[][] thEyeglassAndSunglassWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(TH_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(TH_SUNGLASSES.getProductId())
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_TH_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }




    @DataProvider(name = "thMultiProductWithGV")
    public Object[][] thMultiProductWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(TH_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(TH_SUNGLASSES.getProductId())
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_TH_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "thEyeglassAndSunglassWithSC")
    public Object[][] thEyeglassAndSunglassWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(TH_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(TH_SUNGLASSES.getProductId())
                                        .build()
                        ),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_TH)
                                .isStoreCreditApplicable(true)
                                .build()


                }
        };
    }



    @DataProvider(name = "thSunglassWithoutPowerWithSC")
    public Object[][] thSunglassWithoutPowerWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SG_SUNGLASSES.getProductId())
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_TH)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }




    @DataProvider(name = "thEyeglassWithoutPowerWithSC_GV")
    public Object[][] thEyeglassWithoutPowerWithSC_GV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(TH_EYEGLASSES.getProductId())
                                .build()),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_TH_5PER)
                                .isGiftVoucherApplicable(true)
                                .build(),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_TH)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "theyeglassSingleVisionPowerwithWallet")
    public Object[][] theyeglassSingleVisionPowerwithWallet() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(TH_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build()),
                        true

                }
        };
    }
    @DataProvider(name = "theyeglassAndSunglassWithoutPower")
    public Object[][] theyeglassAndSunglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(TH_SUNGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(TH_SUNGLASSES.getProductId())
                                        .build()
                        )
                }
        };
    }

}