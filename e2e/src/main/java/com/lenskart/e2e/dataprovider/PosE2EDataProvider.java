package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.pos.model.POS;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class PosE2EDataProvider {

    @DataProvider(name = "posOrderContext")
    public Object[][] posOrderContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
                ,
                {
                OrderContext.builder()
                        .phoneNumber("**********")
                        .shipToCust(0)
                        .productLists(List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(ZERO_POWER)
                                        .itemType(ItemType.DTC)
                                        .finalState(NexsOrderState.DISPATCHED)
                                        .isPrescriptionRequired(false)
                                        .build()))
                        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                .country(Countries.IN)
                                .pinCode("560075")
                                .build())
                        .isPosOrder(true)
                        .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                .country(Countries.IN)
                                .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                .build())
                        .paymentMethod(PaymentMethod.OFFLINE_CASH)
                        .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                        .build()
        },
                {
                        OrderContext.builder()
                                .shipToCust(0)
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_CONTACT_LENS.getProductId())
                                                .itemType(ItemType.CONTACT_LENS)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
                ,

                {
                        OrderContext.builder()
                                .shipToCust(0)
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.LOCAL_FITTING)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES_WITHOUT_POWER)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("63274324")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.PLANT_FITTINGS)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode("540563")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SG)
                                        .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("63274324")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode("540563")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SG)
                                        .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("63274324")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES_WITHOUT_POWER)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode("540563")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SG)
                                        .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                // Thailand Sunglass Data Provider
                {
                        OrderContext.builder()
                                .phoneNumber("949134193")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(TH_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.TH)
                                        .pinCode("10110")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.TH)
                                        .storeId(POS.TH_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                // Thailand Bifocal Eyeglass Data Provider
                {

                        OrderContext.builder()
                                .phoneNumber("949134193")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(TH_EYEGLASSES.getProductId())
                                                .powerType(BIFOCAL)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.TH)
                                        .pinCode("10110")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.TH)
                                        .storeId(POS.TH_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()


                },

                // Thailand SV Eyeglass Data Provider
                {
                        OrderContext.builder()
                                .phoneNumber("949134193")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(TH_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.TH)
                                        .pinCode("10110")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.TH)
                                        .storeId(POS.TH_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()


                },

                // Bulk Order Data provider
                {
                        OrderContext.builder()
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .itemType(ItemType.BULK_ORDER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_ADMIN_STORE.getStoreId())
                                        .build())
                                .headers(OrderContext.Headers.builder().client(Client.POS_WEB).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("8088127914")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .itemType(ItemType.OTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()

                },

                //eyeglass with insurance
                {
                        OrderContext.builder()
                                .phoneNumber("8660038279")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_INSURANCE.getProductId())
                                                .productType(ProductTypes.INSURANCE)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("949134193")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(TH_EYEGLASSES.getProductId())
                                                .powerType(BIFOCAL)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.TH)
                                        .pinCode("10110")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.TH)
                                        .storeId(POS.TH_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()


                },

                // UAE Single Vision Eyeglass
                {
                        OrderContext.builder()
                                .phoneNumber("505557037")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(AE_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.AE)
                                        .pinCode("241868")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.AE)
                                        .storeId(POS.AE_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                // UAE Powered Sunglass
                {
                        OrderContext.builder()
                                .phoneNumber("505557037")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(AE_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.AE)
                                        .pinCode("241868")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.AE)
                                        .storeId(POS.AE_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                // UAE BI-FOCAL Eyeglass
                {
                        OrderContext.builder()
                                .phoneNumber("505557037")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(AE_EYEGLASSES.getProductId())
                                                .powerType(BIFOCAL)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.AE)
                                        .pinCode("241868")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.AE)
                                        .storeId(POS.AE_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                // India Accessories
                {
                        OrderContext.builder()
                                .phoneNumber("9945901780")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_ACCESSORIES.getProductId())
                                                .powerType(ACCESSORIES)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                //Plant fitting with gatepass
                {
                        OrderContext.builder()
                                .phoneNumber("63274324")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.PLANT_FITTINGS_WITH_GATEPASS)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode("540563")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SG)
                                        .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                //Bulk Order Data provider
                {
                        OrderContext.builder()
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .itemType(ItemType.BULK_ORDER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_ADMIN_STORE.getStoreId())
                                        .build())
                                .headers(OrderContext.Headers.builder().client(Client.POS_WEB).build())
                                .build()
                },

                //KSA- Sunglasses with Power
                {
                        OrderContext.builder()
                                .phoneNumber("567894321")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SA_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SA)
                                        .pinCode("12215")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SA)
                                        .storeId(POS.KSA_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                //KSA- Eyeglasses with single vision
                {
                        OrderContext.builder()
                                .phoneNumber("567894321")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SA_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SA)
                                        .pinCode("12215")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SA)
                                        .storeId(POS.KSA_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                //KSA- Eyeglasses with bifocal
                {
                        OrderContext.builder()
                                .phoneNumber("567894321")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SA_EYEGLASSES.getProductId())
                                                .powerType(BIFOCAL)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SA)
                                        .pinCode("12215")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SA)
                                        .storeId(POS.KSA_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                //Sunglass without power coco
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES_WITHOUT_POWER)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                //FOFO OTC order
                {
                        OrderContext.builder()
                                .phoneNumber("8088127914")
                                .shipToCust(0)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .itemType(ItemType.OTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()

                },
                // Lens only Local Fitting India COCO
                {
                                OrderContext.builder()
                                        .phoneNumber("9945901780")
                                        .shipToCust(0)
                                        .productLists(List.of(
                                                OrderContext.ProductList.builder()
                                                        .productId(IN_LENS_ONLY_1.getProductId())
                                                        .powerType(SINGLE_VISION)
                                                        .itemType(ItemType.LENS_ONLY_LOCAL_FITTING_IN_COCO)
                                                        .isPrescriptionRequired(true)
                                                        .build()))
                                        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                                .country(Countries.IN)
                                                .pinCode("560075")
                                                .build())
                                        .isPosOrder(true)
                                        .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                                .country(Countries.IN)
                                                .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                                .build())
                                        .paymentMethod(PaymentMethod.MEDIBUDDY)
                                        .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                        .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
                ,
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .shipToCust(1)
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_CONTACT_LENS.getProductId())
                                                .itemType(ItemType.CONTACT_LENS)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
                ,

                {
                        OrderContext.builder()
                                .shipToCust(1)
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.LOCAL_FITTING)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES_WITHOUT_POWER)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("63274324")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.PLANT_FITTINGS)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode("540563")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SG)
                                        .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("63274324")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode("540563")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SG)
                                        .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("63274324")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES_WITHOUT_POWER)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode("540563")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SG)
                                        .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                // Thailand Sunglass Data Provider
                {
                        OrderContext.builder()
                                .phoneNumber("949134193")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(TH_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.TH)
                                        .pinCode("10110")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.TH)
                                        .storeId(POS.TH_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                // Thailand Bifocal Eyeglass Data Provider
                {

                        OrderContext.builder()
                                .phoneNumber("949134193")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(TH_EYEGLASSES.getProductId())
                                                .powerType(BIFOCAL)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.TH)
                                        .pinCode("10110")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.TH)
                                        .storeId(POS.TH_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()


                },

                // Thailand SV Eyeglass Data Provider
                {
                        OrderContext.builder()
                                .phoneNumber("949134193")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(TH_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.TH)
                                        .pinCode("10110")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.TH)
                                        .storeId(POS.TH_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()


                },
                {
                        OrderContext.builder()
                                .phoneNumber("8088127914")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .itemType(ItemType.OTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()

                },

                //eyeglass with insurance
                {
                        OrderContext.builder()
                                .phoneNumber("8660038279")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build(),
                                        OrderContext.ProductList.builder()
                                                .productId(IN_INSURANCE.getProductId())
                                                .productType(ProductTypes.INSURANCE)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("949134193")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(TH_EYEGLASSES.getProductId())
                                                .powerType(BIFOCAL)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.TH)
                                        .pinCode("10110")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.TH)
                                        .storeId(POS.TH_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()


                },

                // UAE Single Vision Eyeglass
                {
                        OrderContext.builder()
                                .phoneNumber("505557037")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(AE_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.AE)
                                        .pinCode("241868")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.AE)
                                        .storeId(POS.AE_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                // UAE Powered Sunglass
                {
                        OrderContext.builder()
                                .phoneNumber("505557037")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(AE_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.AE)
                                        .pinCode("241868")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.AE)
                                        .storeId(POS.AE_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                // UAE BI-FOCAL Eyeglass
                {
                        OrderContext.builder()
                                .phoneNumber("505557037")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(AE_EYEGLASSES.getProductId())
                                                .powerType(BIFOCAL)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.AE)
                                        .pinCode("241868")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.AE)
                                        .storeId(POS.AE_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                // India Accessories
                {
                        OrderContext.builder()
                                .phoneNumber("9945901780")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_ACCESSORIES.getProductId())
                                                .powerType(ACCESSORIES)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                //Plant fitting with gatepass
                {
                        OrderContext.builder()
                                .phoneNumber("63274324")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SG_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.PLANT_FITTINGS_WITH_GATEPASS)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SG)
                                        .pinCode("540563")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SG)
                                        .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                //KSA- Sunglasses with Power
                {
                        OrderContext.builder()
                                .phoneNumber("567894321")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SA_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SA)
                                        .pinCode("12215")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SA)
                                        .storeId(POS.KSA_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                //KSA- Eyeglasses with single vision
                {
                        OrderContext.builder()
                                .phoneNumber("567894321")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SA_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SA)
                                        .pinCode("12215")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SA)
                                        .storeId(POS.KSA_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },

                //KSA- Eyeglasses with bifocal
                {
                        OrderContext.builder()
                                .phoneNumber("567894321")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(SA_EYEGLASSES.getProductId())
                                                .powerType(BIFOCAL)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.SA)
                                        .pinCode("12215")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.SA)
                                        .storeId(POS.KSA_PREPROD_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                //Sunglass without power coco
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .powerType(SUNGLASSES_WITHOUT_POWER)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                //FOFO OTC order
                {
                        OrderContext.builder()
                                .phoneNumber("8088127914")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES_POS.getProductId())
                                                .itemType(ItemType.OTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()

                },
                // Lens only Local Fitting India COCO
                {
                        OrderContext.builder()
                                .phoneNumber("9945901780")
                                .shipToCust(1)
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_LENS_ONLY_1.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.LENS_ONLY_LOCAL_FITTING_IN_COCO)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
        };
    }
}
