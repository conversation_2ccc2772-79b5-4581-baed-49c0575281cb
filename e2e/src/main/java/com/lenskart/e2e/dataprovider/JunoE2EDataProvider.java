package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.commons.utils.GenericUtils;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class JunoE2EDataProvider {

    private OrderContext getBaseOrderContext() {
        return OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.NET_BANKING)
                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                .build();
    }

    @DataProvider(name = "eyeglassSingleVisionPower")
    public Object[][] eyeglassSingleVisionPower() {
        OrderContext baseContext = getBaseOrderContext();
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .countryCodeMapper(baseContext.getCountryCodeMapper())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(baseContext.getHeaders())
                                .build(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build())
                }
        };
    }

    @DataProvider(name = "eyeglassBifocalPower")
    public Object[][] eyeglassBifocalPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(BIFOCAL)
                                .build())
                }
        };
    }

    @DataProvider(name = "eyeglassZeroPower")
    public Object[][] eyeglassZeroPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .build())
                }
        };
    }

    @DataProvider(name = "eyeglassWithoutPower")
    public Object[][] eyeglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "sunglassWithoutPower")
    public Object[][] sunglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "sunglassWithPower")
    public Object[][] sunglassWithPower() {
        OrderContext baseContext = getBaseOrderContext();
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .countryCodeMapper(baseContext.getCountryCodeMapper())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(baseContext.getHeaders())
                                .build(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build())
                }
        };
    }

    @DataProvider(name = "goldPid")
    public Object[][] goldPid() {
        OrderContext baseContext = getBaseOrderContext();
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .countryCodeMapper(baseContext.getCountryCodeMapper())
                                .paymentMethod(PaymentMethod.NET_BANKING)
                                .headers(baseContext.getHeaders())
                                .build(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_LOYALTY.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "frameOnly")
    public Object[][] frameOnly() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_FRAME_ONLY.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "contactLens")
    public Object[][] contactLens() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "contactLensZeroPower")
    public Object[][] contactLensZeroPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS_ZERO_POWER.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "accessories")
    public Object[][] accessories() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_ACCESSORIES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "eyeglassWithPowerAndSunglassAndContactLens")
    public Object[][] eyeglassWithPowerAndSunglassAndContactLens() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_CONTACT_LENS.getProductId())
                                        .powerType(CONTACT_LENS)
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeglassWithPowerAndInsurance")
    public Object[][] eyeglassWithPowerAndInsurance() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_INSURANCE.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeglassWithoutPowerAndInsurance")
    public Object[][] eyeglassWithoutPowerAndInsurance() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_INSURANCE.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "sunglassWithPowerAndInsurance")
    public Object[][] sunglassWithPowerAndInsurance() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_INSURANCE.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "sunglassWithoutPowerAndInsurance")
    public Object[][] sunglassWithoutPowerAndInsurance() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_INSURANCE.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeAndSunglassWithPowerAndGold")
    public Object[][] eyeAndSunglassWithPowerAndGold() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .countryCodeMapper(getBaseOrderContext().getCountryCodeMapper())
                                .paymentMethod(getBaseOrderContext().getPaymentMethod())
                                .headers(getBaseOrderContext().getHeaders())
                                .build(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_LOYALTY.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeAndSunglassWithoutPowerAndGold")
    public Object[][] eyeAndSunglassWithoutPowerAndGold() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .countryCodeMapper(getBaseOrderContext().getCountryCodeMapper())
                                .paymentMethod(getBaseOrderContext().getPaymentMethod())
                                .headers(getBaseOrderContext().getHeaders())
                                .build(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_LOYALTY.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeWithPowerAndSunglassWithoutPowerAndGold")
    public Object[][] eyeWithPowerAndSunglassWithoutPowerAndGold() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .countryCodeMapper(getBaseOrderContext().getCountryCodeMapper())
                                .paymentMethod(getBaseOrderContext().getPaymentMethod())
                                .headers(getBaseOrderContext().getHeaders())
                                .build(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_LOYALTY.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeWithoutPowerAndSunglassWithPowerAndGold")
    public Object[][] eyeWithoutPowerAndSunglassWithPowerAndGold() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .countryCodeMapper(getBaseOrderContext().getCountryCodeMapper())
                                .paymentMethod(getBaseOrderContext().getPaymentMethod())
                                .headers(getBaseOrderContext().getHeaders())
                                .build(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_LOYALTY.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeglassFrameOnlyWithSunglass")
    public Object[][] eyeglassFrameOnlyWithSunglass() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_FRAME_ONLY.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeglassAndSunglassWithoutPower")
    public Object[][] eyeglassAndSunglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeglassFrameOnlyWithContactLens")
    public Object[][] eyeglassFrameOnlyWithContactLens() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_FRAME_ONLY.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_CONTACT_LENS.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeglassAndContactLensWithoutPower")
    public Object[][] eyeglassAndContactLensWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_CONTACT_LENS.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "sunglassWithoutPowerAndContactLens")
    public Object[][] sunglassWithoutPowerAndContactLens() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_CONTACT_LENS.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "sunglassWithoutPowerAndGold")
    public Object[][] sunglassWithoutPowerAndGold() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .countryCodeMapper(getBaseOrderContext().getCountryCodeMapper())
                                .paymentMethod(getBaseOrderContext().getPaymentMethod())
                                .headers(getBaseOrderContext().getHeaders())
                                .build(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_LOYALTY.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "sunglassWithPowerAndGold")
    public Object[][] sunglassWithPowerAndGold() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .countryCodeMapper(getBaseOrderContext().getCountryCodeMapper())
                                .paymentMethod(getBaseOrderContext().getPaymentMethod())
                                .headers(getBaseOrderContext().getHeaders())
                                .build(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_LOYALTY.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "eyeglassFrameOnly")
    public Object[][] eyeglassFrameOnly() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_FRAME_ONLY.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "eyeglassZeroPowerWithSunglassWithPower")
    public Object[][] eyeglassZeroPowerWithSunglassWithPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(ZERO_POWER)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "updatePrescriptionforEyeglassSingeVision")
    public Object[][] updatePrescriptionforEyeglassSingeVision() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build())
                }
        };
    }

    @DataProvider(name = "updatePrescriptionforEyeglassBifocal")
    public Object[][] updatePrescriptionforEyeglassBifocal() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(BIFOCAL)
                                .build())
                }
        };
    }

    @DataProvider(name = "updatePrescriptionforSunglass")
    public Object[][] updatePrescriptionforSunglass() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build())
                }
        };
    }

    @DataProvider(name = "cartupdatePrescriptionforEyeglassSingleVision")
    public Object[][] cartupdatePrescriptionforEyeglassSingleVision() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build()),
                        true
                }
        };
    }

    @DataProvider(name = "cartupdatePrescriptionforEyeglassBifocal")
    public Object[][] cartupdatePrescriptionforEyeglassBifocal() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(BIFOCAL)
                                .build()),
                        true
                }
        };
    }

    @DataProvider(name = "cashbackCheckOnOrderPlacement")
    public Object[][] cashbackCheckOnOrderPlacement() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .countryCodeMapper(getBaseOrderContext().getCountryCodeMapper())
                                .paymentMethod(getBaseOrderContext().getPaymentMethod())
                                .headers(getBaseOrderContext().getHeaders())
                                .build(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_LOYALTY.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "cartupdatePrescriptionforSunglass")
    public Object[][] cartupdatePrescriptionforSunglass() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()),
                        true
                }
        };

    }

    @DataProvider(name = "eyeglassWithPowerAndSunglassWithPower")
    public Object[][] eyeglassWithPowerAndSunglassWithPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()
                        )
                }
        };
    }

    //GV cases
    @DataProvider(name = "eyeglassSingleVisionPowerWithGV")
    public Object[][] eyeglassSingleVisionPowerWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build()),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "eyeglassBifocalPowerWithGV")
    public Object[][] eyeglassBifocalPowerWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(BIFOCAL)
                                .build()),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "sunglassWithPowerWithGV")
    public Object[][] sunglassWithPowerWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "contactLensWithGV")
    public Object[][] contactLensWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .powerType(CONTACT_LENS)
                                .build()),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "eyeglassAndSunglassWithGV")
    public Object[][] eyeglassAndSunglassWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "eyeglassAndContactLensWithGV")
    public Object[][] eyeglassAndContactLensWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_CONTACT_LENS.getProductId())
                                        .powerType(CONTACT_LENS)
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "sunglassAndContactLensWithGV")
    public Object[][] sunglassAndContactLensWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_CONTACT_LENS.getProductId())
                                        .powerType(CONTACT_LENS)
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "multiProductWithGV")
    public Object[][] multiProductWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()

                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "eyeglassAndSunglassWithSC")
    public Object[][] eyeglassAndSunglassWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(     OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build()


                }
        };
    }

    @DataProvider(name = "eyeglassSingleVisionPowerWithSC")
    public Object[][] eyeglassSingleVisionPowerWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "frameOnlyWithSC")
    public Object[][] frameOnlyWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_FRAME_ONLY.getProductId())
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "contactLensWithSC")
    public Object[][] contactLensWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "eyeglassWithoutPowerWithSC")
    public Object[][] eyeglassWithoutPowerWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "eyeglassBifocalPowerWithSC")
    public Object[][] eyeglassBifocalPowerWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(BIFOCAL)
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "eyeglassZeroPowerWithSC")
    public Object[][] eyeglassZeroPowerWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }


    @DataProvider(name = "sunglassWithPowerWithSC")
    public Object[][] sunglassWithPowerWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "sunglassWithPowerWithSC_GV")
    public Object[][] sunglassWithPowerWithSC_GV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()),

                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build(),

                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "sunglassWithoutPowerWithSC_GV")
    public Object[][] sunglassWithoutPowerWithSC_GV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build()),

                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build(),

                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "sunglassWithoutPowerWithSC")
    public Object[][] sunglassWithoutPowerWithSC() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "eyeglassWithoutPowerWithSC_GV")
    public Object[][] eyeglassWithoutPowerWithSC_GV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .build()),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_IN_5PER)
                                .isGiftVoucherApplicable(true)
                                .build(),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "eyeglassSingleVisionPowerwithWallet")
    public Object[][] eyeglassSingleVisionPowerwithWallet() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build()),
                        true

                }
        };
    }

    @DataProvider(name = "sunglassWithPowerWithSC_Wallet")
    public Object[][] sunglassWithPowerWithSC_Wallet() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()),

                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build(),
                        true

                }
        };
    }

    @DataProvider(name = "sunglassWithoutPowerWithSC_Wallet")
    public Object[][] sunglassWithoutPowerWithSC_Wallet() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build()),

                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build(),
                        true


                }
        };
    }

    @DataProvider(name = "frameOnlyWithSC_Wallet")
    public Object[][] frameOnlyWithSC_Wallet() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_FRAME_ONLY.getProductId())
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build(),

                        true
                }
        };
    }

    @DataProvider(name = "eyeglassZeroPowerWithSC_Wallet")
    public Object[][] eyeglassZeroPowerWithSC_Wallet() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .build()),
                        OrderContext.StoreCreditList.builder()
                                .storeCode(StoreCredit.STORECODE_IN)
                                .isStoreCreditApplicable(true)
                                .build(),
                        true


                }
        };
    }

    @DataProvider(name = "sunglassWithPowerAndGold_Wallet")
    public Object[][] sunglassWithPowerAndGold_Wallet() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("126218" + GenericUtils.genrateRandomNumericString(4))
                                .countryCodeMapper(getBaseOrderContext().getCountryCodeMapper())
                                .paymentMethod(getBaseOrderContext().getPaymentMethod())
                                .headers(getBaseOrderContext().getHeaders())
                                .build(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_LOYALTY.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()


                        ),

                        true
                }
        };
    }


    @DataProvider(name = "eyeglassWithPowerMedibuddyOrder")
    public Object[][] eyeglassWithPowerMedibuddyOrder() {
        OrderContext baseContext = getBaseOrderContext();
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .countryCodeMapper(baseContext.getCountryCodeMapper())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .isValidationRequired(true)
                                .headers(baseContext.getHeaders())
                                .build(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build()),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.MEDIBUDDY_GIFTCODE)
                                .isGiftVoucherApplicable(true)
                                .build(),
                        true,
                        true
                }
        };
    }

    @DataProvider(name = "sunglassWithOutPowerMedibuddyOrder")
    public Object[][] sunglassWithOutPowerMedibuddyOrder() {
        OrderContext baseContext = getBaseOrderContext();
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .countryCodeMapper(baseContext.getCountryCodeMapper())
                                .paymentMethod(PaymentMethod.MEDIBUDDY)
                                .isValidationRequired(true)
                                .headers(baseContext.getHeaders())
                                .build(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build()),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.MEDIBUDDY_GIFTCODE)
                                .isGiftVoucherApplicable(true)
                                .build(),
                        true,
                        true
                }
        };
    }
}

