package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.commons.utils.GenericUtils;
import org.testng.annotations.DataProvider;

import java.util.List;
import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class JunoE2ESGDataProvider {

    private OrderContext getBaseOrderContext() {
        return OrderContext.builder()
                .phoneNumber(Countries.SG.getDefaultPhoneNumber())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.SG)
                        .pinCode(Countries.SG.getDefaultPinCode())
                        .build())
                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                .build();
    }

    @DataProvider(name = "sgEyeglassSingleVisionPower")
    public Object[][] sgEyeglassSingleVisionPower() {
        OrderContext baseContext = getBaseOrderContext();
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.SG.getDefaultPhoneNumber())
                                .countryCodeMapper(baseContext.getCountryCodeMapper())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(baseContext.getHeaders())
                                .build(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SG_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build())
                }
        };
    }

    @DataProvider(name = "sgeyeglassAndSunglassWithoutPower")
    public Object[][] sgeyeglassAndSunglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(SG_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SG_SUNGLASSES.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "sgEyeglassBifocalPower")
    public Object[][] sgEyeglassBifocalPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SG_EYEGLASSES.getProductId())
                                .powerType(BIFOCAL)
                                .build())
                }
        };
    }

    @DataProvider(name = "sgEyeglassWithoutPower")
    public Object[][] sgEyeglassWithoutPower() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SG_EYEGLASSES.getProductId())
                                .build())
                }
        };
    }

    @DataProvider(name = "sgSunglassWithPower")
    public Object[][] sgSunglassWithPower() {
        OrderContext baseContext = getBaseOrderContext();
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber(Countries.SG.getDefaultPhoneNumber())
                                .countryCodeMapper(baseContext.getCountryCodeMapper())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(baseContext.getHeaders())
                                .build(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SG_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build())
                }
        };
    }

    @DataProvider(name = "sgEyeglassAndSunglassWithGV")
    public Object[][] sgEyeglassAndSunglassWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(SG_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SG_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_SG_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "sgMultiProductWithGV")
    public Object[][] sgMultiProductWithGV() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(SG_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(SG_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build()
                        ),
                        OrderContext.GiftVoucherList.builder()
                                .giftVoucher(GiftVoucher.TESTGV_SG_5PER)
                                .isGiftVoucherApplicable(true)
                                .build()
                }
        };
    }

    @DataProvider(name = "sgeyeglassSingleVisionPowerwithWallet")
    public Object[][] sgeyeglassSingleVisionPowerwithWallet() {
        return new Object[][]{
                {
                        getBaseOrderContext(),
                        List.of(OrderContext.ProductList.builder()
                                .productId(SG_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build()),
                        true
                }
        };
    }
}