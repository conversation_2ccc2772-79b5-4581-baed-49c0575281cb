<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Discovery Test Suite">
    <listeners>
        <listener class-name="com.lenskart.commons.listeners.ExtentReportListener" />
        <listener class-name="com.lenskart.commons.listeners.TestCategoryListener" />
    </listeners>

    <test name="E2E test">
        <parameter name="environment" value="default" />
        <classes>
            <class name="com.lenskart.e2e.cs.CsCancellationTest" />
            <class name="com.lenskart.e2e.cs.CsReturnTest" />
            <class name="com.lenskart.e2e.cs.CsRTOTest" />
            <class name="com.lenskart.e2e.cs.CsFastRefundTest" />
            <class name="com.lenskart.e2e.cs.CsReturnExchangeTest" />
            <class name="com.lenskart.e2e.cs.CsAwaitedRTOTest" />
            <class name="com.lenskart.e2e.cs.CsDirectReceivingTest" />
            <class name="com.lenskart.e2e.cs.CsTrackingMiddlewareTest" />
            <class name="com.lenskart.e2e.cs.CsEmptyBoxReceivingTest" />
            <class name="com.lenskart.e2e.cs.CsCrmReturnRefundMappingTest" />
            <class name="com.lenskart.e2e.cs.CsCrmInitiateCancellationTest" />
            <class name="com.lenskart.e2e.cs.CsPFUTest" />
        </classes>
    </test>
</suite>