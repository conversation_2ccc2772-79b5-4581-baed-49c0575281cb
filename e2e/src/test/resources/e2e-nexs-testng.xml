<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="NexS Sanity Suite" verbose="1">
    <listeners>
        <listener class-name="com.lenskart.commons.listeners.ExtentReportListener" />
        <listener class-name="com.lenskart.commons.listeners.TestCategoryListener" />
    </listeners>

    <test name="NexS Sanity Tests">
        <classes>
            <class name="com.lenskart.e2e.nexs.AddverbPickingTest" />
            <class name="com.lenskart.e2e.nexs.CIDInventoryUpdatesToJunoTest" />
            <class name="com.lenskart.e2e.nexs.ConsolidationTests" />
            <class name="com.lenskart.e2e.nexs.DistributorOrderTests" />
            <class name="com.lenskart.e2e.nexs.ManualPickingTest" />
            <class name="com.lenskart.e2e.nexs.NexsAutoJitOrderProcessingTest" />
            <class name="com.lenskart.e2e.nexs.NexsCancellationTest" />
            <class name="com.lenskart.e2e.nexs.NexsManulJItOrderProcessingTest" />
            <class name="com.lenskart.e2e.nexs.NexsOrderProcessingTests" />
            <class name="com.lenskart.e2e.nexs.NexsReassignmentTest" />
            <class name="com.lenskart.e2e.nexs.NexsUpdatePrescriptionWhileOrderProcessingInWHTest" />
            <class name="com.lenskart.e2e.nexs.PickingPriorityTests" />
            <class name="com.lenskart.e2e.nexs.QCFailTest" />
        </classes>
    </test>

</suite>