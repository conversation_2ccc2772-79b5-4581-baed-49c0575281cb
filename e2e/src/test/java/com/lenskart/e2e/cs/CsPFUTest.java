package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsAwaitedRTOE2EHelpers;
import com.lenskart.e2e.helper.cs.CsPFUE2EHelpers;
import com.lenskart.scm.model.ScmOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class CsPFUTest {


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerMedibuddyOrderByBotApprovePayment")
    public void verifyUpdatePowerMedibuddyOrderByBotApprovePayment(OrderContext orderContext, CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerMedibuddyOrderByJunoApprovePayment")
    public void verifyUpdatePowerMedibuddyOrderByJunoApprovePayment(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerMedibuddyOrderByBotRejectPayment")
    public void verifyUpdatePowerMedibuddyOrderByBotRejectPayment(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerMedibuddyOrderByJunoRejectPayment")
    public void verifyUpdatePowerMedibuddyOrderByJunoRejectPayment(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByBotCOD")
    public void verifyUpdatePowerWEBDTCOrderByBotCOD(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByBotPending")
    public void verifyUpdatePowerWEBDTCOrderByBotPending(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByBotPrepaid")
    public void verifyUpdatePowerWEBDTCOrderByBotPrepaid(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByJunoCOD")
    public void verifyUpdatePowerWEBDTCByJunoCOD(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByJunoPending")
    public void verifyUpdatePowerWEBDTCByJunoPending(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByJunoPrepaid")
    public void verifyUpdatePowerWEBDTCByJunoPrepaid(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByJunoTwicePending")
    public void verifyUpdatePowerWEBDTCByJunoTwicePending(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByBotTwicePending")
    public void verifyUpdatePowerWEBDTCByBotTwicePending(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByBotInitialPowerGivenCOD")
    public void verifyUpdatePowerWEBDTCByBotInitialPowerGivenCOD(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByJunoInitialPowerGivenCOD")
    public void verifyUpdatePowerWEBDTCByJunoInitialPowerGivenCOD(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByBotInitialPowerGivenPending")
    public void verifyUpdatePowerWEBDTCByBotInitialPowerGivenPending(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByJunoInitialPowerGivenPending")
    public void verifyUpdatePowerWEBDTCByJunoInitialPowerGivenPending(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerWEBDTCByJunoPendingNoPower")
    public void verifyUpdatePowerWEBDTCByJunoPendingNoPower(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerMedibuddyOrderByBotApprovePaymentCOCOLensOnly")
    public void verifyUpdatePowerMedibuddyOrderByBotApprovePaymentCOCOLensOnly(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerMedibuddyOrderByBotRejectPaymentCOCOLensOnly")
    public void verifyUpdatePowerMedibuddyOrderByBotRejectPaymentCOCOLensOnly(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerMedibuddyOrderByJunoApprovePaymentCOCOLensOnly")
    public void verifyUpdatePowerMedibuddyOrderByJunoApprovePaymentCOCOLensOnly(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "updatePowerMedibuddyOrderByJunoRejectPaymentCOCOLensOnly")
    public void verifyUpdatePowerMedibuddyOrderByJunoRejectPaymentCOCOLensOnly(OrderContext orderContext,CsOrderContext.PFUContext pfuContext) {
        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsPFUE2EHelpers.builder()
                .orderContext(orderContext)
                .pfuContext(pfuContext)
                .build()
                .test();
    }
}

