package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsRTOE2EHelpers;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class CsRTOTest {


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "rtoContextCODOrder")
    public void verifyRTOOrderCODOrder(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsRTOE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "rtoContextPrepaidOrder")
    public void verifyRTOOrderPrepaidOrder(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsRTOE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "rtoContextPrepaidQCStatusFail")
    public void verifyRTOOrderPrepaidQCStatusFail(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsRTOE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "rtoContextCODWithGoldMembership")
    public void verifyRTOOrderCODWithGoldMembership(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsRTOE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }
}

