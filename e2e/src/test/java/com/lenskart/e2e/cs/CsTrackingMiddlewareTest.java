package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsTrackingMiddlewareHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@TestCategory(TestCategory.Category.SANITY)
@Slf4j
public class CsTrackingMiddlewareTest {

    /**
     * ✅ BlueDart | Delivered Flow
     * 📦 Verifies the scan events for successful delivery via BlueDart
     * 🔍 Validates: Middleware API push + DB scan status persistence
     */
    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "verifyBlueDartDeliveredEvents")
    public void verifyBlueDartDeliveredEvents(List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList, OrderContext orderContext, CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        CsTrackingMiddlewareHelper.builder()
                .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                .trackingMiddlewareDetails(trackingMiddlewareDetails)
                .orderContext(orderContext)
                .build()
                .test();
    }

    /**
     * ✅ BlueDart | RTO Flow
     * 🚚 Verifies Return-to-Origin scan events for BlueDart
     */
    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "verifyBlueDartRTOEvents")
    public void verifyBlueDartRTOEvents(List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList, OrderContext orderContext, CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        CsTrackingMiddlewareHelper.builder()
                .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                .trackingMiddlewareDetails(trackingMiddlewareDetails)
                .orderContext(orderContext)
                .build()
                .test();
    }

    /**
     * ✅ CriticalLog | Delivered Flow
     * 🧪 Validates successful delivery events from CriticalLog courier
     */
    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "verifyCriticalLogDeliveredEvents")
    public void verifyCriticalLogDeliveredEvents(List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList, OrderContext orderContext, CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        CsTrackingMiddlewareHelper.builder()
                .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                .trackingMiddlewareDetails(trackingMiddlewareDetails)
                .orderContext(orderContext)
                .build()
                .test();
    }

    /**
     * ✅ CriticalLog | RTO Flow
     * 🚚 Verifies return-to-origin tracking events for CriticalLog courier
     */
    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "verifyCriticalLogRTOEvents")
    public void verifyCriticalLogRTOEvents(List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList, OrderContext orderContext, CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        CsTrackingMiddlewareHelper.builder()
                .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                .trackingMiddlewareDetails(trackingMiddlewareDetails)
                .orderContext(orderContext)
                .build()
                .test();
    }

    /**
     * ✅ PurpleDrone | Delivered Flow
     * 🧪 Validates successful delivery scans for PurpleDrone courier
     */
    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "verifyPurpleDroneDeliveredEvents")
    public void verifyPurpleDroneDeliveredEvents(List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList, OrderContext orderContext, CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        CsTrackingMiddlewareHelper.builder()
                .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                .trackingMiddlewareDetails(trackingMiddlewareDetails)
                .orderContext(orderContext)
                .build()
                .test();
    }

    /**
     * ✅ PurpleDrone | RTO Flow
     * 🚚 Validates return-to-origin event flow for PurpleDrone
     */
    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "verifyPurpleDroneRTOEvents")
    public void verifyPurpleDroneRTOEvents(List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList, OrderContext orderContext, CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        CsTrackingMiddlewareHelper.builder()
                .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                .trackingMiddlewareDetails(trackingMiddlewareDetails)
                .orderContext(orderContext)
                .build()
                .test();
    }

    /**
     * ✅ PicoExpress | Delivered Flow
     * 🧪 Ensures tracking scan events for successful PicoExpress delivery are handled correctly
     */
    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "verifyPicoExpressDeliveredEvents")
    public void verifyPicoExpressDeliveredEvents(List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList, OrderContext orderContext, CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        CsTrackingMiddlewareHelper.builder()
                .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                .trackingMiddlewareDetails(trackingMiddlewareDetails)
                .orderContext(orderContext)
                .build()
                .test();
    }

    /**
     * ✅ PicoExpress | RTO Flow
     * 🧪 Validates RTO tracking scan events for PicoExpress
     */
    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "verifyPicoExpressRTOEvents")
    public void verifyPicoExpressRTOEvents(List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList, OrderContext orderContext, CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        CsTrackingMiddlewareHelper.builder()
                .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                .trackingMiddlewareDetails(trackingMiddlewareDetails)
                .orderContext(orderContext)
                .build()
                .test();
    }

    /**
     * ✅ DOT Courier | Delivered Flow
     * 🧪 Verifies scan events for successful DOT deliveries
     */
    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "verifyDotDeliveredEvents")
    public void verifyDotDeliveredEvents(List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList, OrderContext orderContext, CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        CsTrackingMiddlewareHelper.builder()
                .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                .trackingMiddlewareDetails(trackingMiddlewareDetails)
                .orderContext(orderContext)
                .build()
                .test();
    }

    /**
     * ✅ DOT Courier | Delivered Flow
     * 🧪 Verifies scan events for successful DOT deliveries
     */
    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "verifyDotRTOEvents")
    public void verifyDotRTOEvents(List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList, OrderContext orderContext, CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        CsTrackingMiddlewareHelper.builder()
                .trackingMiddlewareDetailsList(trackingMiddlewareDetailsList)
                .trackingMiddlewareDetails(trackingMiddlewareDetails)
                .orderContext(orderContext)
                .build()
                .test();
    }
}
