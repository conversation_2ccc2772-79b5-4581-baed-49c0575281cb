package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.*;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsReturnCancellationE2EHelpers;
import com.lenskart.e2e.helper.cs.CsReturnE2EHelpers;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class CsReturnTest {


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnContextMultiplePayment")
    public void verifyReturnOrderMultiplePayment(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnContextCOCODTC")
    public void verifyReturnOrderCOCODTC(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnContextCOCOOTC")
    public void verifyReturnOrderCOCOOTC(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnContextLocalFitting")
    public void verifyReturnOrderLocalFitting(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnContextCOCODTCReturnWeb")
    public void verifyReturnOrderCOCODTCWEB(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnContextWebDTCCOD")
    public void returnContextWebDTCCOD(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnContextWEBDTCAndGold")
    public void verifyReturnOrderWebDTCAndGold(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }



    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnContextWebDTCPrepaidReturnVSM")
    public void verifyReturnOrderWebDTCPrepaidReturnVSM(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnContextWEBDTCReturnWeb")
    public void verifyReturnOrderWEBDTCReturnWeb(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnCancellationContext")
    public void verifyReturnCancellationOrder(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnCancellationE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }
}

