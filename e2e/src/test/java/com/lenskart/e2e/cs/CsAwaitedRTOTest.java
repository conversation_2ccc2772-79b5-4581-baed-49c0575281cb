package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsAwaitedRTOE2EHelpers;
import com.lenskart.scm.model.ScmOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class CsAwaitedRTOTest {


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "awaitedRTOContextPrepaidOrder")
    public void verifyAwaitedRTOOrderPrepaidOrder(OrderContext orderContext, ScmOrderContext scmOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsAwaitedRTOE2EHelpers.builder()
                .orderContext(orderContext)
                .scmOrderContext(scmOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "awaitedRTOContextCODOrder")
    public void verifyAwaitedRTOOrderCODOrder(OrderContext orderContext, ScmOrderContext scmOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsAwaitedRTOE2EHelpers.builder()
                .orderContext(orderContext)
                .scmOrderContext(scmOrderContext)
                .build()
                .test();
    }
}

