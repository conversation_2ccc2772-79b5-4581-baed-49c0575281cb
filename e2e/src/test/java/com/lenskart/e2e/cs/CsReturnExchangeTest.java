package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsReturnExchangeE2EHelpers;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class CsReturnExchangeTest {


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "returnExchangeContext")
    public void verifyReturnExchangeOrder(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnExchangeE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }
}

