package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.*;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsFastRefundE2EHelpers;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;


@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class CsFastRefundTest {

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "getFastRefundDetailsCashFree")
    public void verifyPrepaidFastRefundCashFree(OrderContext orderContext,NexsOrderContext nexsOrderContext, CsOrderContext csOrderContext) {
        CsFastRefundE2EHelpers.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "getFastRefundDetailsStoreCredit")
    public void verifyPrepaidFastRefundStoreCredit(OrderContext orderContext,NexsOrderContext nexsOrderContext, CsOrderContext csOrderContext) {
        CsFastRefundE2EHelpers.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "getFastRefundDetailsSource")
    public void verifyPrepaidFastRefundSource(OrderContext orderContext,NexsOrderContext nexsOrderContext, CsOrderContext csOrderContext) {
        CsFastRefundE2EHelpers.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }
}