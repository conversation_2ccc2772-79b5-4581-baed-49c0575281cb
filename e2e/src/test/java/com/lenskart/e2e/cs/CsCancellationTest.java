package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsCancellationE2EHelpers;
import org.testng.annotations.Test;


@TestCategory(TestCategory.Category.SANITY)
public class CsCancellationTest {


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "cancellationContextFull")
    public void verifyCancelOrderFull(OrderContext orderContext, CsOrderContext csOrderContext) {
        CsCancellationE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "cancellationContextPartial")
    public void verifyCancelOrderPartial(OrderContext orderContext, CsOrderContext csOrderContext) {
        CsCancellationE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }
}

