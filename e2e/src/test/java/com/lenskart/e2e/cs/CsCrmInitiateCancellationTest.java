package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.helpers.CrmInitiateCancellationHelper;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsCrmInitiateCancellationE2EHelper;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class CsCrmInitiateCancellationTest {

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "crmCancellationProvider")
    public void verifyCrmInitiateCancellation(OrderContext orderContext, CsOrderContext csOrderContext) {

        CsCrmInitiateCancellationE2EHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }
}
