package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsDirectReceivingE2EHelpers;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class CsDirectReceivingTest {


    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "directReceivingContextWEBDTC")
    public void verifyDirectReceivingOrderWEBDTC(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsDirectReceivingE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "directReceivingContextWEBDTCItemMissing")
    public void verifyDirectReceivingOrderWEBDTCItemMissing(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsDirectReceivingE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "directReceivingContextCOCODTC")
    public void verifyDirectReceivingOrderCOCODTC(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsDirectReceivingE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "directReceivingContextCOCOB2B")
    public void verifyDirectReceivingOrderCOCOB2B(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsDirectReceivingE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "directReceivingContextSGWEBB2B")
    public void verifyDirectReceivingOrderSGWEBB2B(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsDirectReceivingE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "directReceivingContextSGCOCODTC")
    public void verifyDirectReceivingOrderSGCOCOCDTC(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsDirectReceivingE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "directReceivingContextFOFOB2B")
    public void verifyDirectReceivingOrderFOFOB2B   (OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsDirectReceivingE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }
}

