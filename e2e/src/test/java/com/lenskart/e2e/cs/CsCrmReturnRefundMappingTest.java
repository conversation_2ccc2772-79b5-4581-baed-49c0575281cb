package com.lenskart.e2e.cs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.CsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsCrmReturnRefundMappingE2EHelper;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class CsCrmReturnRefundMappingTest {

    @Test(dataProviderClass = CsE2EDataProvider.class, dataProvider = "crmReturnRefundMappingProvider")
    public void verifyCrmReturnRefundMapping(OrderContext orderContext, CsOrderContext csOrderContext) {

        CsCrmReturnRefundMappingE2EHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
    }
}
