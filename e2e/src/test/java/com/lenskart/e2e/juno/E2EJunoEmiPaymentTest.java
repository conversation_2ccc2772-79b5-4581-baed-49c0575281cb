
package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.*;
import com.lenskart.juno.helpers.*;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoEmiPaymentTest {

    @Test
    public void testEmiPrepaidPaymentFlow() {

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .paymentMethod(PaymentMethod.CREDIT_CARD)
                .isValidationRequired(true)
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .country(Countries.IN)
                        .build())
                .headers(OrderContext.Headers.builder()
                        .client(Client.DESKTOP)
                        .build())
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                .powerType(PowerTypes.SINGLE_VISION)
                                .build()))
                .build();


        JunoOrderCreationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }
}
