package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2EAEDataProvider;
import com.lenskart.e2e.dataprovider.JunoE2ESADataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoOrderCreationTestAE {

    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeEyeglassPowerOrder")
    public void aeEyeglassPowerOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeEyeglassWithoutPower")
    public void aeEyeglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeSunglassWithPower")
    public void aeSunglassWithPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeSunglassWithoutPower")
    public void aeSunglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeEyeSunContactLensOrder")
    public void aeEyeSunContactLensOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }


    // GV Cases
    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeEyeglassPowerOrderWithGV")
    public void aeEyeglassPowerOrderWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeEyeSunCLOrderWithGV")
    public void aeEyeSunCLOrderWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }


    //SC  Cases
    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeEyeglassAndSunglassWithSC")
    public void aeEyeglassAndSunglassWithSCOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setIsValidationRequired(true);
        orderContext.setStoreCreditList(storeCreditList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeEyeSunCLOrderWithSC")
    public void aeEyeSunCLOrderWithSCOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setIsValidationRequired(true);
        orderContext.setStoreCreditList(storeCreditList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    //SC & GV Cases
    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeSunAndEyeOrderWithSC_GV")
    public void aeSunEyeOrderWithSC_GVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setGiftVoucherList(giftVoucherList);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }

    //wallet Case
    @Test(dataProviderClass = JunoE2EAEDataProvider.class, dataProvider = "aeeyeglassSingleVisionPowerwithWallet")
    public void aeeyeglassSingleVisionPowerwithWallet(OrderContext orderContext, List<OrderContext.ProductList> productLists, boolean isWalletUsed) {
        orderContext.setProductLists(productLists);
        orderContext.setIsWalletUsed(isWalletUsed);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}