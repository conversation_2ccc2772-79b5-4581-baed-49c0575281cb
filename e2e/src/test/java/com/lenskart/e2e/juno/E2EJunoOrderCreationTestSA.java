package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2EAEDataProvider;
import com.lenskart.e2e.dataprovider.JunoE2ESADataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoOrderCreationTestSA {

    @Test(dataProviderClass = JunoE2ESADataProvider.class, dataProvider = "saEyeglassPowerOrder")
    public void saEyeglassPowerOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2ESADataProvider.class, dataProvider = "saEyeglassWithoutPower")
    public void saEyeglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESADataProvider.class, dataProvider = "saSunglassWithPower")
    public void saSunglassWithPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESADataProvider.class, dataProvider = "saSunglassWithoutPower")
    public void saSunglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2ESADataProvider.class, dataProvider = "saEyeSunContactLensOrder")
    public void saEyeSunContactLensOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    // No Accessories pid added for SA currently
    @Test(dataProviderClass = JunoE2ESADataProvider.class, dataProvider = "saAccessories", enabled = false)
    public void saAccessories(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    // GV Cases
    @Test(dataProviderClass = JunoE2ESADataProvider.class, dataProvider = "saEyeglassPowerOrderWithGV")
    public void saEyeglassPowerOrderWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESADataProvider.class, dataProvider = "saEyeSunCLOrderWithGV")
    public void saEyeSunCLOrderWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESADataProvider.class, dataProvider = "saeyeglassSingleVisionPowerwithWallet", enabled = true)
    public void saeyeglassSingleVisionPowerwithWallet(OrderContext orderContext, List<OrderContext.ProductList> productLists, boolean isWalletUsed) {
        orderContext.setProductLists(productLists);
        orderContext.setIsWalletUsed(isWalletUsed);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}