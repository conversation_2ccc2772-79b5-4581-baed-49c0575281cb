package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2EDataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoOrderCreationTest {


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassSingleVisionPower")
    public void eyeglassSingleVisionPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassBifocalPower")
    public void eyeglassBifocalPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPower")
    public void eyeglassZeroPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPower")
    public void eyeglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPower")
    public void sunglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPower")
    public void sunglassWithPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "goldPid", enabled = true)
    public void goldPid(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "frameOnly")
    public void frameOnly(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "contactLens", enabled = true)
    public void contactLens(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "contactLensZeroPower", enabled = true)
    public void contactLensZeroPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "accessories")
    public void accessories(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPowerAndInsurance", enabled = false)
    public void eyeglassWithoutPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerAndInsurance", enabled = true)
    public void sunglassWithPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerAndInsurance", enabled = true)
    public void sunglassWithoutPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeAndSunglassWithPowerAndGold")
    public void eyeAndSunglassWithPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeWithoutPowerAndSunglassWithPowerAndGold")
    public void eyeWithoutPowerAndSunglassWithPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithPowerAndInsurance", enabled = true)
    public void eyeglassWithPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "updatePrescriptionforEyeglassSingeVision")
    public void updatePrescriptionForEyeGlassSingeVision(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "updatePrescriptionforEyeglassBifocal")
    public void updatePrescriptionForEyeGlassBifocal(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cartupdatePrescriptionforEyeglassSingleVision")
    public void cartUpdatePrescriptionForEyeglassSingleVisionOrderCreation(OrderContext orderContext, List<OrderContext.ProductList> productLists, boolean isCartPrescriptionRequired) {
        orderContext.setIsCartPrescriptionRequired(isCartPrescriptionRequired);
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cartupdatePrescriptionforEyeglassBifocal")
    public void cartUpdatePrescriptionForEyeglassBifocalOrderCreation(OrderContext orderContext, List<OrderContext.ProductList> productLists, boolean isCartPrescriptionRequired) {
        orderContext.setProductLists(productLists);
        orderContext.setIsCartPrescriptionRequired(isCartPrescriptionRequired);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cartupdatePrescriptionforSunglass")
    public void cartUpdatePrescriptionForSunglassWithPowerOrderCreation(OrderContext orderContext, List<OrderContext.ProductList> productLists, boolean isCartPrescriptionRequired) {
        orderContext.setProductLists(productLists);
        orderContext.setIsCartPrescriptionRequired(isCartPrescriptionRequired);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cashbackCheckOnOrderPlacement", enabled = true)
    public void cashBackFlowOrderPlacement(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPower")
    public void sunglassWithPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerAndGold", enabled = true)
    public void sunglassWithoutPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerAndGold", enabled = true)
    public void sunglassWithPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassFrameOnly")
    public void eyeglassFrameOnlyGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassAndContactLensWithoutPower", enabled = true)
    public void eyeglassAndContactLensWithoutPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassAndSunglassWithoutPower")
    public void eyeglassAndSunglassWithoutPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassFrameOnlyWithContactLens", enabled = true)
    public void eyeglassFrameOnlyWithContactLensGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerAndContactLens", enabled = true)
    public void sunglassWithoutPowerAndContactLensGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPowerWithSunglassWithPower")
    public void eyeglassZeroPowerWithSunglassWithPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithPowerAndSunglassWithPower")
    public void eyeglassWithPowerAndSunglassWithPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPower")
    public void eyeglassWithoutPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPower")
    public void eyeglassZeroPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    //GV Case
    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassSingleVisionPowerWithGV")
    public void eyeglassSingleVisionPowerWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassBifocalPowerWithGV")
    public void eyeglassBifocalPowerWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerWithGV")
    public void sunglassWithPowerWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassAndSunglassWithGV")
    public void eyeglassAndSunglassWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "multiProductWithGV")
    public void multiProductWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassAndSunglassWithSC")
    public void eyeglassAndSunglassWithSC(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPowerWithSC")
    public void eyeglassWithoutPowerWithSCOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setIsValidationRequired(true);
        orderContext.setStoreCreditList(storeCreditList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassSingleVisionPowerWithSC")
    public void eyeglassSingleVisionPowerWithSCOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassBifocalPowerWithSC")
    public void eyeglassBifocalPowerWithSCOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPowerWithSC")
    public void eyeglassZeroPowerWithSCOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "frameOnlyWithSC")
    public void frameOnlyWithSCOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "contactLensWithSC", enabled = true)
    public void contactLensWithSCOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerWithSC")
    public void sunglassWithoutPowerWithSCOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerWithSC")
    public void sunglassWithPowerWithSCOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();

    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerWithSC_GV")
    public void sunglassWithPowerWithSC_GVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerWithSC_GV")
    public void sunglassWithoutPowerWithSC_GV(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setGiftVoucherList(giftVoucherList);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPowerWithSC_GV")
    public void eyeglassWithoutPowerWithSC_GVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setGiftVoucherList(giftVoucherList);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassSingleVisionPowerwithWallet")
    public void eyeglassSingleVisionPowerwithWallet(OrderContext orderContext, List<OrderContext.ProductList> productLists, boolean isWalletUsed) {
        orderContext.setProductLists(productLists);
        orderContext.setIsWalletUsed(isWalletUsed);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerAndGold_Wallet", enabled = true)
    public void sunglassWithPowerAndGold_Wallet(OrderContext orderContext, List<OrderContext.ProductList> productLists, boolean isWalletUsed) {
        orderContext.setProductLists(productLists);
        orderContext.setIsWalletUsed(isWalletUsed);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPowerWithSC_Wallet")
    public void eyeglassZeroPowerWithSC_Wallet(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList, boolean isWalletUsed) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsWalletUsed(isWalletUsed);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "frameOnlyWithSC_Wallet")
    public void frameOnlyWithSC_Wallet(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList, boolean isWalletUsed) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsWalletUsed(isWalletUsed);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerWithSC_Wallet")
    public void sunglassWithoutPowerWithSC_Wallet(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList, boolean isWalletUsed) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsWalletUsed(isWalletUsed);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerWithSC_Wallet")
    public void sunglassWithPowerWithSC_Wallet(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList, boolean isWalletUsed) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        orderContext.setIsWalletUsed(isWalletUsed);
        orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithPowerMedibuddyOrder")
    public void eyeglassWithPowerMedibuddyOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList, boolean isMedibuddyOrder, boolean isMedibuddyApprovalRequired) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        orderContext.setIsMedibuddyOrder(isMedibuddyOrder);
        orderContext.setIsMedibuddyApprovalRequired(isMedibuddyApprovalRequired);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithOutPowerMedibuddyOrder")
    public void sunglassWithOutPowerMedibuddyOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList, boolean isMedibuddyOrder, boolean isMedibuddyApprovalRequired) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        orderContext.setIsMedibuddyOrder(isMedibuddyOrder);
        orderContext.setIsMedibuddyApprovalRequired(isMedibuddyApprovalRequired);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }
}