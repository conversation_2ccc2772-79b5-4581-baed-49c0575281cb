package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2ESGDataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoOrderCreationTestSG {

    @Test(dataProviderClass = JunoE2ESGDataProvider.class, dataProvider = "sgEyeglassSingleVisionPower")
    public void sgEyeglassSingleVisionPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESGDataProvider.class, dataProvider = "sgeyeglassAndSunglassWithoutPower")
    public void sgeyeglassAndSunglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESGDataProvider.class, dataProvider = "sgEyeglassWithoutPower")
    public void sgEyeglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESGDataProvider.class, dataProvider = "sgSunglassWithPower")
    public void sgSunglassWithPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESGDataProvider.class, dataProvider = "sgEyeglassAndSunglassWithGV")
    public void sgEyeglassAndSunglassWithGV(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESGDataProvider.class, dataProvider = "sgMultiProductWithGV")
    public void sgMultiProductWithGV(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESGDataProvider.class, dataProvider = "sgEyeglassAndSunglassWithSC")
    public void sgEyeglassAndSunglassWithSC(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESGDataProvider.class, dataProvider = "sgSunglassWithoutPowerWithSC")
    public void sgSunglassWithoutPowerWithSC(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESGDataProvider.class, dataProvider = "sgEyeglassWithoutPowerWithSC_GV")
    public void sgEyeglassWithoutPowerWithSC_GV(OrderContext orderContext, List<OrderContext.ProductList> productLists,
                                                OrderContext.GiftVoucherList giftVoucherList, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        orderContext.setStoreCreditList(storeCreditList);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ESGDataProvider.class, dataProvider = "sgeyeglassSingleVisionPowerwithWallet", enabled = true)
    public void sgeyeglassSingleVisionPowerwithWallet(OrderContext orderContext, List<OrderContext.ProductList> productLists, boolean isWalletUsed) {
        orderContext.setProductLists(productLists);
        orderContext.setIsWalletUsed(isWalletUsed);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}