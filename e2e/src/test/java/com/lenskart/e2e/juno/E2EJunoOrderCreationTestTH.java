package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2ESGDataProvider;
import com.lenskart.e2e.dataprovider.JunoE2ETHDataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoOrderCreationTestTH {

    @Test(dataProviderClass = JunoE2ETHDataProvider.class, dataProvider = "thEyeglassSingleVisionPower")
    public void thEyeglassSingleVisionPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ETHDataProvider.class, dataProvider = "theyeglassAndSunglassWithoutPower")
    public void theyeglassAndSunglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ETHDataProvider.class, dataProvider = "thEyeglassWithoutPower")
    public void thEyeglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ETHDataProvider.class, dataProvider = "thSunglassWithoutPower")
    public void thSunglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ETHDataProvider.class, dataProvider = "thEyeglassAndSunglassWithGV")
    public void thEyeglassAndSunglassWithGV(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ETHDataProvider.class, dataProvider = "thMultiProductWithGV")
    public void thMultiProductWithGV(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ETHDataProvider.class, dataProvider = "thEyeglassAndSunglassWithSC")
    public void thEyeglassAndSunglassWithSC(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ETHDataProvider.class, dataProvider = "thSunglassWithoutPowerWithSC")
    public void thSunglassWithoutPowerWithSC(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setStoreCreditList(storeCreditList);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2ETHDataProvider.class, dataProvider = "thEyeglassWithoutPowerWithSC_GV")
    public void thEyeglassWithoutPowerWithSC_GV(OrderContext orderContext, List<OrderContext.ProductList> productLists,
                                                OrderContext.GiftVoucherList giftVoucherList, OrderContext.StoreCreditList storeCreditList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        orderContext.setStoreCreditList(storeCreditList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
    @Test(dataProviderClass = JunoE2ETHDataProvider.class, dataProvider = "theyeglassSingleVisionPowerwithWallet",enabled = false)
    public void theyeglassSingleVisionPowerwithWallet(OrderContext orderContext, List<OrderContext.ProductList> productLists, boolean isWalletUsed) {
        orderContext.setProductLists(productLists);
        orderContext.setIsWalletUsed(isWalletUsed);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}