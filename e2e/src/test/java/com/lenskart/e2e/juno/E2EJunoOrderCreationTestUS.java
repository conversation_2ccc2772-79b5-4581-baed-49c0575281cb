package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2ESADataProvider;
import com.lenskart.e2e.dataprovider.JunoE2EUSDataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoOrderCreationTestUS {

    @Test(dataProviderClass = JunoE2EUSDataProvider.class, dataProvider = "usEyeglassPowerOrder")
    public void usEyeglassPowerOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EUSDataProvider.class, dataProvider = "usEyeglassWithoutPower")
    public void usEyeglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EUSDataProvider.class, dataProvider = "usSunglassWithPower")
    public void usSunglassWithPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EUSDataProvider.class, dataProvider = "usSunglassWithoutPower")
    public void usSunglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }


    // No Accessories pid added for SA currently
    @Test(dataProviderClass = JunoE2EUSDataProvider.class, dataProvider = "usAccessories", enabled = false)
    public void usAccessories(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    // GV Cases
    @Test(dataProviderClass = JunoE2EUSDataProvider.class, dataProvider = "usEyeglassPowerOrderWithGV")
    public void usEyeglassPowerOrderWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EUSDataProvider.class, dataProvider = "usEyeSunglassOrderWithGV")
    public void usEyeSunCLOrderWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
         orderContext.setIsValidationRequired(true);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }

}