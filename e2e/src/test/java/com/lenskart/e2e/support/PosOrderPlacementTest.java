package com.lenskart.e2e.support;

import com.lenskart.commons.model.*;
import com.lenskart.pos.helpers.AuthenticationHelper;
import com.lenskart.pos.helpers.ordersummary.OrderDetailsHelper;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.pos.helpers.nso.StoreListHelper;
import com.lenskart.pos.helpers.ordersummary.OrdersummaryListHelper;
import com.lenskart.pos.model.POS;
import com.lenskart.pos.model.PosContext;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;


import java.util.List;

import static com.lenskart.commons.model.PowerTypes.SINGLE_VISION;
import static com.lenskart.commons.model.ProductId.*;

@Slf4j
public class PosOrderPlacementTest {

    @Test
    public void posOrderPlacement() {

        OrderContext orderContext =
                OrderContext.builder()
                        .phoneNumber(Countries.AE.getDefaultPhoneNumber())
                        .productLists(List.of(
                                OrderContext.ProductList.builder()
                                        .productId(AE_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .itemType(ItemType.B2B)
                                        .finalState(NexsOrderState.DISPATCHED)
                                        .isPrescriptionRequired(true)
                                        .build()))
                        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                .country(Countries.AE)
                                .pinCode("241868")
                                .build())
                        .isPosOrder(true)
                        .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                .country(Countries.AE)
                                .storeId(POS.AE_PREPROD_STORE.getStoreId())
                                .build())
                        .paymentMethod(PaymentMethod.OFFLINE_CASH)
                        .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                        .build();



        PosOrderCreationHelper posOrderCreationHelper = PosOrderCreationHelper
                .builder()
                .orderContext(orderContext)
                .build();
        posOrderCreationHelper.test();

        log.info("Order ID - {}", orderContext.getOrderId());
    }

    @Test
    public void GetStoreList() {
        OrderContext orderContext = OrderContext.builder()
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("560075")
                        .build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .country(Countries.IN)
                        .storeId(POS.IN_ADMIN_STORE.getStoreId())
                        .build())
                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                .build();

        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        StoreListHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test
    public void GetOrderDetails(){
        OrderContext orderContext = OrderContext.builder()
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder().country(Countries.IN).build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder().
                        country(Countries.IN)
                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                        .franchiseId(106).build()).
                headers(OrderContext.Headers.builder().client(Client.DESKTOP).build()).build();

        PosContext posContext = PosContext.builder().
                incrementId(1930753846).build();

        AuthenticationHelper.builder()
                .orderContext(orderContext).
                build().
                test();

        OrderDetailsHelper.builder().
                orderContext(orderContext).
                posContext(posContext).
                build().
                test();
    }


    @Test
    public void GetOrderSummaryList(){
        OrderContext orderContext = OrderContext.builder()
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder().country(Countries.IN).build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder().
                        country(Countries.IN)
                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                        .franchiseId(106).build()).
                headers(OrderContext.Headers.builder().client(Client.DESKTOP).build()).build();

        PosContext posContext = PosContext.builder().
                orderSummaryMapper(PosContext.OrderSummaryMapper.builder()
                        .fromDate("2025-06-12")
                        .toDate("2025-06-27")
                        .size(20)
                        .page(0)
                        .franchiseId(106).
                        build()).build();

        AuthenticationHelper.builder()
                .orderContext(orderContext).
                build().
                test();

        OrdersummaryListHelper.builder().
                orderContext(orderContext).
                posContext(posContext).
                build().
                test();
    }

}