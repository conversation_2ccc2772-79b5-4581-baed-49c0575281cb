package com.lenskart.e2e.support;

import com.lenskart.commons.model.*;
import com.lenskart.e2e.helper.nexs.NexsE2EHelper;
import com.lenskart.nexs.helpers.NexsOrderCompletionHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.pos.model.POS;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.SINGLE_VISION;
import static com.lenskart.commons.model.ProductId.AE_EYEGLASSES;

@Slf4j
public class NexSOrderCompletionTest {
    @Test(enabled = true)
    public void nexsOrderCompletion() {
        // Create order context with necessary data
        NexsOrderContext  nexsOrderContext = NexsOrderContext
                .builder()
         //       .shippingId("SNXS2260000004452294")
           .incrementId("1930790757")
                .headers(NexsOrderContext.Headers.builder()
                        .build())
                .build();

        // Execute the order completion flow
        NexsOrderCompletionHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
    }

    @Test
    public void nexsOrderCompletionE2EHelper(){
        OrderContext orderContext =
                OrderContext.builder()
                        .phoneNumber(Countries.AE.getDefaultPhoneNumber())
                        .productLists(List.of(
                                OrderContext.ProductList.builder()
                                        .productId(AE_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .itemType(ItemType.B2B)
                                        .finalState(NexsOrderState.DISPATCHED)
                                        .isPrescriptionRequired(true)
                                        .build()))
                        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                .country(Countries.AE)
                                .pinCode("241868")
                                .build())
                        .isPosOrder(true)
                        .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                .country(Countries.AE)
                                .storeId(POS.AE_PREPROD_STORE.getStoreId())
                                .build())
                        .paymentMethod(PaymentMethod.OFFLINE_CASH)
                        .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                        .build();

        PosOrderCreationHelper posOrderCreationHelper = PosOrderCreationHelper
                .builder()
                .orderContext(orderContext)
                .build();
        posOrderCreationHelper.test();

        log.info("Order ID - {}", orderContext.getOrderId());

        NexsE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

}