package com.lenskart.e2e.pos;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.PosE2EDataProvider;
import com.lenskart.e2e.helper.pos.PosE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;


@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class E2EPosOrderCreationTest {


    @Test(dataProviderClass = PosE2EDataProvider.class, dataProvider = "posOrderContext")
    public void createPosOrder(OrderContext orderContext) {

        PosE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }

}
