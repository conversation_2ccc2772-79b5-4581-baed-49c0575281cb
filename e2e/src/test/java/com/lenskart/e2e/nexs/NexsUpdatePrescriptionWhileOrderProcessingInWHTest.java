package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.UpdatePowerWhileOrderProcessingInWHHelper;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class NexsUpdatePrescriptionWhileOrderProcessingInWHTest {

    @Test(description = "Validating the power update process for the customer once the power has been updated post order placement",
            dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsUpdatePower")
    public void updatePowerOrder(OrderContext orderContext) {

        UpdatePowerWhileOrderProcessingInWHHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

}
