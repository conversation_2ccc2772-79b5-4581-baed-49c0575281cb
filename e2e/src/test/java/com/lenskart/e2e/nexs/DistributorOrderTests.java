package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.picking.DOPickingHelper;
import com.lenskart.nexs.helpers.picking.DoClosePickingHelper;
import com.lenskart.nexs.helpers.state.DispatchHelper;
import com.lenskart.nexs.helpers.state.ManifestHelper;
import com.lenskart.nexs.helpers.state.PrintInvoiceHelper;
import com.lenskart.nexs.helpers.state.PrintShipmentHelper;
import com.lenskart.nexs.helpers.wms.UpdateBoxCountHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.oms.dto.DistributorCustomerDetailsDto;
import com.lenskart.scm.helpers.ordersensi.*;
import com.lenskart.scm.model.ScmOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

@TestCategory(TestCategory.Category.SANITY)
@Slf4j
public class DistributorOrderTests {

    NexsOrderContext nexsOrderContext;
    NexsBaseHelper nexsBaseHelper;
    Map<String, String> headers;
    private Long createdCustomerId;
    int doId;
    String doIncremetId;
    boolean enablePartialPicking = true;
    int numOfItemsToPartialPick = 1;

    private void CreateDOOrder() {
        ScmOrderContext scmOrderContext = ScmOrderContext.builder()
                .facilityCode(headers.get("facility-code"))
                .poNumber(String.valueOf(System.currentTimeMillis()))
                .customerCode("Cu-1750765500454-4423")
                .doType("OTHERS")
                .userName(nexsOrderContext.getUserName())
                .build();

        DoOrderUploadHelper doOrderUploadHelper = DoOrderUploadHelper.builder()
                .headers(headers)
                .scmOrderContext(scmOrderContext)
                .build();
        doOrderUploadHelper.test();
        doId = doOrderUploadHelper.getDoOrderId();
        log.info("Order created with id : {}", doId);

        CreateDOOrderHelper createDOOrderHelper = CreateDOOrderHelper.builder()
                .headers(headers)
                .lastestDoOrderId(String.valueOf(doId))
                .build();
        createDOOrderHelper.test();
        doIncremetId = RestUtils.getValueFromResponse(createDOOrderHelper.getResponse(), "data").toString();
        nexsOrderContext.setIncrementId(doIncremetId);
    }

    @BeforeClass
    public void setup() {
        nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder().build()).build();
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        nexsBaseHelper = NexsBaseHelper.builder().build();
        headers = nexsBaseHelper.getHeaders(nexsOrderContext);
        headers.put("username", nexsOrderContext.getUserName());
        headers.put("status", nexsOrderContext.getDoRejectStatus());
        nexsOrderContext.setDoRejectStatus("REJECTED");
        nexsOrderContext.setDoCancelStatus("CANCELLED");
        nexsOrderContext.setCancellationReason("Test Order");
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "createDOCustomer", priority = 1, enabled = true)
    public void createDistributorOrderCustomer(DistributorCustomerDetailsDto distributorCustomerDetailsDto) {
        log.info("Creating distributor customer with details: {}", distributorCustomerDetailsDto);

        ScmOrderContext scmOrderContext = ScmOrderContext.builder()
                .distributorCustomerDetailsDto(distributorCustomerDetailsDto)
                .build();

        CreateDOCustomerHelper createHelper = CreateDOCustomerHelper
                .builder()
                .headers(headers)
                .scmOrderContext(scmOrderContext)
                .build();
        createHelper.test();

        // Store the created customer ID for later use in update test
        createdCustomerId = scmOrderContext.getDistributorCustomerDetailsDto().getId();
        log.info("Customer created successfully with ID: {}", createdCustomerId);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "createDOCustomer", priority = 2,
            dependsOnMethods = "createDistributorOrderCustomer", enabled = true)
    public void updateDistributorOrderCustomer(DistributorCustomerDetailsDto distributorCustomerDetailsDto) {
        log.info("Updating distributor customer with original details: {}", distributorCustomerDetailsDto);

        // Set the ID from the previously created customer
        distributorCustomerDetailsDto.setId(createdCustomerId);
        log.info("Setting customer ID for update: {}", createdCustomerId);

        // Modify some properties for the update
        distributorCustomerDetailsDto.setCustomerEnabled(false);

        ScmOrderContext scmOrderContext = ScmOrderContext.builder()
                .distributorCustomerDetailsDto(distributorCustomerDetailsDto)
                .build();

        log.info("Updating customer with ID: {}", scmOrderContext.getDistributorCustomerDetailsDto().getId());

        UpdateDOCustomerHelper
                .builder()
                .headers(headers)
                .scmOrderContext(scmOrderContext)
                .build()
                .test();

        log.info("Customer updated successfully");
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "createDOCustomer", priority = 4,
            dependsOnMethods = "createDistributorOrderCustomer", enabled = true)
    public void disableEnableCustomer(DistributorCustomerDetailsDto distributorCustomerDetailsDto) {
        log.info("Updating distributor customer with original details: {}", distributorCustomerDetailsDto);

        // Set the ID from the previously created customer
        distributorCustomerDetailsDto.setId(createdCustomerId);
        log.info("Setting customer ID for update: {}", createdCustomerId);

        // Modify some properties for the update
        distributorCustomerDetailsDto.setCustomerEnabled(false);

        ScmOrderContext scmOrderContext = ScmOrderContext.builder()
                .distributorCustomerDetailsDto(distributorCustomerDetailsDto)
                .build();

        log.info("Updating customer with ID: {}", scmOrderContext.getDistributorCustomerDetailsDto().getId());

        UpdateDOCustomerHelper
                .builder()
                .headers(headers)
                .scmOrderContext(scmOrderContext)
                .build()
                .test();

        log.info("Customer updated successfully");
    }

    @Test(priority = 5)
    public void uploadDOFile() {
        ScmOrderContext scmOrderContext = ScmOrderContext.builder()
                .facilityCode(headers.get("facility-code"))
                .poNumber(String.valueOf(System.currentTimeMillis()))
                .customerCode("Cu-1750765500454-4423")
                .doType("OTHERS")
                .userName(nexsOrderContext.getUserName())
                .build();

        DoOrderUploadHelper doOrderUploadHelper = DoOrderUploadHelper.builder()
                .headers(headers)
                .scmOrderContext(scmOrderContext)
                .build();
        doOrderUploadHelper.test();
        doId = doOrderUploadHelper.getDoOrderId();
        log.info("Order created with id: {}", doId);
    }

    @Test(priority = 6, dependsOnMethods = "uploadDOFile")
    public void createDoOrder() {
        CreateDOOrderHelper createDOOrderHelper = CreateDOOrderHelper.builder()
                .headers(headers)
                .lastestDoOrderId(String.valueOf(doId))
                .build();
        createDOOrderHelper.test();
        doIncremetId = RestUtils.getValueFromResponse(createDOOrderHelper.getResponse(), "data").toString();
        nexsOrderContext.setIncrementId(doIncremetId);
    }


    @Test(priority = 7, dependsOnMethods = "createDoOrder")
    public void approveDO() {
        AwaitUtils.sleepSeconds(10);
        ApproveDOHelper.builder()
                .headers(headers)
                .orderId(String.valueOf(doId))
                .build()
                .test();
        log.info("Approved do order db ID is " + doId);
    }

    @Test(priority = 8, dependsOnMethods = "approveDO")
    public void doPicking() {
        AwaitUtils.sleepSeconds(10);
        nexsOrderContext.setPartialPicking(enablePartialPicking);
        nexsOrderContext.setNumOfItemsToPartialPick(numOfItemsToPartialPick);
        log.info("enablePartialPicking value is: {}, Items to Pick: {}",
                nexsOrderContext.isPartialPicking(), nexsOrderContext.getNumOfItemsToPartialPick());

        DOPickingHelper doPickingHelper = DOPickingHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build();
        doPickingHelper.test();
        nexsOrderContext.setBarcodes(doPickingHelper.getDoPickedBarcodes());
        log.info("Picking completed for shipping_package_id:" + nexsOrderContext.getShippingId());
        if (enablePartialPicking) {
            DoClosePickingHelper.builder()
                    .nexsOrderContext(nexsOrderContext)
                    .headers(headers)
                    .build()
                    .test();
        }
        log.info("Closed picking summary for :" + nexsOrderContext.getShippingId());
    }

    @Test(priority = 9, dependsOnMethods = "doPicking")
    public void printInvoice() {
        PrintInvoiceHelper newPrintInvoiceHelper = PrintInvoiceHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build();
        newPrintInvoiceHelper.test();
        log.info("Do invoice pushing to kafka");
        newPrintInvoiceHelper.test();
    }

    @Test(priority = 10, dependsOnMethods = "printInvoice")
    public void updateBoxCount() {
        UpdateBoxCountHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
    }

    @Test(priority = 11, dependsOnMethods = "updateBoxCount")
    public void printShipment() {
        PrintShipmentHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
    }

    @Test(priority = 12, dependsOnMethods = "printShipment")
    public void manifestScan() {
        ManifestHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
    }

    @Test(priority = 13, dependsOnMethods = "manifestScan")
    public void manifestClose() {
        DispatchHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
    }

    @Test(priority = 14)
    public void getDoOrderList() {
        DoOrderListHelper.builder()
                .headers(headers)
                .build()
                .test();
        log.info("Do Orders list which are in created State");
    }

    @Test(priority = 15)
    public void rejectDO() {
        CreateDOOrder();
        headers.put("status", nexsOrderContext.getDoRejectStatus());
        headers.put("cancellationreason", nexsOrderContext.getCancellationReason());
        AwaitUtils.sleepSeconds(10);
        DORejectHelper.builder()
                .headers(headers)
                .orderId(String.valueOf(doId))
                .build()
                .test();
        log.info("Rejected do order db ID is {}", doId);
    }

    @Test(priority = 16)
    public void cancelDO() {
        CreateDOOrder();
        headers.put("status", nexsOrderContext.getDoCancelStatus());
        headers.put("cancellationreason", nexsOrderContext.getCancellationReason());
        AwaitUtils.sleepSeconds(10);
        DORejectHelper.builder()
                .headers(headers)
                .orderId(String.valueOf(doId))
                .build()
                .test();
        log.info("Cancelled do order db ID is " + doId);
    }

    @Test(priority = 17, dependsOnMethods = "manifestClose")
    public void returnDO() {
        AwaitUtils.sleepSeconds(10);
        ScmOrderContext scmOrderContext = ScmOrderContext.builder().build();
        List<String> getDoOrderBacodeList = nexsOrderContext.getBarcodes();
        String barcode = getDoOrderBacodeList.getFirst();
        ReturnDOHelper returnDOHelper = ReturnDOHelper.builder()
                .headers(headers)
                .scmOrderContext(scmOrderContext)
                .barcode(barcode)
                .build();
        returnDOHelper.test();
        log.info("Returned do item is {}", scmOrderContext.getScannedBarcode());
    }

    @Test(priority = 18, dependsOnMethods = "manifestClose")
    public void reverseDO() {
        AwaitUtils.sleepSeconds(10);
        ScmOrderContext scmOrderContext = ScmOrderContext.builder().build();
        List<String> getDoOrderBacodeList = nexsOrderContext.getBarcodes();
        String barcode = getDoOrderBacodeList.getLast();
        ReverseDOHelper reverseDOHelper = ReverseDOHelper.builder()
                .headers(headers)
                .scmOrderContext(scmOrderContext)
                .barcode(barcode)
                .build();
        reverseDOHelper.test();
        log.info("Reversed do item is {}", scmOrderContext.getScannedBarcode());
    }
}



