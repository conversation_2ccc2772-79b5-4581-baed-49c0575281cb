package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.NexsAutoOrderCompletionHelper;
import org.testng.annotations.Test;

import java.util.List;
@TestCategory(TestCategory.Category.SANITY)
public class NexsManulJItOrderProcessingTest {

    @Test(enabled = false, dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsOrderManualJit",description = "Process and dispatch the manual jit order from  Biwadi warehouse")
    public void biwadiManualJitOrderProcess(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setIsManulJitOrderProcess(true);
        NexsAutoOrderCompletionHelper autoJitHelper = NexsAutoOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .build();
        orderContext.setProductLists(productLists);
        autoJitHelper.test();

    }
    @Test(enabled = false, dataProviderClass = NexsE2EDataProvider.class, dataProvider = "singaporeManualJitOrderProcess",description = "Process and dispatch the manual jit order from Singapore warehouse")
    public void singaporeManualJitOrderProcess(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setIsManulJitOrderProcess(true);
        NexsAutoOrderCompletionHelper autoJitHelper = NexsAutoOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .build();
        orderContext.setProductLists(productLists);
        autoJitHelper.test();

    }
    @Test(enabled = false, dataProviderClass = NexsE2EDataProvider.class, dataProvider = "dubaiManualJitOrderProcess",description = "Process and dispatch the manual jit order from Dubai warehouse")
    public void uaeManualJitOrderProcess(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setIsManulJitOrderProcess(true);
        NexsAutoOrderCompletionHelper autoJitHelper = NexsAutoOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .build();
        orderContext.setProductLists(productLists);
        autoJitHelper.test();

    }
    @Test(enabled = false, dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsOrderManualJit",description = "Process and dispatch the manual jit order from Manesar warehouse")
    public void manaserManualJitOrderProcess(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setIsManulJitOrderProcess(true);
        NexsAutoOrderCompletionHelper autoJitHelper = NexsAutoOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .build();
        orderContext.setProductLists(productLists);
        autoJitHelper.test();

    }
    // qc fail cases
    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsOrderManualJit",description = "Process and dispatch the manual jit order from  Biwadi warehouse")
    public void biwadiManualJitQcFailOrderProcess(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setIsManulJitOrderProcess(true);
        NexsAutoOrderCompletionHelper autoJitHelper = NexsAutoOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .isManulJitQcFail(true)
                .build();
        orderContext.setProductLists(productLists);
        autoJitHelper.test();

    }
    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "singaporeManualJitOrderProcess",description = "Process and dispatch the manual jit order from Singapore warehouse")
    public void singaporeManualJitQcFailOrderProcess(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setIsManulJitOrderProcess(true);
        NexsAutoOrderCompletionHelper autoJitHelper = NexsAutoOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .isManulJitQcFail(true)
                .build();
        orderContext.setProductLists(productLists);
        autoJitHelper.test();

    }
    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "dubaiManualJitOrderProcess",description = "Process and dispatch the manual jit order from Dubai warehouse")
    public void uaeManualJitQcFailOrderProcess(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setIsManulJitOrderProcess(true);
        NexsAutoOrderCompletionHelper autoJitHelper = NexsAutoOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .isManulJitQcFail(true)
                .build();
        orderContext.setProductLists(productLists);
        autoJitHelper.test();

    }
    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsOrderManualJit",description = "Process and dispatch the manual jit order from Manesar warehouse")
    public void manaserManualJitQcFailOrderProcess(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setIsManulJitOrderProcess(true);
        NexsAutoOrderCompletionHelper autoJitHelper = NexsAutoOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .isManulJitQcFail(true)
                .build();
        orderContext.setProductLists(productLists);
        autoJitHelper.test();

    }
}
