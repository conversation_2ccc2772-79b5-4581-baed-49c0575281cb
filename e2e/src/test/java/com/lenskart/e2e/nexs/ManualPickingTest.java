package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.*;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class ManualPickingTest {

    @Test(description = "hhd frame/sunglass picking",
            dataProviderClass = NexsE2EDataProvider.class, dataProvider = "picking")
    public void hhdFramePicking(OrderContext orderContext) {
        ManualPickingHelper.builder().orderContext(orderContext).build().test();
    }

    @Test(description = "hhd item skip",
            dataProviderClass = NexsE2EDataProvider.class, dataProvider = "picking")
    public void skipItem(OrderContext orderContext) {
        PickingSkipHelper.builder().orderContext(orderContext).build().test();
    }

    @Test(description = "supervisor item  skip",
            dataProviderClass = NexsE2EDataProvider.class, dataProvider = "pickingSG")
    public void supervisorSKip(OrderContext orderContext) {
        SupervisorSkipHelper.builder().orderContext(orderContext).build().test();
    }

    @Test(description = "supervisor item picking",
            dataProviderClass = NexsE2EDataProvider.class, dataProvider = "pickingSG")
    public void supervisorPicking(OrderContext orderContext) {
        SupervisorPickingHelper.builder().orderContext(orderContext).build().test();
    }

    @Test(description = "lens picking",
            dataProviderClass = NexsE2EDataProvider.class, dataProvider = "picking")
    public void hddLensPicking(OrderContext orderContext) {
        LensPickingHelper.builder().orderContext(orderContext).build().test();
    }
}
