package com.lenskart.e2e.nexs;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import org.testng.annotations.Test;

import java.util.List;

import static com.lenskart.e2e.nexs.NexsOrderProcessingTests.nexsOrderCompletion;

public class WarehouseSimulationTests {

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "FrameOnlySG")
    public void processSingaporeOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "FrameOnlyAE")
    public void processUAEOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "eyeglassFrameOnly")
    public void processIndiaOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        nexsOrderCompletion(orderContext);
    }
}
