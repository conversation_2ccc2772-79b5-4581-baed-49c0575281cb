package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.NexsE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class NexsOrderProcessingTests {

    public static void nexsOrderCompletion(OrderContext orderContext) {
        NexsE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "eyeglassWithPower", description = "Process and dispatch the Eyeglasses with power order from the warehouse")
    public void processEyeglassesWithPowerOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        nexsOrderCompletion(orderContext);
    }

    //retryAnalyzer = Retry.class
    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "sunglassesWithPower", description = "Process and dispatch the Sunglasses with power order from the warehouse")
    public void processSunglassesWithPowerOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "eyeglassFrameOnly", description = "Process and dispatch the Eyeglass frame only order from the warehouse")
    public void processEyeglassFrameOnlyOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "sunglasses", description = "Process and dispatch the Sunglasses order from the warehouse")
    public void processSunglassesOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "accessories", description = "Process and dispatch the Accessories order from the warehouse")
    public void processAndDispatchAccessoriesOrderFromWarehouse(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "contactLens", description = "Process and dispatch the Contact Lens order from the warehouse")
    public void processAndDispatchContactLensOrderFromWarehouse(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "contactLensZeroPower", description = "Process and dispatch the CL w/o power from the warehouse")
    public void processAndDispatchContactLensWithoutPowerOrderFromWarehouse(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        nexsOrderCompletion(orderContext);
    }

    @Test(enabled = false, dataProviderClass = NexsE2EDataProvider.class, dataProvider = "fr0_Loyalty", description = "Verify the amount in shipment label for loyalty items with cash on delivery has a payment method")
    public void verifyShipmentLabelForLoyaltyItemsCOD(OrderContext orderContext) {
        NexsE2EHelper nexsE2EHelper = NexsE2EHelper.builder()
                .orderContext(orderContext)
                .build();

        nexsE2EHelper.test();
        Assert.assertEquals(orderContext.getFinalOrderAmount(), nexsE2EHelper.getNexsOrderContext().getCollectibleAmount());
    }

//    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "eyeglassWithPowerSingeVision")
//    public void DispatchEyeglassWithSingleVisionOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
//        orderContext.setProductLists(productLists);
//        nexsOrderCompletion(orderContext);
//    }


    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "storeEyeglassZeroPowerOrder")
    public void storeEyeglassOrder(OrderContext orderContext) {
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "storeSunglassesWithPowerOrder")
    public void storeSunglassesOrder(OrderContext orderContext) {
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "storeSingleVisionOrder")
    public void storeSingleVisionOrder(OrderContext orderContext) {
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "storeContactLensOrder")
    public void storeContactLensOrder(OrderContext orderContext) {
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "storeOtcOrder")
    public void cocoStoreOtcOrder(OrderContext orderContext) {
        nexsOrderCompletion(orderContext);
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "storeBulkOrder")
    public void storeBulkOrder(OrderContext orderContext) {
        nexsOrderCompletion(orderContext);
    }

    @Test(enabled = true, dataProviderClass = NexsE2EDataProvider.class, dataProvider = "fofoOtcOrder")
    public void fofoStoreOtcOrder(OrderContext orderContext) {
        nexsOrderCompletion(orderContext);
    }


}
