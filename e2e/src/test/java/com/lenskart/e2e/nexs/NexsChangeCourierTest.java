package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.NexsChangeCourierHelper;
import org.testng.annotations.Test;

import java.util.List;

@TestCategory(TestCategory.Category.SANITY)
public class NexsChangeCourierTest {

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsOrderAutoJit")
    public void processChangeCourierTestCase(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        NexsChangeCourierHelper nexsChangeCourierHelper = NexsChangeCourierHelper.builder()
                .orderContext(orderContext)
                .build();
        orderContext.setProductLists(productLists);
        nexsChangeCourierHelper.test();

    }
}