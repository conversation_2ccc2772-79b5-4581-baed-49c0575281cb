package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.NexsE2EHelper;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.helpers.consolidation.AllocateItemHelper;
import com.lenskart.nexs.helpers.consolidation.OrderFlushHelper;
import com.lenskart.nexs.helpers.consolidation.ScanItemHelper;
import com.lenskart.nexs.helpers.consolidation.ScanItemInPackingHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.util.OrderAdopterUtil;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.e2e.nexs.NexsOrderProcessingTests.nexsOrderCompletion;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class ConsolidationTests {

    NexsOrderContext nexsOrderContext;
    PosOrderCreationHelper posOrderCreationHelper;

    @Test(priority = 0, dataProviderClass = NexsE2EDataProvider.class, dataProvider = "storeEyeglassOrderForConsolidation")
    public void storeEyeglassOrder(OrderContext orderContext) {

                posOrderCreationHelper = PosOrderCreationHelper
                        .builder()
                        .orderContext(orderContext)
                        .build();
                posOrderCreationHelper.test();

            /* Scm helper to move the order to scm states */
            OrderAdopterUtil.syncOrder(String.valueOf(orderContext.getOrderId()));

            nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder().build())
                    .incrementId(String.valueOf(orderContext.getOrderId()))
                    .isConsolidationAndPackingRequired(false)
                    .build();

            NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                    .orderContext(orderContext)
                    .nexsOrderContext(nexsOrderContext)
                    .sequentialTransition(true)
                    .build();

            stateManager.test();
    }

    @Test(priority = 1, dependsOnMethods = {"storeEyeglassOrder"})
    public void consolidationScanFlow() {
        if (nexsOrderContext.getFacilityCode().equals(Constants.BHIWADI_WAREHOUSE_FACILITY) ||
                nexsOrderContext.getFacilityCode().equals(Constants.UAE_WAREHOUSE_FACILITY)) {

            ScanItemHelper scanItemHelper = ScanItemHelper.builder()
                    .nexsOrderContext(nexsOrderContext)
                    .build();
            scanItemHelper.test();

            // Extract box code from scan response if available
            if (scanItemHelper.getResponse() != null) {
                try {
                    String boxCode = RestUtils.getValueFromResponse(scanItemHelper.getResponse(), "data.boxBarCode").toString();
                    if (boxCode != null && !boxCode.isEmpty()) {
                        nexsOrderContext.setBoxCode(boxCode);
                        log.info("Box code from scan API: {}", boxCode);
                    }
                } catch (Exception e) {
                    log.debug("No box code found in scan response, will generate one if needed");
                }
            }

            // Step 2: Allocate Item (if needed)
            AllocateItemHelper.builder()
                    .nexsOrderContext(nexsOrderContext)
                    .build()
                    .test();
        }
    }

    @Test(priority = 2, dependsOnMethods = {"consolidationScanFlow"})
    public void consolidationPackingFlow() {
        // Step 3: Scan Item in Packing
        ScanItemInPackingHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
    }

    @Test(priority = 3, dependsOnMethods = {"consolidationPackingFlow"})
    public void orderFlushFlow() {
        // Step 4: Order Flush
        OrderFlushHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
    }

}
