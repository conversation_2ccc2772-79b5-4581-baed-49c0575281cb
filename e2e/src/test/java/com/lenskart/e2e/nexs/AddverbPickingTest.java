package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.AddverbE2EHelper;
import com.lenskart.e2e.helper.nexs.MoveAddverbOrderToManualHelper;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class AddverbPickingTest {

    @Test(description = "Move order out of Addverb", dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsOrderReassignmnet")
    public void moveAddverbOrderToManual(OrderContext orderContext) {
        MoveAddverbOrderToManualHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(description = "Addverb E2E", dataProviderClass = NexsE2EDataProvider.class, dataProvider = "addverb")
    public void addverbPickingE2E(OrderContext orderContext) {
        AddverbE2EHelper.builder().orderContext(orderContext).build().test();
    }
}
