package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.NexsAutoOrderCompletionHelper;
import org.testng.annotations.Test;

import java.util.List;

@TestCategory(TestCategory.Category.SANITY)
public class NexsAutoJitOrderProcessingTest {

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsOrderAutoJit")
    public void processAutoJitOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        NexsAutoOrderCompletionHelper autoJitHelper = NexsAutoOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .build();
        orderContext.setProductLists(productLists);
        autoJitHelper.test();
    }
    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsOrderAutoJit")
    public void processAutoJitExternalQCFailOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        NexsAutoOrderCompletionHelper autoJitHelper = NexsAutoOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .isManulJitQcFail(true)
                .build();
        orderContext.setProductLists(productLists);
        autoJitHelper.test();
    }
}