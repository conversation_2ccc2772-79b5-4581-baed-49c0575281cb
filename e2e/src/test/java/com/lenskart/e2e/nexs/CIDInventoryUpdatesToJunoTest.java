package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.juno.database.JunoMongoDbUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.ims.AddGAAInventoryHelper;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.List;

import static com.lenskart.commons.model.ProductId.IN_CONTACT_LENS;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class CIDInventoryUpdatesToJunoTest {


    @Test
    public void cidInventoryUpdatesToJunoTest() {
        int quantity;
        List<Document> data = JunoMongoDbUtils.getQuantityFromActiveInventoryForPid(Integer.parseInt(IN_CONTACT_LENS.getProductId()), Constants.CONTACTS_CART_FACILITY);
        if (data.isEmpty())
            quantity = 0;
        else
            quantity = data.getFirst().getInteger("quantity");
        log.info("Quantity before adding inventory: {}", quantity);
        AddGAAInventoryHelper.builder()
                .pid(Integer.parseInt(IN_CONTACT_LENS.getProductId()))
                .location(Constants.TEST_LOCATION)
                .facility(Constants.CONTACTS_CART_FACILITY)
                .legalOwner(Constants.IN_LEGAL_OWNER)
                .build()
                .test();
        data = JunoMongoDbUtils.getQuantityFromActiveInventoryForPid(Integer.parseInt(IN_CONTACT_LENS.getProductId()), Constants.CONTACTS_CART_FACILITY);
        int quantityAfterAddingInventory = data.getFirst().getInteger("quantity");
        Assert.assertEquals(quantityAfterAddingInventory, quantity + 1);
    }
}
