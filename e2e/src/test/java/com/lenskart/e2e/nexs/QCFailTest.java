package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.QCFailHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class QCFailTest {

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "qcFail")
    public void FR0OrderQCFailFlow(OrderContext orderContext) {
        NexsOrderContext holdContext = NexsOrderContext.builder()
                .build();

        QCFailHelper qcFailHelper = QCFailHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(holdContext).build();
        qcFailHelper.test();
    }
    
    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "FR1QcFail")
    public void FR1OrderQcFailFlow(OrderContext orderContext) {
        NexsOrderContext holdContext = NexsOrderContext.builder()
                .build();

        QCFailHelper qcFailHelper = QCFailHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(holdContext).build();
        qcFailHelper.test();

    }
//jit
    // power change rreguar anc jit

}