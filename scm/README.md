# SCM Module

## Overview

The **SCM (Supply Chain Management) Module** provides automation testing capabilities for Lenskart's supply chain operations, including order cancellation, quality control eligibility, order adoption, and supply chain workflow management.

## 🏗️ **Architecture**

### **Core Components**

- **Order Cancellation Management**: Handle order cancellation workflows
- **Quality Control Operations**: QC eligibility and validation
- **Order Adoption**: Supply chain order adoption processes
- **Database Operations**: Direct database queries for supply chain data
- **Supply Chain Analytics**: Order processing and fulfillment analytics

## 📁 **Package Structure**

```
com.lenskart.scm/
├── api/                 # SCM service API interfaces
├── config/              # SCM-specific configuration management
├── constants/           # SCM-specific constants and enums
├── database/            # Database queries and operations
├── endpoints/           # SCM API endpoints and endpoint manager
├── exceptions/          # SCM-specific exception states
├── helpers/             # Service helper classes for SCM operations
├── model/               # SCM-specific data models
├── requestbuilders/     # Request builders for SCM API calls
└── util/                # SCM-specific utility classes
```

## 🔧 **Key Features**

### **1. Order Cancellation Management**

#### **Cancellation Operations**
```java
CancellationHelper cancellationHelper = new CancellationHelper();

// Cancel order with reason
CancellationRequest request = CancellationRequest.builder()
    .orderId("ORD123456")
    .reason("Customer requested cancellation")
    .cancelledBy("CUSTOMER")
    .build();

boolean cancelled = cancellationHelper.cancelOrder(request);

// Get cancellation status
CancellationStatus status = cancellationHelper.getCancellationStatus("ORD123456");

// Validate cancellation eligibility
boolean canCancel = cancellationHelper.canCancelOrder("ORD123456");
```

### **2. Quality Control Operations**

#### **QC Eligibility**
```java
IsEligibleForQcHelper qcHelper = new IsEligibleForQcHelper();

// Check QC eligibility
boolean isEligible = qcHelper.isEligibleForQc("ORD123456");

// Get QC requirements
QcRequirements requirements = qcHelper.getQcRequirements("ORD123456");

// Validate QC criteria
boolean meetsQcCriteria = qcHelper.validateQcCriteria("ORD123456", qcCriteria);

// Get QC status
QcStatus qcStatus = qcHelper.getQcStatus("ORD123456");
```

### **3. Database Operations**

#### **SCM Database Queries**
```java
ScmQueries scmQueries = new ScmQueries();

// Get order details from database
Map<String, Object> orderData = scmQueries.getOrderDetails("ORD123456");

// Get supply chain status
String scmStatus = scmQueries.getSupplyChainStatus("ORD123456");

// Get fulfillment center information
Map<String, Object> fcInfo = scmQueries.getFulfillmentCenterInfo("FC001");

// Get inventory status
List<Map<String, Object>> inventory = scmQueries.getInventoryStatus("PROD123");
```

### **4. Order Adoption**

#### **Order Adoption Utilities**
```java
OrderAdopterUtil adopterUtil = new OrderAdopterUtil();

// Adopt order to fulfillment center
boolean adopted = adopterUtil.adoptOrder("ORD123456", "FC001");

// Get adoption status
AdoptionStatus status = adopterUtil.getAdoptionStatus("ORD123456");

// Validate adoption eligibility
boolean canAdopt = adopterUtil.canAdoptOrder("ORD123456", "FC001");

// Get available fulfillment centers
List<String> availableFCs = adopterUtil.getAvailableFulfillmentCenters("PROD123");
```

## 🌐 **API Endpoints**

### **SCM Endpoints**
```java
public enum ScmEndpoints implements BaseEndpoint {
    // Order Cancellation
    CANCEL_ORDER("/api/v1/orders/{orderId}/cancel", "scmService", "POST", 
                "Cancel order in supply chain"),
    GET_CANCELLATION_STATUS("/api/v1/orders/{orderId}/cancellation", "scmService", "GET", 
                           "Get order cancellation status"),
    
    // Quality Control
    CHECK_QC_ELIGIBILITY("/api/v1/orders/{orderId}/qc/eligibility", "scmService", "GET", 
                        "Check QC eligibility for order"),
    GET_QC_STATUS("/api/v1/orders/{orderId}/qc/status", "scmService", "GET", 
                 "Get QC status for order"),
    
    // Order Adoption
    ADOPT_ORDER("/api/v1/orders/{orderId}/adopt", "scmService", "POST", 
               "Adopt order to fulfillment center"),
    GET_ADOPTION_STATUS("/api/v1/orders/{orderId}/adoption", "scmService", "GET", 
                       "Get order adoption status"),
    
    // Supply Chain Analytics
    GET_FULFILLMENT_STATUS("/api/v1/orders/{orderId}/fulfillment", "scmService", "GET", 
                          "Get order fulfillment status"),
    GET_INVENTORY_STATUS("/api/v1/inventory/{productId}", "scmService", "GET", 
                        "Get product inventory status");
}
```

### **URL Generation**
```java
// URL with path parameters
String url = ScmEndpoints.CANCEL_ORDER.getUrl(
    Map.of("orderId", "ORD123456")
);

// Using endpoint manager
String url = ScmEndpointManager.getEndpointUrl(
    ScmEndpoints.CHECK_QC_ELIGIBILITY,
    Map.of("orderId", "ORD123456")
);
```

## 🔧 **Configuration**

### **scm.yml Configuration**
```yaml
scm:
  baseUrls:
    scmService: https://scm.preprod.lenskart.com
    
  database:
    scmDatabase: scm_preprod
    inventoryDatabase: inventory_preprod
    fulfillmentDatabase: fulfillment_preprod
    
  fulfillmentCenters:
    - id: "FC001"
      name: "Mumbai FC"
      location: "Mumbai, India"
      capacity: 10000
    - id: "FC002"
      name: "Delhi FC"
      location: "Delhi, India"
      capacity: 8000
    - id: "FC003"
      name: "Bangalore FC"
      location: "Bangalore, India"
      capacity: 12000
      
  qcCriteria:
    enableQcForPrescription: true
    enableQcForHighValue: true
    highValueThreshold: 10000
    qcMandatoryCategories:
      - "PRESCRIPTION_GLASSES"
      - "CONTACT_LENSES"
      
  cancellation:
    allowCancellationStates:
      - "CREATED"
      - "PROCESSING"
      - "IN_PICKING"
    cancellationReasons:
      - "CUSTOMER_REQUEST"
      - "INVENTORY_UNAVAILABLE"
      - "QUALITY_ISSUE"
      - "DELIVERY_ISSUE"
      
  timeouts:
    apiTimeout: 30000
    databaseTimeout: 15000
    adoptionTimeout: 60000
```

### **Configuration Usage**
```java
// Get SCM configuration
ScmConfig config = ScmConfigLoader.loadConfig();

// Get fulfillment centers
List<FulfillmentCenter> fcs = config.getFulfillmentCenters();

// Get QC criteria
QcCriteria qcCriteria = config.getQcCriteria();

// Get cancellation settings
CancellationSettings settings = config.getCancellationSettings();
```

## 🧪 **Testing**

### **Test Categories**

#### **Sanity Tests**
```java
@Test
@TestCategory(TestCategory.Category.SANITY)
public void testOrderCancellation() {
    CancellationHelper helper = new CancellationHelper();
    
    CancellationRequest request = CancellationRequest.builder()
        .orderId("ORD123456")
        .reason("CUSTOMER_REQUEST")
        .build();
        
    boolean cancelled = helper.cancelOrder(request);
    assert cancelled;
}

@Test
@TestCategory(TestCategory.Category.SANITY)
public void testQcEligibility() {
    IsEligibleForQcHelper helper = new IsEligibleForQcHelper();
    boolean eligible = helper.isEligibleForQc("ORD123456");
    // Assert based on order characteristics
}
```

#### **Regression Tests**
```java
@Test
@TestCategory(TestCategory.Category.REGRESSION)
public void testSupplyChainWorkflow() {
    // Test complete supply chain workflow
    OrderAdopterUtil adopterUtil = new OrderAdopterUtil();
    IsEligibleForQcHelper qcHelper = new IsEligibleForQcHelper();
    
    String orderId = "ORD123456";
    
    // 1. Check order adoption
    boolean canAdopt = adopterUtil.canAdoptOrder(orderId, "FC001");
    if (canAdopt) {
        boolean adopted = adopterUtil.adoptOrder(orderId, "FC001");
        assert adopted;
    }
    
    // 2. Check QC eligibility
    boolean qcEligible = qcHelper.isEligibleForQc(orderId);
    if (qcEligible) {
        QcStatus qcStatus = qcHelper.getQcStatus(orderId);
        assert qcStatus != null;
    }
}
```

### **Running SCM Tests**
```bash
# Run all SCM tests
mvn test -pl scm

# Run specific test categories
mvn test -pl scm -DtestCategory=SANITY

# Run with specific environment
mvn test -pl scm -Denvironment=preprod

# Run specific test class
mvn test -pl scm -Dtest=CancellationHelperTest
```

## 📊 **Data Models**

### **SCM-Specific Models**
```java
// Cancellation Request Model
@Data
@Builder
public class CancellationRequest {
    private String orderId;
    private String reason;
    private String cancelledBy;
    private String comments;
    private LocalDateTime requestedAt;
}

// QC Requirements Model
@Data
public class QcRequirements {
    private String orderId;
    private boolean qcRequired;
    private List<String> qcChecks;
    private String qcCenter;
    private int estimatedQcTime;
}

// Fulfillment Center Model
@Data
public class FulfillmentCenter {
    private String id;
    private String name;
    private String location;
    private int capacity;
    private List<String> supportedCategories;
    private boolean active;
}
```

## 🛠️ **Request Builders**

### **SCM Request Builder**
```java
SCMRequestBuilders builder = new SCMRequestBuilders();

// Build cancellation request
Map<String, Object> cancellationRequest = builder
    .withOrderId("ORD123456")
    .withReason("CUSTOMER_REQUEST")
    .withCancelledBy("CUSTOMER")
    .buildCancellationRequest();

// Build adoption request
Map<String, Object> adoptionRequest = builder
    .withOrderId("ORD123456")
    .withFulfillmentCenter("FC001")
    .withPriority("HIGH")
    .buildAdoptionRequest();
```

## 🗄️ **Database Integration**

### **SCM Database Queries**
```java
ScmQueries queries = new ScmQueries();

// Order-related queries
String orderStatus = queries.getOrderStatus("ORD123456");
Map<String, Object> orderDetails = queries.getOrderDetails("ORD123456");

// Inventory queries
int availableStock = queries.getAvailableStock("PROD123", "FC001");
List<String> stockLocations = queries.getStockLocations("PROD123");

// Fulfillment queries
String assignedFC = queries.getAssignedFulfillmentCenter("ORD123456");
List<String> availableFCs = queries.getAvailableFulfillmentCenters("PROD123");
```

## 🔍 **Exception Handling**

### **SCM Exception States**
```java
public enum ScmExceptionStates {
    ORDER_NOT_FOUND("SCM_001", "Order not found in supply chain"),
    CANCELLATION_NOT_ALLOWED("SCM_002", "Order cancellation not allowed in current state"),
    QC_NOT_REQUIRED("SCM_003", "Quality control not required for this order"),
    ADOPTION_FAILED("SCM_004", "Order adoption to fulfillment center failed"),
    INVENTORY_UNAVAILABLE("SCM_005", "Product inventory not available"),
    FC_NOT_AVAILABLE("SCM_006", "Fulfillment center not available");
}
```

## 📈 **Performance Features**

- **Database Connection Pooling**: Efficient database connection management
- **Query Optimization**: Optimized database queries for performance
- **Async Operations**: Support for asynchronous supply chain operations
- **Caching**: Cache frequently accessed supply chain data
- **Batch Processing**: Support for batch operations on multiple orders

## 🔗 **Integration Points**

### **With Other Modules**
- **Commons**: Uses shared database connections and utilities
- **Juno**: Receives order data for supply chain processing
- **NEXS**: Integrates for order fulfillment and state management
- **Cosmos**: Provides supply chain state updates

### **External Services**
- **Inventory Management System**: Real-time inventory updates
- **Fulfillment Centers**: Integration with FC management systems
- **Quality Control System**: QC workflow integration
- **Analytics Service**: Supply chain performance analytics

## 🚀 **Getting Started**

### **1. Add SCM Dependency**
```xml
<dependency>
    <groupId>com.lenskart</groupId>
    <artifactId>scm</artifactId>
    <version>1.0.0</version>
</dependency>
```

### **2. Configure SCM Service**
Update `scm.yml` with your SCM service and database configuration.

### **3. Create Your First Test**
```java
public class MyScmTest {
    @Test
    @TestCategory(TestCategory.Category.SANITY)
    public void testOrderCancellation() {
        CancellationHelper helper = new CancellationHelper();
        boolean canCancel = helper.canCancelOrder("ORD123456");
        assert canCancel != null; // Based on order state
    }
}
```

## 📚 **Examples**

Check the test classes for comprehensive examples:
- **Order Cancellation**: Complete cancellation workflow
- **QC Operations**: Quality control eligibility and processing
- **Order Adoption**: Fulfillment center adoption processes
- **Database Operations**: Direct database queries and operations
- **Supply Chain Analytics**: Performance and status monitoring

The SCM module provides comprehensive automation capabilities for testing Lenskart's supply chain management operations, ensuring efficient order processing, quality control, and fulfillment workflows.
