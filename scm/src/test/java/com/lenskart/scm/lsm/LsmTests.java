package com.lenskart.scm.lsm;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.scm.helpers.lsm.LsmServiceabilityHelper;
import com.lenskart.scm.model.ScmOrderContext;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class LsmTests {

    @Test(description = "Verify the serviceability of the shipment using LSM Serviceability api")
    public void verifyLsmServiceability() {
        ScmOrderContext scmOrderContext = ScmOrderContext.builder().pincode("122002").isValidationRequired(true).build();
        ScmOrderContext.LsmServicibility lsmServicibility = ScmOrderContext.LsmServicibility
                .builder()
                .is_enabled(true)
                .facility_code("NXS2")
                .build();

        LsmServiceabilityHelper.builder()
                .pincode(scmOrderContext.getPincode())
                .country("IN")
                .scmOrderContext(scmOrderContext)
                .lsmServicibility(lsmServicibility)
                .build()
                .test();
    }
}
