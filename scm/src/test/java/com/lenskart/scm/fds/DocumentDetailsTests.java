package com.lenskart.scm.fds;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.helpers.fds.DocumentDetailsHelper;
import com.lenskart.scm.model.ScmOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class DocumentDetailsTests {
    ScmOrderContext scmOrderContext;

    @Test(description = "validate document details")
    public void documentDetails() {
        scmOrderContext = ScmOrderContext.builder().isValidationRequired(true).build();
        if(scmOrderContext.getShippingId()==null){
            List<Map<String, Object>> dbValuesDispatchedShipment = ScmDbUtils.getDispatchedShipment();
            scmOrderContext.setShippingId(dbValuesDispatchedShipment.getFirst().get("shipping_package_id").toString());
            scmOrderContext = ScmOrderContext.builder().shippingId(scmOrderContext.getShippingId()).isValidationRequired(true).build();
        }
        log.info("Shipping id is {}", scmOrderContext.getShippingId());
        DocumentDetailsHelper.builder()
                .scmOrderContext(scmOrderContext)
                .build()
                .test();
    }

    @Test(description = "verify if the line item for shipping charges are present in invoice")
    public void verifyShippingChargesInInvoice() {
        scmOrderContext = ScmOrderContext.builder().isValidationRequired(true).build();
        ScmDbUtils.getShippingIdForShippingCharges().forEach(item -> {
            scmOrderContext.setShippingId(item.get("shipping_package_id").toString());
            scmOrderContext.setShippingCharges(Float.parseFloat(item.get("shipping_charges").toString()));
        });
        DocumentDetailsHelper.builder()
                .scmOrderContext(scmOrderContext)
                .build()
                .test();
    }
}
