package com.lenskart.scm.requestbuilders;

import com.lenskart.oms.dto.DistributorCustomerDetailsDto;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.model.ordersensi.GetPayloadForDoOrderUploadApiModel;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.io.File;
import java.io.InputStream;
import java.util.Objects;

@Slf4j
public class OrderSenseiRequestBuilder {

    public static String processOTCShipmentEventPaylod(String eventType, Object shipmentId) {
        JSONObject payload = new JSONObject();
        payload.put("shipmentId", shipmentId);
        payload.put("eventType", eventType);
        return payload.toString();
    }

    public static GetPayloadForDoOrderUploadApiModel getPayloadForDoOrder(ScmOrderContext scmOrderContext) {
        String filePath;
        try {
            // First try to get the file from the classpath resources
            java.net.URL resourceUrl = OrderSenseiRequestBuilder.class.getClassLoader()
                    .getResource("distriButedOrders_sample_upload_file.csv");

            if (resourceUrl == null) {
                // If not found in classpath, try to find it in the project structure
                String alternativePath = "scm/src/main/resources/distriButedOrders_sample_upload_file.csv";
                File file = new java.io.File(alternativePath);

                if (file.exists()) {
                    filePath = file.getAbsolutePath();
                    log.info("Using file from project structure: {}", filePath);
                } else {
                    // If still not found, create a temporary file with the required content
                    log.warn("CSV file not found in classpath or project structure. Creating a temporary file.");
                    filePath = createTemporaryCSVFile();
                    log.info("Created temporary CSV file: {}", filePath);
                }
            } else {
                // Use the file from classpath
                filePath = resourceUrl.getPath();
                log.info("Using file from classpath: {}", filePath);

                // Check if the file is inside a JAR (starts with "file:" and contains "!")
                if (filePath.startsWith("file:") && filePath.contains("!")) {
                    log.info("File is inside a JAR. Creating a temporary copy.");
                    filePath = extractFileFromJar(resourceUrl);
                    log.info("Extracted file from JAR to: {}", filePath);
                }
            }
        } catch (Exception e) {
            log.error("Error accessing CSV file: {}", e.getMessage(), e);
            // Fallback to creating a temporary file
            filePath = createTemporaryCSVFile();
            log.info("Created temporary CSV file due to error: {}", filePath);
        }

        return GetPayloadForDoOrderUploadApiModel.builder()
                .filePath(filePath)
                .facilityCode(scmOrderContext.getFacilityCode())
                .poNumber(scmOrderContext.getPoNumber())
                .customerCode(scmOrderContext.getCustomerCode())
                .doType(scmOrderContext.getDoType())
                .userName(scmOrderContext.getUserName())
                .build();
    }

    /**
     * Creates a temporary CSV file with the required content
     *
     * @return Path to the temporary file
     */
    private static String createTemporaryCSVFile() {
        try {
            java.io.File tempFile = java.io.File.createTempFile("distriButedOrders", ".csv");
            tempFile.deleteOnExit(); // Delete the file when the JVM exits

            try (java.io.FileWriter writer = new java.io.FileWriter(tempFile)) {
                // Write the CSV header and sample data
                writer.write("\"Item PID\",\"Item Name\",\"Item Type\",\"Qty\",\"Selling Price\"\n");
                writer.write("148246,Test Product Name 1,Eyeglasses,2,100.55\n");
                // Add more sample data if needed
            }

            return tempFile.getAbsolutePath();
        } catch (Exception e) {
            log.error("Failed to create temporary CSV file: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create temporary CSV file", e);
        }
    }

    /**
     * Extracts a file from a JAR to a temporary location
     *
     * @param resourceUrl URL of the resource in the JAR
     * @return Path to the extracted file
     */
    private static String extractFileFromJar(java.net.URL resourceUrl) {
        try {
            // Create a temporary file
            java.io.File tempFile = java.io.File.createTempFile("extracted_", ".csv");
            tempFile.deleteOnExit(); // Delete the file when the JVM exits

            // Copy the content from the JAR to the temporary file
            try (java.io.InputStream inputStream = resourceUrl.openStream();
                 java.io.FileOutputStream outputStream = new java.io.FileOutputStream(tempFile)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            return tempFile.getAbsolutePath();
        } catch (Exception e) {
            log.error("Failed to extract file from JAR: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to extract file from JAR", e);
        }
    }

    public static String getPayloadForDoReturnApiModel(String returnFacility, String qcStatus, String qcFailReason, String returnReason, String returnType, String userName, String scannedBarcode) {
        JSONObject payload = new JSONObject();
        payload.put("returnFacility", returnFacility);
        payload.put("qcStatus", qcStatus);
        payload.put("qcFailReason", qcFailReason);
        payload.put("returnReason", returnReason);
        payload.put("returnType", returnType);
        payload.put("userName", userName);
        payload.put("scannedBarcode", scannedBarcode);
        return payload.toString();
    }
}
