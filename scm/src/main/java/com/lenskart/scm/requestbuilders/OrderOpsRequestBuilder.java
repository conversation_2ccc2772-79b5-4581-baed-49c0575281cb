package com.lenskart.scm.requestbuilders;

import com.lenskart.scm.constants.ScmConstants;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

public class OrderOpsRequestBuilder {
    public static JSONArray getHsnTaxRatesPayload(int hsnCode, String powerType, String stateCode, double totalValue) {
        JSONArray hsnTaxRatesPayload = new JSONArray();
        JSONObject HsnTaxRatesPayload = new JSONObject();
        HsnTaxRatesPayload.put("hsnCode", hsnCode);
        HsnTaxRatesPayload.put("hsnClassification", powerType);
        HsnTaxRatesPayload.put("destinationStateCode", stateCode);
        HsnTaxRatesPayload.put("totalPrice", totalValue);
        hsnTaxRatesPayload.put(HsnTaxRatesPayload);
        return hsnTaxRatesPayload;
    }
    public static JSONObject getStatusUpdatePayload(String increment_id, String status, String state) {
        JSONObject statusUpdatePayload = new JSONObject();
        statusUpdatePayload.put("increment_id", increment_id);
        statusUpdatePayload.put("status", status);
        statusUpdatePayload.put("state", state);
        statusUpdatePayload.put("tracking_no", ScmConstants.CONFIRM);
        return statusUpdatePayload;
    }

     public static JSONObject getItemStatusPayload(String itemId, String status, String state, String uwItemId, String source) {
         JSONObject itemStatusPayload = new JSONObject();
         JSONObject items = new JSONObject();
         items.put("itemId", itemId);
         items.put("status", status);
         items.put("state", state);
         items.put("uwItemId", uwItemId);
         itemStatusPayload.put("source", source);
         itemStatusPayload.put("items", items);
         return itemStatusPayload;

     }
}
