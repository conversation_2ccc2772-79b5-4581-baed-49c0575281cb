package com.lenskart.scm.requestbuilders;


import org.json.JSONArray;
import org.json.JSONObject;

public class OrderOpsSchedulerRequestBuilder {
    public static String getPublishToWhReadyPayload(String incrementId) {
        JSONArray payload = new JSONArray();
        JSONObject obj = new JSONObject();
        obj.put("incrementId", incrementId);
        obj.put("srePanel", false);
        payload.put(obj);
        return payload.toString();
    }

}
