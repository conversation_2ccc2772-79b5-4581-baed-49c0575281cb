package com.lenskart.scm.requestbuilders;


import com.lenskart.commons.utils.DateUtils;
import com.lenskart.scm.model.*;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Collections;
import java.util.Date;
import java.util.List;

public class OrderInterceptorRequestBuilder {
    public static JSONArray getHsnTaxRatesPayload(int hsnCode, String powerType, String stateCode, double totalValue) {
        JSONArray hsnTaxRatesPayload = new JSONArray();
        JSONObject HsnTaxRatesPayload = new JSONObject();
        HsnTaxRatesPayload.put("hsnCode", hsnCode);
        HsnTaxRatesPayload.put("hsnClassification", powerType);
        HsnTaxRatesPayload.put("destinationStateCode", stateCode);
        HsnTaxRatesPayload.put("totalPrice", totalValue);
        hsnTaxRatesPayload.put(HsnTaxRatesPayload);
        return hsnTaxRatesPayload;
    }

    public static TrackingRequest getTrackingRequest(String incrementID,String  trackingNo, String unicomOrderCode, String carrier,ScmOrderContext scmOrderContext) {
        return TrackingRequest.builder()
                .version(scmOrderContext.getVersion())
                .tracking(getTrackingList(trackingNo,carrier,scmOrderContext))
                .uid(incrementID.concat("_").concat(unicomOrderCode).concat("_").concat(trackingNo))
                .orderNumber(incrementID+"_"+unicomOrderCode)
                .publishedDate(DateUtils.getFormattedDate("yyyy-MM-dd'T'HH:mm:ssXXX"))
                .build();

    }

    private static List<Tracking> getTrackingList(String  trackingNo, String carrier,ScmOrderContext scmOrderContext) {
        return Collections.singletonList(Tracking.builder()
                .carrierMoniker(carrier)
                .trackingNumber(trackingNo)
                .eddSource(scmOrderContext.getEddSource())
                .events(getEventList(carrier,scmOrderContext))
                .build());

    }

    private static List<Events> getEventList(String carrier, ScmOrderContext scmOrderContext) {
        return Collections.singletonList(Events.builder()
                .date(DateUtils.getFormattedDate("yyyy-MM-dd'T'HH:mm:ssXXX"))
                .carrierMoniker(carrier)
                .statusCodeMapping(getStatusCodeMapping(scmOrderContext))
                .location(getLocation(scmOrderContext))
                .build());

    }

    private static Events.Location getLocation(ScmOrderContext scmOrderContext) {
        return Events.Location.builder()
                .city(scmOrderContext.getCity()).build();

    }

    private static Events.StatusCodeMapping getStatusCodeMapping(ScmOrderContext scmOrderContext) {
        return Events.StatusCodeMapping.builder()
                .code(scmOrderContext.getCode())
                .codeDescription(scmOrderContext.getCodeDescription())
                .build();

    }
}
