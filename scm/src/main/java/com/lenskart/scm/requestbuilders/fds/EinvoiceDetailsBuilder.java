package com.lenskart.scm.requestbuilders.fds;

import org.json.JSONObject;

public class EinvoiceDetailsBuilder {
    public static JSONObject getEinvoicePayload(String shippingId) {
        String payload = "{\"documentSourceRefId\": \"" + shippingId + "\"}";
        JSONObject Payload = new JSONObject();
        Payload.put("payload", payload);
        Payload.put("einvoiceProvider", "EY");
        Payload.put("einvoiceType", "SALE_ORDER");
        Payload.put("client", "Automation");
        Payload.put("documentType", "INVOICE");
        return Payload;
    }
}
