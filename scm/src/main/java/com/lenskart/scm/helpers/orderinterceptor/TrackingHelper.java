package com.lenskart.scm.helpers.orderinterceptor;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.model.TrackingRequest;
import com.lenskart.scm.requestbuilders.OrderInterceptorRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;


import static com.lenskart.scm.endpoints.ScmEndpoints.V1_TRACKING;

@SuperBuilder
public class TrackingHelper extends ScmBaseHelper implements ServiceHelper {

    Response response;
    TrackingRequest request;
    String incrementID;
    String trackingNo;
    String carrier;
    String unicomOrderCode;
    ScmOrderContext scmOrderContext;

    @Override
    public ServiceHelper init() {

        request = OrderInterceptorRequestBuilder.getTrackingRequest(incrementID,trackingNo,unicomOrderCode,carrier,scmOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(V1_TRACKING.getUrl(), null, JsonUtils.convertObjectToJsonString(request), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
