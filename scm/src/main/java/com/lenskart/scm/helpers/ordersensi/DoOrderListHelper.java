package com.lenskart.scm.helpers.ordersensi;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_SENSEI_DO_ORDER_LIST;

@SuperBuilder
public class DoOrderListHelper extends ScmBaseHelper implements ServiceHelper {
    Response response;

    @Override
    public ServiceHelper init() {
        queryParams = getQueryParamsForDoListing("0", "35", "createdAt", "DESC", "status.in:CREATED___facility.eq:NXS2");

        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(ORDER_SENSEI_DO_ORDER_LIST.getUrl(), headers, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        return init().process().validate();

    }
}

