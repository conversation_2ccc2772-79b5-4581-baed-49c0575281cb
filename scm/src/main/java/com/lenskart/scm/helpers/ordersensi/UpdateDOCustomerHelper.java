package com.lenskart.scm.helpers.ordersensi;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_SENSEI_UPDATE_DO_CUSTOMER;

@SuperBuilder
@Slf4j
public class UpdateDOCustomerHelper extends ScmBaseHelper implements ServiceHelper {
    Response response;
    String payload;
    ScmOrderContext scmOrderContext;

    public UpdateDOCustomerHelper init() {
        payload = JsonUtils.convertObjectToJsonString(scmOrderContext.getDistributorCustomerDetailsDto());
        return this;
    }

    public UpdateDOCustomerHelper process() {
        log.info("id: {}", scmOrderContext.getDistributorCustomerDetailsDto().getId());
        response = RestUtils.put(ORDER_SENSEI_UPDATE_DO_CUSTOMER.getUrl(Map.of("id", scmOrderContext.getDistributorCustomerDetailsDto().getId().toString())),
                headers, payload, 200);
        return this;
    }

    public UpdateDOCustomerHelper validate() {
        Assert.assertFalse((boolean) RestUtils.getValueFromResponse(response, "data.customerEnabled"));
        return this;
    }

    public UpdateDOCustomerHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
