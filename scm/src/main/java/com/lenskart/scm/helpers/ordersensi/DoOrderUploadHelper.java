package com.lenskart.scm.helpers.ordersensi;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.model.ordersensi.GetPayloadForDoOrderUploadApiModel;
import com.lenskart.scm.requestbuilders.OrderSenseiRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_SENSEI_DO_ORDER_UPLOAD;


@SuperBuilder
public class DoOrderUploadHelper extends ScmBaseHelper implements ServiceHelper {
    private GetPayloadForDoOrderUploadApiModel payload;
    Response response;
    Map<String, Object> formData;
    private ScmOrderContext scmOrderContext;
    @Getter
    int doOrderId;

    @Override
    public ServiceHelper init() {
        payload = OrderSenseiRequestBuilder.getPayloadForDoOrder(scmOrderContext);
        formData = new HashMap<>();
        formData.put("facilityCode", payload.getFacilityCode());
        formData.put("poNumber", payload.getPoNumber());
        formData.put("customerCode", payload.getCustomerCode());
        formData.put("doType", payload.getDoType());
        formData.put("userName", payload.getUserName());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.postWithMultipartFile(
                ORDER_SENSEI_DO_ORDER_UPLOAD.getUrl(), headers, null, payload.getFilePath(), "csvFile", formData);
        assert response.getStatusCode() == 200;
         doOrderId = (int) RestUtils.getValueFromResponse(response, "data.referenceOrderId");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
