package com.lenskart.scm.helpers.vsm;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.scm.config.ScmConfig;
import com.lenskart.scm.config.ScmConfigRegistry;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.model.ScmOrderContext;
import com.microsoft.playwright.*;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.microsoft.playwright.options.SelectOption;
import com.microsoft.playwright.options.WaitForSelectorState;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.lenskart.scm.constants.ScmConstants.MANUAL_QC_FAIL_REASON;
import static com.lenskart.scm.endpoints.ScmEndpoints.VSM_LOGIN;

@Slf4j
@SuperBuilder
public class ManulJItProcessHelper extends ScmBaseHelper implements ServiceHelper {
    ScmOrderContext scmOrderContext;
    String jitInvoiceNo;
    List<String> barcodes;
    ScmConfig.CredentialsOfVsm vsmCredentials;

    @Override
    public ServiceHelper init() {
        barcodes = new ArrayList<>();
        log.info("facility code: {}", scmOrderContext.getFacilityCode());
        ScmDbUtils.updateCreatedAtOnUworders(scmOrderContext);

        vsmCredentials = ScmConfigRegistry.getInstance().getCredentialsOfVsm(scmOrderContext.getFacilityCode());

        if (vsmCredentials == null) {
            log.error("No VSM credentials found for facility code: {}", scmOrderContext.getFacilityCode());
            throw new RuntimeException("VSM credentials not configured for facility: " + scmOrderContext.getFacilityCode());
        }

        if (vsmCredentials.getUsername() == null || vsmCredentials.getPassword() == null) {
            log.error("VSM credentials are incomplete for facility code: {}", scmOrderContext.getFacilityCode());
            throw new RuntimeException("VSM credentials are incomplete for facility: " + scmOrderContext.getFacilityCode());
        }

        log.info("VSM credentials loaded for facility: {}", scmOrderContext.getFacilityCode());
        log.info("VSM username: {} and password: [MASKED]", vsmCredentials.getUsername());

        log.info("Manual JIT Process on VSM Started for order: {}", scmOrderContext.getIncrementId());
        return this;
    }

    @Override
    public ServiceHelper process() {
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(true));
            Page page = browser.newPage();
            log.info("Navigating to vsm login URL: {}", VSM_LOGIN.getUrl());
            page.navigate(VSM_LOGIN.getUrl());
            // Initial wait for the page to load
            log.info("Waiting 10 seconds for appearing the home page...");
            page.waitForTimeout(1000);
            page.locator("#username").click();
            page.locator("#username").fill(vsmCredentials.getUsername().toString());
            page.locator("#password").click();
            page.locator("#password").fill(vsmCredentials.getPassword().toString());
            try {
                page.click("text='Submit'");
            } catch (Exception e) {
                page.click("text='Submit'");
            }
            if(scmOrderContext.getIsQcFailOrder()){
                log.info("Identiying the Procurement page on vsm for QC failed items {} ");
                page.hover("text='Procurement'");
                page.hover("text='New Jit Prescription Lenses'");
                page.click("text='New Jit Prescription Lenses'");
                log.info("successfully moving the Jit processing page  url on vsm  {}", page.url().toString());
                page.selectOption("#productType", "1");
                page.selectOption("#viewName", "pw_all");
                page.locator("#orderId").click();
                page.locator("#orderId").fill(scmOrderContext.getIncrementId());
                page.selectOption("#poGeneratedFlag", "1");
                page.click("#btnSearch");
                page.click("#chkSelectAll");
                log.info("Start Receiving button clicking");
                List<ElementHandle> buttons = page.querySelectorAll("input[name='btnQcFail']");
                log.info("size off the button :{}",buttons.size());
                for (ElementHandle button : buttons) {
                    log.info(button.getProperties().toString());
                    button.click();
                    page.fill("#qcFailReason", MANUAL_QC_FAIL_REASON);
                    // ✅ Register dialog *and* wait for it explicitly
                    try {
                        // Create a future to wait for dialog in parallel
                        CompletableFuture<Dialog> dialogFuture = new CompletableFuture<>();
                        page.onceDialog(dialog -> {
                            log.info("Dialog message stockin barcode page : " + dialog.message());
                            dialog.accept();
                            dialogFuture.complete(dialog);
                        });
                        page.click("#btnQcFailUpdate");
                        // ✅ Wait explicitly for dialog to show and be handled
                        dialogFuture.get(10, TimeUnit.SECONDS);

                    } catch (Exception e) {
                        log.warn("Dialog was not shown or failed to handle it: {}", e.getMessage());
                    }

                     // This triggers the dialog
                    log.info("updating the qc fail reason for the order id : {}", scmOrderContext.getIncrementId());
                }



            }
            log.info("Identiying the Procurement page on vsm {} ");
            page.hover("text='Procurement'");
            page.hover("text='New Jit Prescription Lenses'");
            page.click("text='New Jit Prescription Lenses'");
            log.info("successfully moving the Jit processing page  url on vsm  {}", page.url().toString());
            page.selectOption("#productType", "1");
            page.selectOption("#viewName", "pw_all");
            page.locator("#orderId").click();
            page.locator("#orderId").fill(scmOrderContext.getIncrementId());
            page.click("#btnSearch");
            log.info("Waiting 10 seconds for load the order_item context in the same page ...{}" + page.url().toString());
            page.locator("#chkSelectAll").click();
            page.waitForTimeout(1000);
            page.click("text='Generate PO'");
            log.info("Generate PO successfully completed");
            Locator option = page.locator("#jitVendor option");
            int count = option.count();
            log.info("Number of options: {} " + count);

            for (int i = 0; i < count; i++) {
                String text = option.nth(i).innerText().trim();
                log.info("Option text: {} " + text + " and supplier id " + scmOrderContext.getSuplierID());

                if (text.contains(scmOrderContext.getSuplierID())) {
                    String value = option.nth(i).getAttribute("value");
                    page.click("#jitVendor");
                    log.info("Option value" + value);
                    page.selectOption("#jitVendor", value);
                    log.info("Supplier selected successfully {} " + scmOrderContext.getSuplierID());
                    break;
                }
            }
            Locator generatePoButtonLocator = page.locator("#btnGeneratePo");
            generatePoButtonLocator.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
            page.onceDialog(dialog -> {
                log.info("Dialog message: " + dialog.message());
                dialog.accept();  // This clicks the "OK" button
            });
            generatePoButtonLocator.click();

            log.info("Identiying the Procurement page on vsm {} ");
            page.hover("text='Procurement'");
            page.hover("text='New Jit Prescription Lenses'");
            page.click("text='New Jit Prescription Lenses'");
            log.info("successfully moving the Jit processing page  url on vsm  {}", page.url().toString());
            page.selectOption("#productType", "1");
            page.selectOption("#viewName", "pw_all");
            page.locator("#orderId").click();
            page.locator("#orderId").fill(scmOrderContext.getIncrementId());
            page.selectOption("#poGeneratedFlag", "1");
            page.click("#btnSearch");
            page.click("#chkSelectAll");
            log.info("Start Receiving button clicking");
            AwaitUtils.sleepSeconds(5);
            try {
                Locator startReceiving = page.locator("text='Start Receiving'");
                startReceiving.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
                startReceiving.click();

            } catch (Exception e) {
                AwaitUtils.sleepSeconds(5);
                page.click("text='Start Receiving'");
            }
            Locator startInvoiceNoLocator = page.locator("#startInvoiceNo");
            try {
                startInvoiceNoLocator.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
                startInvoiceNoLocator.click();
            } catch (Exception e) {
                page.locator("#startInvoiceNo").click();
            }


            jitInvoiceNo = "INV" + System.currentTimeMillis();
            scmOrderContext.setJitInvoiceNo(jitInvoiceNo);
            page.locator("#startInvoiceNo").fill(jitInvoiceNo);
            page.locator("#startInvoiceDate").click();
            page.locator("#startInvoiceDate").fill(GenericUtils.currentDatePlus(0));
            log.info("btnSaveStartReceiving clicking");
            Locator closeIcon = page.locator("img[src='/public/images/cal_close.gif']");
            try {
                closeIcon.hover();
                closeIcon.click();
            } catch (Exception e) {
                closeIcon.hover();
                closeIcon.click();
            }

            Locator btnSaveStartReceivingLocator = page.locator("#btnSaveStartReceiving");
            btnSaveStartReceivingLocator.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
            try{
                btnSaveStartReceivingLocator.click();

            }catch (Exception e){
                btnSaveStartReceivingLocator.click();
            }
            try {
                Locator receiveLocator = page.locator("text='Receive'");
                receiveLocator.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
                receiveLocator.click();

            } catch (Exception e) {
                page.locator("text='Receive'").click();
            }
            page.onceDialog(dialog -> {
                log.info("Dialog message: " + dialog.message());
                dialog.accept();  // This clicks the "OK" button
            });
            AwaitUtils.sleepSeconds(10);
            try {
                Locator saveJitReceivingLocator = page.locator("#btnSaveJitReceiving");
                saveJitReceivingLocator.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
                saveJitReceivingLocator.click();
            } catch (Exception e) {
                log.info("retrying on catch block");
                page.click("#btnSaveJitReceiving");
            }
            log.info("Identiying JIT PO page on vsm {} ");
            page.hover("text='PO'");
            page.hover("text='JIT PO'");
            page.click("text='JIT PO'");
            page.selectOption("#vendorId", scmOrderContext.getSupplierJitViewName());
            page.click("#invoiceNo");
            log.info(scmOrderContext.getJitInvoiceNo());
            AwaitUtils.sleepSeconds(5);
            SelectOption[] options = {
                    new SelectOption().setValue(scmOrderContext.getJitInvoiceNo())
            };
            page.selectOption("#invoiceNo", options);
            page.click("#btnSearchJitItems");
            AwaitUtils.sleepSeconds(5);
            List<Locator> trElements = page.locator("tr[id]").all();
            log.info("Number of tr elements found: {} " + trElements.size() + " list {} ");

            for (Locator tr : trElements) {
                String rowId = tr.getAttribute("id");  // like "1227168786"
                log.info("Value of Row ID : {}", rowId);
                Locator inputLocator = tr.locator("input[id$='editPoMrp']");

                if (inputLocator.count() > 0) {
                    ElementHandle inputHandle = inputLocator.elementHandle();
                    // Check if it's readonly
                    if (inputHandle.getAttribute("readonly") != null) {
                        // Remove readonly using JS
                        log.info("removing  readonly {} ");
                        page.evaluate("el => el.removeAttribute('readonly')", inputHandle);
                    }

                    // Fill with value 14
                    page.evaluate("el => el.value = '14'", inputHandle);
                }
            }
            Locator inputLocator = page.locator("#editCST");
            ElementHandle inputHandle = inputLocator.elementHandle();

            if (inputHandle != null) {
                // 1. Check and remove readonly (if present)
                String isReadonly = inputHandle.getAttribute("readonly");
                if (isReadonly != null) {
                    page.evaluate("el => el.removeAttribute('readonly')", inputHandle);
                }

                // 2. Update the value to desired (e.g., "5")
                page.evaluate("el => el.value = '1'", inputHandle);
            }
            page.locator("input[type=submit][value='Submit']").click();
            Locator invNoNewLocator = page.locator("#invNoNew");
            invNoNewLocator.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
            invNoNewLocator.click();
            page.locator("#invNoNew").fill(scmOrderContext.getJitInvoiceNo());
            page.locator("#invDate").click();
            page.locator("#invDate").fill(GenericUtils.currentDatePlus(0));

            page.locator("#submitInvoiceButton").click();
            AwaitUtils.sleepSeconds(5);
            boolean ISsecondBarodeScan = false;
            log.info("trying SCAN THE BARCODE ");
            List<Locator> stockInButtons = page.locator("input[type='button'][name$='jitStockinButton']").all();
            log.info("Number of stockInButtons: {} " + stockInButtons.size());
            for (Locator stockinButton : stockInButtons) {
                stockinButton.click();
                log.info("stockinButton ::: {}" + stockinButton.toString());
                List<Locator> ScanBarcodes = page.locator("input[type='text'][name*='ScanBarcodes']").all();
                log.info("Number of ScanBarcodes: {} " + ScanBarcodes.size());
                for (int i = 0; i < ScanBarcodes.size(); i++) {
                    Locator scanBarcode = ScanBarcodes.get(i);
                    if (ISsecondBarodeScan) {
                        ISsecondBarodeScan = false;
                        continue;
                    }
                    ElementHandle barcodeHandle = scanBarcode.elementHandle();
                    log.info("Barcode Handle: {}", barcodeHandle.toString());
                    String barCode = "SKY" + GenericUtils.genrateRandomNumericString(9);
                    log.info("Barcode: {}", barCode);
                    barcodeHandle.click();
                    barcodeHandle.fill(barCode);
                    page.onceDialog(dialog -> {
                        log.info("Dialog message stockin barocde page : " + dialog.message());
                        dialog.accept();  // This clicks the "OK" button
                    });
                    page.keyboard().press("Enter");
                    barcodes.add(barCode);
                    AwaitUtils.sleepSeconds(3);
                    if (stockInButtons.size() == 2) {
                        ISsecondBarodeScan = true;
                        break;
                    }
                }
                AwaitUtils.sleepSeconds(5);
            }
            log.info("clicking on generateGRN button after scanning all barcodes");
            page.locator("#generateGRN").click();
            scmOrderContext.setBarcodes(barcodes);
            AwaitUtils.sleepSeconds(2);

            log.info("vsm process for manual jit is completed");


        } catch (Exception e) {
            log.error("Playwright automation failed or not available. vsm login required. Open URL: {}", VSM_LOGIN.getUrl());
            log.error("Error details: {}", e.getMessage());
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
