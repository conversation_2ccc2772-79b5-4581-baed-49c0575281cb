package com.lenskart.scm.helpers.optima;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.model.optima.OptimaRequest;
import com.lenskart.scm.model.optima.ShipmentItems;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.OPTIMA_ASSIGN_FULLFILLER;

@SuperBuilder
@Slf4j
public class AssignFullfillerHelper extends ScmBaseHelper implements ServiceHelper {

     Response response;
     OptimaRequest optimaRequest;
     List<ShipmentItems> shipmentItemsList;
     String shipmentType,itemType;
     String hubCode,unicomOrderCode;;
     ScmOrderContext scmOrderContext;
     String unicomcode;

     @Override
    public ServiceHelper init() {

        headers = getHeadersForOptima();

        shipmentItemsList = new ArrayList<>();
        optimaRequest = new OptimaRequest();

        String wmsOrderCode = scmOrderContext.getUnicomOrderCode();
        List<Map<String, Object>> itemsList = ScmDbUtils.getUWorderDetails(wmsOrderCode);
        String pincode = ScmDbUtils.getDetailsOrderAddress(scmOrderContext.getUnicomOrderCode()).getFirst().get("postcode").toString();

        optimaRequest.setWmsOrderCode(scmOrderContext.getUnicomOrderCode());
        optimaRequest.setPincode(pincode);


        for (Map<String, Object> item : itemsList) {
            ShipmentItems shipmentItem = new ShipmentItems();

            String uwItemId = item.get("uw_item_id").toString();
            String itemId = item.get("item_id").toString();
            String productId = item.get("product_id").toString();

            shipmentItem.setItemId(Integer.parseInt(uwItemId));
            shipmentItem.setProductId(Integer.parseInt(productId));
            shipmentItem.setNavChannel(item.get("nav_channel").toString());
            shipmentItem.setFittingId(Integer.parseInt(item.get("parent_uw").toString()));

            String hsnClassification = ScmDbUtils.getProductDetails(productId).getFirst().get("hsn_classification").toString();

            if (hsnClassification.contains("eye-frames")) {
                itemType = "FRAME";
                if (itemsList.size() > 1) {
                    shipmentType = "FR1";
                    hubCode = ScmDbUtils.getHubcode(itemId).getFirst().get("hub_code").toString();
                } else {
                    shipmentType = "FR0";
                    hubCode = ScmDbUtils.getHubcode(itemId).getFirst().get("hub_code").toString();
                }
            } else if (hsnClassification.contains("sunglasses")) {
                itemType = "SUNGLASS";
                shipmentType = (itemsList.size() > 1) ? "FR2" : "FR0";
            } else {
                String rightLens = ScmDbUtils.getRightLens(itemId);
                itemType = rightLens.equalsIgnoreCase("right") ? "RIGHTLENS" : "LEFTLENS";
            }

            shipmentItem.setItemType(itemType);
            shipmentItemsList.add(shipmentItem);

        }

        optimaRequest.setShipmentItems(shipmentItemsList);
        optimaRequest.setShipmentType(shipmentType);
        optimaRequest.setHubCode(hubCode);

        return this;
    }

    @Override
    public ServiceHelper process() {
        String jsonRequest = JsonUtils.convertObjectToJsonString(optimaRequest);
        log.info("Sending OptimaRequest: {}", jsonRequest);
        response = RestUtils.post(OPTIMA_ASSIGN_FULLFILLER.getUrl(), headers, jsonRequest);
        log.info("Response status code: {}", response.getStatusCode());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        // Add validation logic if needed
        String facility = ScmDbUtils.getShipmentDetailsOptima(scmOrderContext.getUnicomOrderCode());
        Assert.assertEquals(facility,RestUtils.getValueFromResponse(response,"data.facility"));
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
