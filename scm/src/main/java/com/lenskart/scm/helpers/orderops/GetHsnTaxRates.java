package com.lenskart.scm.helpers.orderops;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import static com.lenskart.scm.endpoints.ScmEndpoints.GET_HSN_TAX_RATES;
import static com.lenskart.scm.requestbuilders.OrderOpsRequestBuilder.getHsnTaxRatesPayload;


@SuperBuilder
@Slf4j
public class GetHsnTaxRates extends ScmBaseHelper implements ServiceHelper {

     ScmOrderContext scmOrderContext;
     Response response;
    JSONArray payload;


    @Override
    public ServiceHelper init() {
    log.info("Hitting get hsn tax rates api");
    payload = getHsnTaxRatesPayload(scmOrderContext.getHsnCode(), scmOrderContext.getPowerType(), scmOrderContext.getStateCode(), scmOrderContext.getTotalValue());
    return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(
                GET_HSN_TAX_RATES.getUrl(),
                null,
                payload.toString(),
                200);

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}
