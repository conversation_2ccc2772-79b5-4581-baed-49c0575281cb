package com.lenskart.scm.helpers.fds;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.fds.response.thermalInvoice.ThermalInvoiceResponse;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.validator.fds.DocumentDetailsValidator;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;


import java.util.List;
import java.util.Map;
import static com.lenskart.scm.endpoints.ScmEndpoints.DOCUMENT_DETAILS;

@SuperBuilder
@Slf4j
public class DocumentDetailsHelper extends ScmBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    ScmOrderContext scmOrderContext;
    List<Map<String, Object>> referenceId;
    @Getter
    ThermalInvoiceResponse thermalInvoiceResponse;

    @Override
    public ServiceHelper init() {
        referenceId = ScmDbUtils.getReferenceId(scmOrderContext.getShippingId());
        scmOrderContext.setOrderId(referenceId.getFirst().get("order_id").toString());
        queryParams = getQueryParamsForDocumentDetails(referenceId.getFirst().get("magento_item_id").toString());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(DOCUMENT_DETAILS.getUrl(), null, queryParams, 200);
        statusCode = response.getStatusCode();
        thermalInvoiceResponse = parseResponse(RestUtils.getValueFromResponse(response, "data"), ThermalInvoiceResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
       DocumentDetailsValidator validator = DocumentDetailsValidator.builder().scmOrderContext(scmOrderContext).documentDetailsHelper(this).build();
        validator.validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

