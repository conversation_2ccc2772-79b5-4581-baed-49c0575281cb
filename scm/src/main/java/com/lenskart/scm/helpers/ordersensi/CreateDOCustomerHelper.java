package com.lenskart.scm.helpers.ordersensi;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.oms.dto.DistributorCustomerDetailsDto;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_SENSEI_CREATE_DO_CUSTOMER;

@SuperBuilder
@Slf4j
public class CreateDOCustomerHelper extends ScmBaseHelper implements ServiceHelper {
    Response response;
    String payload;
    ScmOrderContext scmOrderContext;

    @Override
    public ServiceHelper init() {
        payload = JsonUtils.convertObjectToJsonString(scmOrderContext.getDistributorCustomerDetailsDto());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(ORDER_SENSEI_CREATE_DO_CUSTOMER.getUrl(), headers, payload, 200);
        scmOrderContext.getDistributorCustomerDetailsDto().setId(Long.valueOf(RestUtils.getValueFromResponse(response, "data.id").toString()));
        log.info("DO customer created with id: {}", scmOrderContext.getDistributorCustomerDetailsDto().getId());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
