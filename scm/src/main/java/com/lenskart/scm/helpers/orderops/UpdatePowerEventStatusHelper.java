package com.lenskart.scm.helpers.orderops;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;

@Slf4j
@SuperBuilder
public class UpdatePowerEventStatusHelper extends ScmBaseHelper implements ServiceHelper {
   ScmOrderContext scmOrderContext;

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        boolean success = AwaitUtils.pollUntil(() ->
                        ScmDbUtils.getPowerChangeEventStatus(scmOrderContext).getFirst().get("unicom_syn_status").toString().equalsIgnoreCase("Yes"),
                "Update Power event  to be picked", Duration.ofSeconds(240), Duration.ofSeconds(30), true);

        if (success) {
            //After event got pushed to wms there will be coupple scheduler again so moving the order processing to manual
            log.info("Processing an external event: customer prescription update for the order ID  is completed && please process the order Manually:{}", scmOrderContext.getIncrementId());

        }
       return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}