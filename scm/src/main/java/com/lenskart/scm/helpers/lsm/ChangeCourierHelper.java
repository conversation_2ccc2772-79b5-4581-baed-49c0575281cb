package com.lenskart.scm.helpers.lsm;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.requestbuilders.lsmshipments.ChangeCourierRequestBuilder;
import com.lenskart.scm.validator.lsm.ChangeCourierValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.scm.endpoints.ScmEndpoints.LSM_CHANGE_COURIER;
@Slf4j
@SuperBuilder
public class ChangeCourierHelper extends ScmBaseHelper implements ServiceHelper {
    Response response;
    String payload;
    ScmOrderContext scmOrderContext;



    @Override
    public ServiceHelper init() {
        //Need  to change the logic for Local Fitting orders
        log.info("Change courier request for the order: {} and Courier code: {}",scmOrderContext.getIncrementId(),scmOrderContext.getCourierCode());
        payload = ChangeCourierRequestBuilder.changeCourierPaylod(scmOrderContext.getCourierCode(),scmOrderContext.getShippingId());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(LSM_CHANGE_COURIER.getUrl(),null,payload,200);
        return this;
    }


    @Override
    public ServiceHelper validate() {
        log.info("Validating the change courier request for the shipping id: {}", scmOrderContext.getShippingId());
        ChangeCourierValidator validator = ChangeCourierValidator.builder().scmOrderContext(scmOrderContext).build();
        validator.validateNode();
        return this;
    }
    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;

    }

}
