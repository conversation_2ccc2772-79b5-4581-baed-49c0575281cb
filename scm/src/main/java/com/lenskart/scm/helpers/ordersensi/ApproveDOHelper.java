package com.lenskart.scm.helpers.ordersensi;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_SENSEI_APPROVE_DO;


@SuperBuilder
    public class ApproveDOHelper extends ScmBaseHelper implements ServiceHelper {
        Response response;
        String orderId;

        @Override
        public ServiceHelper init() {
            return this;
        }

        @Override
        public ServiceHelper process() {
            response = RestUtils.post(ORDER_SENSEI_APPROVE_DO.getUrl(Map.of("orderId",orderId)),headers,null,null,200);
            return this;
        }

        @Override
        public ServiceHelper validate() {
            return this;
        }

        @Override
        public ServiceHelper test() {
            return init().process().validate();

        }
}
