package com.lenskart.scm.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.function.Supplier;

import static com.lenskart.scm.endpoints.ScmEndpoints.*;

@SuperBuilder
@Slf4j
public class OrderSyncHealthCheckHelper extends ScmBaseHelper implements ServiceHelper {

    private static final Map<String, Supplier<String>> MANDATORY_SERVICES = Map.of(
            "Order Ops", ORDER_OPS_ACTUATOR_HEALTH::getUrl,
            "Order Interceptor", ORDER_INTERCEPTOR_ACTUATOR_HEALTH::getUrl
    );

    private static final Map<String, Supplier<String>> OPTIONAL_SERVICES = Map.of(
            "Optima", OPTIMA_ACTUATOR_HEALTH::getUrl,
            "Order Sensei", ORDER_SENSEI_ACTUATOR_HEALTH::getUrl,
            "Order Adopter", ORDER_ADOPTER_ACTUATOR_HEALTH::getUrl
    );

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        // Check mandatory services - throw exception if down
        MANDATORY_SERVICES.forEach((name, urlSupplier) -> checkServiceHealth(name, urlSupplier.get(), true));

        // Check optional services - log only
        OPTIONAL_SERVICES.forEach((name, urlSupplier) -> checkServiceHealth(name, urlSupplier.get(), false));

        return this;
    }

    private void checkServiceHealth(String serviceName, String url, boolean failOnError) {
        Response response = RestUtils.get(url, null, null);
        if (response.getStatusCode() == 200) {
            log.info("{} is up and running", serviceName);
        } else {
            log.error("{} is not up and running. Status code: {}", serviceName, response.getStatusCode());
            if (failOnError) {
                throw new RuntimeException(serviceName + " is not up and running");
            }
        }
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        return init().process().validate();
    }
}
