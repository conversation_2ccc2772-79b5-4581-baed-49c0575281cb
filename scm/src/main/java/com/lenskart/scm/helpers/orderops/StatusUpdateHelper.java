package com.lenskart.scm.helpers.orderops;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.requestbuilders.OrderOpsRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_OPS_STATUS_UPDATE;

@Slf4j
@SuperBuilder
public class StatusUpdateHelper extends ScmBaseHelper implements ServiceHelper {
    ScmOrderContext scmOrderContext;
    Response response;
    private JSONObject payload;

    @Override
    public ServiceHelper init() {
        payload = OrderOpsRequestBuilder.getStatusUpdatePayload(scmOrderContext.getIncrementId(), scmOrderContext.getStatus(), scmOrderContext.getState());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(ORDER_OPS_STATUS_UPDATE.getUrl(), null, payload.toString(),200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
