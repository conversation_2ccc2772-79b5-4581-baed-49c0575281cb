package com.lenskart.scm.helpers.ordersensi;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;
import static com.lenskart.commons.utils.RestUtils.getValueFromResponse;
import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_SENSEI_GET_ORDER_DETAILS;

@SuperBuilder
@Slf4j
public class CreateDOOrderHelper extends ScmBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    String lastestDoOrderId;

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(ORDER_SENSEI_GET_ORDER_DETAILS.getUrl(Map.of("doOrderId", lastestDoOrderId)), headers, null, 200);
        log.info("DO order created api response: {}", getValueFromResponse(response, "meta.displayMessage"));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}
