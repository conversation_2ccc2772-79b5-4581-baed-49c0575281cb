package com.lenskart.scm.helpers.orderops;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.scm.requestbuilders.OrderOpsRequestBuilder.getItemStatusPayload;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_OPS_ITEM_STATUS;


@SuperBuilder
public class GetItemStatusHelper extends ScmBaseHelper implements ServiceHelper {
    ScmOrderContext scmOrderContext;
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        payload = getItemStatusPayload(scmOrderContext.getMagentoItemId(), scmOrderContext.getStatus(), scmOrderContext.getState(),
                scmOrderContext.getUwItemId(), scmOrderContext.getSource());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(ORDER_OPS_ITEM_STATUS.getUrl(), null, payload);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}
