package com.lenskart.scm.helpers.orderops;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_OPS_ORDER_SYNC;

@SuperBuilder
public class OrderSyncHelper extends ScmBaseHelper implements ServiceHelper {
    String incrementId;
   Response response;

    @Override
    public ServiceHelper init() {
        queryParams = getQueryParamsForOrderSync(incrementId);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(ORDER_OPS_ORDER_SYNC.getUrl(), null, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }



}
