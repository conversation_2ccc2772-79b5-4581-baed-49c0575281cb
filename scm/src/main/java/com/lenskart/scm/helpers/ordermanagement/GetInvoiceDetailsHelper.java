package com.lenskart.scm.helpers.ordermanagement;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_MANAGEMENT;

@SuperBuilder
public class GetInvoiceDetailsHelper extends ScmBaseHelper implements ServiceHelper {

    Response response;
    ScmOrderContext scmordercontext;

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override

    public ServiceHelper process() {
        response = RestUtils.get(ORDER_MANAGEMENT.getUrl(Map.of("shippingId", scmordercontext.getShippingId(),
                        "orderId", scmordercontext.getOrderId(),
                        "facilityCode", scmordercontext.getFacilityCode())),
                null, null, 200);
        return this;

    }
@Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper  test() {
        init();
        process();
        validate();
        return this;
    }
}