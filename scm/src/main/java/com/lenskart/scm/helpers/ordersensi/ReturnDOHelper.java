package com.lenskart.scm.helpers.ordersensi;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.requestbuilders.OrderSenseiRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;


import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_SENSEI_DO_RETURN;

@Slf4j
@SuperBuilder
public class ReturnDOHelper extends ScmBaseHelper implements ServiceHelper {
    String payload;
    private ScmOrderContext scmOrderContext;
    Response response;
    String barcode;


    public ServiceHelper init() {
        scmOrderContext.setReturnFacility("NXS2");
        scmOrderContext.setQcStatus("GOOD");
        scmOrderContext.setQcFailReason("");
        scmOrderContext.setReturnReason("SO");
        scmOrderContext.setReturnType("RTO");
        scmOrderContext.setUserName("144034");
        scmOrderContext.setScannedBarcode(barcode);

        payload = OrderSenseiRequestBuilder.getPayloadForDoReturnApiModel(scmOrderContext.getReturnFacility(), scmOrderContext.getQcStatus(), scmOrderContext.getQcFailReason(), scmOrderContext.getReturnReason(), scmOrderContext.getReturnType(), scmOrderContext.getUserName(), scmOrderContext.getScannedBarcode());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(ORDER_SENSEI_DO_RETURN.getUrl(), headers, payload, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        return init().process().validate();
    }
}