package com.lenskart.scm.helpers.lsm;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.core.model.EstimatedTatDetails;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.*;
@SuperBuilder
@Slf4j
public class LsmHealthCheckHelper extends ScmBaseHelper implements ServiceHelper {
    Response response;
    ScmOrderContext scmOrderContext;

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(LSM_SHIPMENTS_ACTUATOR_HEALTH.getUrl(), null, null);
        if (response.getStatusCode() != 200) {
            log.error("Lsm shipments not up and running");
        }
        log.info("Lsm shipments is up and running");
        response = RestUtils.get(LSM_COURIERS_ACTUATOR_HEALTH.getUrl(), null, null);
        if (response.getStatusCode() != 200) {
            log.error("Lsm couriers is not up and running");
        }
        log.info("Lsm couriers is up and running");
        response = RestUtils.get(SHIPPING_ACTUATOR_HEALTH.getUrl(), null, null);
        if (response.getStatusCode() != 200) {
            log.error("Shipping is not up and running");
        }
        log.info("Shipping is up and running");

        response = RestUtils.get(LSM_SERVICEABILITY_ACTUATOR_HEALTH.getUrl(Map.of("pincode",scmOrderContext.getPincode())), null, null);
        if (response.getStatusCode() != 200) {
            log.error("Lsm serviceability is not up and running");
        }
        log.info("Lsm serviceability is up and running");
        /* not working in preprod
        response = RestUtils.get(LSM_COURIER_ADAPTER_ACTUATOR_HEALTH.getUrl(), null, null);
        if (response.getStatusCode() != 200) {
            log.error("Lsm courier adapter is not up and running");
        }
        log.info("Lsm courier adapter is up and running");*/
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
