package com.lenskart.scm.helpers.lsm;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.validator.lsm.LsmServiceabilityValidator;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.LSM_SERVICEABILITY;

@SuperBuilder
public class LsmServiceabilityHelper extends ScmBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    String country;
    String pincode;
    ScmOrderContext.LsmServicibility lsmServicibility;
    ScmOrderContext scmOrderContext;

    @Override
    public ServiceHelper init() {
        queryParams = getQueryParamsForLsmServicibility(lsmServicibility);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(LSM_SERVICEABILITY.getUrl(Map.of("country", country, "pincode", pincode)),
                null, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        LsmServiceabilityValidator validator = LsmServiceabilityValidator.builder().scmOrderContext(scmOrderContext).lsmServiceabilityHelper(this)
                .lsmServicibility(lsmServicibility).build();
        validator.validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
