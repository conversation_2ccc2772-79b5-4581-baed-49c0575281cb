package com.lenskart.scm.helpers.orderopsscheduler;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.requestbuilders.OrderOpsSchedulerRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_OPS_SCHEDULER_PUBLISH_TO_WH_READY;

@SuperBuilder
public class PublishToWhReadyHelper extends ScmBaseHelper implements ServiceHelper {
    String incrementId;
    Response response;
    String payload;

    @Override
    public ServiceHelper init() {
        payload = OrderOpsSchedulerRequestBuilder.getPublishToWhReadyPayload(incrementId);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(ORDER_OPS_SCHEDULER_PUBLISH_TO_WH_READY.getUrl(), null, payload, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
