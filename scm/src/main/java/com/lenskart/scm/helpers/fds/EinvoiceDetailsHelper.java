package com.lenskart.scm.helpers.fds;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.requestbuilders.fds.EinvoiceDetailsBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.scm.endpoints.ScmEndpoints.EINVOICE_DETAILS;

@SuperBuilder
public class EinvoiceDetailsHelper extends ScmBaseHelper implements ServiceHelper {

    ScmOrderContext scmOrderContext;
    Response response;
    JSONObject payload;


    @Override
    public ServiceHelper init() {
        payload = EinvoiceDetailsBuilder.getEinvoicePayload(String.valueOf(scmOrderContext.getShippingId()));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(EINVOICE_DETAILS.getUrl(), null, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
