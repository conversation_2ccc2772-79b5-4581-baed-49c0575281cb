package com.lenskart.scm.config;

import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility class to load Juno configurations from YAML files
 */
@Slf4j
public class ScmConfigLoader {
    // Configuration cache
    private static final Map<String, ScmConfig> configCache = new ConcurrentHashMap<>();

    // Default values
    private static final String DEFAULT_CONFIG_PATH = "scm.yml";
    private static final String DEFAULT_ENVIRONMENT = "preprod";

    /**
     * Loads the Juno configuration from the default YAML file
     *
     * @return JunoConfig object with configuration details
     */
    public static ScmConfig loadConfig() {
        return loadConfig(DEFAULT_CONFIG_PATH);
    }

    /**
     * Loads the Juno configuration from a specified YAML file
     *
     * @param configPath Path to the YAML file
     * @return JunoConfig object with configuration details
     */
    public static ScmConfig loadConfig(String configPath) {
        // Return cached config if available
        if (configCache.containsKey(configPath)) {
            return configCache.get(configPath);
        }

        try {
            Yaml yaml = new Yaml();

            // Try to load from classpath first
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(configPath);

            // If not found in classpath, try as a file path
            if (inputStream == null) {
                try {
                    inputStream = new FileInputStream(configPath);
                } catch (IOException e) {
                    log.warn("Could not find configuration file: {}", configPath);
                    ScmConfig defaultConfig = createDefaultConfig();
                    configCache.put(configPath, defaultConfig);
                    return defaultConfig;
                }
            }

            // Load the YAML file as a map
            Map<String, Object> yamlMap = yaml.load(inputStream);
            inputStream.close();

            if (yamlMap == null || yamlMap.isEmpty()) {
                log.warn("Empty or invalid configuration file: {}", configPath);
                ScmConfig defaultConfig = createDefaultConfig();
                configCache.put(configPath, defaultConfig);
                return defaultConfig;
            }

            // Create a new ScmConfig object
            ScmConfig config = new ScmConfig();

            // Process each environment in the YAML file
            for (Map.Entry<String, Object> entry : yamlMap.entrySet()) {
                String envName = entry.getKey();

                if (entry.getValue() instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> envConfig = (Map<String, Object>) entry.getValue();

                    // Create a new environment configuration
                    ScmConfig.EnvironmentConfig environmentConfig = new ScmConfig.EnvironmentConfig();

                    // Process base URLs if available
                    if (envConfig.containsKey("baseUrls") && envConfig.get("baseUrls") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, String> baseUrls = (Map<String, String>) envConfig.get("baseUrls");
                        environmentConfig.setBaseUrls(baseUrls);
                    }

                    // Process vsmcredentials if available
                    if (envConfig.containsKey("credentialsOfVsm") && envConfig.get("credentialsOfVsm") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> credentialsMap = (Map<String, Object>) envConfig.get("credentialsOfVsm");

                        Map<String, ScmConfig.CredentialsOfVsm> credentialsOfVsm = new HashMap<>();

                        // Process each serial number entry
                        for (Map.Entry<String, Object> credEntry : credentialsMap.entrySet()) {
                            String facilityCode = credEntry.getKey();

                            if (credEntry.getValue() instanceof Map) {
                                @SuppressWarnings("unchecked")
                                Map<String, String> credDetails = (Map<String, String>) credEntry.getValue();

                                ScmConfig.CredentialsOfVsm credentialsOfVsmConfig = new ScmConfig.CredentialsOfVsm();
                                credentialsOfVsmConfig.setUsername(credDetails.get("username"));
                                credentialsOfVsmConfig.setPassword(credDetails.get("password"));
                                credentialsOfVsm.put(facilityCode, credentialsOfVsmConfig);

                                log.debug("Loaded credentials for serial number: {}", facilityCode);
                            }
                        }

//                        environmentConfig.setCredentials(credentials);
                        environmentConfig.setCredentialsOfVsm(credentialsOfVsm);
                        log.info("Loaded {} credential entries", credentialsOfVsm.size());
                    }

                    // Add the environment configuration to the ScmConfig
                    config.getEnvironments().put(envName, environmentConfig);
                }
            }

            // Cache the config
            configCache.put(configPath, config);

            log.info("Loaded Scm configuration from: {}", configPath);
            return config;

        } catch (Exception e) {
            log.error("Failed to load Scm configuration: {}", e.getMessage(), e);
            ScmConfig defaultConfig = createDefaultConfig();
            configCache.put(configPath, defaultConfig);
            return defaultConfig;
        }
    }

    /**
     * Creates a default Scm configuration
     *
     * @return Default ScmConfig
     */
    private static ScmConfig createDefaultConfig() {
        ScmConfig config = new ScmConfig();

        // Create default environment configuration
        ScmConfig.EnvironmentConfig environmentConfig = new ScmConfig.EnvironmentConfig();

        // Add default base URLs
        Map<String, String> baseUrls = new HashMap<>();
        baseUrls.put("sessionService", "https://api-gateway.scm.preprod.lenskart.com");
        baseUrls.put("testService", "https://jsonplaceholder.typicode.com");
        environmentConfig.setBaseUrls(baseUrls);

        // Add the environment configuration to the ScmConfig
        config.getEnvironments().put(DEFAULT_ENVIRONMENT, environmentConfig);

        return config;
    }

    /**
     * Clears the configuration cache
     */
    public static void clearCache() {
        configCache.clear();
        log.info("Configuration cache cleared");
    }

    /**
     * Get the default environment name
     *
     * @return Default environment name
     */
    public static String getDefaultEnvironment() {
        return DEFAULT_ENVIRONMENT;
    }

    /**
     * Get the default configuration path
     *
     * @return Default configuration path
     */
    public static String getDefaultConfigPath() {
        return DEFAULT_CONFIG_PATH;
    }
}
