package com.lenskart.scm.config;

import com.lenskart.commons.config.ConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Configuration provider for Scm module that wraps ScmConfigRegistry
 * to implement the standard ConfigProvider interface.
 */
@Slf4j
public class ScmConfigProvider implements ConfigProvider {

    private final ScmConfigRegistry scmConfigRegistry;

    // Singleton instance
    private static volatile ScmConfigProvider instance;

    /**
     * Private constructor for singleton pattern
     */
    private ScmConfigProvider() {
        this.scmConfigRegistry = ScmConfigRegistry.getInstance();
    }
    
    /**
     * Gets the singleton instance of ScmConfigProvider
     *
     * @return The singleton instance
     */
    public static ScmConfigProvider getInstance() {
        if (instance == null) {
            synchronized (ScmConfigProvider.class) {
                if (instance == null) {
                    instance = new ScmConfigProvider();
                }
            }
        }
        return instance;
    }
    
    @Override
    public String getBaseUrl(String serviceName) {
        try {
            return scmConfigRegistry.getBaseUrl(serviceName);
        } catch (Exception e) {
            log.error("Error getting base URL for service {} from Scm registry: {}",
                serviceName, e.getMessage());
            return null;
        }
    }
    
    @Override
    public Map<String, String> getAllBaseUrls() {
        try {
            return scmConfigRegistry.getAllBaseUrls();
        } catch (Exception e) {
            log.error("Error getting all base URLs from Scm registry: {}", e.getMessage());
            return Map.of();
        }
    }
    
    @Override
    public void refresh() {
        try {
            scmConfigRegistry.refresh();
            log.info("Juno configuration refreshed successfully");
        } catch (Exception e) {
            log.error("Error refreshing Scm configuration: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isInitialized() {
        return scmConfigRegistry != null;
    }
    
    @Override
    public String getProviderName() {
        return "ScmConfigProvider";
    }
    
    /**
     * Gets the underlying Scm registry for direct access if needed
     *
     * @return The JunoConfigRegistry instance
     */
    public ScmConfigRegistry getJunoRegistry() {
        return scmConfigRegistry;
    }
}
