package com.lenskart.scm.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import org.bson.Document;

import java.util.List;

public class ScmMongoDbUtils {
    public static List<Document> getServiceabilityDetails(String pincode, String facility_code){
        return MongoDBQueryExecutor.find(
                "scm_cluster",
                "logistics",
                "serviceability",
                new Document("pincode", pincode).append("facility_code",facility_code)
        );
    }
}
