package com.lenskart.scm.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.scm.constants.ScmConstants;
import com.lenskart.scm.model.ScmOrderContext;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class ScmDbUtils {

    /**
     * Helper method to safely convert database result to string
     *
     * @param value The value from database query result
     * @param fieldName Name of the field for logging
     * @param identifier Identifier used in the query for logging
     * @return String value or null if the value is null
     */
    private static String safeToString(Object value, String fieldName, String identifier) {
        if (value == null) {
            log.warn("{} is null for identifier: {}", fieldName, identifier);
            return null;
        }
        return value.toString();
    }
    public static String getShipmentState(String incrementId) {
        Object shipmentStateValue = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_SHIPMENT_STATE,
                        incrementId).getFirst().get("shipment_state");

        if (shipmentStateValue == null) {
            log.warn("Shipment state is null for increment ID: {}", incrementId);
            return null;
        }

        return shipmentStateValue.toString();
    }

    public static List<Map<String, Object>> getShippingPackageId(String incrementId) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_SHIPPING_PACKAGE_ID,
                        incrementId);
    }

    public static List<Map<String, Object>> getOrderStatus(String incrementId,String productID) {
        List<Map<String, Object>> orderStatus = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.ORDER_STATUS,
                        incrementId,productID) ;
        return orderStatus;
    }

    public static List<Map<String, Object>> getAWBNumber(String incrementId) {
        List<Map<String, Object>> awbNo = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_AWB_NUMBER,
                        incrementId) ;
        return awbNo;
    }


    public static int updateCarrierCode(String carrierCode,String incrementId) {
        return MySQLQueryExecutor
                .executeUpdate(Cluster.SCM_CLUSTER.getClusterName(),
                        ScmConstants.INVENTORY_DB,
                        ScmQueries.UPDATE_CARRIER_CODE,
                        carrierCode, incrementId);
    }

    public static List<Map<String, Object>> getReferenceId(String shippingPackageId) {
        return  MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_REFRENCE_ID,
                        shippingPackageId);
    }

    public static List<Map<String, Object>> getBillingAddress(String orderID) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_BILLING_ADDRESS,
                        orderID);
    }

    public static List<Map<String, Object>> getShippingAddress(String orderID) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_SHIPPING_ADDRESS,
                        orderID);
    }

    public static List<Map<String, Object>> getDispatchedShipment() {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_DISPATCHED_SHIPMENT);
    }

    public static List<Map<String, Object>> getOMSOrderDetails(String unicomOrderCode) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_OMS_ORDERS,
                        unicomOrderCode);
    }

    public static List<Map<String, Object>> getShipmentDetailsForSensei(String unicomOrderCode) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(), ScmConstants.ORDER_SENSEI_DB,
                        ScmQueries.GET_SHIPMENT_DETAILS_FOR_SENSEI,
                        unicomOrderCode);
    }

    public static String getMagentoItemId(String shippingPackageId) {
        Object magentoItemIdValue = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_MAGENTO_ITEM_ID,
                        shippingPackageId).getFirst().get("magento_item_id");

        return safeToString(magentoItemIdValue, "magento_item_id", shippingPackageId);
    }

    public static List<Map<String, Object>> getShippingIdForShippingCharges() {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_SHIPPING_ID_FOR_SHIPPING_CHARGES);
    }

    public static List<Map<String, Object>> getVirtualShipmentCancelTasks(String incrementId) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_VIRTUAL_SHIPMENT_CANCEL_TASKS,
                        incrementId);
    }


    // Get auto replenishment order ID from POS.bulk_order
    public static List<Map<String, Object>> getAutoReplenishmentOrderId(String facilityCode, String productId) {
        return MySQLQueryExecutor.executeQuery(Cluster.SCM_CLUSTER.getClusterName(),
                ScmConstants.INVENTORY_DB,
                ScmQueries.GET_AUTO_REPLENISHMENT_ORDER_ID,
                facilityCode, productId);
    }

    // Get entity_value and shipping_package_id using increment_id
    public static List<Map<String, Object>> getEntityValueAndPackageId(String incrementId) {
        return MySQLQueryExecutor.executeQuery(Cluster.SCM_CLUSTER.getClusterName(),
                ScmConstants.INVENTORY_DB,
                ScmQueries.GET_ENTITY_VALUE_AND_PACKAGE_ID,
                incrementId);
    }

    public static List<Map<String, Object>> getUniOrderLogDetails(String incrementId) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_UNIORDER_LOG_DETAILS,
                        incrementId);
    }
    /* please don't run this query on production */
    public static int updateCreatedAtOnUworders(ScmOrderContext scmOrderContext) {
        return MySQLQueryExecutor
                .executeUpdate(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.UPDATE_CREATED_AT_ON_UW_ODERS,
                        scmOrderContext.getIncrementId());
    }

    public static List<Map<String, Object>> getLast10DayPIDs() {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_LAST_10DAYS_PIDS);
    }

    public static List<Map<String, Object>> getQuantityFromStockAdjustment(String pid) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_LAST_STOCK_ADJUSTMENT_QTY,
                        pid);
    }
    public static List<Map<String, Object>> getPowerChangeEventStatus(ScmOrderContext scmOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_POWER_CHANGED_EVENT_STATUS,
                        scmOrderContext.getIncrementId());
    }
    public static List<Map<String, Object>> getUWorderDetails(String unicom_order_code) {
        return MySQLQueryExecutor.executeQuery(
                Cluster.SCM_CLUSTER.getClusterName(),
                ScmConstants.INVENTORY_DB,
                ScmQueries.GET_UW_ORDER_DETAILS,
                unicom_order_code
        );
    }
    public static List<Map<String, Object>> getUWitemwiseDetails(String uw_item_id) {
        return MySQLQueryExecutor.executeQuery(
                Cluster.SCM_CLUSTER.getClusterName(),
                ScmConstants.INVENTORY_DB,
                ScmQueries.GET_UW_ITEM_DETAILS,
                uw_item_id
        );
    }
    public static List<Map<String, Object>> getProductDetails(String product_id) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.HSN_CLASSIFICATION,product_id);
    }
    public static List<Map<String, Object>> getHubcode(String item_id) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_HUB_CODE,item_id);
    }
    public static String getRightLens(String item_id) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.RIGHT_LENS,item_id).getFirst().get("right_lens").toString();
    }
    public static List<Map<String, Object>> getNavChannel(String unicomOrderCode) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_UW_ORDER_DETAILS,unicomOrderCode);
    }
    public static List<Map<String, Object>> getDetailsOrderAddress(String item_id) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_PINCODE,item_id);
    }
    public static String getShipmentDetailsOptima(String wmsOrdercode) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(), ScmConstants.OPTIMA_DB,
                        ScmQueries.GET_FACILITY_SCORE,wmsOrdercode).getFirst().get("facility").toString();
    }
    public static String getIncrementId(String shippingpackageID) {
        Object incrementIdValue = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_INCREMENT_ID,
                        shippingpackageID).getFirst().get("increment_id");

        if (incrementIdValue == null) {
            log.warn("Increment ID is null for shipping package ID: {}", shippingpackageID);
            return null;
        }

        return incrementIdValue.toString();
    }
    public static String getJitFlag(String fittingId) {
        Object jitFlagValue = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_JIT_FLAG_FOR_FIITING_ID,
                        fittingId).getFirst().get("jit_flag");

        if (jitFlagValue == null) {
            log.warn("JIT flag is null for fitting ID: {}", fittingId);
            return "0"; // or return a default value like "0" or "false"
        }
        return safeToString(jitFlagValue, "jit_flag", fittingId);
    }
}
