package com.lenskart.scm.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.scm.constants.ScmConstants;

import java.util.List;
import java.util.Map;

public class FDSDbUtils {
    public static List<Map<String, Object>> getDocumentDetails(String shippingId) {
       return   MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(),
                        ScmConstants.FDS_DB,
                        FDSQueries.GET_DOCUMENT_DETAILS,
                        shippingId);

    }
    public static List<Map<String, Object>> getEInvoiceDetails(int id) {
        return  MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(),
                        ScmConstants.FDS_DB,
                        FDSQueries.GET_EINVOICE_DETAILS,
                        id);

    }

}
