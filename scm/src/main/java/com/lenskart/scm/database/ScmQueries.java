package com.lenskart.scm.database;

public class ScmQueries {

    //manifest table Queries
    public static final String GET_MANIFEST_ID = "select shipping_manifest_id from manifest.manifest_shipments";

    //uw_orders table Queries
    public static final String GET_DETAILS_TO_SYNC_ORDER = "select distinct (`unicom_order_code`), `shipment_status`, unicom_syn_status, `shipping_package_id`, `nav_channel`,`facility_code` from uw_orders where `increment_id`=?";
    public static final String GET_SHIPMENT_STATE = "select distinct shipment_state from uw_orders where increment_id=?";
    public static final String GET_SHIPPING_PACKAGE_ID = "select distinct shipping_package_id from uw_orders where increment_id=?";
    public static final String ORDER_STATUS = "select shipment_status from uw_orders where increment_id=? and product_id=?";
    public static final String GET_REFRENCE_ID = "SELECT o.* FROM orders o JOIN (SELECT item_id FROM uw_orders WHERE shipping_package_id =? AND parent_uw = '0' ORDER BY uw_item_id ASC LIMIT 1) u ON o.item_id = u.item_id ";
    public static final String GET_SHIPMENT_STATUS = "select shipment_status from uw_orders where =?";
    public static final String GET_DISPATCHED_SHIPMENT = "select * from uw_orders where unicom_shipment_status ='DISPATCHED'  and product_delivery_type != 'OTC' order by uw_item_id desc limit 1 ";
    public static final String GET_SHIPPING_ID_FOR_SHIPPING_CHARGES = "SELECT u.`shipping_package_id`, i.`shipping_charges` FROM item_wise_prices i JOIN uw_orders u ON i.item_id = u.item_id WHERE i.shipping_charges != 0 AND u.unicom_shipment_status = 'DISPATCHED' and u.facility_code in ('NXS2', 'QNXS2') ORDER BY i.id DESC limit 1";
    public static final String GET_UW_ORDER_DETAILS ="select * from inventory.uw_orders where unicom_order_code=?";
    public static final String GET_UW_ITEM_DETAILS ="select * from inventory.uw_orders where uw_item_id=?";
    public static final String navChannel ="select distinct navChannel from inventory.uw_orders where unicom_order_code=?";
    public static final String GET_INCREMENT_ID ="select distinct increment_id  from uw_orders where shipping_package_id=?";

    //  public static final String GET_LAST_10DAYS_PIDS = "SELECT oh.lk_country, GROUP_CONCAT(DISTINCT uo.product_id) AS distinct_product_ids FROM orders_header oh JOIN (SELECT DISTINCT product_id, increment_id FROM uw_orders WHERE product_id NOT IN ('19873','19872','128269','303961','81734686') AND created_at > NOW() - INTERVAL 10 DAY) uo ON oh.increment_id = uo.increment_id GROUP BY oh.lk_country;";
    public static final String GET_LAST_10DAYS_PIDS = "SELECT oh.lk_country, GROUP_CONCAT(DISTINCT uo.product_id) AS distinct_product_ids FROM orders_header oh JOIN (SELECT DISTINCT product_id, increment_id FROM uw_orders WHERE product_id NOT IN ('19873','19872','128269','303961','72073648','228197434','70018455') AND LENGTH(product_id) > 6 AND created_at > NOW() - INTERVAL 3 DAY) uo ON oh.increment_id = uo.increment_id GROUP BY oh.lk_country;";
    //order_address_update table Queries
    public static final String GET_BILLING_ADDRESS =  "select * from order_address_update where parent_id=? and address_type ='billing'" ;
    public static final String GET_SHIPPING_ADDRESS =  "select * from order_address_update where parent_id=? and address_type ='shipping'" ;
    public static final String GET_AWB_NUMBER =  "select tracking_no from shipping_status where order_no=?" ;

    public static final String UPDATE_CARRIER_CODE =  "UPDATE shipping_status SET carrier_code =? WHERE order_no=?;" ;

    //OMS table Queries
    public static final String GET_OMS_ORDERS = "select * from oms_orders where unicom_order_code=?";

    //Order Sensi table queries
    public static final String GET_SHIPMENT_DETAILS_FOR_SENSEI = " select * from order_sensei.shipment where `wms_order_code`=?";

    //Orders table Queries
    public static final String GET_MAGENTO_ITEM_ID = "SELECT o.magento_item_id FROM orders o JOIN uw_orders u ON o.item_id = u.item_id WHERE u.shipping_package_id =? LIMIT 1;";
    public static final String GET_HUB_CODE = "select hub_code from inventory.orders where item_id=?";

    //virtual_shipment_cancel_tasks table
    public static final String GET_VIRTUAL_SHIPMENT_CANCEL_TASKS = "select * from inventory.virtual_shipment_cancel_tasks where `increment_id`=?";

    public static final String GET_AUTO_REPLENISHMENT_ORDER_ID = "select deals_order_id from POS.bulk_order where uploaded_file LIKE '%auto%' and facility_code = ? and status = 'SUCCESS' and product_id = ? ORDER BY deals_order_id DESC LIMIT 1";
    public static final String GET_ENTITY_VALUE_AND_PACKAGE_ID = "select omd.entity_value, uo.shipping_package_id from inventory.uw_orders uo join inventory.orders_meta_data omd ON uo.item_id = omd.item_id where omd.entity_key = 'ORDER_PRIORITY_TYPE' and uo.increment_id = ?";

    //uniorder_log table
    public static final String GET_UNIORDER_LOG_DETAILS = "select * from inventory.uniorder_log where `increment_id`=?";



//product table
    public static final String HSN_CLASSIFICATION="select * from inventory.products where product_id=?;";

    public static final String RIGHT_LENS ="select right_lens from inventory.custom_options where item_id=?;";

    public static final String GET_PINCODE ="select * from inventory.order_address_update where parent_id=? and address_type='shipping';";

    //optima table
    public static final String GET_FACILITY_SCORE ="select * from optimadb.shipment_details where shipment_id=?;";


    //updating created at for Manul_jit order
    //not please don't run on prod
    public static final String UPDATE_CREATED_AT_ON_UW_ODERS = "UPDATE uw_orders  SET created_at = (NOW() - INTERVAL 6 HOUR) where increment_id=? ";

    //stock_adjustment_update table
    public static final String GET_LAST_STOCK_ADJUSTMENT_QTY = "select qty from inventory.stock_adjustment_update where product_id=? order by created_at desc limit 1";

    //sync_unicom_status table
    public static final String GET_POWER_CHANGED_EVENT_STATUS = "select * from sync_unicom_status where  unicom_status='powerChanged' and increment_id=?  ORDER BY created_at desc limit 1 ;";



}
