package com.lenskart.scm.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.scm.constants.ScmConstants;
import com.lenskart.scm.model.ScmOrderContext;

import java.util.List;
import java.util.Map;


public class LogisticsDbUtils {

    public static List<Map<String, Object>> getCourierShipmentDetails(ScmOrderContext scmOrderContext)  {
        return MySQLQueryExecutor.executeQuery(Cluster.SCM_CLUSTER.getClusterName(),
                ScmConstants.LOGISTICS_DB,
                LogisticsDbQueries.GET_COURIER_SHIPMENT_DETAILS,
                scmOrderContext.getIncrementId());
    }
}
