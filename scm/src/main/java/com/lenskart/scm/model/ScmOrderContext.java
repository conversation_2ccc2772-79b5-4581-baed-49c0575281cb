package com.lenskart.scm.model;

import com.lenskart.oms.dto.DistributorCustomerDetailsDto;
import lombok.Builder;
import lombok.Data;
import java.util.List;

@Data
@Builder
public class ScmOrderContext {
    String shippingId;
    String code;
    String codeDescription;
    String city;
    String eddSource;
    String incrementId;
    String version;
    String magentoItemId;
    String status;
    String state;
    String uwItemId;
    String orderId;
    String source;
    int hsnCode;
    String powerType;
    String stateCode;
    double totalValue;
    String courierCode;
    String packetId;
    @Builder.Default
    Boolean isValidationRequired = false;
    String unicomOrderCode;
    float shippingCharges;
    @Builder.Default
    String pincode = "121004";
    String poNumber;
    String customerCode;
    String doType;
    String userName;
    String filePath;
    String facilityCode;
    @Builder.Default
    String suplierID="pw_orders_gkb";
    String jitInvoiceNo;
    List<String> barcodes;
    @Builder.Default
    String supplierJitViewName="GKB HITECH LENSES PVT LTD";
    @Builder.Default
    Boolean isQcFailOrder=false;
    DistributorCustomerDetailsDto distributorCustomerDetailsDto;
    String returnFacility;
    String qcStatus;
    String qcFailReason;
    String returnReason;
    String returnType;
    String scannedBarcode;

    @Builder
    @Data
    public static class LsmServicibility {
        private Boolean is_reverse_pickup;
        private Boolean is_express;
        private String facility_code;
        private Boolean is_cod;
        private Boolean is_prepaid;
        private Boolean is_liquid;
        private Boolean is_enabled;
    }

}
