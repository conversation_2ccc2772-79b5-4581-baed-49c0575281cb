package com.lenskart.scm.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Tracking {

    @JsonProperty("carrier_moniker")
    private String carrierMoniker;
    @JsonProperty("tracking_number")
    private String trackingNumber;
    @JsonProperty("edd_source")
    private String eddSource;
    @JsonProperty("events")
    private List<Events> events;


}
