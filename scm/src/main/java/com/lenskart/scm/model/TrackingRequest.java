package com.lenskart.scm.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TrackingRequest {

    @JsonProperty("version")
    private String version;
    @JsonProperty("published_date")
    private String publishedDate;
    @JsonProperty("order_number")
    private String orderNumber;
    @JsonProperty("uid")
    private String uid;
    @JsonProperty("tracking")
    private List<Tracking> tracking;


}
