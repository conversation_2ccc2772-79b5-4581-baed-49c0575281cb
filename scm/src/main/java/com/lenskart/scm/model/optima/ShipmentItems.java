package com.lenskart.scm.model.optima;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShipmentItems {
    public int itemId;
    public int productId;
    public String itemType;
    public String navChannel;
    public int fittingId;
}
