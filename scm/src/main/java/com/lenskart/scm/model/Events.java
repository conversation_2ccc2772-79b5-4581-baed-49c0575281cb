package com.lenskart.scm.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Events {

    @JsonProperty("date")
    private String date;
    @JsonProperty("carrier_moniker")
    private String carrierMoniker;
    @JsonProperty("status_code_mapping")
    private StatusCodeMapping statusCodeMapping;
    @JsonProperty("location")
    private Location location;

    @Builder
    @Data
    public static class Location {
        @JsonProperty("city")
        private String city;
    }

    @Builder
    @Data
    public static class StatusCodeMapping {

        @JsonProperty("code")
        private String code;
        @JsonProperty("code_description")
        private String codeDescription;
    }

}
