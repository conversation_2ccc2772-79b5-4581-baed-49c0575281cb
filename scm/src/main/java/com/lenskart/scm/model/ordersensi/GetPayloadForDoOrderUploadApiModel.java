package com.lenskart.scm.model.ordersensi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetPayloadForDoOrderUploadApiModel {
    private String filePath;
    private String facilityCode;
    private String poNumber;
    private String customerCode;
    private String doType;
    private String userName;
}
