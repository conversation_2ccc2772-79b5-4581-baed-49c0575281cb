package com.lenskart.scm.model.optima;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OptimaRequest {
        public String wmsOrderCode;
        public String shipmentType;
        public String pincode;
        public String hubCode;
        List<ShipmentItems> shipmentItems;

    }

