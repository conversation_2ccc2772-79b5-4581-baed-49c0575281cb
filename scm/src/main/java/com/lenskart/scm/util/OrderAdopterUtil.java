package com.lenskart.scm.util;

import com.lenskart.commons.constants.Constants;
import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.constants.ScmConstants;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.database.ScmQueries;
import com.lenskart.scm.helpers.OrderSyncHealthCheckHelper;
import com.lenskart.scm.helpers.orderadopter.AsyncOrderHelper;
import com.lenskart.scm.helpers.orderops.OrderSyncHelper;
import com.lenskart.scm.helpers.orderopsscheduler.PublishToWhReadyHelper;
import com.lenskart.scm.helpers.ordersensi.ProcessOTCShipmentEventHelper;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.List;
import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.SYNC_ORDER;

@Slf4j
public class OrderAdopterUtil {


    public static void syncOrder(String incrementId) {
        AwaitUtils.sleep(Duration.ofSeconds(20));
        OrderSyncHealthCheckHelper.builder().build().test();
        boolean success = AwaitUtils.retryOperation(() -> {
            try {
                log.debug("Attempting to sync order for increment id: {}", incrementId);

                List<Map<String, Object>> syncOrderDetails = MySQLQueryExecutor.executeQuery(
                        Cluster.SCM_CLUSTER.getClusterName(),
                        ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_DETAILS_TO_SYNC_ORDER,
                        incrementId
                );

                if (syncOrderDetails.isEmpty()) {
                    throw new RuntimeException("Order is not created in SCM");
                }

                for (Map<String, Object> map : syncOrderDetails) {
                    String unicomOrderCode = String.valueOf(map.get("unicom_order_code"));
                    String facilityCode = String.valueOf(map.get("facility_code"));
                    String navChannel = String.valueOf(map.get("nav_channel")).toLowerCase();
                    String shippingPackageId = String.valueOf(map.get("shipping_package_id"));
                    String unicomSyncStatus = String.valueOf(map.get("unicom_syn_status"));
                    String shipmentStatus = String.valueOf(map.get("shipment_status"));

               //     if (navChannel.contains("b2b") && !Constants.WAREHOUSE_FACILITIES.contains(facilityCode)) {
                    if (!Constants.WAREHOUSE_FACILITIES.contains(facilityCode)) {
                        log.info("Facility code {} is not in allowed facilities, skipping", facilityCode);
                        continue;
                    }

                    if (!shippingPackageId.isEmpty()) {
                        continue; // skip orders with shipping package
                    }

                    if (ScmConstants.READY_TO_WH.equals(unicomSyncStatus)) {
                        PublishToWhReadyHelper.builder().incrementId(incrementId).build().test();
                        AwaitUtils.sleepSeconds(3);
                        syncOrderDetails = MySQLQueryExecutor.executeQuery(
                                Cluster.SCM_CLUSTER.getClusterName(),
                                ScmConstants.INVENTORY_DB,
                                ScmQueries.GET_DETAILS_TO_SYNC_ORDER,
                                incrementId
                        );
                        unicomSyncStatus = String.valueOf(syncOrderDetails.getFirst().get("unicom_syn_status"));
                        shipmentStatus = String.valueOf(syncOrderDetails.getFirst().get("shipment_status"));
                        log.info("unicomSyncStatus {}", unicomSyncStatus);
                    }
                    log.info("unicomSyncStatus {}", unicomSyncStatus);
                    log.info("ShipmentStatus {}", shipmentStatus);
                    if (!ScmConstants.READY_TO_SYNC.equals(unicomSyncStatus)
                            || ScmConstants.READY_TO_WH.equals(unicomSyncStatus)) {
                        List<Map<String, Object>> uniOrderLogDetails = ScmDbUtils.getUniOrderLogDetails(incrementId);
                        if (!uniOrderLogDetails.isEmpty()) {
                            throw new RuntimeException(String.format(
                                    "Order is not in ready to sync, success status = %s, response = %s",
                                    uniOrderLogDetails.getFirst().get("success_status"),
                                    uniOrderLogDetails.getFirst().get("response")
                            ));
                        }
//                        else {
//                            throw new RuntimeException(String.format(
//                                    "Order is not in ready to sync, status = %s, order status = %s",
//                                    unicomSyncStatus,
//                                    shipmentStatus
//                            ));
//                        }
                    }


                    List<Map<String, Object>> omsDetails = ScmDbUtils.getOMSOrderDetails(unicomOrderCode);
                    boolean isOMSOrder = !omsDetails.isEmpty()
                            && Boolean.TRUE.equals(omsDetails.getFirst().get("is_enabled"));

                    if (isOMSOrder) {
                        if (navChannel.contains("otc")) {
                            log.info("Order is OTC, calling OTC shipment event helper");
                            List<Map<String, Object>> shipmentDetails = ScmDbUtils.getShipmentDetailsForSensei(unicomOrderCode);
                            if (shipmentDetails.isEmpty()) {
                                throw new RuntimeException("No shipment found for unicom order: " + unicomOrderCode);
                            }
                            Object shipmentId = shipmentDetails.getFirst().get("id");

                            ProcessOTCShipmentEventHelper.builder()
                                    .shipmentId(shipmentId)
                                    .eventType(ScmConstants.INVOICED)
                                    .build()
                                    .test();
                        } else {
                            AsyncOrderHelper.builder()
                                    .unicomOrderCode(unicomOrderCode)
                                    .build()
                                    .test();
                        }
                    } else if (!isOMSOrder) {
                        AsyncOrderHelper.builder()
                            .unicomOrderCode(unicomOrderCode)
                            .build()
                            .test();
                        AwaitUtils.sleepSeconds(5);

                    } else {
                        OrderSyncHelper.builder().incrementId(incrementId).build().test();
                        AwaitUtils.sleepSeconds(3);
                        Map<String, Object> queryParam = Map.of("uniOrderCode", unicomOrderCode);
                        RestUtils.get(SYNC_ORDER.getUrl(), null, queryParam, 200);
                    }
                }
                    AwaitUtils.sleepSeconds(13);
                List<Map<String, Object>> verifySyncDetails = MySQLQueryExecutor.executeQuery(
                        Cluster.SCM_CLUSTER.getClusterName(),
                        ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_DETAILS_TO_SYNC_ORDER,
                        incrementId
                );

                for (Map<String, Object> map : verifySyncDetails) {
                    String syncStatus = String.valueOf(map.get("unicom_syn_status"));
                    String navChannel = String.valueOf(map.get("nav_channel")).toLowerCase();
                    String facilityCode = String.valueOf(map.get("facility_code"));
                    String shippingPackageId = String.valueOf(map.get("shipping_package_id"));
                    //       if (navChannel.contains("b2b") && !Constants.WAREHOUSE_FACILITIES.contains(facilityCode)) {
                    if (!Constants.WAREHOUSE_FACILITIES.contains(facilityCode)) {
                        log.info("Facility code {} is not in allowed facilities, skipping", facilityCode);
                        continue;
                    }
                    if(shippingPackageId.isEmpty()) {
             //           throw new RuntimeException("Shipment package id is empty for this order");
                        log.info("Shipment package id is empty for this order {}",incrementId);
                    }

                    if (!ScmConstants.SYNC_SUCCESS.equalsIgnoreCase(syncStatus)) {
                        log.warn("Order not yet synced. Current sync status: {}", syncStatus);
                        return false;  // trigger retry
                    }
                }
                log.info("✅ Order successfully synced for increment id: {}", incrementId);
                return true;

            } catch (Exception e) {
                log.warn("Exception during sync order attempt: {}", e.getMessage());
                return false;
            }
        }, "Sync Order Retry", 2, Duration.ofSeconds(15));

        if (!success) {
            log.error("❌ Sync Order failed after polling timeout for increment id: {}", incrementId);
            throw new RuntimeException("Sync Order failed after multiple attempts for increment id: " + incrementId);
        }
    }
}