package com.lenskart.scm.validator.lsm;

import com.lenskart.commons.base.IValidator;
import com.lenskart.scm.database.LogisticsDbUtils;
import com.lenskart.scm.model.ScmOrderContext;
import lombok.experimental.SuperBuilder;
import org.testng.Assert;
import java.util.List;
import java.util.Map;

@SuperBuilder
public class ChangeCourierValidator implements IValidator {
    ScmOrderContext scmOrderContext;
    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        if (scmOrderContext.getIsValidationRequired()) {
            List<Map<String, Object>> courierShipmentDetails = LogisticsDbUtils.getCourierShipmentDetails(scmOrderContext);
            if (courierShipmentDetails.isEmpty()) {
                Assert.fail("No courier shipment details found for the order");
            } else {
                String courrierCode = courierShipmentDetails.getFirst().get("courier_code").toString();
                Assert.assertTrue(
                        courrierCode.equalsIgnoreCase(scmOrderContext.getCourierCode()),
                        "Actual value '" + courrierCode + " ' did not match any expected courier code "
                );
            }
        }
    }
}
