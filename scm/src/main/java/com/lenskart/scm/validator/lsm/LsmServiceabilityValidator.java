package com.lenskart.scm.validator.lsm;

import com.lenskart.commons.base.IValidator;
import com.lenskart.scm.database.ScmMongoDbUtils;
import com.lenskart.scm.helpers.lsm.LsmServiceabilityHelper;
import com.lenskart.scm.model.ScmOrderContext;
import io.restassured.response.Response;
import lombok.Builder;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.json.JSONArray;
import org.testng.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Builder
@Slf4j
public class LsmServiceabilityValidator implements IValidator {
    ScmOrderContext scmOrderContext;
    ScmOrderContext.LsmServicibility lsmServicibility;
    LsmServiceabilityHelper lsmServiceabilityHelper;
    Response response;
    List<Map<String, Object>>  resultList;

    @Override
    public void validateNode() {
         response = lsmServiceabilityHelper.getResponse();
        resultList = response.jsonPath().getList("result");
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
       if(scmOrderContext.getIsValidationRequired()){
           List<Document> data = ScmMongoDbUtils.getServiceabilityDetails(scmOrderContext.getPincode(), lsmServicibility.getFacility_code());

           Assert.assertEquals(data.size(), resultList.size(), "Mismatch in list sizes between Mongo and API");

           for (int i = 0; i < data.size(); i++) {
               Document mongoDoc = data.get(i);
               Map<String, Object> apiData = resultList.get(i);

               Assert.assertEquals(
                       apiData.get("courier_code"),
                       mongoDoc.get("courier_code"),
                       "Mismatch in courier_code at index " + i
               );

               Assert.assertEquals(
                       apiData.get("priority"),
                       mongoDoc.get("priority"),
                       "Mismatch in priority at index " + i
               );

               Assert.assertEquals(
                       apiData.get("facility_code"),
                       mongoDoc.get("facility_code"),
                       "Mismatch in facility_code at index " + i
               );
           }

       }
    }
}
