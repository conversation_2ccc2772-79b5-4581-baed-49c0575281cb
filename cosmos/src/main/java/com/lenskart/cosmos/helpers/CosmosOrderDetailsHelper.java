package com.lenskart.cosmos.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import lombok.Getter;
import lombok.experimental.SuperBuilder;


@Getter
@SuperBuilder
public class CosmosOrderDetailsHelper extends CosmosBaseHelper implements ExecutionHelper {

    OrderContext orderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        /* Authenticate User */
        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Get Order Details */
        GetOrderDetailsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
