package com.lenskart.cosmos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cosmos.config.CosmosConfig;
import com.lenskart.cosmos.config.CosmosConfigRegistry;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.commons.constants.Constants.BEARER_TOKEN;
import static com.lenskart.cosmos.endpoints.CosmosEndpoints.CREATE_AUTH;


@SuperBuilder
@Slf4j
public class AuthenticationHelper extends CosmosBaseHelper implements ServiceHelper {

    Response response;
    OrderContext orderContext;
    CosmosConfigRegistry configRegistry;
    OrderContext.Headers orderContextHeader;

    @Override
    public ServiceHelper init() {
        configRegistry = CosmosConfigRegistry.getInstance();
        CosmosConfig.AuthConfig authConfig = configRegistry.getAuth();
        headers = getHeaders(authConfig);
        orderContextHeader = orderContext.getHeaders();
        return this;
    }

    @Override
    public ServiceHelper process() {

        /* Fetch session Token */
        response = RestUtils.post(CREATE_AUTH.getUrl(), headers, null, 200);
        orderContextHeader.setCosmosAuthToken(BEARER_TOKEN.concat((String) RestUtils.getValueFromResponse(response, "result.access_token")));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
