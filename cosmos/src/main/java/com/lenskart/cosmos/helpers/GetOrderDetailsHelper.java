package com.lenskart.cosmos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.osm.cosmos.utils.models.dto.OrderStateDto;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.commons.constants.Constants.RESULT;
import static com.lenskart.cosmos.endpoints.CosmosEndpoints.GET_ORDER_BY_ID;

@SuperBuilder
@Getter
public class GetOrderDetailsHelper extends CosmosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    OrderStateDto orderStateDto;

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeaders(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {

        response = RestUtils.get(GET_ORDER_BY_ID.getUrl(Map.of("orderId", String.valueOf(orderContext.getOrderId()))),
                headers, null, 200);
        orderStateDto = parseResponse(RestUtils.getValueFromResponse(response, RESULT), OrderStateDto.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
