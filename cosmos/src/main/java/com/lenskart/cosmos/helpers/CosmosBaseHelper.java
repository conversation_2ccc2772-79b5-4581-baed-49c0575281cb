package com.lenskart.cosmos.helpers;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cosmos.config.CosmosConfig;
import com.lenskart.cosmos.exceptions.CosmosExceptionStates;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;


@SuperBuilder
@Slf4j
public class CosmosBaseHelper extends BaseHelper<CosmosExceptionStates, Object> {

    public Map<String, String> getHeaders(CosmosConfig.AuthConfig authConfig) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_AUTH_TOKEN.getHeaderName(), authConfig.getDefaultToken());
        return headers;
    }

    public Map<String, String> getHeaders(OrderContext orderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_AUTH_TOKEN.getHeaderName(), orderContext.getHeaders().getCosmosAuthToken());
        return headers;
    }

}
