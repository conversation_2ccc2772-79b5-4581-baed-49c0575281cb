package com.lenskart.cosmos.exceptions;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum containing exception states and error codes for the Cosmos module
 */
@AllArgsConstructor
@Getter
public enum CosmosExceptionStates {
    
    // Configuration related exceptions
    CONFIG_NOT_FOUND("COSMOS_CONFIG_001", "Configuration not found", "Cosmos configuration file or environment config not found"),
    CONFIG_LOAD_FAILED("COSMOS_CONFIG_002", "Configuration load failed", "Failed to load cosmos configuration from file"),
    INVALID_ENVIRONMENT("COSMOS_CONFIG_003", "Invalid environment", "Specified environment configuration not found");

    
    private final String errorCode;
    private final String errorMessage;
    private final String description;
}
