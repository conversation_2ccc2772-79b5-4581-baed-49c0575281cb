package com.lenskart.cosmos.config;

import com.lenskart.commons.config.ConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Configuration provider for Cosmos module that wraps CosmosConfigRegistry
 * to implement the standard ConfigProvider interface.
 */
@Slf4j
public class CosmosConfigProvider implements ConfigProvider {
    
    private final CosmosConfigRegistry cosmosRegistry;
    
    // Singleton instance
    private static volatile CosmosConfigProvider instance;
    
    /**
     * Private constructor for singleton pattern
     */
    private CosmosConfigProvider() {
        this.cosmosRegistry = CosmosConfigRegistry.getInstance();
    }
    
    /**
     * Gets the singleton instance of CosmosConfigProvider
     *
     * @return The singleton instance
     */
    public static CosmosConfigProvider getInstance() {
        if (instance == null) {
            synchronized (CosmosConfigProvider.class) {
                if (instance == null) {
                    instance = new CosmosConfigProvider();
                }
            }
        }
        return instance;
    }
    
    @Override
    public String getBaseUrl(String serviceName) {
        try {
            return cosmosRegistry.getBaseUrl(serviceName);
        } catch (Exception e) {
            log.error("Error getting base URL for service {} from Cosmos registry: {}", 
                serviceName, e.getMessage());
            return null;
        }
    }
    
    @Override
    public Map<String, String> getAllBaseUrls() {
        try {
            return cosmosRegistry.getAllBaseUrls();
        } catch (Exception e) {
            log.error("Error getting all base URLs from Cosmos registry: {}", e.getMessage());
            return Map.of();
        }
    }
    
    @Override
    public void refresh() {
        try {
            cosmosRegistry.refresh();
            log.info("Cosmos configuration refreshed successfully");
        } catch (Exception e) {
            log.error("Error refreshing Cosmos configuration: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isInitialized() {
        return cosmosRegistry != null;
    }
    
    @Override
    public String getProviderName() {
        return "CosmosConfigProvider";
    }
    
    /**
     * Gets the underlying Cosmos registry for direct access if needed
     *
     * @return The CosmosConfigRegistry instance
     */
    public CosmosConfigRegistry getCosmosRegistry() {
        return cosmosRegistry;
    }
}
