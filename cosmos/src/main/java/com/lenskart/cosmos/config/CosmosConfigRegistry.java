package com.lenskart.cosmos.config;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Central registry for Cosmos configurations
 */
@Slf4j
public class CosmosConfigRegistry {
    // Singleton instance
    private static CosmosConfigRegistry instance;
    
    // Configuration caches
    private final Map<String, String> baseUrls = new ConcurrentHashMap<>();
    
    // Constants
    private static final String DEFAULT_ENVIRONMENT = "preprod";
    
    // Private constructor for singleton
    private CosmosConfigRegistry() {
        // Initialize configurations
        loadConfigurations();
    }
    
    /**
     * Get singleton instance
     * 
     * @return CosmosConfigRegistry instance
     */
    public static synchronized CosmosConfigRegistry getInstance() {
        if (instance == null) {
            instance = new CosmosConfigRegistry();
        }
        return instance;
    }
    
    /**
     * Load all configurations
     */
    private void loadConfigurations() {
        loadBaseUrls();
    }
    
    /**
     * Load base URLs for services
     */
    @SuppressWarnings("unchecked")
    private void loadBaseUrls() {
        try {
            // Load the Cosmos configuration
            CosmosConfig config = CosmosConfigLoader.loadConfig();
            
            // Get the environment configuration
            CosmosConfig.EnvironmentConfig envConfig = config.getEnvironment(DEFAULT_ENVIRONMENT);
            
            if (envConfig == null) {
                log.warn("Environment not found: {}", DEFAULT_ENVIRONMENT);
                return;
            }
            
            // Get the base URLs
            Map<String, String> urls = envConfig.getBaseUrls();
            
            if (urls == null || urls.isEmpty()) {
                log.warn("No base URLs found in environment: {}", DEFAULT_ENVIRONMENT);
                return;
            }
            
            // Store the base URLs
            baseUrls.putAll(urls);
            
            log.info("Loaded {} base URLs for Cosmos", baseUrls.size());
        } catch (Exception e) {
            log.error("Failed to load base URLs for Cosmos: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Get base URL for a service
     * 
     * @param serviceName Service name
     * @return Base URL
     */
    public String getBaseUrl(String serviceName) {
        return baseUrls.get(serviceName);
    }
    
    /**
     * Get all base URLs
     * 
     * @return Map of base URLs
     */
    public Map<String, String> getAllBaseUrls() {
        return new HashMap<>(baseUrls);
    }
    
    /**
     * Refresh all configurations
     */
    public synchronized void refresh() {
        // Clear caches
        baseUrls.clear();
        
        // Clear the config loader cache
        CosmosConfigLoader.reloadConfig();
        
        // Reload configurations
        loadConfigurations();
        
        log.info("All Cosmos configurations refreshed");
    }

    public CosmosConfig.AuthConfig getAuth() {
        try {
            // Load the Cosmos configuration
            CosmosConfig config = CosmosConfigLoader.loadConfig();

            // Get the environment configuration
            CosmosConfig.EnvironmentConfig envConfig = config.getEnvironment(DEFAULT_ENVIRONMENT);

            if (envConfig == null) {
                log.warn("Environment not found: {}", DEFAULT_ENVIRONMENT);
                return null;
            }

            // Get the auth list
            return envConfig.getAuth();
        } catch (Exception e) {
            log.error("Failed to load credentials: {}", e.getMessage(), e);
            return null;
        }
    }
}
