package com.lenskart.cosmos.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Enum containing all Cosmos service endpoints
 */
@Slf4j
@Getter
public enum CosmosEndpoints implements BaseEndpoint {

    // Stateview Service Endpoints

    GET_ORDER_BY_ID("/v1/order/{$orderId}", "stateViewService"),
    CREATE_AUTH("/draco/v1.0/users/auth/token", "junoService");

    private final String endpoint;
    private final String serviceName;

    /**
     * Constructor for CosmosEndpoints
     *
     * @param endpoint    Endpoint path
     * @param serviceName Name of the service (should match baseUrls in config)
     */
    CosmosEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    @Override
    public String getUrl() {
        return CosmosEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return CosmosEndpointManager.getEndpointUrl(this, pathParams);
    }

}
