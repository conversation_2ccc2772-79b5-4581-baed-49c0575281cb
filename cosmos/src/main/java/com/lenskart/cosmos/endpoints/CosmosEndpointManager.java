package com.lenskart.cosmos.endpoints;

import com.lenskart.commons.endpoints.EndpointManager;
import com.lenskart.cosmos.config.CosmosConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Endpoint manager for Cosmos module that handles URL generation and endpoint management
 */
@Slf4j
public class CosmosEndpointManager extends EndpointManager<CosmosEndpoints> {

    // Singleton instance
    private static volatile CosmosEndpointManager instance;

    /**
     * Private constructor for singleton pattern
     */
    private CosmosEndpointManager() {
        super(CosmosConfigProvider.getInstance(), CosmosEndpoints.class);
        log.info("CosmosEndpointManager initialized");
    }
    
    /**
     * Gets the singleton instance of CosmosEndpointManager
     *
     * @return The singleton instance
     */
    public static CosmosEndpointManager getInstance() {
        if (instance == null) {
            synchronized (CosmosEndpointManager.class) {
                if (instance == null) {
                    instance = new CosmosEndpointManager();
                }
            }
        }
        return instance;
    }

    /**
     * Convenience method to get URL for a Cosmos endpoint
     *
     * @param endpoint The Cosmos endpoint
     * @return Complete URL for the endpoint
     */
    public static String getEndpointUrl(CosmosEndpoints endpoint) {
        return getInstance().getUrl(endpoint);
    }

    /**
     * Convenience method to get URL for a Cosmos endpoint with path parameters
     *
     * @param endpoint The Cosmos endpoint
     * @param pathParams Map of path parameters to replace in the URL
     * @return Complete URL with path parameters replaced
     */
    public static String getEndpointUrl(CosmosEndpoints endpoint, Map<String, String> pathParams) {
        return getInstance().getUrl(endpoint, pathParams);
    }

}
