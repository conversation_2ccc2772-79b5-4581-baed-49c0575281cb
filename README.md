# BE Automation Framework

A comprehensive backend automation framework for running service-level and end-to-end automated functional tests across Juno, SCM, NEXS, CS (Customer Service), POS, Cosmos, and other Lenskart services.

## Overview

This framework provides a modular, maintainable, and extensible solution for testing backend services. It follows a multi-layered architecture with a strong emphasis on reusability and configurability. The framework is designed to support testing at different levels - from individual service APIs to complex end-to-end flows across multiple services.

### Architecture

The framework follows a layered architecture:

1. **Core Layer (Commons Module)**: Contains reusable components, utilities, and base classes
2. **Service Layer**: Service-specific modules with their own configurations and test implementations
3. **Integration Layer**: E2E module that orchestrates tests across multiple services
4. **Execution Layer**: Test runners, CI/CD integration, and reporting mechanisms

### Key Features

- **Modular Architecture**: Independent modules that can be used separately or together, allowing teams to work on different service tests simultaneously without conflicts

- **Reusable Components**: Common utilities extracted into a shared commons module, including:
  - Database connection managers with connection pooling
  - REST API utilities with request/response logging
  - Configuration management with environment-specific settings
  - Test execution helpers and base classes
  - Reporting and logging utilities

- **Design Patterns Implementation**:
  - <PERSON><PERSON> Pattern for configuration registries
  - <PERSON><PERSON><PERSON> <PERSON> for test data and request construction
  - Factory Pattern for database connections
  - Template Method Pattern for standardized test execution flow
  - Strategy Pattern for different validation approaches
  - Facade Pattern for simplifying complex subsystems

- **Flexible Configuration**: Environment-specific configuration through YAML files with:
  - Hierarchical configuration structure
  - Environment-specific overrides (preprod, prod)
  - Runtime configuration changes
  - Centralized configuration registry

- **Multi-Cluster Database Connectivity**: Advanced database support with multi-cluster architecture:
  - MySQL Multi-Cluster with dynamic connection management and SSH tunneling
  - MongoDB Multi-Cluster with dynamic query execution across clusters
  - Redis with connection pooling and key-value operations
  - Elasticsearch with high-level REST client and search capabilities

- **Comprehensive Reporting**: Rich HTML reporting with professional design:
  - Extent Reports with interactive dashboards, charts, and real-time updates
  - Mobile-friendly responsive design with dark/light themes
  - Detailed logging with module-specific log files
  - Automatic report archiving and email attachments in CI/CD

- **Test Categorization**: Hierarchical test organization:
  - By module (juno, scm, nexs, cs, pos, cosmos, e2e)
  - By category (SANITY, REGRESSION, E2E)
  - By functionality (using TestNG groups)
  - Custom filtering through TestNG listeners

- **CI/CD Integration**: Advanced Jenkins pipeline with rich notifications:
  - Environment selection (preprod, prod)
  - Test suite selection (by module or all modules)
  - Test category selection (by test type)
  - Rich HTML email notifications with test statistics and report attachments
  - Automatic artifact archiving and professional email design

## Project Structure

```
be-automation/
├── commons/                                # Core reusable components
│   ├── src/main/java/com/lenskart/commons/
│   │   ├── annotations/                   # Custom annotations for test categorization
│   │   ├── base/                          # Base classes and interfaces
│   │   ├── config/                        # Configuration classes (multi-cluster support)
│   │   ├── constants/                     # Constants, enums, and header mappings
│   │   ├── database/                      # Multi-cluster database connectivity
│   │   │   ├── mysql/                     # MySQL multi-cluster connection and queries
│   │   │   ├── mongodb/                   # MongoDB multi-cluster connection and queries
│   │   │   ├── redis/                     # Redis connection and operations
│   │   │   └── elasticsearch/             # Elasticsearch connection and operations
│   │   ├── endpoints/                     # Base endpoint interfaces and management
│   │   ├── examples/                      # Comprehensive usage examples
│   │   ├── listeners/                     # TestNG listeners for reporting and categorization
│   │   ├── loader/                        # Configuration loaders and registries
│   │   ├── model/                         # Common data models and enums
│   │   ├── reporting/                     # Extent Reports management
│   │   └── utils/                         # Utility classes (REST, JSON, Await, Generic)
│   ├── src/main/resources/                # Configuration files
│   └── README.md                          # Commons module documentation
│
├── juno/                                  # Juno service tests (Order Management)
│   ├── src/main/java/com/lenskart/juno/
│   │   ├── config/                        # Juno-specific configuration
│   │   ├── endpoints/                     # Juno API endpoints and endpoint manager
│   │   ├── exceptions/                    # Juno-specific exception states
│   │   ├── helpers/                       # Order creation, cart, payment helpers
│   │   ├── requestbuilder/                # Request builders for Juno APIs
│   │   └── util/                          # Juno-specific utilities
│   ├── src/main/resources/                # juno.yml configuration
│   ├── src/test/java/com/lenskart/juno/   # Juno test classes
│   └── README.md                          # Juno module documentation
│
├── cosmos/                                # Cosmos service tests (Order State Management)
│   ├── src/main/java/com/lenskart/cosmos/
│   │   ├── config/                        # Cosmos-specific configuration
│   │   ├── endpoints/                     # Cosmos API endpoints and endpoint manager
│   │   ├── exceptions/                    # Cosmos-specific exception states
│   │   └── helpers/                       # Order state and details helpers
│   ├── src/main/resources/                # cosmos.yml configuration
│   ├── src/test/java/com/lenskart/cosmos/ # Cosmos test classes
│   └── README.md                          # Cosmos module documentation
│
├── pos/                                   # POS service tests (Point of Sale)
│   ├── src/main/java/com/lenskart/pos/
│   │   ├── config/                        # POS-specific configuration
│   │   ├── endpoints/                     # POS API endpoints and endpoint manager
│   │   ├── exceptions/                    # POS-specific exception states
│   │   ├── helpers/                       # POS order, barcode, cart helpers
│   │   │   └── order/                     # Order-specific helper classes
│   │   ├── model/                         # POS-specific data models
│   │   └── requestbuilders/               # Request builders for POS APIs
│   ├── src/main/resources/                # pos.yml configuration
│   ├── src/test/java/com/lenskart/pos/    # POS test classes
│   └── README.md                          # POS module documentation
│
├── scm/                                   # SCM service tests (Supply Chain Management)
│   ├── src/main/java/com/lenskart/scm/
│   │   ├── api/                           # SCM service API interfaces
│   │   ├── config/                        # SCM-specific configuration
│   │   ├── constants/                     # SCM-specific constants and enums
│   │   ├── database/                      # Database queries and operations
│   │   ├── endpoints/                     # SCM API endpoints and endpoint manager
│   │   ├── exceptions/                    # SCM-specific exception states
│   │   ├── helpers/                       # Cancellation, QC, adoption helpers
│   │   ├── model/                         # SCM-specific data models
│   │   ├── requestbuilders/               # Request builders for SCM APIs
│   │   └── util/                          # SCM-specific utility classes
│   ├── src/main/resources/                # scm.yml configuration
│   ├── src/test/java/com/lenskart/scm/    # SCM test classes
│   └── README.md                          # SCM module documentation
│
├── nexs/                                  # NEXS service tests (Warehouse Management)
│   ├── src/main/java/com/lenskart/nexs/
│   │   ├── api/                           # External service API interfaces
│   │   ├── base/                          # Base classes for state transitions
│   │   ├── config/                        # NEXS-specific configuration
│   │   ├── constants/                     # NEXS-specific constants and enums
│   │   ├── database/                      # Database queries and WMS operations
│   │   ├── endpoints/                     # NEXS API endpoints and endpoint manager
│   │   ├── examples/                      # Usage examples for NEXS operations
│   │   ├── exceptions/                    # NEXS-specific exception states
│   │   ├── helpers/                       # State transition and workflow helpers
│   │   │   └── state/                     # State-specific helper classes
│   │   ├── manager/                       # Order state transition managers
│   │   ├── model/                         # NEXS-specific data models
│   │   │   ├── fitting/                   # Fitting-related models
│   │   │   ├── ims/                       # IMS integration models
│   │   │   ├── orderqc/                   # Order QC models
│   │   │   ├── packing/                   # Packing operation models
│   │   │   └── picking/                   # Picking operation models
│   │   ├── requestBuilder/                # Request builders for NEXS APIs
│   │   └── requestbuilders/               # Additional request builders
│   ├── src/main/resources/                # nexs.yml configuration
│   ├── src/test/java/com/lenskart/nexs/   # NEXS test classes
│   └── README.md                          # NEXS module documentation
│
├── cs/                                    # CS service tests (Customer Service)
│   ├── src/main/java/com/lenskart/cs/
│   │   ├── config/                        # CS-specific configuration
│   │   ├── endpoints/                     # CS API endpoints and endpoint manager
│   │   ├── exceptions/                    # CS-specific exception states
│   │   ├── helpers/                       # RTO, support, refund helpers
│   │   │   └── rto/                       # RTO-specific helper classes
│   │   ├── model/                         # CS-specific data models
│   │   └── requestbuilders/               # Request builders for CS APIs
│   ├── src/main/resources/                # cs.yml configuration
│   ├── src/test/java/com/lenskart/cs/     # CS test classes
│   └── README.md                          # CS module documentation
│
├── e2e/                                   # End-to-end tests across services
│   ├── src/main/java/com/lenskart/e2e/
│   │   ├── helper/                        # E2E test helpers
│   │   └── validator/                     # E2E validators
│   ├── src/test/java/com/lenskart/e2e/    # E2E test classes
│   ├── src/test/resources/                # TestNG configuration
│   └── README.md                          # E2E module documentation
│
├── example/                               # Example implementation and demos
│   ├── src/main/java/com/lenskart/example/
│   ├── src/test/java/com/lenskart/example/
│   └── README.md                          # Example module documentation
│
├── docs/                                  # Documentation
│   └── TestCategories.md                  # Test categories documentation
│
├── Jenkinsfile                            # CI/CD pipeline definition
├── BE_Automation_Framework_Design.md      # Detailed design document
└── pom.xml                                # Maven project configuration
```

## Modules

### Commons
The foundational module providing shared utilities, configuration management, multi-cluster database connectivity, and common functionality used across all other modules. Features include:
- **Multi-Cluster Database Support**: MySQL, MongoDB, Redis, Elasticsearch with dynamic connection management
- **Configuration Management**: Unified configuration loading with environment-specific support
- **REST API Utilities**: Comprehensive REST client with multipart support
- **Test Categorization**: Annotations and listeners for test organization
- **Extent Reports**: Rich HTML reporting with interactive dashboards
- **Utility Classes**: JSON processing, async operations, generic utilities

### Juno
Tests for Juno service, which handles customer order management, cart operations, and payment processing. Features include:
- **Complete Order Lifecycle**: Order creation, cart management, payment processing
- **Customer Management**: Authentication, customer details, order history
- **Product & Package Management**: Product catalog and package operations
- **Prescription Management**: Prescription creation and validation
- **API Integration**: Comprehensive Juno API endpoint coverage

### Cosmos
Tests for Cosmos service, which provides order state management and tracking across the entire order lifecycle. Features include:
- **Order State Management**: Real-time order state tracking and transitions
- **Order Details Retrieval**: Comprehensive order information and status
- **State Validation**: Order existence and state transition validation
- **Tracking Integration**: Order visibility and tracking information
- **Authentication Management**: Secure API access and token management

### POS (Point of Sale)
Tests for point-of-sale system including in-store order management, barcode scanning, and retail operations. Features include:
- **Multi-Country Support**: India, Singapore, UAE with country-specific configurations
- **Barcode Management**: Product and lens-only barcode validation
- **In-Store Order Processing**: Complete POS order lifecycle automation
- **Prescription Handling**: In-store prescription creation and image upload
- **Cart Operations**: POS-specific cart management and operations

### SCM (Supply Chain Management)
Tests for supply chain operations including order fulfillment, inventory management, and logistics coordination. Features include:
- **Order Cancellation Management**: Complete cancellation workflow automation
- **Quality Control Operations**: QC eligibility, validation, and processing
- **Order Adoption**: Fulfillment center adoption and assignment
- **Database Operations**: Direct database queries for supply chain data
- **Supply Chain Analytics**: Performance monitoring and reporting

### NEXS (Next-Generation Execution System)
Tests for warehouse management system including order processing, picking, packing, and shipping operations. Features include:
- **Order State Management**: Complete order state transition automation
- **Picking Operations**: Warehouse picking workflow and validation
- **Packing Processes**: Order packing and completion workflows
- **Quality Control**: QC operations and validation processes
- **Consolidation**: Order consolidation and shipment management
- **WMS Integration**: Warehouse Management System operations

### CS (Customer Service)
Tests for customer service operations including RTO management, support tickets, and customer communication. Features include:
- **RTO Management**: Return to Origin order processing and workflows
- **Support Ticket Management**: Customer service case creation and resolution
- **Order Assistance**: Customer order support and modification
- **Refund Processing**: Return and refund workflow automation
- **Customer Communication**: Notification and communication management

### E2E (End-to-End)
Cross-service integration tests that validate complete business workflows spanning multiple services. Features include:
- **Cross-Service Workflows**: Complete order-to-delivery scenarios
- **Integration Validation**: Service-to-service communication testing
- **Business Process Testing**: End-to-end business workflow validation
- **Data Consistency**: Cross-service data integrity verification

### Example
Demonstration module with example implementations and test patterns for reference and learning. Features include:
- **Usage Examples**: Comprehensive examples for all framework features
- **Test Patterns**: Best practices and design patterns for test automation
- **Configuration Examples**: Sample configurations for different scenarios
- **Learning Resources**: Educational content for framework adoption

## Getting Started

### Prerequisites

- Java 17 or higher
- Maven 3.8 or higher
- Git

### Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/lenskart/be-automation.git
   cd be-automation
   ```

2. Install dependencies:
   ```bash
   mvn clean install -DskipTests
   ```

### Running Tests

#### Run All Tests

```bash
mvn test
```

#### Run Tests for a Specific Module

```bash
mvn test -pl juno          # Order management tests
mvn test -pl cosmos        # Order state management tests
mvn test -pl pos           # Point of sale tests
mvn test -pl scm           # Supply chain management tests
mvn test -pl nexs          # Warehouse management tests
mvn test -pl cs            # Customer service tests
mvn test -pl e2e           # End-to-end integration tests
```

#### Run Tests by Category

```bash
mvn test -DtestCategory=SANITY
```

#### Run Tests for a Specific Environment

```bash
mvn test -Denvironment=preprod
```

#### Combining Parameters

```bash
# Run SANITY tests for Juno in preprod environment
mvn test -pl juno -DtestCategory=SANITY -Denvironment=preprod

# Run REGRESSION tests for SCM in prod environment
mvn test -pl scm -DtestCategory=REGRESSION -Denvironment=prod

# Run E2E tests for Cosmos
mvn test -pl cosmos -DtestCategory=E2E -Denvironment=preprod

# Run specific test class
mvn test -pl nexs -Dtest=OrderProcessingTest

# Run POS tests for specific country
mvn test -pl pos -DtestCategory=SANITY -Dcountry=IN

# Run CS tests for RTO functionality
mvn test -pl cs -Dtest=CsRTOHelperTest
```

## Test Categories

Tests in this framework are categorized into three types:

1. **SANITY**: Basic smoke tests that verify core functionality
2. **REGRESSION**: More comprehensive tests that verify all functionality
3. **E2E**: End-to-end tests that verify complete user flows

For more details, see [Test Categories Documentation](docs/TestCategories.md).

## Configuration

The framework uses YAML configuration files for environment-specific settings. The configuration system is designed to be hierarchical, with common settings in the central configuration and service-specific settings in module-specific files.

### Configuration Files

- `commons/src/main/resources/config.yml`: Common configuration for all modules (multi-cluster databases, SSH, base URLs)
- `juno/src/main/resources/juno.yml`: Juno-specific configuration (order management, authentication, defaults)
- `cosmos/src/main/resources/cosmos.yml`: Cosmos-specific configuration (order state management, authentication)
- `pos/src/main/resources/pos.yml`: POS-specific configuration (multi-country users, store configurations)
- `scm/src/main/resources/scm.yml`: SCM-specific configuration (fulfillment centers, QC criteria, cancellation settings)
- `nexs/src/main/resources/nexs.yml`: NEXS-specific configuration (warehouse operations, order states, timeouts)
- `cs/src/main/resources/cs.yml`: CS-specific configuration (RTO settings, support categories, refund processing)

### Configuration Structure

Each configuration file follows a similar structure with environment-specific sections:

```yaml
# Environment-specific configuration
preprod:
  # Database configurations
  databases:
    default:
      url: ***************************************************
      username: preprod_user
      password: preprod_password
      # Other database settings...

  # Base URLs for services
  baseUrls:
    serviceA: https://api-gateway.servicea.preprod.example.com
    serviceB: https://api-gateway.serviceb.preprod.example.com

# Production configuration
prod:
  # Similar structure with production values
  # ...
```

### Configuration Management

The framework provides comprehensive configuration management with multi-cluster support:

#### Core Configuration Classes
- `ConfigLoader`: Loads raw configuration from YAML files with environment support
- `ConfigRegistry`: Central registry for all configurations with caching and validation

#### Multi-Cluster Database Configuration
- `MySQLMultiClusterConfig`: Multi-cluster MySQL configuration with dynamic connection management
- `MongoDBMultiClusterConfig`: Multi-cluster MongoDB configuration with dynamic query execution
- `RedisConfig`: Redis configuration with connection pooling
- `ElasticsearchConfig`: Elasticsearch configuration with high-level REST client
- `SSHConfig`: SSH tunneling configuration with automatic tunnel management

#### Module-Specific Configuration
- `JunoConfig`: Juno service configuration with authentication and defaults
- `CosmosConfig`: Cosmos service configuration with state management settings
- `PosConfig`: POS configuration with multi-country user management
- `ScmConfig`: SCM configuration with fulfillment center and QC settings
- `NexsConfig`: NEXS configuration with warehouse and order state settings
- `CsConfig`: CS configuration with RTO, support, and refund settings

### Usage Examples

#### Multi-Cluster Database Access
```java
// Get multi-cluster configurations
ConfigRegistry registry = ConfigRegistry.getInstance();

// MySQL multi-cluster access
MySQLMultiClusterConfig mysqlConfig = registry.getMySQLMultiClusterConfig();
Connection connection = DynamicMySQLConnectionManager.getConnection("user_cluster", "userdb");

// MongoDB multi-cluster access
MongoDBMultiClusterConfig mongoConfig = registry.getMongoDBMultiClusterConfig();
List<Document> users = DynamicMongoDBQueryExecutor.find(
    "user_cluster", "userdb", "users",
    new Document("status", "active")
);

// Redis configuration
RedisConfig redisConfig = registry.getRedisConfig("cache");

// Elasticsearch configuration
ElasticsearchConfig esConfig = registry.getElasticsearchConfig("search");
```

#### Module-Specific Configuration
```java
// Juno configuration
JunoConfig junoConfig = JunoConfigLoader.loadConfig();
String baseUrl = junoConfig.getBaseUrl("junoService");

// POS configuration with multi-country support
PosConfig posConfig = PosConfigLoader.loadConfig();
PosUser indianUser = posConfig.getPosUser("IN", 0);

// NEXS configuration
NexsConfig nexsConfig = NexsConfigLoader.loadConfig();
List<String> supportedStates = nexsConfig.getSupportedStates();

```

## Database Connectivity

The framework provides advanced multi-cluster database connectivity with support for multiple database types. All database connections can be secured through SSH tunneling when accessing databases in protected environments.

### Supported Database Types

- **MySQL**: For relational database access
  - Connection pooling with HikariCP
  - Type-safe query execution
  - Parameterized queries to prevent SQL injection
  - Transaction support

- **MongoDB**: For NoSQL document database access
  - Connection pooling
  - CRUD operations with type mapping
  - Query builders and filters
  - Aggregation pipeline support

- **Redis**: For caching and key-value storage
  - Connection pooling
  - Support for all Redis data structures (strings, lists, sets, hashes, etc.)
  - Pub/Sub functionality
  - Lua scripting support

- **Elasticsearch**: For search and analytics
  - High-level REST client
  - Index management
  - Document CRUD operations
  - Search and aggregation queries

### SSH Tunneling

All database connections support SSH tunneling for secure access to databases in protected environments:

```yaml
# SSH Tunneling configuration in config.yml
sshConfig:
  hostname: ssh-bastion.example.com
  port: 22
  username: tunnel_user
  password: tunnel_password  # Or use privateKeyPath
  privateKeyPath: /path/to/private/key
  enabled: true  # Enable/disable tunneling
```

### Usage Examples

#### MySQL Multi-Cluster Example

```java
// Get connection to any cluster and database
Connection connection = MySQLConnectionManager.getConnection("user_cluster", "userdb");

// Execute queries across different clusters
List<Map<String, Object>> users = MySQLQueryExecutor.executeQuery(
    "user_cluster", "userdb",
    "SELECT * FROM users WHERE status = ? AND created_at > ?",
    "active", yesterday
);

List<Map<String, Object>> products = MySQLQueryExecutor.executeQuery(
    "inventory_cluster", "inventory",
    "SELECT * FROM products WHERE category = ?",
    "eyewear"
);

// Execute updates with automatic connection management
int rowsAffected = MySQLQueryExecutor.executeUpdate(
    "user_cluster", "userdb",
    "UPDATE users SET status = ? WHERE id = ?",
    "inactive", userId
);

// Connection automatically managed and returned to pool
```

#### MongoDB Multi-Cluster Example

```java
// Execute operations across different MongoDB clusters
List<Document> users = MongoDBQueryExecutor.find(
    "user_cluster", "userdb", "users",
    new Document("status", "active")
);

List<Document> orders = MongoDBQueryExecutor.find(
    "orders_cluster", "orders", "orders",
    new Document("customerId", "CUST123")
);

// Insert documents across clusters
Document newUser = new Document("name", "John Doe")
    .append("email", "<EMAIL>")
    .append("status", "active");


// Aggregation across clusters
List<Document> pipeline = Arrays.asList(
    new Document("$match", new Document("status", "active")),
    new Document("$group", new Document("_id", "$country")
        .append("count", new Document("$sum", 1)))
);

List<Document> results = DynamicMongoDBQueryExecutor.aggregate(
    "user_cluster", "userdb", "users",
    pipeline
);
```

## Logging

The framework implements a comprehensive logging system using SLF4J with Logback as the implementation. Logs are organized by module and stored in the `logs` directory.

### Log File Structure

- `logs/automation.log`: Main log file containing all logs
- `logs/http-requests.log`: Dedicated log file for HTTP request/response details
- `logs/modules/`: Directory containing module-specific log files:
  - `logs/modules/commons.log`: Commons module logs
  - `logs/modules/juno.log`: Juno module logs
  - `logs/modules/scm.log`: SCM module logs
  - `logs/modules/nexs.log`: NEXS module logs
  - `logs/modules/cse.log`: CSE module logs
  - `logs/modules/pos.log`: POS module logs
  - `logs/modules/e2e.log`: E2E module logs
  - `logs/modules/example.log`: Example module logs
- `logs/test-report.html`: HTML-formatted log report

### Logging Configuration

Logging is configured in `commons/src/main/resources/logback.xml` with the following features:

- **Log Levels**: Different log levels (DEBUG, INFO, WARN, ERROR) for different components
- **Log Rotation**: Daily log rotation with compression
- **Size Limits**: Maximum log file sizes and history to prevent disk space issues
- **Formatted Output**: Consistent timestamp and thread information
- **Console Output**: Real-time logs to console during test execution

### Usage Example

```java
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MyClass {
    public void myMethod() {
        log.debug("Debug message with details: {}", someVariable);
        log.info("Information message");
        log.warn("Warning message");
        log.error("Error message", exception);
    }
}
```

## Reporting

The framework generates comprehensive test reports using **Extent Reports** to provide detailed insights into test execution.

### Extent Reports

Extent Reports provides rich, interactive HTML reports with comprehensive test execution details:

- **Interactive Dashboard**: Visual representation of test execution with charts and statistics
- **Test Hierarchy**: Tests organized by suites, classes, and methods
- **Detailed Test Steps**: Step-by-step execution details with timestamps
- **Screenshots and Logs**: Automatic attachment of screenshots and log files
- **Environment Information**: System and test environment details
- **Real-time Updates**: Live report updates during test execution
- **Mobile-Friendly**: Responsive design for viewing on any device
- **Dark/Light Theme**: Customizable themes for better viewing experience

#### Report Generation

Extent reports are automatically generated during test execution and saved as:
```
test-output/extent-reports/test-report.html
```

#### Report Features

- **Test Statistics**: Pass/Fail/Skip counts with percentage calculations
- **Execution Timeline**: Chronological view of test execution
- **Test Categories**: Tests grouped by categories (SANITY, REGRESSION, E2E)
- **System Information**: OS, Java version, user details
- **Exception Details**: Full stack traces for failed tests
- **Custom Logs**: Test-specific logging and information

#### Viewing Reports

After test execution, open the generated HTML report in any web browser:
```bash
open test-output/extent-reports/test-report.html
```

### TestNG Reports

TestNG generates its own HTML reports with test execution details:

- **Suite Summary**: Overview of test suite execution
- **Test Methods**: Details of each test method
- **Chronological View**: Tests in execution order
- **Reporter Output**: Custom output from tests

TestNG reports are generated in the `test-output` directory.

## CI/CD Integration

The repository includes an advanced Jenkins pipeline configuration in the `Jenkinsfile` that enables automated test execution with rich reporting and professional email notifications as part of the CI/CD process.

### Pipeline Stages

1. **Checkout**: Cleans the workspace and checks out the code from Git
2. **Build**: Builds the project without running tests (`mvn clean package -DskipTests`)
3. **Test**: Runs tests based on selected parameters

### Pipeline Parameters

The pipeline is parameterized to allow flexible test execution:

- **ENVIRONMENT**: Environment to run tests against
  - `preprod`: Pre-production environment
  - `prod`: Production environment

- **SUITE_TYPE**: Test suite to run
  - `juno`: Juno service tests (Order Management)
  - `cosmos`: Cosmos service tests (Order State Management)
  - `pos`: POS service tests (Point of Sale)
  - `scm`: SCM service tests (Supply Chain Management)
  - `nexs`: NEXS service tests (Warehouse Management)
  - `cs`: Customer Service tests (RTO, Support, Refunds)
  - `e2e`: End-to-end tests
  - `all`: All test suites

- **TEST_CATEGORY**: Test category to run
  - `SANITY`: Basic smoke tests
  - `REGRESSION`: Comprehensive regression tests
  - `E2E`: End-to-end tests
  - `ALL`: All test categories

### Pipeline Artifacts

The pipeline archives the following artifacts:

- **Test Results**: JUnit XML reports for test result tracking
- **Extent Reports**: Rich HTML reports with test execution details
- **Test Output**: Complete test-output directory with all generated files
- **Logs**: Application and test logs for debugging

### Pipeline Notifications

The pipeline sends **rich HTML email notifications** with comprehensive build information to:

- **Developers**: Who made changes to the codebase
- **Test Requesters**: Who triggered the pipeline
- **QA Team**: <EMAIL>
- **Automation Team**: <EMAIL>

#### Email Features

- **Visual Dashboard**: Test statistics with pass/fail/skip counts and percentage calculations
- **Build Information**: Environment, suite type, execution time, build number, git information
- **Quick Links**: Direct access to build details, console output, test results, artifacts
- **Automatic Attachments**: Extent reports automatically attached to emails
- **Status Indicators**: Color-coded success/failure indicators (✅❌⚠️)
- **Professional Design**: Mobile-friendly HTML template with Lenskart branding
- **Rich Content**: Test execution timeline, failure details, and comprehensive statistics

### Example Pipeline Execution

```bash
# Run sanity tests for the Juno module in preprod environment
java -jar jenkins-cli.jar -s http://jenkins-server build be-automation \
    -p ENVIRONMENT=preprod \
    -p SUITE_TYPE=juno \
    -p TEST_CATEGORY=SANITY
```

## Test Development

### Creating a New Test

To create a new test, follow these steps:

1. **Create a Test Context Class**:

```java
@Builder
@Data
public class MyTestContext {
    private String param1;
    private String param2;
    @Builder.Default
    private int statusCode = 200;
}
```

2. **Create a Request Builder**:

```java
@Builder
public class MyRequestBuilder implements IRequestBuilder<MyRequest> {
    private MyTestContext testContext;

    @Override
    public MyRequest init() {
        return MyRequest.builder()
                .param1(testContext.getParam1())
                .param2(testContext.getParam2())
                .build();
    }
}
```

3. **Create a Service Helper**:

```java
@SuperBuilder
public class MyServiceHelper extends BaseHelper<MyExceptionState, MyRequest> implements ServiceHelper {
    private MyTestContext testContext;
    private MyResponse response;
    private MyServiceAPI client;

    @Override
    public ServiceHelper init() {
        // Initialize the test
        statusCode = testContext.getStatusCode();
        requestBuilders = MyRequestBuilder.builder()
                .testContext(testContext)
                .build()
                .init();
        client = new MyServiceAPI();
        return this;
    }

    @Override
    public ServiceHelper process() {
        // Execute the API call
        response = parseResponse(client.callApi(requestBuilders, headers), MyResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        // Validate the response
        MyValidator validator = MyValidator.builder()
                .request(requestBuilders)
                .response(response)
                .testContext(testContext)
                .build();
        validator.validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        // Orchestrate the test flow
        init();
        process();
        validate();
        return this;
    }
}
```

4. **Create a Validator**:

```java
@Builder
public class MyValidator implements IValidator {
    private MyTestContext testContext;
    private MyRequest request;
    private MyResponse response;
    private static SoftAssert softAssert = new SoftAssert();

    @Override
    public void validateNode() {
        softAssert.assertNotNull(response, "Response should not be null");
        // Add more validations
        softAssert.assertAll();
    }

    @Override
    public void validateDBEntities() {
        // Validate database state if needed
    }
}
```

5. **Create a Test Class**:

```java
@TestCategory(TestCategory.Category.SANITY)
public class MyTest {
    @Test(dataProvider = "myDataProvider")
    public void testMyFeature(String param1, String param2) {
        MyServiceHelper.builder()
                .testContext(MyTestContext.builder()
                        .param1(param1)
                        .param2(param2)
                        .build())
                .build()
                .test();
    }

    @DataProvider(name = "myDataProvider")
    public Object[][] myDataProvider() {
        return new Object[][] {
            {"value1", "value2"},
            {"value3", "value4"}
        };
    }
}
```

### Best Practices

1. **Use Builder Pattern**: Use Lombok's `@Builder` and `@SuperBuilder` for creating objects
2. **Separate Concerns**: Keep request building, API calls, and validation separate
3. **Use Data Providers**: Parameterize tests with TestNG data providers
4. **Add Test Categories**: Categorize tests as SANITY, REGRESSION, or E2E
5. **Implement Soft Assertions**: Use TestNG's SoftAssert for multiple validations
6. **Document Tests**: Add clear comments and documentation to tests
7. **Follow Naming Conventions**: Use consistent naming for test classes and methods

## Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Add tests for your changes
4. Ensure all tests pass
5. Submit a pull request

## License

Proprietary - Lenskart

## Contact

For questions or support, contact the QA Automation team.