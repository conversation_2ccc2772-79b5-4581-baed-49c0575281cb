package com.lenskart.example.api;

import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.example.model.GetRequest;
import io.restassured.response.Response;

import java.util.Map;

public class DiscoveryServiceAPI {

    String url = "https://httpbin.org/anything/123";

    public String getData(GetRequest payload, Map<String, String> headers) {
        return RestUtils.post(url, headers, JsonUtils.convertObjectToJsonString(payload)).asString();
    }
}