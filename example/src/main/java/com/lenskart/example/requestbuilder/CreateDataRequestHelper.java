package com.lenskart.example.requestbuilder;

import com.lenskart.commons.base.IRequestBuilder;
import com.lenskart.example.model.DiscoveryTestContext;
import com.lenskart.example.model.GetRequest;
import lombok.Builder;
import java.util.List;


@Builder
public class CreateDataRequestHelper implements IRequestBuilder<GetRequest> {

    DiscoveryTestContext testContext;

    @Override
    public GetRequest init() {

        return GetRequest.builder()
                .expand(List.of(testContext.getUserId()))
                .amount(GetRequest.Amount.builder().amount(100)
                        .currency("INR")
                        .build())
                .build();
    }
}