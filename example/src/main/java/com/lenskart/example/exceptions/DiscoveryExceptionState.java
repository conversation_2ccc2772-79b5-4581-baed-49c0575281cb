package com.lenskart.example.exceptions;


import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DiscoveryExceptionState {

    X_DEVICE_HEADER_NOT_PASSED(null, null, 200),
    X_USER_ID_HEADER_NOT_PASSED("INTERNAL_SERVER_ERROR", "Something went wrong. Please try again in sometime.", 400),
    X_MERCHANT_ID_HEADER_NOT_PASSED(null, null, 200);

    private final String code;
    private final String message;
    private final int statusCode;
}