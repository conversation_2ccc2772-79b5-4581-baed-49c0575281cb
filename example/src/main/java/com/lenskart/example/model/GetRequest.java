package com.lenskart.example.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Builder
@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GetRequest {

    private List<String> expand;
    private Amount amount;

    @Builder
    @Data
    public static class Amount {

        private Object amount;
        @Builder.Default
        private String currency = "INR";

    }
    
}