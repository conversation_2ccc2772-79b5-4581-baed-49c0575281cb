package com.lenskart.example.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GetResponse {
    private String method;
    private String url;
    private Map<String, String> headers;
    private String origin;
    private String data;
    private Map<String, Object> json;
    private Map<String, String> args;
}
