package com.lenskart.example.helper;

import lombok.experimental.SuperBuilder;
import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.example.api.DiscoveryServiceAPI;
import com.lenskart.example.exceptions.DiscoveryExceptionState;
import com.lenskart.example.model.DiscoveryTestContext;
import com.lenskart.example.model.GetRequest;
import com.lenskart.example.model.GetResponse;
import com.lenskart.example.requestbuilder.CreateDataRequestHelper;
import com.lenskart.example.validator.GetDataValidator;

import java.util.Objects;

@SuperBuilder
public class GetDataHelper extends BaseHelper<DiscoveryExceptionState, GetRequest> implements ServiceHelper {

    GetResponse response;
    DiscoveryTestContext testContext;
    DiscoveryServiceAPI client;

    @Override
    public ServiceHelper init() {

        statusCode = Objects.nonNull(validationState) ? validationState.getStatusCode() : testContext.getStatusCode();
        requestBuilders = CreateDataRequestHelper.builder()
                .testContext(testContext)
                .build()
                .init();
        client = new DiscoveryServiceAPI();
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = parseResponse(client.getData(requestBuilders, headers), GetResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        GetDataValidator validator = GetDataValidator.builder()
                .request(requestBuilders)
                .response(response)
                .testContext(testContext)
                .build();
        validator.validateNode();
        return this;
    }


    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}