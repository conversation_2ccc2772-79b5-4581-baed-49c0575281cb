package com.lenskart.juno.requestbuilder;

import com.lenskart.commons.model.*;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.juno.config.JunoConfig;
import com.lenskart.juno.config.JunoConfigLoader;
import com.lenskart.juno.config.JunoConfigRegistry;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import static com.lenskart.commons.model.ProductId.IN_CONTACT_LENS_ZERO_POWER;

import java.util.Objects;

@Slf4j
public class JunoOrderRequestBuilder {

    public static JSONObject createPayloadForSession(OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        payload.put("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode());
        payload.put("telephone", Objects.isNull(orderContext.getPhoneNumber()) ?
                orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber() : orderContext.getPhoneNumber());
        payload.put("code", orderContext.getOtp());
        return payload;
    }

    public static JSONObject createPayloadForAddCart(OrderContext.ProductList product) {
        JSONObject payload = new JSONObject();
        payload.put("productId", product.getProductId());
        if (product.getPowerType() != null) {
            payload.put("addOns", "");
            payload.put("packageId", product.getPackageId());
            payload.put("powerType", product.getPowerType().getDisplayName());
        } else if (product.getProductId().equals(IN_CONTACT_LENS_ZERO_POWER.getProductId())) {
            payload.put("quantity", "2");
            JSONObject presObj = new JSONObject();
            presObj.put("userName", "lenskart user");
            presObj.put("dob", "");
            presObj.put("notes", "");
            presObj.put("gender", "");
            presObj.put("powerType", PowerTypes.CONTACT_LENS.name());
            product.setPowerType(PowerTypes.CONTACT_LENS);
            
            JSONObject leftPower = new JSONObject();
            leftPower.put("sph", "0.00");
            leftPower.put("boxes", "1");
            presObj.put("left", leftPower);

            JSONObject rightPower = new JSONObject();
            rightPower.put("sph", "0.00");
            rightPower.put("boxes", "1");
            presObj.put("right", rightPower);
            
            payload.put("prescription", presObj);
        } else if (product.getProductType().equals(ProductTypes.CONTACT_LENS)) {
            payload.put("quantity", "2");
            JSONObject presObj = new JSONObject();
            presObj.put("userName", "lenskart user");
            presObj.put("dob", "");
            presObj.put("notes", "");
            presObj.put("gender", "");
            presObj.put("powerType", PowerTypes.CONTACT_LENS.name());
            product.setPowerType(PowerTypes.CONTACT_LENS);
            JSONObject leftPower = new JSONObject();
            leftPower.put("boxes", "1");
            if (product.isPrescriptionRequired()) {
                leftPower.put("sph", "-0.75");
            }
            JSONObject rightPower = new JSONObject();
            rightPower.put("boxes", "1");
            if (product.isPrescriptionRequired()) {
                rightPower.put("sph", "-0.75");
            }
            presObj.put("left", leftPower);
            presObj.put("right", rightPower);
            payload.put("prescription", presObj);
        }
        return payload;
    }

    public static JSONObject createPayloadForShippingAddress(OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        JSONObject address = new JSONObject();
        payload.put("firstName", "test");
        payload.put("lastName", "test");
        payload.put("country", orderContext.getCountryCodeMapper().getCountry().name());
        payload.put("gender", "Male");
        payload.put("phone", Objects.isNull(orderContext.getPhoneNumber()) ?
                orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber() : orderContext.getPhoneNumber());
        payload.put("postcode",  Objects.isNull(orderContext.getCountryCodeMapper().getPinCode()) ?
                orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber() : orderContext.getCountryCodeMapper().getPinCode());
        payload.put("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode());
        payload.put("addressline1", "Test order Automation");
        switch (orderContext.getCountryCodeMapper().getCountry().name()) {
            case "IN" -> {
                payload.put("city", "FARIDABAD");
                payload.put("state", "HARYANA");
            }
            case "SG" -> {
                payload.put("city", "SINGAPORE");
                payload.put("state", "SINGAPORE");
            }
            case "AE" -> {
                payload.put("city", "UAE");
                payload.put("state", "UAE");
            }
            case "TH" -> {
                payload.put("city", "Bangkok");
                payload.put("state", "Bangkok");
            }
            case "SA" -> {
                payload.put("city", "Riyadh");
                payload.put("state", "SAUDI ARABIA");
            }
            default -> {
                payload.put("city", "Bangalore");
                payload.put("state", "Karnataka");
            }
        }
        payload.put("email", "<EMAIL>");
        address.put("address", payload);
        return address;
    }


    public static JSONObject createPayloadForOrderPayment(OrderContext orderContext) {
        JSONObject paymentInfo = new JSONObject();
        JSONObject payload = new JSONObject();
        JSONObject netbanking = new JSONObject();

        String paymentMethod = Objects.isNull(orderContext.getPaymentMethod()) ?
                orderContext.getCountryCodeMapper().getCountry().getDefaultPaymentMethod() : String.valueOf(orderContext.getPaymentMethod().getDisplayName());
        if (Objects.isNull(orderContext.getPaymentMethod()))
            orderContext.setPaymentMethod(PaymentMethod.getByDisplayName(paymentMethod));
        log.info("Payment method: {}", orderContext.getPaymentMethod());
        paymentInfo.put("paymentMethod", paymentMethod);
        if (paymentMethod.equals(PaymentMethod.NET_BANKING.getDisplayName())) {
            paymentInfo.put("gatewayId", "PU");
            netbanking.put("bankCode", "PU:162B");
            paymentInfo.put("netbanking", netbanking);
        }
        payload.put("device", orderContext.getHeaders().getClient().getDisplayName());
        payload.put("paymentInfo", paymentInfo);
        return payload;
    }

    public static JSONObject createPayloadForPrescription(String powerType,OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        payload.put("powerType", powerType);
        payload.put("left", leftEye(powerType, orderContext));
        payload.put("right", rightEye(powerType,orderContext));
        payload.put("userName", "Automation User");
        return payload;
    }

    public static JSONObject createPayloadForCartUpdatePrescription(String powerType, OrderContext orderContext) {
        JSONObject prescription = new JSONObject();
        JSONObject payload = new JSONObject();
        payload.put("powerType", powerType);
        payload.put("left", leftEye(powerType, orderContext));
        payload.put("right", rightEye(powerType,orderContext));
        payload.put("userName", "Automation User");
        prescription.put("prescription",payload);
        return prescription;
    }

    public static JSONObject leftEye(String powerType, OrderContext orderContext) {
        JSONObject left = new JSONObject();
        if (PowerTypes.BIFOCAL.getDisplayName().equalsIgnoreCase(powerType)) {
            if(orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH() || orderContext.getIsManulJitOrderProcess()){
                left.put("sph", "0.50");
            }else{
                left.put("sph", "-0.50");
            }

            left.put("cyl", "-2.00");
            left.put("axis", "100");
            left.put("pd", "29");
            left.put("ap", "+1.25");
            left.put("lensHeight", "35 mm");
            left.put("lensWidth", "51 mm");
            left.put("ed", "10");
            left.put("dbl", "10");
            left.put("bottomDistance", "1");
            left.put("topDistance", "10");
            left.put("effectiveDia", "10");
        } else if (PowerTypes.SINGLE_VISION.getDisplayName().equalsIgnoreCase(powerType)) {
            if(orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH() || orderContext.getIsManulJitOrderProcess()){
                left.put("sph", "-3.00");
            }else{
                left.put("sph", "+1.00");
            }
            left.put("cyl", "+2.00");
            left.put("axis", "6");
            left.put("pd", "30.5");
        } else if (PowerTypes.SUNGLASSES.getDisplayName().equalsIgnoreCase(powerType) && Countries.IN.name().equalsIgnoreCase(orderContext.getCountryCodeMapper().getCountry().name())) {
            if(orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()  || orderContext.getIsManulJitOrderProcess()){
                left.put("sph", "0.50");
            }else{
                left.put("sph", "0.00");
            }
            left.put("cyl", "+2.00");
            left.put("axis", "166");
        }
        else if (PowerTypes.SUNGLASSES.getDisplayName().equalsIgnoreCase(powerType)) {
            if(orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()  || orderContext.getIsManulJitOrderProcess()){
                left.put("sph", "0.50");
            }else{
                left.put("sph", "0.00");
            }
            left.put("cyl", "0.00");
            left.put("axis", "166");
        }
        return left;
    }


    public static JSONObject rightEye(String powerType,OrderContext orderContext) {
        JSONObject right = new JSONObject();
        if (PowerTypes.BIFOCAL.getDisplayName().equalsIgnoreCase(powerType)) {
            if(orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()  || orderContext.getIsManulJitOrderProcess()){
                right.put("sph", "0.50");
            }else{
                right.put("sph", "-0.50");
            }
            right.put("cyl", "-2.00");
            right.put("axis", "100");
            right.put("pd", "29");
            right.put("ap", "+1.25");
            right.put("lensHeight", "35 mm");
            right.put("lensWidth", "51 mm");
            right.put("ed", "10");
            right.put("dbl", "10");
            right.put("bottomDistance", "1");
            right.put("topDistance", "10");
            right.put("effectiveDia", "10");
        } else if (PowerTypes.SINGLE_VISION.getDisplayName().equalsIgnoreCase(powerType)) {
            if(orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()  || orderContext.getIsManulJitOrderProcess() ){
                right.put("sph", "-3.00");
            }else{
                right.put("sph", "+1.00");
            }
            right.put("cyl", "+2.00");
            right.put("axis", "6");
            right.put("pd", "30.5");
        } else if (PowerTypes.SUNGLASSES.getDisplayName().equalsIgnoreCase(powerType) && Countries.IN.name().equalsIgnoreCase(orderContext.getCountryCodeMapper().getCountry().name())) {
           if(orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()  || orderContext.getIsManulJitOrderProcess() ){
               right.put("sph", "0.50");}
           else{
               right.put("sph", "0.00");}

            right.put("cyl", "+2.00");
            right.put("axis", "166");
        }
         else if (PowerTypes.SUNGLASSES.getDisplayName().equalsIgnoreCase(powerType)) {
             if(orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()  || orderContext.getIsManulJitOrderProcess()){
                 right.put("sph", "0.50");
             }else{
                 right.put("sph", "0.00");
             }
        right.put("cyl", "0.00");
        right.put("axis", "166");
    }
        return right;
    }

    /**
     * Creates a bulk credit payload for wallet operations
     *
     * @param amount      The amount to credit
     * @param phoneNumber The phone number for the credit
     * @param walletType  The type of wallet (default: "lenskartplus")
     * @param narration   The narration for the credit (default: "credited")
     * @return JSONArray containing the bulk credit payload
     */
    public static JSONArray createBulkCreditPayload(int amount, String phoneNumber, String walletType, String narration) {
        JSONObject bulkCredit = new JSONObject();
        bulkCredit.put("amount", amount);
        bulkCredit.put("merchantRefId", GenericUtils.generateRandomString(15));
        bulkCredit.put("mobileNumber", phoneNumber);
        bulkCredit.put("narration", narration != null ? narration : "credited");
        bulkCredit.put("walletType", walletType != null ? walletType : "lenskartplus");

        JSONArray req = new JSONArray();
        req.put(bulkCredit);

        log.info("Created bulk credit payload: {}", req.toString());
        return req;
    }

    /**
     * Creates a bulk credit payload with default values
     *
     * @param amount      The amount to credit
     * @param phoneNumber The phone number for the credit
     * @return JSONArray containing the bulk credit payload
     */
    public static JSONArray createBulkCreditPayload(int amount, String phoneNumber) {
        return createBulkCreditPayload(amount, phoneNumber, "lenskartplus", "credited");
    }


    public static JSONObject dracoLoginPayload(OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        payload.put("username", JunoConfigRegistry.getInstance().getDracoUsername());
        payload.put("password", JunoConfigRegistry.getInstance().getDracoPassword());
        return payload;

    }

    public static JSONObject medibuddyApprovalPayload(OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        payload.put("id" , orderContext.getOrderId());
        payload.put("closingStatus", "PROCESSING");
        payload.put("closingComments", "testing");
        payload.put("closedBy", "Automation");
        payload.put("mediBuddyId", "4534534");
        return payload;
    }

    public static JSONObject medibuddyRejectPayload(OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        payload.put("id" , orderContext.getOrderId());
        payload.put("closingStatus", "REJECTED");
        payload.put("closingComments", "reason");
        payload.put("closedBy", "Automation");
        return payload;
    }

}
