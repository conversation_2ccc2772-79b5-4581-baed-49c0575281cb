package com.lenskart.juno.requestbuilder;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PaymentMethod;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Map;


@Slf4j
public class JusPayRequestBuilder {


    public static JSONObject createPayloadForOrderPayment(OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        JSONObject paymentInfo = new JSONObject();
        JSONObject card = new JSONObject();
        JSONObject emi = new JSONObject();
        payload.put("device", "desktop");
        payload.put("storeCard", true);

        PaymentMethod paymentMethod = orderContext.getPaymentMethod();

        if (paymentMethod == PaymentMethod.CREDIT_CARD ) {
            paymentInfo.put("paymentMethod", "juspay_card");
            paymentInfo.put("gatewayId", "JUSPAY");

            card.put("cardError", new JSONObject());
            card.put("expired", false);
            card.put("isNewCard", true);
            card.put("number", "512345XXXXXXXXXX");
            card.put("storeCard", true);

            paymentInfo.put("card", card);

        } else if (paymentMethod == PaymentMethod.EMI) {
            paymentInfo.put("paymentMethod", "juspay_emi");
            paymentInfo.put("gatewayId", "JUSPAY");


            card.put("cardError", new JSONObject());
            card.put("expired", false);
            card.put("isNewCard", true);
            card.put("number", "480855XXXXXXXXXX");
            card.put("storeCard", true);


            emi.put("bankCode", "JP_ICICI");
            emi.put("emiTenure", 3);
            emi.put("emiType", "NO_COST_EMI");

            paymentInfo.put("card", card);
            paymentInfo.put("emi", emi);

        } else if (paymentMethod == PaymentMethod.DEBIT_CARD) {
            paymentInfo.put("paymentMethod", "juspay_card");
            paymentInfo.put("gatewayId", "JUSPAY");

            card.put("cardError", new JSONObject());
            card.put("expired", false);
            card.put("isNewCard", true);
            card.put("number", "511870XXXXXXXXXX");
            card.put("storeCard", true);

            paymentInfo.put("card", card);


        } else {
            throw new IllegalArgumentException("Unsupported payment method: " + paymentMethod);
        }
        payload.put("paymentInfo", paymentInfo);

        log.info("Created Juspay order payment payload for {}: {}", paymentMethod, payload.toString());
        return payload;
    }


    public static Map<String, String> createJuspayHeaders() {
        Map<String, String> headers = new java.util.HashMap<>();
        headers.put("content-type", "application/x-www-form-urlencoded");
        headers.put("accept", "application/json");
        headers.put("juspay-payjs-version", "sandbox_dcd983a");
        headers.put("x-merchantid", "lenskart");
        log.info("Created Juspay headers: {}", headers);
        return headers;
    }

    public static Map<String, String> getJuspayFormParams(OrderContext orderContext, String prepaidOrderId) {
        Map<String, String> formParams = new java.util.HashMap<>();
        PaymentMethod paymentMethod = orderContext.getPaymentMethod();

        formParams.put("metadata", "{}");
        formParams.put("merchant_id", "lenskart");
        formParams.put("order_id", prepaidOrderId);
        formParams.put("save_to_locker", "true");
        formParams.put("tokenize", "true");
        formParams.put("name_on_card", "test");
        formParams.put("auth_type", "");
        formParams.put("redirect_after_payment", "true");
        formParams.put("authentication_method", "GET");
        formParams.put("format", "json");
        formParams.put("payjs_version", "v3");

        if (paymentMethod == PaymentMethod.CREDIT_CARD ) {
            formParams.put("emi_type", "STANDARD_EMI");
            formParams.put("card_number", "****************");
            formParams.put("card_exp_month", "05");
            formParams.put("card_exp_year", "30");
            formParams.put("card_security_code", "123");
            formParams.put("payment_method_type", "CARD");
            formParams.put("payment_method", "");

        } else if (paymentMethod == PaymentMethod.EMI) {
            formParams.put("emi_type", "NO_COST_EMI");
            formParams.put("card_number", "****************");
            formParams.put("card_exp_month", "05");
            formParams.put("card_exp_year", "30");
            formParams.put("card_security_code", "123");
            formParams.put("payment_method_type", "EMI");
            formParams.put("payment_method", "juspay_emi");
        }
        else if (paymentMethod == PaymentMethod.DEBIT_CARD) {
            formParams.put("emi_type", "STANDARD_EMI");
            formParams.put("card_number", "****************");
            formParams.put("card_exp_month", "05");
            formParams.put("card_exp_year", "30");
            formParams.put("card_security_code", "123");
            formParams.put("payment_method_type", "CARD");
            formParams.put("payment_method", "");
        }
        else {
            throw new IllegalArgumentException("Unsupported payment method: " + paymentMethod);
        }

        log.info("Created Juspay form parameters for {}: {}", paymentMethod, formParams);
        return formParams;
    }
}