package com.lenskart.juno.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

import static com.lenskart.juno.constants.Constants.GOLD_MAX_LOYALTY_CASHBACK;
import static com.lenskart.juno.constants.Constants.LENSKART_PLUS_WALLET;

/**
 * Model for Wallet Credit API Request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WalletCreditRequest {
    
    @JsonProperty("amount")
    private String amount;
    
    @JsonProperty("mobileNumber")
    private String mobileNumber;
    
    @JsonProperty("merchantRefId")
    private String merchantRefId;
    
    @JsonProperty("expiryDate")
    private String expiryDate;
    
    @JsonProperty("walletType")
    private String walletType;
    
    @JsonProperty("campaignId")
    private String campaignId;
    
    @JsonProperty("campaignName")
    private String campaignName;
    
    /**
     * Create a test wallet credit request
     */

    public static WalletCreditRequest createTestRequest(String mobileNumber, String amount, String merchantRefId) {
        return WalletCreditRequest.builder()
            .amount(amount)
            .mobileNumber(mobileNumber)
            .merchantRefId(merchantRefId)
            .expiryDate(LocalDate.now().plusDays(5).format(DateTimeFormatter.ISO_LOCAL_DATE))
            .walletType(LENSKART_PLUS_WALLET)
            .campaignId(GOLD_MAX_LOYALTY_CASHBACK)
            .campaignName("Test Concurrency Wallet Credit")
            .build();
    }

}
