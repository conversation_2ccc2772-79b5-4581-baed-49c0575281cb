package com.lenskart.juno.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * Model for Wallet Credit API Response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WalletCreditResponse {

    private Result result;
    private int status;
    private String traceId;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String message;
        private Transaction transaction;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Transaction {
        private String transactionId;
        private String walletId;
        private String mobileNumber;
        private String merchantRefId;
        private BigDecimal preTransactionBalance;
        private BigDecimal postTransactionBalance;
        private BigDecimal amount;
        private String walletType;
        private String walletTransactionType;
        private String transactionStatus;
        private String expiryDate;
        private String transactionDate;
        private String campaignId;
        private String campaignName;
        private int version;
    }

}
