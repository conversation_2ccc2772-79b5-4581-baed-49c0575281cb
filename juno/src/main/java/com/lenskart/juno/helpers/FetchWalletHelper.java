package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.config.JunoConfigRegistry;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

import static com.lenskart.juno.endpoints.JunoEndpoints.FETCH_WALLET_BALANCE;

@SuperBuilder
@Slf4j
public class FetchWalletHelper extends JunoBaseHelper implements ServiceHelper {
    OrderContext orderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (orderContext.isGuestUser()) {
            log.info("Skipping wallet balance check for guest user");
            return this;
        }

        Response response = RestUtils.get(FETCH_WALLET_BALANCE.getUrl(), headers, null, 200);

        BigDecimal balance = BigDecimal.valueOf(response.jsonPath().getFloat("result.balance"));
        log.info("Wallet balance: {}", balance);

        if (balance.compareTo(BigDecimal.ZERO) == 0) {
            log.info("Wallet balance is zero, adding credit");

            // Use WalletTokenGenerationHelper to generate token
            WalletTokenGenerationHelper tokenHelper = (WalletTokenGenerationHelper) WalletTokenGenerationHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();

            String walletToken = tokenHelper.getWalletToken();
            if (walletToken != null && !walletToken.isEmpty()) {
                // Use WalletBulkCreditHelper to add credit
                WalletBulkCreditHelper.builder()
                        .orderContext(orderContext)
                        .walletAuthToken(walletToken)
                        .amount(JunoConfigRegistry.getInstance().getBulkCreditAmount())
                        .phoneNumber(orderContext.getPhoneNumber())
                        .build()
                        .test();

                log.info("Successfully added credit to wallet");
            } else {
                log.error("Failed to generate wallet token for credit addition");
            }
        } else {
            log.info("Wallet has sufficient balance: {}", balance);
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}