package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.microsoft.playwright.options.AriaRole;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import com.microsoft.playwright.*;

import static com.lenskart.juno.endpoints.JunoEndpoints.CHECKOUT_SUCCESS;

@SuperBuilder
@Slf4j
public class PlaywrightPaymentHelper extends JunoBaseHelper implements ServiceHelper {

    String authenticationUrl;
    String prepaidOrderId;
    OrderContext orderContext;

    @Override
    public ServiceHelper init() {
        log.info(" authentication for order: {}", prepaidOrderId);
        return this;
    }

    @Override
    public ServiceHelper process() {
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(true));
            Page page = browser.newPage();
            log.info("Navigating to authentication URL: {}", authenticationUrl);
            page.navigate(authenticationUrl);

            // Initial wait for the page to load
            log.info("Waiting 15 seconds for simulator page (OTP input) to appear...");
            page.waitForTimeout(15000);

            log.info("Simulator page loaded. Adding an extra 20-second wait before entering OTP...");
            page.waitForTimeout(20000);

            // Entering OTP
            log.info("Entering OTP...");
            page.locator("#password").click();
            page.locator("#password").fill("123456");
            page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("PAY")).click();

            page.waitForTimeout(20000);

            page.navigate(CHECKOUT_SUCCESS.getUrl());


            log.info("OTP submitted. Waiting 10 seconds after clicking PAY...");
            page.waitForTimeout(10000);

            browser.close();
            log.info("Authentication completed successfully via Playwright");
        } catch (Exception e) {
            log.error("Playwright automation failed or not available. Manual authentication required. Open URL: {}", authenticationUrl);
            log.error("Error details: {}", e.getMessage());
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
} 