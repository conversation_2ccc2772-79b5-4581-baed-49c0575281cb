package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.config.JunoConfigRegistry;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.commons.constants.HeaderMapper.X_REFRESH_TOKEN;
import static com.lenskart.juno.endpoints.JunoEndpoints.GENERATE_WALLET_TOKEN;

@SuperBuilder
@Slf4j
@Getter
public class WalletTokenGenerationHelper extends JunoBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    private String walletToken;

    @Override
    public ServiceHelper init() {
        if (orderContext.isGuestUser()) {
            log.info("Skipping wallet token generation for guest user");
            return this;
        }
        headers = getHeadersWithSessionToken(orderContext);

        /* Add refresh token header for wallet token generation */
        headers.put(X_REFRESH_TOKEN.getHeaderName(), JunoConfigRegistry.getInstance().getRefreshToken());
        return this;
    }

    @Override
    public ServiceHelper process() {

        log.info("Generating wallet token for user: {}", orderContext.getPhoneNumber());

        response = RestUtils.post(GENERATE_WALLET_TOKEN.getUrl(), headers, null, 200);
        log.info("Wallet token generation response: {}", response.asString());

        walletToken = response.jsonPath().getString("result.token");
        log.info("Generated wallet token: {}", walletToken);

        return this;
    }

    @Override
    public ServiceHelper validate() {

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}