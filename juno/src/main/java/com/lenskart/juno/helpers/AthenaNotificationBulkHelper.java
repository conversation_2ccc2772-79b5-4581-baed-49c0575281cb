package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.juno.endpoints.JunoEndpoints.ATHENA_NOTIFICATION_BULK;

@SuperBuilder
@Slf4j
public class AthenaNotificationBulkHelper extends JunoBaseHelper implements ServiceHelper {
    Response response;
    String payload;
    OrderContext orderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        queryParams = getQueryParamsForAthenaNotificationBulk(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(ATHENA_NOTIFICATION_BULK.getUrl(), headers, queryParams, null);
        assert response.getStatusCode() == 200;
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
