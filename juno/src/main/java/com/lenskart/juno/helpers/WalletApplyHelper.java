package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.validator.cart.CartValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.testng.asserts.SoftAssert;

import java.util.List;
import java.util.Map;

import static com.lenskart.juno.endpoints.JunoEndpoints.APPLY_WALLET;
import static com.lenskart.juno.endpoints.JunoEndpoints.V2_CART;

@SuperBuilder
@Slf4j
public class WalletApplyHelper extends JunoBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    static SoftAssert softAssert = new SoftAssert();

    @Override
    public ServiceHelper init() {
        if (orderContext.isGuestUser()) {
            log.info("Skipping wallet apply for guest user");
            return this;
        }
        headers = getHeadersWithSessionToken(orderContext);
        queryParams=getQueryParamsForFetchCartAPI();
        return this;
    }


    @Override
    public ServiceHelper process() {
        log.info("Applying wallet for user: {}", orderContext.getPhoneNumber());

        JSONObject payload = new JSONObject();
        payload.put("customerMobile", orderContext.getPhoneNumber());

        response = RestUtils.get(V2_CART.getUrl(), headers, queryParams, 200);
        log.info("Apply wallet response: {}", response.asString());

        List<Map> arr = response.jsonPath().getList("result.totals.discounts");
        log.info("Discounts from response: {}", arr.toString());

        boolean discountWalletFound = false;
        for (Map obj : arr) {
            if (!obj.containsKey("type")) {
                continue;
            }
            String type = obj.get("type").toString();
            if ("lenskartplus".equalsIgnoreCase(type) || "lenskart".equalsIgnoreCase(type)) {
                discountWalletFound = true;
                break;
            }
        }

        softAssert.assertTrue(discountWalletFound, "Wallet discount is not applied");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        if(orderContext.getIsValidationRequired()) {
            CartValidator.builder()
                    .orderContext(orderContext)
                    .build()
                    .validateNode();
        }



        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}