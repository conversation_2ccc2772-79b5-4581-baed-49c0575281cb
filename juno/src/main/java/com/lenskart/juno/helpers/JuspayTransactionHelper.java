package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.juno.requestbuilder.JusPayRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.lenskart.juno.endpoints.JunoEndpoints.JUSPAY_TRANSACTION;

@SuperBuilder
@Slf4j
public class JuspayTransactionHelper extends JunoBaseHelper implements ServiceHelper {

    String prepaidOrderId;
    OrderContext orderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = JusPayRequestBuilder.createJuspayHeaders();
        return this;
    }

    @Override
    public ServiceHelper process() {
        log.info("JusPay transaction for order ID: {}", prepaidOrderId);

        Map<String, String> formParams = JusPayRequestBuilder.getJuspayFormParams(orderContext, prepaidOrderId);
        String formData = GenericUtils.buildFormData(formParams);

        response = RestUtils.postFormData(JUSPAY_TRANSACTION.getUrl(), headers, formData, 200);


        String status = (String) RestUtils.getValueFromResponse(response, "status");
        if ("PENDING_VBV".equals(status)) {
            String authUrl = (String) RestUtils.getValueFromResponse(response, "payment.authentication.url");

            log.info(" authentication required. Auth URL: {}", authUrl);

            if (authUrl != null && !authUrl.isEmpty()) {
                PlaywrightPaymentHelper.builder()
                        .authenticationUrl(authUrl)
                        .prepaidOrderId(prepaidOrderId)
                        .orderContext(orderContext)
                        .build()
                        .test();
            }
        } else {
            log.info("No  authentication required. Status: {}", status);
            // Assert for failure - auth is always required, if authUrl is not coming then probably there is some issue from juspay
            assert false : "Authentication is always required. Status: " + status + " - Possible issue with Juspay response";
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
} 