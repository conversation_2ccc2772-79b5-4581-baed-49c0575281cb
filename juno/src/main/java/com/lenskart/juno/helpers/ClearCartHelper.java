package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_CART_ITEMS;


@SuperBuilder
@Slf4j
public class ClearCartHelper extends JunoBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.delete(V2_CART_ITEMS.getUrl(), headers);
        if (response.getStatusCode() == 404) {
            log.info("Cart is already empty");
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
