package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.testng.asserts.SoftAssert;
import static com.lenskart.juno.endpoints.JunoEndpoints.GET_CASHBACK_DETAILS;



@SuperBuilder
@Slf4j
public class CashBackHelper extends JunoBaseHelper implements ServiceHelper {
    OrderContext orderContext;
    Response response;
    int pendingCashBack;
    static SoftAssert softAssert = new SoftAssert();
    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_CASHBACK_DETAILS.getUrl(), headers, null, 200);
        JSONObject orderResponse = new JSONObject(response.asString());
        log.info("Order response: {}", orderResponse);
        JSONObject pendingCashback = orderResponse.getJSONObject("result").getJSONObject("pendingCashback");
        pendingCashBack = pendingCashback.getInt("value");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        softAssert.assertTrue(pendingCashBack > 0.0, "Cashback amount must be greater than 0");
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}