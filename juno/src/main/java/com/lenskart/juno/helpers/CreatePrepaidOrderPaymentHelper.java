package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.requestbuilder.JusPayRequestBuilder;
import com.lenskart.juno.validator.order.OrderValidator;
import com.lenskart.juno.validator.payment.PaymentValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.util.List;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_ORDER_PAYMENT;

@SuperBuilder
@Slf4j
public class CreatePrepaidOrderPaymentHelper extends JunoBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;

    @Override
    public ServiceHelper init() {

        headers = getHeadersWithSessionToken(orderContext);
        payload = JusPayRequestBuilder.createPayloadForOrderPayment(orderContext);
        log.info("Created EMI payload: {}", payload.toString());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(V2_ORDER_PAYMENT.getUrl(), headers, payload.toString(), 200);

        String responseBody = response.getBody().asString();
        if (!responseBody.contains("Your order is rolled back")) {
            orderContext.setFinalOrderAmount(BigDecimal.valueOf((float) RestUtils.getValueFromResponse(response, "result.payment.amount.price")));
        } else {
            log.info("Order rollback detected, skipping price validation");
        }

        orderContext.setOrderId((int) RestUtils.getValueFromResponse(response, "result.order.id"));
        log.info("Order created with id: {}", orderContext.getOrderId());

        AwaitUtils.sleepSeconds(3);

        if (orderContext.getIsValidationRequired()) {
            OrderValidator.builder()
                    .orderContext(orderContext)
                    .build()
                    .validateNode();


        }
        AwaitUtils.sleepSeconds(3);

        String actionInfoPayload = (String) RestUtils.getValueFromResponse(response, "result.payment.actionInfo.requestParams.payload");
        if (actionInfoPayload != null && !actionInfoPayload.isEmpty()) {
            try {
                JSONObject actionPayloadJson = new JSONObject(actionInfoPayload);
                String prepaidOrderId = actionPayloadJson.getString("orderId");
                log.info("Prepaid order ID extracted: {}", prepaidOrderId);

                JuspayTransactionHelper.builder()
                        .prepaidOrderId(prepaidOrderId)
                        .orderContext(orderContext)
                        .build()
                        .test();
            } catch (Exception e) {
                log.warn("Failed to extract prepaid order ID: {}", e.getMessage());
            }
        } else {
            log.warn("ActionInfo payload is null or empty");
        }

        List<OrderContext.ProductList> productLists = orderContext.getProductLists();
        int itemCount = 0;
        for (OrderContext.ProductList productList : productLists) {
            productList.setItemId((int) RestUtils.getValueFromResponse(response, "result.order.items["
                    .concat(String.valueOf(itemCount))
                    .concat("].id")));
            itemCount++;
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
} 