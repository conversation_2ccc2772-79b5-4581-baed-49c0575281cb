package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;


@SuperBuilder
@Slf4j
public class WalletTokenHelper extends JunoBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        if (orderContext.isGuestUser()) {
            log.info("Skipping wallet token generation for guest user");
            return this;
        }
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        log.info("Generating wallet token for user: {}", orderContext.getPhoneNumber());

        WalletTokenGenerationHelper tokenHelper = WalletTokenGenerationHelper.builder()
                .orderContext(orderContext)
                .build();
        tokenHelper.test();
        String walletToken = tokenHelper.getWalletToken();
        log.info("Generated wallet token: {}", walletToken);

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}