package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.config.JunoConfigRegistry;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.AriaRole;
import com.microsoft.playwright.options.Geolocation;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import java.util.ArrayList;
import java.util.List;

import static com.lenskart.juno.endpoints.JunoEndpoints.CHECKOUT_SUCCESS;

@SuperBuilder
@Slf4j
public class PlaywrightRePaymentHelper extends JunoBaseHelper implements ServiceHelper {

    String mobileNumber;
    OrderContext orderContext;

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        try (Playwright playwright = Playwright.create()) {

            Browser browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                    .setHeadless(true)
                    .setArgs(List.of("--disable-notifications"))); // Block notification popups

            // Block location by not granting permission
            BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                    .setPermissions(new ArrayList<>()) // No location permission
                    .setGeolocation(new Geolocation(28.6139, 77.2090)) // Optional mock location
                    .setLocale("en-US")
            );

            Page page = context.newPage();
            page.navigate(JunoConfigRegistry.getInstance().getBaseUrl("webUrl"));

            // Optionally hide HTML modals (push or location prompts)
            page.evaluate("() => {" +
                    "  const locPopup = document.querySelector('[aria-label=\"Know your location\"]');" +
                    "  if (locPopup) locPopup.style.display = 'none';" +
                    "  const pushBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent?.includes('Sign me up!'));" +
                    "  if (pushBtn?.parentElement) pushBtn.parentElement.style.display = 'none';" +
                    "}");
            page.locator("xpath=.//button//span[text()='Sign In']").click();
            page.locator("xpath=.//input[@placeholder='Mobile / Email']").type(mobileNumber);
            page.locator("xpath=.//button[@id='remove-button']").click();
            page.waitForTimeout(5000);
            page.locator("xpath=.//input[contains(@class,'OtpTextField')]").fill("7352");
            page.waitForTimeout(5000);
            page.navigate(JunoConfigRegistry.getInstance().getBaseUrl("webUrlOrderDetails")+orderContext.getOrderId());
            page.waitForTimeout(5000);
            page.locator("xpath=.//div[text()='Pay Now']").click();
            page.waitForTimeout(10000);
            page.locator("xpath=.//div[@id='juspay_card']//div[text()='**** 4242']").click();
            Locator iframes = page.locator("iframe.security_code_iframe");
            int count = iframes.count();
            for (int i = 0; i < count; i++) {
                FrameLocator iframe = page.frameLocator("iframe.security_code_iframe").nth(i);
                if (iframe.locator("input[placeholder='Enter CVV']").isVisible()) {
                    iframe.locator("input[placeholder='Enter CVV']").fill("123");
                    break;
                }
            }
            page.locator("xpath=.//h3[contains(@title,'Pay Now')]").click();
            page.waitForTimeout(10000);
            page.locator("#password").click();
            page.locator("#password").fill("123456");
            page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("PAY")).click();
            page.waitForTimeout(20000);
            Assert.assertTrue(page.url().contains(CHECKOUT_SUCCESS.getEndpoint()),"URL does not contain success text");
            browser.close();
        } catch (Exception e) {
            log.error("Error details: {}", e.getMessage());
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
} 