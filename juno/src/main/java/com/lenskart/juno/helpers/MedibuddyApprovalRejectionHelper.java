package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.requestbuilder.JunoOrderRequestBuilder;
import com.lenskart.juno.validator.order.MedibuddyValidator;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.time.Duration;

import static com.lenskart.juno.endpoints.JunoEndpoints.*;

@SuperBuilder
@Slf4j
@Getter
public class MedibuddyApprovalRejectionHelper extends JunoBaseHelper implements ServiceHelper {
    JSONObject payload;
    JSONObject MedibuddyPayload;
    OrderContext orderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        payload = JunoOrderRequestBuilder.dracoLoginPayload(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        approveRejectMedibuddyOrder(orderContext.getIsMedibuddyApprovalRequired());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        // TODO - Add Medibuddy order validator
        if (orderContext.getIsValidationRequired()) {
            log.info("Medibuddy order Validator");
            AwaitUtils.sleepSeconds(2);
            MedibuddyValidator.builder()
                    .orderContext(orderContext)
                    .build()
                    .validateNode();
        }

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    private void approveRejectMedibuddyOrder(boolean isMedibuddyHelperRequired){
        AwaitUtils.sleepSeconds(3);

        // Draco Auth token generation
        response = RestUtils.post(DRACO_AUTH_TOKEN.getUrl(), headers, payload.toString(), 200);
        String token = (String) RestUtils.getValueFromResponse(response, "result.access_token");
        headers.put(HeaderMapper.X_AUTH_TOKEN.getHeaderName(), "Bearer ".concat(token));

        if(isMedibuddyHelperRequired){
            MedibuddyPayload = JunoOrderRequestBuilder.medibuddyApprovalPayload(orderContext);
        }
        else {
            MedibuddyPayload = JunoOrderRequestBuilder.medibuddyRejectPayload(orderContext);
        }

        queryParams = getQueryParamsForMedibuddyStatusUpdateAPI();

        boolean success = AwaitUtils.retryOperation(
                () -> {
                    try {
                        response = RestUtils.put(MEDIBUDDY_UPDATE_STATUS_API.getUrl(), headers, queryParams, MedibuddyPayload.toString());
                        if (response.statusCode() == 200) {
                            return true;
                        } else {
                            log.warn("Medibuddy status update failed with status: {}. Retrying...", response.statusCode());
                            return false;
                        }
                    } catch (Exception e) {
                        log.error("Exception during Medibuddy status update: {}", e.getMessage(), e);
                        return false;
                    }
                },
                "Retry Medibuddy Status Update",
                3,
                Duration.ofSeconds(3)
        );

        if (!success) {
            throw new RuntimeException("Medibuddy approval failed after 3 retry attempts. Order ID: " + orderContext.getOrderId());
        }
    }

}
