package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.validator.cart.CartValidator;
import io.restassured.response.Response;
import static io.restassured.RestAssured.*;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.lenskart.juno.endpoints.JunoEndpoints.*;

@SuperBuilder
@Slf4j
@Getter
public class StoreCreditHelper extends JunoBaseHelper implements ServiceHelper{

    private OrderContext orderContext;
    private Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }


    @Override
    public ServiceHelper process() {

        // Apply SC to Cart
        response = RestUtils.post(APPLY_STORE_CREDIT.getUrl(Map.of("storeCode", orderContext.getStoreCreditList().getStoreCode().getCode(), "amount", "5")), headers, null);
        return this;
    }


    @Override
    public ServiceHelper validate() {
        if (orderContext.getIsValidationRequired()) {
            CartValidator.builder()
                    .orderContext(orderContext)
                    .build()
                    .validateNode();
        }
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}