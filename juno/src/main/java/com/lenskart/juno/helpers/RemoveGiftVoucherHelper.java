package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.validator.cart.CartValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;
import com.lenskart.juno.util.GiftVoucherUtil;
import org.json.JSONObject;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_CART_GIFT_VOUCHER;

@SuperBuilder
@Slf4j
public class RemoveGiftVoucherHelper extends JunoBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        // Get current cart details
        GetCartDetailsHelper cartDetailsHelper = (GetCartDetailsHelper)
                GetCartDetailsHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();

        JSONObject cartJson = new JSONObject(cartDetailsHelper.response.asString());
        String existingVoucherCode = GiftVoucherUtil.getFirstApplicableVoucher(cartJson);

        if (existingVoucherCode == null) {
            log.info("No gift voucher found in cart. Skipping voucher removal.");
            return this;
        }
        Map<String, String> params = Map.of("giftVoucherCode", existingVoucherCode);
        String url = V2_CART_GIFT_VOUCHER.getUrl(params);
        log.info("Removing gift voucher {} from cart using DELETE {}", existingVoucherCode, url);
        response = RestUtils.delete(url, headers, 200);
        log.info("Remove gift voucher response: {}", response.asString());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        if(orderContext.getIsValidationRequired()) {}
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
} 