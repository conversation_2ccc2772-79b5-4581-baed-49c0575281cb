package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.requestbuilder.JunoOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;

import static com.lenskart.commons.constants.HeaderMapper.X_API_AUTH_TOKEN;
import static com.lenskart.juno.endpoints.JunoEndpoints.WALLET_BULK_CREDIT;

@SuperBuilder
@Slf4j
public class WalletBulkCreditHelper extends JunoBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    String walletAuthToken;
    int amount;
    String phoneNumber;

    @Override
    public ServiceHelper init() {
        if (orderContext.isGuestUser()) {
            log.info("Skipping wallet bulk credit for guest user");
            return this;
        }
        headers = getHeadersWithSessionToken(orderContext);

        /* Add wallet auth token header */
        headers.put(X_API_AUTH_TOKEN.getHeaderName(), walletAuthToken);
        return this;
    }

    @Override
    public ServiceHelper process() {

        /* Create bulk credit payload using JunoOrderRequestBuilder */
        JSONArray req = JunoOrderRequestBuilder.createBulkCreditPayload(amount, phoneNumber);

        response = RestUtils.post(WALLET_BULK_CREDIT.getUrl(), headers, req.toString(), 200);
        log.info("Bulk credit response: {}", response.asString());

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}