package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.validator.cart.CartValidator;
import com.lenskart.juno.validator.order.OrderValidator;
import com.lenskart.juno.validator.order.MedibuddyValidator;
import com.lenskart.juno.validator.payment.PaymentValidator;
import com.lenskart.juno.validator.session.SessionValidator;

import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@SuperBuilder
@Slf4j
public class CommonValidationHelper extends JunoBaseHelper implements ServiceHelper {

    private OrderContext orderContext;

    @Override
    public ServiceHelper init() {
        if (!orderContext.getIsValidationRequired()) {
            log.info("Validation not required for order ID: {}", orderContext.getOrderId());
            return this;
        }
        log.info("Initializing CommonValidationHelper for order ID: {}", orderContext.getOrderId());
        return this;

    }

    @Override
    public ServiceHelper process() {

        log.info("Starting comprehensive validation for order ID: {}", orderContext.getOrderId());

        // Validate Order
        log.info("Validating order for order ID: {}", orderContext.getOrderId());
        OrderValidator.builder()
                .orderContext(orderContext)
                .build()
                .validateNode();

        // Validate Payment
        log.info("Validating payment for order ID: {}", orderContext.getOrderId());
        PaymentValidator.builder()
                .orderContext(orderContext)
                .build()
                .validateNode();


        // Validate Medibuddy
        if (orderContext.getIsMedibuddyOrder()) {
            log.info("Validating Medibuddy for order ID: {}", orderContext.getOrderId());
            MedibuddyValidator.builder()
                    .orderContext(orderContext)
                    .build()
                    .validateNode();
        }

        log.info("Comprehensive validation completed successfully for order ID: {}", orderContext.getOrderId());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
} 