package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import static com.lenskart.juno.endpoints.JunoEndpoints.V2_CART_ESTIMATIONS;
import com.lenskart.juno.requestbuilder.JunoOrderRequestBuilder;


@SuperBuilder
@Slf4j
public class CartEstimationHelpers extends JunoBaseHelper implements ServiceHelper {
    private OrderContext orderContext;
    JSONObject payload;
    private Response response;

    @Override
    public ServiceHelper init() {
        {
            headers = getHeadersForAdminAPI(orderContext);
            return this;
        }
    }


    @Override
    public ServiceHelper process() {

        AuthenticationHelper.builder().orderContext(orderContext).build().test();
        GetProductAndPackageHelper.builder().orderContext(orderContext).build().test();

        if (orderContext.getProductLists().isEmpty()) {
            throw new RuntimeException("No products added to the order context");
        } else {
            int itemCount = 0;
            for (OrderContext.ProductList product : orderContext.getProductLists()) {
                JSONObject payload = JunoOrderRequestBuilder.createPayloadForAddCart(product);
                response = RestUtils.post(V2_CART_ESTIMATIONS.getUrl(), headers, payload.toString(), 200);

                log.info("Estimation response for product {} (index {}): {}", product.getProductId(), itemCount, response.asString());
                itemCount++;
            }
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}