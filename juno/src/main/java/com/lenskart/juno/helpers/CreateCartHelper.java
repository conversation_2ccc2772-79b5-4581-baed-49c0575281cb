package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.validator.cart.CartValidator;
import com.lenskart.juno.requestbuilder.JunoOrderRequestBuilder;
import com.lenskart.juno.helpers.UpdateGiftVoucherHelper;
import com.lenskart.juno.util.GiftVoucherUtil;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.List;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_CART;

@SuperBuilder
@Slf4j
public class CreateCartHelper extends JunoBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (orderContext.getProductLists().isEmpty()) {
            throw new RuntimeException("No products added to the order context");
        } else {
            List<OrderContext.ProductList> productLists = orderContext.getProductLists();
            int itemCount = 0;
            for (OrderContext.ProductList productList : productLists) {
                payload = JunoOrderRequestBuilder.createPayloadForAddCart(productList);
                response = RestUtils.post(V2_CART.getUrl(), headers, payload.toString(), 200);
                productList.setItemId((int) RestUtils.getValueFromResponse(response, "result.items["
                        .concat(String.valueOf(itemCount))
                        .concat("].id")));
                orderContext.setCartId((int) RestUtils.getValueFromResponse(response, "result.cartId"));
                itemCount++;
            }
        }
        // After cart creation, remove any gift voucher if present using RemoveGiftVoucherHelper
        RemoveGiftVoucherHelper.builder()
            .orderContext(orderContext)
            .build()
            .init()
            .process();
        return this;
    }

    @Override
    public ServiceHelper validate() {
        if (orderContext.getIsValidationRequired()) {
            CartValidator.builder()
                    .orderContext(orderContext)
                    .build()
                    .validateNode();
        }
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
