package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.lenskart.juno.endpoints.JunoEndpoints.GET_REVIEW;

@Slf4j
@SuperBuilder
public class GetReviewsHelper extends JunoBaseHelper implements ServiceHelper {

    private OrderContext orderContext;
    Response response;



    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        queryParams = getQueryParamsForReview("review", 10);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_REVIEW.getUrl( Map.of("productID", orderContext.getProductLists().get(0).getProductId())), headers, queryParams, 200);
        log.info(JsonUtils.convertObjectToJsonString(response.asPrettyString()));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
