package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_CANCEL_ORDER;

@SuperBuilder
@Slf4j
public class CancelOrderHelper extends JunoBaseHelper implements ServiceHelper {
    private OrderContext orderContext;
    private Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(V2_CANCEL_ORDER.getUrl(Map.of("orderID", String.valueOf(orderContext.getOrderId()))), headers, null, 200);
        log.info("Order cancelled successfully: {}", orderContext.getOrderId());
        return this;
    }

    @Override
    public ServiceHelper validate() {

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}