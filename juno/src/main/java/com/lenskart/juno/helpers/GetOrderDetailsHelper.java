package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.config.JunoConfigRegistry;
import com.lenskart.juno.util.OrderUtil;
import com.lenskart.juno.validator.order.AthenaNotificationBulkValidator;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;


import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_GET_ORDER_DETAILS;

@SuperBuilder
@Slf4j
@Getter
public class GetOrderDetailsHelper extends JunoBaseHelper implements ServiceHelper {
    OrderContext orderContext;
    Response response;
    boolean isOrderSyncCheckRequired;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        headers.put(HeaderMapper.X_AUTH_TOKEN.getHeaderName(), JunoConfigRegistry.getInstance().getAdminToken());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(V2_GET_ORDER_DETAILS.getUrl(Map.of("orderID", String.valueOf(orderContext.getOrderId()))), headers, null, 200);
        orderContext.setConfirmOrder(OrderUtil.setConfirmOrder(response));
        log.info("Confirm order getProductLists : {}",orderContext.getProductLists());
        if (orderContext.isPosOrder()) {
            List<OrderContext.ProductList> productLists = orderContext.getProductLists();
            int itemCount = 0;
            for (OrderContext.ProductList productList : productLists) {
                productList.setItemId((int) RestUtils.getValueFromResponse(response, "result.items["
                        .concat(String.valueOf(itemCount))
                        .concat("].id")));
                itemCount++;
            }
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        if(isOrderSyncCheckRequired) {
            AwaitUtils.sleep(Duration.ofSeconds(2));
            AthenaNotificationBulkValidator.builder().orderContext(orderContext).build().validateNode();
        }
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}