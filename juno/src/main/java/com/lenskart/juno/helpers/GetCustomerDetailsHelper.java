package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.schema.v2.customer.Customer;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.commons.constants.Constants.RESULT;
import static com.lenskart.commons.constants.Constants.RESULT_INDEX;
import static com.lenskart.juno.endpoints.JunoEndpoints.GET_CUSTOMER_DETAILS;
import static com.lenskart.juno.endpoints.JunoEndpoints.V2_SESSIONS;

@SuperBuilder
@Getter
public class GetCustomerDetailsHelper extends JunoBaseHelper implements ServiceHelper {


    private JSONObject payload;
    private OrderContext orderContext;
    private Response response;
    OrderContext.Headers orderContextHeader;
    Customer customer;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeaders(orderContext);
        queryParams = getQueryParamsForAdminAPI(orderContext);
        orderContextHeader = orderContext.getHeaders();
        return this;
    }

    @Override
    public ServiceHelper process() {

        /* Fetch session Token */
        response = RestUtils.post(V2_SESSIONS.getUrl(), headers, null, 200);
        orderContextHeader.setSessionToken((String) RestUtils.getValueFromResponse(response, "result.id"));

        headers = getHeadersForAdminAPI(orderContext);
        response = RestUtils.get(GET_CUSTOMER_DETAILS.getUrl(), headers, queryParams, 200);
        customer = parseResponse(RestUtils.getValueFromResponse(response, RESULT_INDEX), Customer.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
