package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.schema.v2.customer.Customer;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;
import static com.lenskart.commons.constants.Constants.RESULT_INDEX;
import static com.lenskart.juno.endpoints.JunoEndpoints.*;

@SuperBuilder
@Getter
public class GetCustomerDetailsPlainHelper extends JunoBaseHelper implements ServiceHelper {
     OrderContext orderContext;
     Response response;
     Customer customer;

    @Override
    public ServiceHelper init() {
        statusCode =200;
        return this;
    }

    @Override
    public ServiceHelper process() {
        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        // Get customer details
        headers = getHeadersWithSessionToken(orderContext);
        response = RestUtils.get(GET_CUSTOMER_DETAILS_PLAIN.getUrl(), headers,null ,200);
        customer = parseResponse(RestUtils.getValueFromResponse(response, RESULT_INDEX), Customer.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}