package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.requestbuilder.JunoOrderRequestBuilder;
import com.lenskart.juno.validator.cart.CartValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_CARTS_SHIPPING_ADDRESS;

@SuperBuilder
@Slf4j
public class AddShippingAddressHelper extends JunoBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        payload = JunoOrderRequestBuilder.createPayloadForShippingAddress(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(V2_CARTS_SHIPPING_ADDRESS.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        if(orderContext.getIsValidationRequired()) {
            CartValidator.builder()
                    .orderContext(orderContext)
                    .build()
                    .validateNode();
        }
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
