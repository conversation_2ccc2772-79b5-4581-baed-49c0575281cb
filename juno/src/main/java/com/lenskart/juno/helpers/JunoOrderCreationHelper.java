package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PaymentMethod;
import com.lenskart.commons.model.PowerTypes;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import java.util.List;


@SuperBuilder
@Slf4j
public class JunoOrderCreationHelper extends JunoBaseHelper implements ExecutionHelper {

    OrderContext orderContext;
    JSONObject payload;
    Response response;
    boolean isGuestUser;
    boolean isPrescriptionRequired;
    boolean isCartPrescriptionRequired;
    boolean isWalletUsed;



    /*
     * method to create order
     * call all the apis in sequential order
     * and store intermediate state and headers in
     * the order context
     *  */


    @Override
    public ExecutionHelper init() {
        orderContext.setGuestUser(isGuestUser);
        List<OrderContext.ProductList> productLists = orderContext.getProductLists();
        if (isPrescriptionRequired) {
            if (productLists != null) {
                for (OrderContext.ProductList productList : productLists) {
                    productList.isPrescriptionRequired = true;
                }
            }
        }

        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        if (orderContext.isGuestUser()) {
            /* Create Session for Guest User */
            CreateSessionHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        } else {
            /* Authenticate User */
            AuthenticationHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        }

        /* Search for product and get package id */
        GetProductAndPackageHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Clear Cart */
        ClearCartHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Create Cart */
        CreateCartHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        if (orderContext.getIsCartPrescriptionRequired() && orderContext.getIsCartPrescriptionRequired() != null ){
            /* Create Prescription */
            CartUpdatePrescriptionHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        }

        if (orderContext.getIsWalletUsed() && !orderContext.isGuestUser()) {
            FetchWalletHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();

            WalletApplyHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        }
        /* Add Shipping Address */
        AddShippingAddressHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();



        /* update GV */
        if (orderContext.getGiftVoucherList() != null && orderContext.getGiftVoucherList().isGiftVoucherApplicable()) {
            UpdateGiftVoucherHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        }

        /* Add SC */
        if (orderContext.getStoreCreditList() != null && orderContext.getStoreCreditList().isStoreCreditApplicable() && !orderContext.isGuestUser()){
            StoreCreditHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        }



        /* Create Order Payment */
        if (orderContext.getPaymentMethod().equals(PaymentMethod.CREDIT_CARD) ||
                orderContext.getPaymentMethod().equals(PaymentMethod.DEBIT_CARD) ||
                orderContext.getPaymentMethod().equals(PaymentMethod.EMI)) {

            CreatePrepaidOrderPaymentHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        } else {
            // Default to CreateOrderPaymentHelper for other payment methods
            CreateOrderPaymentHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        }

        /* Get Order Details */
        GetOrderDetailsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Confirm Order */
        if (orderContext.getPaymentMethod() != null && PaymentMethod.COD.equals(orderContext.getPaymentMethod())
                && orderContext.isConfirmOrder()
                && orderContext.getHeaders().getClient().equals(Client.DESKTOP)) {
            ConfirmOrderHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();

        }

        /* Create Prescription */
        List<OrderContext.ProductList> productLists = orderContext.getProductLists();
        if (productLists != null) {
            for (OrderContext.ProductList productList : productLists) {
                // Check if prescription is required and power type is valid
                if (productList.isPrescriptionRequired &&
                        productList.getPowerType() != null) {
                    String powerTypeDisplayName = productList.getPowerType().getDisplayName();
                    log.info("Checking prescription creation for product: {} with powerType: {}",
                            productList.getProductId(), powerTypeDisplayName);
                    // Create prescription for products that are not ZERO_POWER or CONTACT_LENS
                    if (!PowerTypes.ZERO_POWER.getDisplayName().equals(powerTypeDisplayName) &&
                            !PowerTypes.CONTACT_LENS.getDisplayName().equals(powerTypeDisplayName)) {
                        log.info("Creating prescription for product: {} with powerType: {}",
                                productList.getProductId(), powerTypeDisplayName);
                        CreatePrescriptionHelper.builder()
                                .orderContext(orderContext)
                                .build()
                                .test();
                    } else {
                        log.info("Skipping prescription creation for product: {} with powerType: {} (ZERO_POWER or CONTACT_LENS)",
                                productList.getProductId(), powerTypeDisplayName);
                    }
                } else {
                    log.info("Skipping prescription creation for product: {} - isPrescriptionRequired: {}, powerType: {}",
                            productList.getProductId(),
                            productList.isPrescriptionRequired,
                            productList.getPowerType());
                }
            }
        }

        /* Insurance Approval Handling */
        if (orderContext.getIsMedibuddyOrder()) {
            MedibuddyApprovalRejectionHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        }

        /* Get Order Details */
        GetOrderDetailsHelper.builder()
                .isOrderSyncCheckRequired(true)
                .orderContext(orderContext)
                .build()
                .test();

        CommonValidationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();


        log.info("OrderId : {}", orderContext.getOrderId());


        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;

    }
}