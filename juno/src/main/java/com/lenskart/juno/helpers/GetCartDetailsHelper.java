package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_CART;

@SuperBuilder
@Slf4j
@Getter
public class GetCartDetailsHelper extends JunoBaseHelper implements ServiceHelper {
    OrderContext orderContext;
    JSONObject payload;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        queryParams=getQueryParamsForFetchCartAPI();
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (orderContext.getIsWalletUsed() && !orderContext.isGuestUser()) {

            response = RestUtils.get(V2_CART.getUrl(), headers, queryParams, 200);

        }
            else {
            response = RestUtils.get(V2_CART.getUrl(), headers, null, 200);


        }


        return this;
    }


    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}