package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.ConcurrencyTestUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.models.WalletCreditRequest;
import com.lenskart.juno.validator.wallet.WalletValidator;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import static com.lenskart.juno.endpoints.JunoEndpoints.CREDIT_WALLET;

/**
 * Helper class for Wallet API operations
 */

@Slf4j
@SuperBuilder
@Getter
public class WalletApiHelper extends JunoBaseHelper implements ServiceHelper {

    private int threadCount;
    private String testAmount;
    private String customerId;
    private String mobileNumber;
    private String sessionToken;
    private String authToken;
    private String countryCode;
    private String merchantRefId;
    ConcurrencyTestUtils.ConcurrentExecutionResult result;



    @Override
    public ServiceHelper init() {
        headers = createWalletHeaders(sessionToken, authToken, customerId, countryCode);
        return this;
    }

    @Override
    public ServiceHelper process() {

        String url = CREDIT_WALLET.getUrl();

        ConcurrencyTestUtils.ConcurrentExecutionConfig config = ConcurrencyTestUtils.ConcurrentExecutionConfig.builder()
                .threadCount(threadCount)
                .logIndividualResponses(false)
                .build();

        result = ConcurrencyTestUtils.executeConcurrentRequests(config, () -> {
            WalletCreditRequest uniqueRequest = WalletCreditRequest.createTestRequest(
                    mobileNumber,
                    testAmount,
                    merchantRefId
            );

            String requestBody = JsonUtils.convertObjectToJsonString(uniqueRequest);
            return RestUtils.post(url, headers, requestBody);
        });

        return this;
    }

    @Override
    public ServiceHelper validate() {

        WalletValidator.builder()
                .result(result)
                .build()
                .validateNode();

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
