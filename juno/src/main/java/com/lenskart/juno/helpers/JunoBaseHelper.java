package com.lenskart.juno.helpers;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.juno.exceptions.JunoExceptionStates;
import com.lenskart.juno.config.JunoConfigRegistry;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.lenskart.commons.constants.Constants.APPLICATION_JSON;
import static com.lenskart.commons.constants.HeaderMapper.*;
import static com.lenskart.commons.constants.HeaderMapper.CONTENT_TYPE;
import static com.lenskart.commons.constants.HeaderMapper.X_AUTH_TOKEN;
import static com.lenskart.commons.constants.HeaderMapper.X_CLIENT_ORG;
import static com.lenskart.commons.constants.HeaderMapper.X_CUSTOMER_ID;
import static com.lenskart.juno.constants.Constants.LENSKART;

@SuperBuilder
@Slf4j
public class JunoBaseHelper extends BaseHelper<JunoExceptionStates, Object> {


    public Map<String, String> getHeaders(OrderContext orderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_COUNTRY_CODE.getHeaderName(), orderContext.getCountryCodeMapper().getCountry().name());
        headers.put(HeaderMapper.X_API_CLIENT.getHeaderName(), orderContext.getHeaders().getClient().getDisplayName());
        return headers;
    }

    public Map<String, String> getHeadersWithSessionToken(OrderContext orderContext) {
        headers = getHeaders(orderContext);
        headers.put(HeaderMapper.X_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getSessionToken());
        headers.put(HeaderMapper.CONTENT_TYPE.getHeaderName(), "application/json");
        return headers;
    }

    public Map<String, String> getHeadersForAdminAPI(OrderContext orderContext) {
        headers = getHeaders(orderContext);
        headers.put(HeaderMapper.X_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getSessionToken());
        headers.put(HeaderMapper.X_AUTH_TOKEN.getHeaderName(), JunoConfigRegistry.getInstance().getAdminToken());
        return headers;
    }

    public Map<String, Object> getQueryParams(String frameType, PowerTypes powerType) {
        queryParams = new HashMap<>();
        queryParams.put("frame_type", frameType);
        queryParams.put("power_type", Objects.isNull(powerType) ? PowerTypes.SINGLE_VISION.getDisplayName() : powerType.getDisplayName());
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForAdminAPI(OrderContext orderContext) {
        queryParams = new HashMap<>();
        queryParams.put("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode());
        queryParams.put("mobile", orderContext.getPhoneNumber());
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForReview(String review, int count) {
        queryParams = new HashMap<>();
        queryParams.put("view", review);
        queryParams.put("reviewLimit", count);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForAthenaNotificationBulk(OrderContext orderContext) {
        queryParams = new HashMap<>();
        queryParams.put("orders", orderContext.getOrderId());
        return queryParams;
    }
    public Map<String, Object> getQueryParamsForFetchCartAPI() {
        queryParams = new HashMap<>();
        queryParams.put("applyWallet", true);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForMedibuddyStatusUpdateAPI() {
        queryParams = new HashMap<>();
        queryParams.put("type", "ORDER");
        return queryParams;
    }

    public Map<String, String> createWalletHeaders(String sessionToken, String authToken,
                                                          String customerId, String countryCode) {
        headers = new HashMap<>();
        headers.put(X_SESSION_TOKEN.getHeaderName(), sessionToken);
        headers.put(X_API_CLIENT.getHeaderName(), Client.DESKTOP.getDisplayName());
        headers.put(X_COUNTRY_CODE.getHeaderName(), countryCode);
        headers.put(X_AUTH_TOKEN.getHeaderName(), authToken);
        headers.put(X_CUSTOMER_ID.getHeaderName(), customerId);
        headers.put(X_CLIENT_ORG.getHeaderName(), LENSKART);
        headers.put(CONTENT_TYPE.getHeaderName(), APPLICATION_JSON);
        return headers;
    }


}
