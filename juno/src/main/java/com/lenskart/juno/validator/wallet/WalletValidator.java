package com.lenskart.juno.validator.wallet;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.utils.ConcurrencyTestUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.juno.models.WalletCreditResponse;
import io.restassured.response.Response;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Builder
public class WalletValidator implements IValidator {

    ConcurrencyTestUtils.ConcurrentExecutionResult result;

    @Override
    public void validateNode() {

        validateConcurrentWalletCreditResponses(result);
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        // to add db validations
    }

    /**
     * Validate wallet credit responses for concurrency testing
     */
    public static void validateConcurrentWalletCreditResponses(ConcurrencyTestUtils.ConcurrentExecutionResult result) {

        List<Response> responses = result.getResponses();

        // Validate all responses have expected status code
        long successfulResponses = responses.stream()
                .filter(response -> response.getStatusCode() == 200)
                .count();

        // Validate response structure and business logic
        int validBusinessResponses = 0;
        for (Response response : responses) {
            try {
                if (response.getStatusCode() == 200) {
                    WalletCreditResponse walletResponse = JsonUtils.parseJsonString(
                            response.getBody().asString(), WalletCreditResponse.class);

                    if (walletResponse != null && walletResponse.getStatus() == 200) {
                        validBusinessResponses++;
                    }
                }
            } catch (Exception e) {
                log.info("Exception in parsing {}", e.getMessage());
            }
        }

        Assert.assertEquals(successfulResponses, validBusinessResponses, "All responses should be successful");

        // Check for race conditions or locking issues
        validateWalletLockingMechanism(responses);
    }

    /**
     * Validate wallet locking mechanism by checking for race conditions
     */
    private static void validateWalletLockingMechanism(List<Response> responses) {

        // Check for duplicate transaction IDs (should not happen with proper locking)
        Map<String, Integer> transactionIdCounts = new HashMap<>();
        int duplicateTransactions = 0;

        for (Response response : responses) {
            try {
                if (response.getStatusCode() == 200) {
                    WalletCreditResponse walletResponse = JsonUtils.parseJsonString(
                            response.getBody().asString(), WalletCreditResponse.class);

                    String transactionId = walletResponse.getResult().getTransaction().getTransactionId();
                    transactionIdCounts.merge(transactionId, 1, Integer::sum);

                    if (transactionIdCounts.get(transactionId) > 1) {
                        duplicateTransactions++;
                    }
                }
            } catch (Exception e) {
                log.info("Could not parse response {}", e.getMessage());
            }
        }
        log.info("Duplicate transactions count is {}", duplicateTransactions);
        Assert.assertEquals(duplicateTransactions, 0, "Duplicate transactions should not occur");

    }
}
