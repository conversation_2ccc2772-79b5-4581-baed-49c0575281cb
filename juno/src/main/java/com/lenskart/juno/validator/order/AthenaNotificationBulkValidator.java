package com.lenskart.juno.validator.order;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.juno.database.JunoCommonDbUtils;
import com.lenskart.juno.helpers.AthenaNotificationBulkHelper;
import lombok.extern.slf4j.Slf4j;
import lombok.Builder;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONObject;

import java.time.Duration;
import java.util.Objects;

@Builder
@Slf4j
public class AthenaNotificationBulkValidator implements IValidator {

    OrderContext orderContext;
    JSONObject result;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        boolean success = AwaitUtils.retryOperation(
                () -> {
                    try {
                        Document document = JunoCommonDbUtils.getAthenaOrderLogDetails(orderContext.getOrderId());

                        if (document == null || document.isEmpty()) {
                            log.warn("Athena document is null or empty. Retrying sync...");
                            pushAthenaNotificationBulk();
                            return false;
                        }

                        log.info("Athena Notification Bulk Document: {}", document);
                        result = JsonUtils.convertStringToJson(document.toJson());
                        log.info("Athena Notification Bulk Json: {}", result);

                        if ("SYNCED".equals(result.optString("orderSyncStage"))) {
                            return true;
                        } else {
                            log.warn("Order not in SYNCED state. Found: {}", result.optString("orderSyncStage"));
                            pushAthenaNotificationBulk();
                            return false;
                        }
                    } catch (Exception e) {
                        log.error("Exception during Athena sync validation: {}", e.getMessage(), e);
                        return false;
                    }
                },
                "Retry Athena Notification Bulk",
                4,
                Duration.ofSeconds(4)
        );

        if (success) {
            log.info("✅ Successfully pushed the order to SCM");
        } else {
            log.error("❌ Failed to push order to SCM after retries");
            throw new RuntimeException("Failed to push order to SCM from JUNO after retries with error messages: " + result.optJSONArray("errorMessages") +"and OrderSyncStage: " + result.optString("orderSyncStage"));
        }
    }

    private void pushAthenaNotificationBulk() {
        AthenaNotificationBulkHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

}
