package com.lenskart.juno.validator.payment;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.util.PaymentApiDbExtractUtils;
import com.lenskart.juno.helpers.GetOrderDetailsHelper;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.util.List;

@Slf4j
@Builder
public class PaymentValidator implements IValidator {

    static SoftAssert softAssert = new SoftAssert();
    OrderContext orderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        String orderId = String.valueOf(orderContext.getOrderId());
        log.info("Starting payment validation for order ID: {}", orderId);

        Document paymentDetails = PaymentApiDbExtractUtils.fetchPaymentDetails(orderId);
        GetOrderDetailsHelper orderDetailsHelper = PaymentApiDbExtractUtils.fetchOrderDetailsHelper(orderContext);

        List<String> apiMethods = PaymentApiDbExtractUtils.extractApiPaymentMethods(orderDetailsHelper);
        List<String> dbMethods = PaymentApiDbExtractUtils.extractDbPaymentMethods(paymentDetails);

        validatePaymentMethods(apiMethods, dbMethods, orderId);

        double apiTotal = PaymentApiDbExtractUtils.extractApiAmountTotal(orderDetailsHelper);
        double dbTotal = PaymentApiDbExtractUtils.extractDbAmountTotal(paymentDetails);

        softAssert.assertEquals(apiTotal, dbTotal,
                "Total amount mismatch for order ID: " + orderId);

        // juspay_card
        if ((apiMethods.stream().map(String::toLowerCase).toList().contains("juspay_card")
                && dbMethods.contains("juspay_card"))
                || (apiMethods.stream().map(String::toLowerCase).toList().contains("juspay_emi")
                && dbMethods.contains("juspay_emi"))) {

            Document paymentStatus = (Document) paymentDetails.get("paymentStatus");
            Assert.assertNotNull(paymentStatus, "paymentStatus is missing in DB for order ID: " + orderId);

            String status = paymentStatus.getString("status");
            Assert.assertNotNull(status, "paymentStatus.status is null in DB for order ID: " + orderId);
            Assert.assertEquals(status, "SUCCESS", "Payment status is not SUCCESS for order ID: " + orderId);
            log.info("Payment status is SUCCESS for order ID: {}", orderId);
        } else {
            log.info("Skipping paymentStatus SUCCESS validation as payment method is not juspay_card for order ID: {}", orderId);
        }

        softAssert.assertAll();
        log.info("Payment validation completed successfully for order ID: {}", orderId);
    }

    private void validatePaymentMethods(List<String> apiMethods, List<String> dbMethods, String orderId) {
        List<String> apiLower = apiMethods.stream().map(String::toLowerCase).toList();
        boolean matches = (apiLower.contains("cc") && dbMethods.contains("juspay_card")) || apiLower.equals(dbMethods);
        log.debug("Validating payment methods: API={} DB={}", apiLower, dbMethods);
        softAssert.assertTrue(matches, "Payment methods mismatch for order ID: " + orderId);
    }
}

