package com.lenskart.juno.validator.cart;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.util.CartApiDbExtractUtils;
import com.lenskart.juno.helpers.GetCartDetailsHelper;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@Builder
public class CartValidator implements IValidator {

    private final OrderContext orderContext;
    static SoftAssert softAssert = new SoftAssert();

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        int cartId = orderContext.getCartId();
        log.info("Starting cart validation for cart ID: {}", cartId);

        Document cartDetails = CartApiDbExtractUtils.fetchCartDetails(cartId);
        GetCartDetailsHelper apiCartHelper = CartApiDbExtractUtils.fetchCartDetailsFromApi(orderContext);

        validateBasicCartFields(cartDetails, apiCartHelper, cartId);
        validateGFVoucherFields(cartDetails, apiCartHelper, cartId);
        validateSCVoucherFields(cartDetails, apiCartHelper, cartId);
        validateWalletFields(cartDetails, apiCartHelper, cartId);

        softAssert.assertAll();
        log.info("Cart validation completed successfully for cart ID: {}", cartId);
    }

    private void validateBasicCartFields(Document dbCart, GetCartDetailsHelper apiHelper, int cartId) {
        String apiCartId = String.valueOf(com.lenskart.commons.utils.RestUtils.getValueFromResponse(apiHelper.getResponse(), "result.cartId"));
        String apiCustomerId = String.valueOf(com.lenskart.commons.utils.RestUtils.getValueFromResponse(apiHelper.getResponse(), "result.customerId"));
        String apiCurrencyCode = String.valueOf(com.lenskart.commons.utils.RestUtils.getValueFromResponse(apiHelper.getResponse(), "result.currencyCode"));

        String dbCartId = String.valueOf(dbCart.get("_id"));
        String dbCustomerId = String.valueOf(dbCart.get("customerId"));
        String dbCurrencyCode = String.valueOf(dbCart.get("currencyCode"));

        log.debug("Validating cart fields for cart ID: {}", cartId);
        softAssert.assertEquals(apiCartId, dbCartId, "Cart ID mismatch");
        softAssert.assertEquals(apiCustomerId, dbCustomerId, "Customer ID mismatch");
        softAssert.assertEquals(apiCurrencyCode, dbCurrencyCode, "Currency code mismatch");
    }

    private void validateGFVoucherFields(Document dbCart, GetCartDetailsHelper apiHelper, int cartId) {
        log.debug("Validating GV voucher fields for cart ID: {}", cartId);

        Map<String, Object> totalsMap = CartApiDbExtractUtils.extractTotalsMapFromApi(apiHelper);
        List<Map<String, Object>> apiDiscounts = CartApiDbExtractUtils.extractDiscountsFromTotalsMap(totalsMap);
        List<Map<String, Object>> gvApiDiscounts = apiDiscounts.stream()
                .filter(d -> "gv".equalsIgnoreCase((String) d.get("type")))
                .toList();

        Document amountDoc = (Document) dbCart.get("totals");
        Assert.assertNotNull(amountDoc, "Amount section is missing in DB for cart ID: " + cartId);
        List<Document> dbDiscounts = CartApiDbExtractUtils.extractDiscountsFromDbTotals(amountDoc);
        List<Document> gvDbDiscounts = dbDiscounts.stream()
                .filter(d -> "gv".equalsIgnoreCase(d.getString("type")))
                .toList();

        if (gvApiDiscounts.isEmpty() && gvDbDiscounts.isEmpty()) {
            log.debug("No GV discounts present in both API and DB for cart ID: {}", cartId);
        } else {
            for (int i = 0; i < gvApiDiscounts.size(); i++) {
                Map<String, Object> apiDiscount = gvApiDiscounts.get(i);
                boolean matched = gvDbDiscounts.stream().anyMatch(dbDiscount ->
                        apiDiscount.get("code").equals(dbDiscount.getString("code")) &&
                                "gv".equalsIgnoreCase(dbDiscount.getString("type")) &&
                                new BigDecimal(apiDiscount.get("amount").toString())
                                        .compareTo(new BigDecimal(dbDiscount.get("amount").toString())) == 0
                );
            }
        }
        log.debug("GV voucher field validation completed for cart ID: {}", cartId);
    }

    private void validateSCVoucherFields(Document dbCart, GetCartDetailsHelper apiHelper, int cartId) {
        log.debug("Validating SC voucher fields for cart ID: {}", cartId);
        Map<String, Object> totalsMap = CartApiDbExtractUtils.extractTotalsMapFromApi(apiHelper);
        List<Map<String, Object>> apiDiscounts = CartApiDbExtractUtils.extractDiscountsFromTotalsMap(totalsMap);
        List<Map<String, Object>> scApiDiscounts = apiDiscounts.stream()
                .filter(d -> "sc".equalsIgnoreCase((String) d.get("type")))
                .toList();
        Document amountDoc = (Document) dbCart.get("totals");
        Assert.assertNotNull(amountDoc, "Amount section is missing in DB for cart ID: " + cartId);
        List<Document> dbDiscounts = CartApiDbExtractUtils.extractDiscountsFromDbTotals(amountDoc);
        List<Document> scDbDiscounts = dbDiscounts.stream()
                .filter(d -> "sc".equalsIgnoreCase(d.getString("type")))
                .toList();
        if (scApiDiscounts.isEmpty() && scDbDiscounts.isEmpty()) {
            log.debug("No SC discounts present in both API and DB for cart ID: {}", cartId);
        } else {
            for (int i = 0; i < scApiDiscounts.size(); i++) {
                Map<String, Object> apiDiscount = scApiDiscounts.get(i);
                boolean matched = scDbDiscounts.stream().anyMatch(dbDiscount ->
                        apiDiscount.get("code").equals(dbDiscount.getString("code")) &&
                                "sc".equalsIgnoreCase(dbDiscount.getString("type")) &&
                                new BigDecimal(apiDiscount.get("amount").toString())
                                        .compareTo(new BigDecimal(dbDiscount.get("amount").toString())) == 0
                );
                softAssert.assertTrue(matched,
                        String.format("SC Discount mismatch at index %d for cart ID: %d. API: %s", i, cartId, apiDiscount));
            }
        }
        log.debug("SC voucher field validation completed for cart ID: {}", cartId);
    }

    private void validateWalletFields(Document dbCart, GetCartDetailsHelper apiHelper, int cartId) {
        log.debug("Validating wallet fields for cart ID: {}", cartId);
        Map<String, Object> totalsMap = CartApiDbExtractUtils.extractTotalsMapFromApi(apiHelper);
        List<Map<String, Object>> apiDiscounts = CartApiDbExtractUtils.extractDiscountsFromTotalsMap(totalsMap);
        List<Map<String, Object>> walletApiDiscounts = apiDiscounts.stream()
                .filter(d -> CartApiDbExtractUtils.isWalletType((String) d.get("type")))
                .toList();
        log.info("API wallet discounts found: {}", walletApiDiscounts);
        if (!orderContext.getIsWalletUsed()) {
            log.debug("Wallet not used, skipping wallet validation for cart ID: {}", cartId);
            return;
        }
        if (walletApiDiscounts.isEmpty()) {
            log.info("No wallet discounts in API response - wallet might have zero balance for cart ID: {}", cartId);
            return;
        }
        Document finalDbCart = retryDbValidation(cartId, walletApiDiscounts);
        if (finalDbCart == null) {
            softAssert.fail(String.format("Failed to get updated cart details from DB after retries for cart ID: %d", cartId));
            return;
        }
        Document amountDoc = (Document) finalDbCart.get("totals");
        log.info("Final DB totals section: {}", amountDoc != null ? amountDoc.toJson() : "null");
        if (amountDoc == null) {
            softAssert.fail(String.format("Wallet was marked as used, but 'totals' section is missing in DB for cart ID: %d", cartId));
            return;
        }
        List<Document> dbDiscounts = CartApiDbExtractUtils.extractDiscountsFromDbTotals(amountDoc);
        List<Document> walletDbDiscounts = dbDiscounts.stream()
                .filter(d -> CartApiDbExtractUtils.isWalletType(d.getString("type")))
                .toList();
        log.info("Final DB wallet discounts found: {}", walletDbDiscounts.stream()
                .map(d -> String.format("type: %s, amount: %s", d.getString("type"), d.get("amount")))
                .toList());
        softAssert.assertFalse(walletDbDiscounts.isEmpty(),
                String.format("Wallet discount present in API but missing in DB for cart ID: %d. API discounts: %s",
                        cartId, walletApiDiscounts));
        softAssert.assertEquals(walletApiDiscounts.size(), walletDbDiscounts.size(),
                String.format("Mismatch in wallet discount count between API (%d) and DB (%d) for cart ID: %d",
                        walletApiDiscounts.size(), walletDbDiscounts.size(), cartId));
        for (int i = 0; i < walletApiDiscounts.size(); i++) {
            Map<String, Object> apiDiscount = walletApiDiscounts.get(i);
            String apiWalletType = (String) apiDiscount.get("type");
            String apiAmount = apiDiscount.get("amount").toString();
            boolean matched = walletDbDiscounts.stream().anyMatch(dbDiscount -> {
                String dbWalletType = dbDiscount.getString("type");
                String dbAmount = dbDiscount.get("amount").toString();
                boolean typeMatch = apiWalletType != null && apiWalletType.equalsIgnoreCase(dbWalletType);
                boolean amountMatch = new BigDecimal(apiAmount).compareTo(new BigDecimal(dbAmount)) == 0;
                return typeMatch && amountMatch;
            });
            softAssert.assertTrue(matched,
                    String.format("Wallet discount mismatch at index %d for cart ID: %d. Expected type: %s, amount: %s to be applied in DB.",
                            i, cartId, apiWalletType, apiAmount));
        }
        log.debug("Wallet field validation completed for cart ID: {}", cartId);
    }

    private Document retryDbValidation(int cartId, List<Map<String, Object>> expectedWalletDiscounts) {
        int maxRetries = 3;
        int retryDelayMs = 2000; // 2 seconds
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            log.info("Attempt {} to fetch updated cart details for cart ID: {}", attempt, cartId);
            Document cartDetails = CartApiDbExtractUtils.fetchCartDetails(cartId);
            Document amountDoc = (Document) cartDetails.get("totals");
            if (amountDoc != null) {
                List<Document> dbDiscounts = CartApiDbExtractUtils.extractDiscountsFromDbTotals(amountDoc);
                if (dbDiscounts != null) {
                    List<Document> walletDbDiscounts = dbDiscounts.stream()
                            .filter(d -> CartApiDbExtractUtils.isWalletType(d.getString("type")))
                            .toList();
                    if (!walletDbDiscounts.isEmpty() && walletDbDiscounts.size() == expectedWalletDiscounts.size()) {
                        log.info("Found matching wallet discounts in DB on attempt {}", attempt);
                        return cartDetails;
                    }
                }
            }
            if (attempt < maxRetries) {
                log.info("Wallet discounts not yet synced to DB, waiting {}ms before retry...", retryDelayMs);
                try {
                    Thread.sleep(retryDelayMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Retry sleep interrupted");
                    break;
                }
            }
        }
        log.warn("Failed to find wallet discounts in DB after {} attempts for cart ID: {}", maxRetries, cartId);
        return CartApiDbExtractUtils.fetchCartDetails(cartId);
    }
}


