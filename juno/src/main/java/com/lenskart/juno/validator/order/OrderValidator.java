package com.lenskart.juno.validator.order;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PaymentMethod;
import com.lenskart.juno.util.OrderApiDbExtractUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.util.List;
import java.util.Map;

@Builder
@Slf4j
public class OrderValidator implements IValidator {

    private final OrderContext orderContext;
    static SoftAssert softAssert = new SoftAssert();

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        long orderId = orderContext.getOrderId();
        log.info("Starting order validation for order ID: {}", orderId);

        JSONObject orderJson = OrderApiDbExtractUtils.fetchOrderDetails(orderId);
        validateOrderId(orderJson, orderId);
        validateCartId(orderJson, orderId);

        String orderIdStr = String.valueOf(orderId);
        Document paymentDetails = OrderApiDbExtractUtils.fetchPaymentDetails(orderIdStr);

        var orderDetailsHelper = OrderApiDbExtractUtils.fetchOrderDetailsHelper(orderContext);

        validateOrderIdApiVsDb(orderDetailsHelper, orderJson, orderIdStr);
        validateCartIdApiVsDb(orderDetailsHelper, orderJson, orderIdStr);

        List<String> apiMethods = OrderApiDbExtractUtils.extractApiPaymentMethods(orderDetailsHelper);
        List<String> dbMethods = OrderApiDbExtractUtils.extractDbPaymentMethods(paymentDetails);

        validatePaymentMethods(apiMethods, dbMethods, orderIdStr);

        double apiTotal = OrderApiDbExtractUtils.extractApiAmountTotal(orderDetailsHelper);
        double dbTotal = OrderApiDbExtractUtils.extractDbAmountTotal(paymentDetails);

        softAssert.assertEquals(apiTotal, dbTotal,
                "Total amount mismatch for order ID: " + orderIdStr);

        validatePaymentStatusApiVsDb(orderDetailsHelper, paymentDetails, orderIdStr);


        Document orderDoc = Document.parse(orderJson.toString());
        validateIsPaymentCapturedApiVsDb(orderDetailsHelper, orderDoc, orderIdStr);

        softAssert.assertAll();
        log.info("Order validation completed successfully for order ID: {}", orderId);
    }

    private void validateOrderId(JSONObject orderJson, long orderId) {
        log.debug("Validating order ID for order ID: {}", orderId);
        String expectedOrderId = String.valueOf(orderId);
        String dbOrderId = orderJson.optString("_id");
        Assert.assertEquals(dbOrderId, expectedOrderId, "OrderId is not matching");
    }

    private void validateCartId(JSONObject orderJson, long orderId) {
        log.debug("Validating cart ID for order ID: {}", orderId);
        Object dbCartIdObj = orderJson.opt("cartId");
        Assert.assertNotNull(dbCartIdObj, "cartId is missing in DB for order ID: " + orderId);

        String dbCartId = String.valueOf(dbCartIdObj);
        String expectedCartId = String.valueOf(orderContext.getCartId());
        Assert.assertEquals(dbCartId, expectedCartId, "CartId is not matching");
    }

    private void validatePaymentMethods(List<String> apiMethods, List<String> dbMethods, String orderId) {
        List<String> apiLower = apiMethods.stream().map(String::toLowerCase).toList();
        boolean matches = (apiLower.contains("cc") && dbMethods.contains("juspay_card")) || apiLower.equals(dbMethods);
        log.debug("Validating payment methods: API={} DB={}", apiLower, dbMethods);
        softAssert.assertTrue(matches, "Payment methods mismatch for order ID: " + orderId);
    }

    private void validateOrderIdApiVsDb(com.lenskart.juno.helpers.GetOrderDetailsHelper helper, JSONObject orderJson, String orderId) {
        log.debug("Validating Order ID - API vs DB for order ID: {}", orderId);
        String dbOrderId = orderJson.optString("_id");
        String apiOrderId = String.valueOf(com.lenskart.commons.utils.RestUtils.getValueFromResponse(helper.getResponse(), "result.id"));
        Assert.assertEquals(apiOrderId, dbOrderId, "Order ID mismatch between API and DB for order ID: " + orderId);
        log.info("Order ID validation successful for order ID: {}", orderId);
    }

    private void validateCartIdApiVsDb(com.lenskart.juno.helpers.GetOrderDetailsHelper helper, JSONObject orderJson, String orderId) {
        log.debug("Validating Cart ID - API vs DB for order ID: {}", orderId);
        String dbCartId = orderJson.optString("cartId");
        String apiCartId = String.valueOf(com.lenskart.commons.utils.RestUtils.getValueFromResponse(helper.getResponse(), "result.cartId"));
        Assert.assertEquals(apiCartId, dbCartId, "Cart ID mismatch between API and DB for order ID: " + orderId);
        log.info("Cart ID validation successful for order ID: {}", orderId);
    }

    private void validatePaymentStatusApiVsDb(com.lenskart.juno.helpers.GetOrderDetailsHelper helper, Document paymentDetails, String orderId) {
        log.debug("Validating Payment Status - API vs DB for order ID: {}", orderId);

    // First check if payment method is juspay_card to determine if payment status validation is needed
        List<String> apiMethods = OrderApiDbExtractUtils.extractApiPaymentMethods(helper);
        List<String> dbMethods = OrderApiDbExtractUtils.extractDbPaymentMethods(paymentDetails);

        // Only validate payment status if juspay_card payment method is present
        if (apiMethods.stream().map(String::toLowerCase).toList().contains("cc")
                && dbMethods.contains("juspay_card")) {

            Object apiPaymentStatusObj = com.lenskart.commons.utils.RestUtils.getValueFromResponse(helper.getResponse(), "result.order.status");
            if (apiPaymentStatusObj == null) {
                apiPaymentStatusObj = com.lenskart.commons.utils.RestUtils.getValueFromResponse(helper.getResponse(), "result.paymentStatus");
            }

            if (apiPaymentStatusObj != null) {
                Document dbPaymentStatus = (Document) paymentDetails.get("paymentStatus");
                Assert.assertNotNull(dbPaymentStatus, "Payment Status missing in DB for order ID: " + orderId);

                Map<String, Object> apiPaymentStatusMap = (Map<String, Object>) apiPaymentStatusObj;
                String apiStatus = String.valueOf(apiPaymentStatusMap.get("status"));
                String dbStatus = dbPaymentStatus.getString("status");

                Assert.assertEquals(apiStatus, dbStatus, "Payment Status mismatch between API and DB for order ID: " + orderId);
                log.info("Payment Status validation successful for order ID: {}", orderId);
            } else {
                log.info("Skipping payment status validation as payment status not found in API response for order ID: {}", orderId);
            }
        } else {
            log.info("Skipping payment status validation as payment method is not juspay_card for order ID: {}", orderId);
        }
    }

    private void validateIsPaymentCapturedApiVsDb(com.lenskart.juno.helpers.GetOrderDetailsHelper helper, Document orderDoc, String orderId) {
        log.debug("Validating isPaymentCaptured field for order ID: {}", orderId);


        Object apiStatusObj = com.lenskart.commons.utils.RestUtils.getValueFromResponse(helper.getResponse(), "result.status.status");
        String apiStatus = apiStatusObj != null ? apiStatusObj.toString().toUpperCase() : null;

        Object apiIsCapturedObj = com.lenskart.commons.utils.RestUtils.getValueFromResponse(helper.getResponse(), "result.isPaymentCaptured");
        boolean apiIsPaymentCaptured = apiIsCapturedObj != null && Boolean.parseBoolean(apiIsCapturedObj.toString());


        log.info("API Extracted - Status: {}, isPaymentCaptured: {}", apiStatus, apiIsPaymentCaptured);


        Document statusDoc = (Document) orderDoc.get("status");
        Assert.assertNotNull(statusDoc, "DB status document missing for order ID: " + orderId);

        String dbStatus = statusDoc.getString("status");
        Assert.assertNotNull(dbStatus, "DB status string missing for order ID: " + orderId);
        dbStatus = dbStatus.toUpperCase();

        Boolean dbIsPaymentCaptured = orderDoc.getBoolean("isPaymentCaptured");
        Assert.assertNotNull(dbIsPaymentCaptured, "DB isPaymentCaptured missing for order ID: " + orderId);


        log.info("DB Extracted - Status: {}, isPaymentCaptured: {}", dbStatus, dbIsPaymentCaptured);

        // Validate API and DB status fields are not null
        Assert.assertNotNull(apiStatus, "API order status is missing for order ID: " + orderId);

        //  API and DB responses match
        softAssert.assertEquals(apiStatus, dbStatus,
                "Mismatch in order status between API and DB for order ID: " + orderId);

        softAssert.assertEquals(apiIsPaymentCaptured, dbIsPaymentCaptured.booleanValue(),
                "Mismatch in isPaymentCaptured between API and DB for order ID: " + orderId);


        if (!apiStatus.equals(dbStatus) || apiIsPaymentCaptured != dbIsPaymentCaptured.booleanValue()) {
            String errorMsg = String.format(
                    "Mismatch between API and DB for order ID: %s | API status: %s, DB status: %s | API isPaymentCaptured: %s, DB isPaymentCaptured: %s",
                    orderId, apiStatus, dbStatus, apiIsPaymentCaptured, dbIsPaymentCaptured);

            log.error(errorMsg);
            throw new AssertionError(errorMsg);
        }

        //   if API and DB match
        boolean expectedIsCaptured;
        switch (apiStatus) {
            case "PENDING":
                expectedIsCaptured = false;
                break;
            case "PROCESSING":
            case "PROCESSING_POWER_FOLLOWUP":
                expectedIsCaptured = true;
                break;
            default:
                expectedIsCaptured = false;
                break;
        }

        softAssert.assertEquals(apiIsPaymentCaptured, expectedIsCaptured,
                "API isPaymentCaptured value invalid for order ID: " + orderId + " with status: " + apiStatus);

        softAssert.assertEquals(dbIsPaymentCaptured.booleanValue(), expectedIsCaptured,
                "DB isPaymentCaptured value invalid for order ID: " + orderId + " with status: " + dbStatus);

        log.info("isPaymentCaptured hardcoded validation passed for order ID: {} with status: {}", orderId, apiStatus);
    }

    public void validatePreOrderPlacement( ) {
        log.info("Pre-order placement validation started");
        long orderId = orderContext.getOrderId();
        JSONObject orderJsonBeforePayment = OrderApiDbExtractUtils.fetchOrderDetails(orderId);
        Document orderDocbeforePayment = Document.parse(orderJsonBeforePayment.toString());
        Document statusDocBeforePayment = (Document) orderDocbeforePayment.get("status");
        String dbStatusBeforePayment = statusDocBeforePayment.getString("status");
        Boolean dbIsPaymentCapturedFlagBeforePayment = orderDocbeforePayment.getBoolean("isPaymentCaptured");
        dbStatusBeforePayment = dbStatusBeforePayment.toUpperCase();
        log.info("Order status before payment: {}", dbStatusBeforePayment);
        log.info("Payment capture flag before complete payment: {}", dbIsPaymentCapturedFlagBeforePayment);
        log.info("Asserting Order status before payment");
        if (orderContext.getPaymentMethod().equals(PaymentMethod.CREDIT_CARD) ||
                orderContext.getPaymentMethod().equals(PaymentMethod.DEBIT_CARD) ||
                orderContext.getPaymentMethod().equals(PaymentMethod.EMI)) {
            Assert.assertEquals(dbStatusBeforePayment, "PENDING", "Order status mismatch before payment");
            Assert.assertFalse(dbIsPaymentCapturedFlagBeforePayment, "Payment capture flag mismatch before payment");
        } else if (orderContext.getPaymentMethod().equals(PaymentMethod.COD) || orderContext.getPaymentMethod().equals(PaymentMethod.OFFLINE_CASH)) {
            Assert.assertEquals(dbStatusBeforePayment, "PROCESSING", "Order status mismatch before payment");
            Assert.assertTrue(dbIsPaymentCapturedFlagBeforePayment, "Payment capture flag mismatch before payment");
        } else {
            Assert.assertEquals(dbStatusBeforePayment, "PENDING", "Order status mismatch before payment");
            Assert.assertFalse(dbIsPaymentCapturedFlagBeforePayment, "Payment capture flag mismatch before payment");
        }

    }

    public void validatePostOrderCompletion() {
        log.info("Post-order placement validation started");
        JSONObject orderJsonAfterPayment = OrderApiDbExtractUtils.fetchOrderDetails(orderContext.getOrderId());
        Document orderDocAfterPayment = Document.parse(orderJsonAfterPayment.toString());
        Document statusDocAfterPayment = (Document) orderDocAfterPayment.get("status");
        String dbStatusAfterPayment = statusDocAfterPayment.getString("status");
        Boolean dbIsPaymentCapturedFlagAfterPayment = orderDocAfterPayment.getBoolean("isPaymentCaptured");
        dbStatusAfterPayment = dbStatusAfterPayment.toUpperCase();
        log.info("Order status after payment: {}", dbStatusAfterPayment);
        log.info("Payment capture flag after complete payment: {}", dbIsPaymentCapturedFlagAfterPayment);
        log.info("Asserting Order status after payment");
        Assert.assertEquals(dbStatusAfterPayment, "PROCESSING", "Order status mismatch after payment");
        Assert.assertTrue(dbIsPaymentCapturedFlagAfterPayment, "Payment capture flag mismatch after payment");
    }
}

