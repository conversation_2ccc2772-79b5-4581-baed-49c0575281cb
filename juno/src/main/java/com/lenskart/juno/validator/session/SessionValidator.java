package com.lenskart.juno.validator.session;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.database.JunoSessionDbUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

@Slf4j
@Builder

public class SessionValidator implements IValidator {

    OrderContext orderContext;

    static SoftAssert softAssert = new SoftAssert();

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {

        String sessionToken = extractSessionTokens();
        Document sessionDetails = fetchSessionDetails(sessionToken);

        String dbSessionId = extractDbSessionId(sessionDetails);
        validateSessionId(dbSessionId, sessionToken);

        softAssert.assertAll();
        log.info("Session validation completed successfully for session token: {}", sessionToken);
    }


    private String extractSessionTokens() {
        String sessionToken = orderContext.getHeaders().getSessionToken();
        log.info("Extracted session token from request headers: {}", sessionToken);
        Assert.assertNotNull(sessionToken, "Session token must not be null");
        return sessionToken;
    }

    private Document fetchSessionDetails(String sessionToken) {
        log.debug("Fetching session details from DB for session token: {}", sessionToken);
        Document sessionDetails = JunoSessionDbUtils.getSessionDetails(sessionToken);
        Assert.assertNotNull(sessionDetails, "Session details should not be null for session token: " + sessionToken);

        if (sessionDetails.isEmpty()) {
            log.error("Session details are empty for session token: {}", sessionToken);
            Assert.fail("Session details should not be empty for session token: " + sessionToken);
        }
        log.debug("Fetched session details: {}", sessionDetails.toJson());
        return sessionDetails;
    }

    private String extractDbSessionId(Document sessionDetails) {
        String dbSessionId = sessionDetails.get("_id").toString();
        log.info("Extracted DB Session ID: {}", dbSessionId);
        Assert.assertNotNull(dbSessionId, "Session ID (_id) should not be null in DB");
        return dbSessionId;
    }

    private void validateSessionId(String dbSessionId, String expectedSessionToken) {
        log.debug("Validating session ID - DB Session ID: {}, Expected Session Token: {}", dbSessionId, expectedSessionToken);
        softAssert.assertEquals(dbSessionId, expectedSessionToken, "Session ID in DB does not match session token from request");
    }
}
