package com.lenskart.juno.validator.order;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.GiftVoucher;
import com.lenskart.juno.util.OrderApiDbExtractUtils;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.testng.asserts.SoftAssert;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@Builder
public class MedibuddyValidator implements IValidator {

    private final OrderContext orderContext;
    private static final SoftAssert softAssert = new SoftAssert();

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        long orderId = orderContext.getOrderId();
        log.info("Starting Medibuddy order validation for order ID: {}", orderId);

        Document dbOrder = OrderApiDbExtractUtils.getOrderFromDB(orderId, softAssert);
        var apiHelper = OrderApiDbExtractUtils.getOrderFromAPI(orderContext);

        // Extract data from API & DB
        String apiOrderId = OrderApiDbExtractUtils.extractApiField(apiHelper, "result.id", orderId, softAssert);
        String dbOrderId = OrderApiDbExtractUtils.extractDbField(dbOrder, "_id", orderId, softAssert);

        String apiCartId = OrderApiDbExtractUtils.extractApiField(apiHelper, "result.cartId", orderId, softAssert);
        String dbCartId = OrderApiDbExtractUtils.extractDbField(dbOrder, "cartId", orderId, softAssert);

        String apiCustomerId = OrderApiDbExtractUtils.extractApiField(apiHelper, "result.customerId", orderId, softAssert);
        String dbCustomerId = OrderApiDbExtractUtils.extractDbField(dbOrder, "customerId", orderId, softAssert);

        List<Map<String, Object>> apiDiscounts = OrderApiDbExtractUtils.extractApiMedibuddyDiscounts(apiHelper, orderId, softAssert);
        List<org.bson.Document> dbDiscounts = OrderApiDbExtractUtils.extractDbMedibuddyDiscounts(dbOrder, orderId, softAssert);

        List<String> apiPaymentMethods = OrderApiDbExtractUtils.extractApiPaymentMethods(apiHelper, orderId, softAssert);
        List<String> dbPaymentMethods = OrderApiDbExtractUtils.extractDbPaymentMethods(dbOrder, orderId, softAssert);

        String apiStatus = OrderApiDbExtractUtils.extractApiOrderStatus(apiHelper, orderId, softAssert);
        String dbStatus = OrderApiDbExtractUtils.extractDbOrderStatus(dbOrder, orderId, softAssert);

        String apiState = OrderApiDbExtractUtils.extractApiOrderState(apiHelper, orderId, softAssert);
        String dbState = OrderApiDbExtractUtils.extractDbOrderState(dbOrder, orderId, softAssert);

        // Perform validations
        validateEquals(apiOrderId, dbOrderId, "Order ID mismatch", orderId);
        validateEquals(apiCartId, dbCartId, "Cart ID mismatch", orderId);
        validateEquals(apiCustomerId, dbCustomerId, "Customer ID mismatch", orderId);

        validateMedibuddyDiscounts(apiDiscounts, dbDiscounts, orderId);
        validatePaymentMethods(apiPaymentMethods, dbPaymentMethods, orderId);

        validateEquals(apiStatus, "PROCESSING", "API order status should be PROCESSING", orderId);
        validateEquals(dbStatus, "PROCESSING", "DB order status should be PROCESSING", orderId);
        validateEquals(apiState, dbState, "Order state mismatch between API and DB", orderId);

        softAssert.assertAll();
        log.info("Medibuddy order validation completed successfully for order ID: {}", orderId);
    }

    // Validation Methods
    private void validateEquals(String actual, String expected, String message, long orderId) {
        softAssert.assertEquals(actual, expected, message + " for order ID: " + orderId);
        log.info("{} - Actual: {}, Expected: {}", message, actual, expected);
    }

    private void validateMedibuddyDiscounts(List<Map<String, Object>> apiDiscounts, List<org.bson.Document> dbDiscounts, long orderId) {
        softAssert.assertEquals(apiDiscounts.size(), dbDiscounts.size(),
                "Mismatch in Medibuddy discounts count for order ID: " + orderId);

        int count = Math.min(apiDiscounts.size(), dbDiscounts.size());

        for (int i = 0; i < count; i++) {
            Map<String, Object> apiDiscount = apiDiscounts.get(i);
            org.bson.Document dbDiscount = dbDiscounts.get(i);

            validateEquals(String.valueOf(apiDiscount.get("code")), dbDiscount.getString("code"),
                    "Discount code mismatch", orderId);

            validateEquals(String.valueOf(apiDiscount.get("type")), dbDiscount.getString("type"),
                    "Discount type mismatch", orderId);

            validateEquals(String.valueOf(apiDiscount.get("subType")), dbDiscount.getString("subType"),
                    "Discount subType mismatch", orderId);

            BigDecimal apiAmount = new BigDecimal(String.valueOf(apiDiscount.get("amount"))).stripTrailingZeros();
            BigDecimal dbAmount = new BigDecimal(String.valueOf(dbDiscount.get("amount"))).stripTrailingZeros();

            softAssert.assertTrue(apiAmount.compareTo(dbAmount) == 0,
                    "Discount amount mismatch for order ID: " + orderId +
                            " expected [" + dbAmount + "] but found [" + apiAmount + "]");

            softAssert.assertTrue(apiAmount.compareTo(BigDecimal.ZERO) == 0,
                    "Medibuddy discount amount should be 0.00 for order ID: " + orderId +
                            " but found [" + apiAmount + "]");

            //  validation
            softAssert.assertEquals(apiDiscount.get("code"), GiftVoucher.MEDIBUDDY_GIFTCODE.getCode(),
                    "Medibuddy discount code should be MDBY-TEST-SCRIPT for order ID: " + orderId);
            softAssert.assertEquals(apiDiscount.get("type"), "gv",
                    "Medibuddy discount type should be gv for order ID: " + orderId);
            softAssert.assertEquals(apiDiscount.get("subType"), "MEDIBUDDY",
                    "Medibuddy discount subType should be MEDIBUDDY for order ID: " + orderId);

            log.info("Medibuddy discount validated - Code: {}, Type: {}, SubType: {}, Amount: {}",
                    apiDiscount.get("code"), apiDiscount.get("type"), apiDiscount.get("subType"), apiAmount);
        }
    }

    private void validatePaymentMethods(List<String> apiMethods, List<String> dbMethods, long orderId) {
        softAssert.assertEquals(apiMethods.size(), dbMethods.size(),
                "Payment methods count mismatch for order ID: " + orderId);

        softAssert.assertTrue(apiMethods.contains("medibuddy"),
                "API payment methods should contain 'medibuddy' for order ID: " + orderId);

        softAssert.assertTrue(dbMethods.contains("medibuddy"),
                "DB payment methods should contain 'medibuddy' for order ID: " + orderId);

        for (String method : apiMethods) {
            softAssert.assertTrue(dbMethods.contains(method),
                    "Payment method '" + method + "' not found in DB for order ID: " + orderId);
        }

        log.info("Payment methods validated - API: {}, DB: {}", apiMethods, dbMethods);
    }
}
