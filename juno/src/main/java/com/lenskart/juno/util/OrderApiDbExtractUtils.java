package com.lenskart.juno.util;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.database.JunoOrderDBUtils;
import com.lenskart.juno.helpers.GetOrderDetailsHelper;
import org.bson.Document;
import org.testng.asserts.SoftAssert;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.lenskart.commons.utils.JsonUtils;
import org.json.JSONObject;

public class OrderApiDbExtractUtils {

    public static Document getOrderFromDB(long orderId, SoftAssert softAssert) {
        Document orderDetails = JunoOrderDBUtils.getOrderDetails((int) orderId);
        softAssert.assertNotNull(orderDetails, "Order details should not be null in DB for order ID: " + orderId);
        return orderDetails;
    }

    public static GetOrderDetailsHelper getOrderFromAPI(OrderContext orderContext) {
        return (GetOrderDetailsHelper) GetOrderDetailsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    public static String extractApiField(GetOrderDetailsHelper helper, String path, long orderId, SoftAssert softAssert) {
        Object val = RestUtils.getValueFromResponse(helper.getResponse(), path);
        softAssert.assertNotNull(val, "Missing " + path + " in API response for order ID: " + orderId);
        return val != null ? String.valueOf(val) : null;
    }

    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> extractApiMedibuddyDiscounts(GetOrderDetailsHelper helper, long orderId, SoftAssert softAssert) {
        Object itemsObj = RestUtils.getValueFromResponse(helper.getResponse(), "result.items");
        softAssert.assertNotNull(itemsObj, "API items list is null for order ID: " + orderId);
        if (itemsObj == null) return Collections.emptyList();
        List<Map<String, Object>> items = (List<Map<String, Object>>) itemsObj;
        if (items.isEmpty()) return Collections.emptyList();
        Map<String, Object> firstItem = items.get(0);
        Map<String, Object> amount = (Map<String, Object>) firstItem.get("amount");
        if (amount == null) return Collections.emptyList();
        List<Map<String, Object>> discounts = (List<Map<String, Object>>) amount.get("discounts");
        if (discounts == null) return Collections.emptyList();
        return discounts.stream()
                .filter(d -> "gv".equalsIgnoreCase((String) d.get("type")) &&
                        "MEDIBUDDY".equalsIgnoreCase((String) d.get("subType")))
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    public static List<String> extractApiPaymentMethods(GetOrderDetailsHelper helper, long orderId, SoftAssert softAssert) {
        Object paymentListObj = RestUtils.getValueFromResponse(helper.getResponse(), "result.payments.paymentList");
        softAssert.assertNotNull(paymentListObj, "Payment list missing in API response for order ID: " + orderId);
        if (paymentListObj == null) return Collections.emptyList();
        List<Map<String, Object>> paymentList = (List<Map<String, Object>>) paymentListObj;
        return paymentList.stream()
                .map(payment -> String.valueOf(payment.get("method")))
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    public static String extractApiOrderStatus(GetOrderDetailsHelper helper, long orderId, SoftAssert softAssert) {
        Object statusObj = RestUtils.getValueFromResponse(helper.getResponse(), "result.status");
        softAssert.assertNotNull(statusObj, "Order status missing in API response for order ID: " + orderId);
        if (statusObj == null) return null;
        Map<String, Object> statusMap = (Map<String, Object>) statusObj;
        return String.valueOf(statusMap.get("status"));
    }

    @SuppressWarnings("unchecked")
    public static String extractApiOrderState(GetOrderDetailsHelper helper, long orderId, SoftAssert softAssert) {
        Object statusObj = RestUtils.getValueFromResponse(helper.getResponse(), "result.status");
        softAssert.assertNotNull(statusObj, "Order status missing in API response for order ID: " + orderId);
        if (statusObj == null) return null;
        Map<String, Object> statusMap = (Map<String, Object>) statusObj;
        return String.valueOf(statusMap.get("state"));
    }

    public static String extractDbField(Document dbOrder, String key, long orderId, SoftAssert softAssert) {
        Object val = dbOrder.get(key);
        softAssert.assertNotNull(val, "Missing " + key + " in DB for order ID: " + orderId);
        return val != null ? String.valueOf(val) : null;
    }

    @SuppressWarnings("unchecked")
    public static List<Document> extractDbMedibuddyDiscounts(Document dbOrder, long orderId, SoftAssert softAssert) {
        List<Document> items = (List<Document>) dbOrder.get("items");
        softAssert.assertNotNull(items, "DB items list is null for order ID: " + orderId);
        if (items == null || items.isEmpty()) return Collections.emptyList();
        Document firstItem = items.get(0);
        Document amount = (Document) firstItem.get("amount");
        if (amount == null) return Collections.emptyList();
        List<Document> discounts = (List<Document>) amount.get("discounts");
        if (discounts == null) return Collections.emptyList();
        return discounts.stream()
                .filter(d -> "gv".equalsIgnoreCase(d.getString("type")) &&
                        "MEDIBUDDY".equalsIgnoreCase(d.getString("subType")))
                .collect(Collectors.toList());
    }

    public static String extractDbOrderStatus(Document dbOrder, long orderId, SoftAssert softAssert) {
        Document status = (Document) dbOrder.get("status");
        softAssert.assertNotNull(status, "Order status missing in DB for order ID: " + orderId);
        return status != null ? status.getString("status") : null;
    }

    public static String extractDbOrderState(Document dbOrder, long orderId, SoftAssert softAssert) {
        Document status = (Document) dbOrder.get("status");
        softAssert.assertNotNull(status, "Order status missing in DB for order ID: " + orderId);
        return status != null ? status.getString("state") : null;
    }

    @SuppressWarnings("unchecked")
    public static List<String> extractDbPaymentMethods(Document dbOrder, long orderId, SoftAssert softAssert) {
        Document payments = (Document) dbOrder.get("payments");
        softAssert.assertNotNull(payments, "Payments missing in DB for order ID: " + orderId);
        if (payments == null) return Collections.emptyList();
        List<Document> paymentList = (List<Document>) payments.get("paymentList");
        softAssert.assertNotNull(paymentList, "Payment list missing in DB for order ID: " + orderId);
        if (paymentList == null) return Collections.emptyList();
        return paymentList.stream()
                .map(payment -> payment.getString("method"))
                .collect(Collectors.toList());
    }

    // --- OrderValidator extraction methods ---

    public static JSONObject fetchOrderDetails(long orderId) {
        Document orderDocument = JunoOrderDBUtils.getOrderDetails((int) orderId);
        return JsonUtils.convertStringToJson(orderDocument.toJson());
    }

    public static Document fetchPaymentDetails(String orderId) {
        return com.lenskart.juno.database.PaymentDbUtils.getPaymentDetails(orderId);
    }

    public static GetOrderDetailsHelper fetchOrderDetailsHelper(OrderContext orderContext) {
        return (GetOrderDetailsHelper) GetOrderDetailsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @SuppressWarnings("unchecked")
    public static List<String> extractApiPaymentMethods(GetOrderDetailsHelper helper) {
        Object paymentListObj = com.lenskart.commons.utils.RestUtils.getValueFromResponse(helper.getResponse(), "result.payments.paymentList");
        if (paymentListObj == null) return java.util.Collections.emptyList();
        List<Map<String, Object>> paymentList = (List<Map<String, Object>>) paymentListObj;
        return paymentList.stream()
                .map(payment -> String.valueOf(payment.get("method")).toLowerCase())
                .collect(java.util.stream.Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    public static List<String> extractDbPaymentMethods(Document paymentDetails) {
        Document paymentGatewayInfo = (Document) paymentDetails.get("paymentGatewayInfo");
        if (paymentGatewayInfo == null) return java.util.Collections.emptyList();
        return java.util.List.of(paymentGatewayInfo.getString("paymentMethod").toLowerCase());
    }

    public static double extractDbAmountTotal(Document paymentDetails) {
        Document amount = (Document) paymentDetails.get("amount");
        if (amount == null) return 0.0;
        Object totalObj = amount.get("total");
        if (totalObj == null) return 0.0;
        java.math.BigDecimal totalDecimal = new java.math.BigDecimal(totalObj.toString()).stripTrailingZeros();
        return totalDecimal.doubleValue();
    }

    @SuppressWarnings("unchecked")
    public static double extractApiAmountTotal(GetOrderDetailsHelper helper) {
        Object amountObj = com.lenskart.commons.utils.RestUtils.getValueFromResponse(helper.getResponse(), "result.amount");
        if (amountObj == null) return 0.0;
        Map<String, Object> amountMap = (Map<String, Object>) amountObj;
        Object totalObj = amountMap.get("total");
        if (totalObj == null) return 0.0;
        java.math.BigDecimal totalDecimal = new java.math.BigDecimal(totalObj.toString()).stripTrailingZeros();
        return totalDecimal.doubleValue();
    }
}