package com.lenskart.juno.util;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.database.PaymentDbUtils;
import com.lenskart.juno.helpers.GetOrderDetailsHelper;
import com.lenskart.commons.utils.RestUtils;
import org.bson.Document;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class PaymentApiDbExtractUtils {
    public static Document fetchPaymentDetails(String orderId) {
        return PaymentDbUtils.getPaymentDetails(orderId);
    }

    public static GetOrderDetailsHelper fetchOrderDetailsHelper(OrderContext orderContext) {
        return (GetOrderDetailsHelper) GetOrderDetailsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @SuppressWarnings("unchecked")
    public static List<String> extractApiPaymentMethods(GetOrderDetailsHelper helper) {
        Object paymentListObj = RestUtils.getValueFromResponse(helper.getResponse(), "result.payments.paymentList");
        if (paymentListObj == null) return List.of();
        List<Map<String, Object>> paymentList = (List<Map<String, Object>>) paymentListObj;
        return paymentList.stream()
                .map(payment -> String.valueOf(payment.get("method")).toLowerCase())
                .toList();
    }

    @SuppressWarnings("unchecked")
    public static List<String> extractDbPaymentMethods(Document paymentDetails) {
        Document paymentGatewayInfo = (Document) paymentDetails.get("paymentGatewayInfo");
        if (paymentGatewayInfo == null) return List.of();
        return List.of(paymentGatewayInfo.getString("paymentMethod").toLowerCase());
    }

    public static double extractDbAmountTotal(Document paymentDetails) {
        Document amount = (Document) paymentDetails.get("amount");
        if (amount == null) return 0.0;
        Object totalObj = amount.get("total");
        if (totalObj == null) return 0.0;
        BigDecimal totalDecimal = new BigDecimal(totalObj.toString()).stripTrailingZeros();
        return totalDecimal.doubleValue();
    }

    @SuppressWarnings("unchecked")
    public static double extractApiAmountTotal(GetOrderDetailsHelper helper) {
        Object amountObj = RestUtils.getValueFromResponse(helper.getResponse(), "result.amount");
        if (amountObj == null) return 0.0;
        Map<String, Object> amountMap = (Map<String, Object>) amountObj;
        Object totalObj = amountMap.get("total");
        if (totalObj == null) return 0.0;
        BigDecimal totalDecimal = new BigDecimal(totalObj.toString()).stripTrailingZeros();
        return totalDecimal.doubleValue();
    }
}