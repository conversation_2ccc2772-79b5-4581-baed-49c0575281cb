package com.lenskart.juno.util;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.database.JunoCartDbUtils;
import com.lenskart.juno.helpers.GetCartDetailsHelper;
import com.lenskart.commons.utils.RestUtils;
import org.bson.Document;

import java.util.List;
import java.util.Map;

public class CartApiDbExtractUtils {
    public static Document fetchCartDetails(int cartId) {
        return JunoCartDbUtils.getCartDbUtils(cartId);
    }

    public static GetCartDetailsHelper fetchCartDetailsFromApi(OrderContext orderContext) {
        return (GetCartDetailsHelper) GetCartDetailsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> extractTotalsMapFromApi(GetCartDetailsHelper apiHelper) {
        Object totalsObj = RestUtils.getValueFromResponse(apiHelper.getResponse(), "result.totals");
        if (totalsObj == null) return Map.of();
        return (Map<String, Object>) totalsObj;
    }

    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> extractDiscountsFromTotalsMap(Map<String, Object> totalsMap) {
        Object discountsObj = totalsMap.get("discounts");
        if (discountsObj == null) return List.of();
        return (List<Map<String, Object>>) discountsObj;
    }

    @SuppressWarnings("unchecked")
    public static List<org.bson.Document> extractDiscountsFromDbTotals(Document dbTotals) {
        if (dbTotals == null) return List.of();
        List<org.bson.Document> discounts = dbTotals.getList("discounts", org.bson.Document.class);
        return discounts != null ? discounts : List.of();
    }

    public static boolean isWalletType(String type) {
        if (type == null) return false;
        String lowerType = type.toLowerCase();
        return "lenskart".equals(lowerType) || "lenskartplus".equals(lowerType);
    }

    // Optionally, add retryDbValidation if you want to reuse it elsewhere
}