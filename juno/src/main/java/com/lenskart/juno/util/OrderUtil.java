package com.lenskart.juno.util;

import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class OrderUtil {
    public static boolean setConfirmOrder(Response response) {
        List<Object> items = response.jsonPath().getList("result.items");
        for (int i = 0; i < items.size(); i++) {
            String powerRequired = response.jsonPath().getString("result.items[" + i + "].powerRequired");
            if ("POWER_REQUIRED".equalsIgnoreCase(powerRequired)) {
                return false;
            }
        }
        return true;
    }
}
