package com.lenskart.juno.config;

import com.lenskart.commons.config.ConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Configuration provider for Juno module that wraps JunoConfigRegistry
 * to implement the standard ConfigProvider interface.
 */
@Slf4j
public class JunoConfigProvider implements ConfigProvider {

    private final JunoConfigRegistry junoRegistry;

    // Singleton instance
    private static volatile JunoConfigProvider instance;

    /**
     * Private constructor for singleton pattern
     */
    private JunoConfigProvider() {
        this.junoRegistry = JunoConfigRegistry.getInstance();
    }

    /**
     * Gets the singleton instance of JunoConfigProvider
     *
     * @return The singleton instance
     */
    public static JunoConfigProvider getInstance() {
        if (instance == null) {
            synchronized (JunoConfigProvider.class) {
                if (instance == null) {
                    instance = new JunoConfigProvider();
                }
            }
        }
        return instance;
    }

    @Override
    public String getBaseUrl(String serviceName) {
        try {
            return junoRegistry.getBaseUrl(serviceName);
        } catch (Exception e) {
            log.error("Error getting base URL for service {} from Juno registry: {}",
                    serviceName, e.getMessage());
            return null;
        }
    }

    @Override
    public Map<String, String> getAllBaseUrls() {
        try {
            return junoRegistry.getAllBaseUrls();
        } catch (Exception e) {
            log.error("Error getting all base URLs from Juno registry: {}", e.getMessage());
            return Map.of();
        }
    }

    @Override
    public void refresh() {
        try {
            junoRegistry.refresh();
            log.info("Juno configuration refreshed successfully");
        } catch (Exception e) {
            log.error("Error refreshing Juno configuration: {}", e.getMessage(), e);
        }
    }

    @Override
    public boolean isInitialized() {
        return junoRegistry != null;
    }

    @Override
    public String getProviderName() {
        return "JunoConfigProvider";
    }

    /**
     * Gets the underlying Juno registry for direct access if needed
     *
     * @return The JunoConfigRegistry instance
     */
    public JunoConfigRegistry getJunoRegistry() {
        return junoRegistry;
    }
}
