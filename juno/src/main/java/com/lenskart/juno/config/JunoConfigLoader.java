package com.lenskart.juno.config;

import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility class to load Juno configurations from YAML files
 */
@Slf4j
public class JunoConfigLoader {

    // Configuration cache
    private static final Map<String, JunoConfig> configCache = new ConcurrentHashMap<>();

    // Default values
    private static final String DEFAULT_CONFIG_PATH = "juno.yml";
    private static final String DEFAULT_ENVIRONMENT = "preprod";

    /**
     * Loads the Juno configuration from the default YAML file
     *
     * @return JunoConfig object with configuration details
     */
    public static JunoConfig loadConfig() {
        return loadConfig(DEFAULT_CONFIG_PATH);
    }

    /**
     * Loads the Juno configuration from a specified YAML file
     *
     * @param configPath Path to the YAML file
     * @return JunoConfig object with configuration details
     */
    public static JunoConfig loadConfig(String configPath) {
        // Return cached config if available
        if (configCache.containsKey(configPath)) {
            return configCache.get(configPath);
        }

        try {
            Yaml yaml = new Yaml();

            // Try to load from classpath first
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(configPath);

            // If not found in classpath, try as a file path
            if (inputStream == null) {
                try {
                    inputStream = new FileInputStream(configPath);
                } catch (IOException e) {
                    log.warn("Could not find configuration file: {}", configPath);
                    JunoConfig defaultConfig = createDefaultConfig();
                    configCache.put(configPath, defaultConfig);
                    return defaultConfig;
                }
            }

            // Load the YAML file as a map
            Map<String, Object> yamlMap = yaml.load(inputStream);
            inputStream.close();

            if (yamlMap == null || yamlMap.isEmpty()) {
                log.warn("Empty or invalid configuration file: {}", configPath);
                JunoConfig defaultConfig = createDefaultConfig();
                configCache.put(configPath, defaultConfig);
                return defaultConfig;
            }

            // Create a new JunoConfig object
            JunoConfig config = new JunoConfig();

            // Process each environment in the YAML file
            for (Map.Entry<String, Object> entry : yamlMap.entrySet()) {
                String envName = entry.getKey();

                if (entry.getValue() instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> envConfig = (Map<String, Object>) entry.getValue();

                    // Create a new environment configuration
                    JunoConfig.EnvironmentConfig environmentConfig = new JunoConfig.EnvironmentConfig();

                    // Process base URLs if available
                    if (envConfig.containsKey("baseUrls") && envConfig.get("baseUrls") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, String> baseUrls = (Map<String, String>) envConfig.get("baseUrls");
                        environmentConfig.setBaseUrls(baseUrls);
                    }

                    // Process auth configuration if available
                    if (envConfig.containsKey("auth") && envConfig.get("auth") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, String> auth = (Map<String, String>) envConfig.get("auth");
                        environmentConfig.setAuth(auth);
                        log.info("Loaded auth configuration: {}", auth);
                    }

                    // Process wallet configuration if available
                    if (envConfig.containsKey("wallet") && envConfig.get("wallet") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> wallet = (Map<String, Object>) envConfig.get("wallet");
                        environmentConfig.setWallet(wallet);
                        log.info("Loaded wallet configuration: {}", wallet);
                    }

                    // Process draco configuration if available
                    if (envConfig.containsKey("draco") && envConfig.get("draco") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, String> draco = (Map<String, String>) envConfig.get("draco");
                        environmentConfig.setDraco(draco);
                        log.info("Loaded draco configuration: {}", draco);
                    }

                    // Add the environment configuration to the JunoConfig
                    config.getEnvironments().put(envName, environmentConfig);
                }
            }

            // Cache the config
            configCache.put(configPath, config);

            log.info("Loaded Juno configuration from: {}", configPath);
            return config;

        } catch (Exception e) {
            log.error("Failed to load Juno configuration: {}", e.getMessage(), e);
            JunoConfig defaultConfig = createDefaultConfig();
            configCache.put(configPath, defaultConfig);
            return defaultConfig;
        }
    }

    /**
     * Creates a default Juno configuration
     *
     * @return Default JunoConfig
     */
    private static JunoConfig createDefaultConfig() {
        JunoConfig config = new JunoConfig();

        // Create default environment configuration
        JunoConfig.EnvironmentConfig environmentConfig = new JunoConfig.EnvironmentConfig();

        // Add default base URLs
        Map<String, String> baseUrls = new HashMap<>();
        baseUrls.put("sessionService", "https://api-gateway.juno.preprod.lenskart.com");
        baseUrls.put("testService", "https://jsonplaceholder.typicode.com");
        environmentConfig.setBaseUrls(baseUrls);

        // Add the environment configuration to the JunoConfig
        config.getEnvironments().put(DEFAULT_ENVIRONMENT, environmentConfig);

        return config;
    }

    /**
     * Clears the configuration cache
     */
    public static void clearCache() {
        configCache.clear();
        log.info("Configuration cache cleared");
    }

    /**
     * Get the default environment name
     *
     * @return Default environment name
     */
    public static String getDefaultEnvironment() {
        return DEFAULT_ENVIRONMENT;
    }

    /**
     * Get the default configuration path
     *
     * @return Default configuration path
     */
    public static String getDefaultConfigPath() {
        return DEFAULT_CONFIG_PATH;
    }
}
