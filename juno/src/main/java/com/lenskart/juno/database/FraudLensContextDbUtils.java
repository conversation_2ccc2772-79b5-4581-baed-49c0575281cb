package com.lenskart.juno.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import com.lenskart.commons.model.Cluster;
import org.bson.Document;



public class FraudLensContextDbUtils {

    public static final String clusterName = Cluster.POS_CLUSTER.getClusterName();
    public static final String fraudLensContextDatabaseName = "fraud-lens-context";

    public static Document getOrderUserTotalAggregationDetails(int customerId) {
        Document data = MongoDBQueryExecutor.findOne(
                clusterName,
                fraudLensContextDatabaseName,
                "order_user_total_aggregation",
                new Document("_id", customerId)
        );
        return data;
    }

    public static Document getOrderPincodeShortDurationAggregationDetails(String pincode) {
        Document data = MongoDBQueryExecutor.findOne(
                clusterName,
                fraudLensContextDatabaseName,
                "order_pincode_short_duration_aggregation",
                new Document("_id", pincode)
        );
        return data;
    }

    public static Document getOrderPincodeLongDurationAggregationDetails(String pincode) {
        Document data = MongoDBQueryExecutor.findOne(
                clusterName,
                fraudLensContextDatabaseName,
                "order_pincode_long_duration_aggregation",
                new Document("_id", pincode)
        );
        return data;
    }

    public static Document getOrderPaymentMethodShortDurationAggregationDetails(String paymentMethod) {
        Document data = MongoDBQueryExecutor.findOne(
                clusterName,
                fraudLensContextDatabaseName,
                "order_payment_method_short_duration_aggregation",
                new Document("_id", paymentMethod)
        );
        return data;
    }

    public static Document getOrderPaymentMethodLongDurationAggregationDetails(String paymentMethod) {
        Document data = MongoDBQueryExecutor.findOne(
                clusterName,
                fraudLensContextDatabaseName,
                "order_payment_method_long_duration_aggregation",
                new Document("_id", paymentMethod)
        );
        return data;
    }
}
