package com.lenskart.juno.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import com.lenskart.commons.model.Cluster;
import groovy.transform.builder.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;


@Builder
@Slf4j
public class JunoCartDbUtils {

    public static final String clusterName = Cluster.JUNO_CLUSTER.getClusterName();
    public static final String cartDatabaseName = "juno_v2_preprod_cart_latest";
    public static final String cartCollectionName = "cart";

    public static Document getCartDbUtils(int cartId) {
        Document data = MongoDBQueryExecutor.findOne(
                clusterName,
                cartDatabaseName,
                cartCollectionName,
                new Document("_id", cartId)
        );
        return data;
    }
}