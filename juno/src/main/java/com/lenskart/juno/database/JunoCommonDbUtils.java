package com.lenskart.juno.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import org.bson.Document;


import groovy.transform.builder.Builder;
import lombok.extern.slf4j.Slf4j;

@Builder
@Slf4j

public class JunoCommonDbUtils {


    public static Document getAthenaOrderLogDetails(int orderId) {
        Document data = MongoDBQueryExecutor.findOne(
                "juno_cluster",
                "juno_v2_preprod_common",
                "athena_order_log",
                new Document("_id", orderId)
        );
        return data;
    }
}
