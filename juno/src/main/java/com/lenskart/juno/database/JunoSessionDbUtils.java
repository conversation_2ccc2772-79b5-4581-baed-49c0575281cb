package com.lenskart.juno.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import com.lenskart.commons.model.Cluster;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;


@Builder
@Slf4j
public class JunoSessionDbUtils {

    public static String clusterName = Cluster.JUNO_CLUSTER.getClusterName();
    public static String SessionDatabaseName = "juno_v2_preprod_session_new";
    public static String SessionCollectionName = "session_v2";

    public static Document getSessionDetails(String sessionToken) {
        Document data = MongoDBQueryExecutor.findOne(
                clusterName,
                SessionDatabaseName,
                SessionCollectionName,
                new Document("_id", sessionToken)
        );
        return data;
    }
}