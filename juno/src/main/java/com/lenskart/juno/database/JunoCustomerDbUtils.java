package com.lenskart.juno.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import com.lenskart.commons.model.Cluster;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

@Builder
@Slf4j
public class JunoCustomerDbUtils {


    public static String clusterName = Cluster.JUNO_CLUSTER.getClusterName();
    public static String CustomerDatabaseName = "juno_v2_preprod_customer_latest";
    public static String CustomerCollectionName = "customer_v2";

    public static Document getCustomerDetails(String customerId) {

        Document data = MongoDBQueryExecutor.findOne(
                clusterName,
                CustomerDatabaseName,
                CustomerCollectionName,
                new Document("_id", customerId)
        );
        return data;
    }
}