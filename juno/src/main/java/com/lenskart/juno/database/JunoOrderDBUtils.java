package com.lenskart.juno.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import com.lenskart.commons.model.Cluster;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;


@Builder
@Slf4j
public class JunoOrderDBUtils {

    public static String clusterName = Cluster.JUNO_CLUSTER.getClusterName();
    public static String OrderDatabaseName = "juno_v2_preprod_order_latest";
    public static String OrderCollectionName = "order_v2";


    public static Document getOrderDetails(int orderId) {
        Document data = MongoDBQueryExecutor.findOne(
                clusterName,
                OrderDatabaseName,
                OrderCollectionName,
                new Document("_id", orderId)
        );
        return data;
    }
}