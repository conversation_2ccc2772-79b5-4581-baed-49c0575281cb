package com.lenskart.juno.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import com.lenskart.commons.model.Cluster;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

@Builder
@Slf4j
public class PaymentDbUtils {


    public static String clusterName = Cluster.JUNO_CLUSTER.getClusterName();
    public static String PaymentDatabaseName = "juno_v2_preprod_payment";
    public static String PaymentCollectionName = "payment_v2";

    public static Document getPaymentDetails(String orderId) {
        Document data = MongoDBQueryExecutor.findOne(
                clusterName,
                PaymentDatabaseName,
                PaymentCollectionName,
                new Document("orderId", orderId)
        );
        return data;
    }
}