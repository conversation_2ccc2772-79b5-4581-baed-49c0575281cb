package com.lenskart.juno.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.constants.Constants;

import java.util.List;
import java.util.Map;

public class Juno3SepDbUtils {

    public static List<Map<String, Object>> getOrderDetails(OrderContext orderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.JUNO_CLUSTER.getClusterName(),
                        Constants.SALES_FLAT_ORDER_DB,
                        SalesFlatOrderQueries.ORDER_DETAILS,
                        orderContext.getOrderId());
    }
}