# Configuration file for the juno module

# QA environment configuration
preprod:
  # Base URLs for different services
  baseUrls:
    sessionService: https://api-gateway.juno.preprod.lenskart.com
    juspayService: https://sandbox.juspay.in
    webUrl: https://preprod.lenskart.com
    webUrlOrderDetails: https://preprod.lenskart.com/customer/account/order-detail/
    walletService: https://wallet.juno.preprod.lenskart.com

  # Authentication configuration
  auth:
    adminToken: "8e8b0816-4c73-4f08-8f7d-022dcd186a91"
    refreshToken: "8e8b0816-8f7d-4f08-4c73-022dcd186a91"

  # Wallet configuration
  wallet:
    bulkCreditAmount: 50
  draco:
    username: "<EMAIL>"
    password: "TNguUq2?PAR7"