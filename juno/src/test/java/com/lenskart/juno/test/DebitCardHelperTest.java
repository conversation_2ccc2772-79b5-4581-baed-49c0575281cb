
package com.lenskart.juno.test;

import com.lenskart.commons.model.*;
import com.lenskart.juno.helpers.*;
import org.testng.annotations.Test;
import java.util.List;


public class DebitCardHelperTest {
    @Test
    public void DebitCardHelperTest() {

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .paymentMethod(PaymentMethod.DEBIT_CARD)
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .pinCode(Countries.IN.getDefaultPinCode())
                        .country(Countries.IN)
                        .build())
                .headers(OrderContext.Headers.builder()
                        .client(Client.DESKTOP)
                        .build())

                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                .powerType(PowerTypes.SINGLE_VISION)
                                .build())).build();


        JunoOrderCreationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();




    }
}