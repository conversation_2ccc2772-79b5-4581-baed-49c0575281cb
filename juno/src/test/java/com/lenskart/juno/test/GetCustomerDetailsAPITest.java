package com.lenskart.juno.test;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.juno.helpers.GetCustomerDetailsPlainHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.Client;
import org.testng.annotations.Test;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class GetCustomerDetailsAPITest {

    @Test
    public void testGetCustomerDetailsPlainAPI() {
        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .build())
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build())
                .build();

        GetCustomerDetailsPlainHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}