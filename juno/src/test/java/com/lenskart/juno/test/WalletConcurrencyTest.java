package com.lenskart.juno.test;

import com.lenskart.commons.listeners.ExtentReportListener;
import com.lenskart.commons.utils.ConcurrencyTestUtils;
import com.lenskart.juno.helpers.WalletApiHelper;
import org.testng.Assert;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

import java.util.UUID;

/**
 * Concurrency tests for Wallet APIs to verify locking mechanisms and balance updates
 * Uses Java 21 Virtual Threads for high-performance concurrent testing
 */

@Listeners({ExtentReportListener.class})
public class WalletConcurrencyTest {


    // Test configuration, move all the token to juno.yaml after confirmation
    private static final int CONCURRENT_THREADS = 10;
    private static final String TEST_AMOUNT = "100";
    private static final String CUSTOMER_ID = "29732673";
    private static final String MOBILE_NUMBER = "9999999999";
    private static final String SESSION_TOKEN = "9abf3120-7f4e-413f-b7f7-e9ab42cb7e37";
    private static final String AUTH_TOKEN = "8e8b0816-4c73-4f08-8f7d-022dcd186a91";
    private static final String COUNTRY_CODE = "IN";


    @Test(description = "Test concurrent wallet credit API calls to verify locking mechanism and balance updates", priority = 1)
    public void testConcurrentWalletCredit() {

        WalletApiHelper walletApiHelper = WalletApiHelper.builder()
                .threadCount(CONCURRENT_THREADS)
                .testAmount(TEST_AMOUNT)
                .customerId(CUSTOMER_ID)
                .mobileNumber(MOBILE_NUMBER)
                .sessionToken(SESSION_TOKEN)
                .authToken(AUTH_TOKEN)
                .countryCode(COUNTRY_CODE)
                .merchantRefId(UUID.randomUUID().toString().substring(0, 6))
                .build();

        walletApiHelper.test();

        ConcurrencyTestUtils.ConcurrentExecutionResult result = walletApiHelper.getResult();

        // Assert all requests completed
        Assert.assertEquals(result.getTotalRequests(), CONCURRENT_THREADS, "Total requests should match expected thread count");

        // Assert no exceptions occurred
        Assert.assertTrue(result.getExceptions().isEmpty(), "No exceptions should occur during concurrent execution");


        // Assert success rate is 100% (all requests should succeed)
        double successRate = (double) result.getSuccessCount() / result.getTotalRequests() * 100;

        Assert.assertEquals(successRate, 100.0, 0.1, "Success rate should be 100%");
    }

}
