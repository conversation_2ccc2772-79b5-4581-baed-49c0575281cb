# Juno Module

## Overview

The **Juno Module** provides comprehensive automation testing capabilities for Lenskart's Juno service, which handles customer order management, cart operations, payment processing, and order fulfillment workflows.

## 🏗️ **Architecture**

### **Core Components**

- **Order Management**: Complete order lifecycle automation
- **Cart Operations**: Add to cart, modify cart, clear cart functionality
- **Payment Processing**: Payment creation and confirmation
- **Customer Management**: Customer details and authentication
- **Product & Package Management**: Product catalog and package operations
- **Prescription Management**: Prescription creation and validation

## 📁 **Package Structure**

```
com.lenskart.juno/
├── config/              # Juno-specific configuration management
├── endpoints/           # Juno API endpoints and endpoint manager
├── exceptions/          # Juno-specific exception states
├── helpers/             # Service helper classes for Juno operations
├── requestbuilder/      # Request builders for Juno API calls
└── util/                # Juno-specific utility classes
```

## 🔧 **Key Features**

### **1. Complete Order Lifecycle Management**

#### **Order Creation Workflow**
```java
JunoOrderCreationHelper orderHelper = new JunoOrderCreationHelper();

// Create complete order with all steps
String orderId = orderHelper.createCompleteOrder(
    customerId, 
    productId, 
    prescriptionData, 
    shippingAddress, 
    paymentMethod
);
```

#### **Individual Order Steps**
```
// 1. Authentication
AuthenticationHelper authHelper = new AuthenticationHelper();
String sessionToken = authHelper.authenticateCustomer(customerId);

// 2. Create Cart
CreateCartHelper cartHelper = new CreateCartHelper();
String cartId = cartHelper.createCart(customerId, productId, quantity);

// 3. Add Shipping Address
AddShippingAddressHelper addressHelper = new AddShippingAddressHelper();
addressHelper.addShippingAddress(cartId, shippingAddress);

// 4. Create Prescription
CreatePrescriptionHelper prescriptionHelper = new CreatePrescriptionHelper();
String prescriptionId = prescriptionHelper.createPrescription(prescriptionData);

// 5. Create Payment
CreateOrderPaymentHelper paymentHelper = new CreateOrderPaymentHelper();
String paymentId = paymentHelper.createPayment(cartId, paymentDetails);

// 6. Confirm Order
ConfirmOrderHelper confirmHelper = new ConfirmOrderHelper();
String orderId = confirmHelper.confirmOrder(cartId, paymentId);
```

### **2. Cart Management**

#### **Cart Operations**
```
CreateCartHelper cartHelper = new CreateCartHelper();

// Create new cart
String cartId = cartHelper.createCart(customerId, productId, quantity);

// Add items to existing cart
cartHelper.addItemToCart(cartId, productId, quantity);

// Clear cart
ClearCartHelper clearHelper = new ClearCartHelper();
clearHelper.clearCart(cartId);
```

### **3. Customer Management**

#### **Customer Operations**
```java
GetCustomerDetailsHelper customerHelper = new GetCustomerDetailsHelper();

// Get customer details
CustomerDetails customer = customerHelper.getCustomerDetails(customerId);

// Validate customer
boolean isValid = customerHelper.validateCustomer(customerId);

// Get customer orders
List<Order> orders = customerHelper.getCustomerOrders(customerId);
```

### **4. Product & Package Management**

#### **Product Operations**
```java
GetProductAndPackageHelper productHelper = new GetProductAndPackageHelper();

// Get product details
ProductDetails product = productHelper.getProductDetails(productId);

// Get package information
PackageDetails packageInfo = productHelper.getPackageDetails(packageId);

// Validate product availability
boolean isAvailable = productHelper.isProductAvailable(productId, locationId);
```

### **5. Order Details and Tracking**

#### **Order Information**
```java
GetOrderDetailsHelper orderHelper = new GetOrderDetailsHelper();

// Get complete order details
OrderDetails order = orderHelper.getOrderDetails(orderId);

// Get order status
String status = orderHelper.getOrderStatus(orderId);

// Get order items
List<OrderItem> items = orderHelper.getOrderItems(orderId);

// Get order timeline
List<OrderEvent> timeline = orderHelper.getOrderTimeline(orderId);
```

## 🌐 **API Endpoints**

### **Juno Endpoints**
```java
public enum JunoEndpoints implements BaseEndpoint {
    // Authentication
    V2_SESSIONS("/api/v2/sessions", "junoService", "POST", "Customer authentication"),
    
    // Cart Management
    CREATE_CART("/api/v1/carts", "junoService", "POST", "Create new cart"),
    ADD_TO_CART("/api/v1/carts/{cartId}/items", "junoService", "POST", "Add item to cart"),
    CLEAR_CART("/api/v1/carts/{cartId}/clear", "junoService", "POST", "Clear cart"),
    
    // Order Management
    CREATE_ORDER("/api/v1/orders", "junoService", "POST", "Create new order"),
    GET_ORDER_DETAILS("/api/v1/orders/{orderId}", "junoService", "GET", "Get order details"),
    CONFIRM_ORDER("/api/v1/orders/{orderId}/confirm", "junoService", "POST", "Confirm order"),
    
    // Payment
    CREATE_PAYMENT("/api/v1/payments", "junoService", "POST", "Create payment"),
    
    // Customer
    GET_CUSTOMER("/api/v1/customers/{customerId}", "junoService", "GET", "Get customer details"),
    
    // Product
    GET_PRODUCT("/api/v1/products/{productId}", "junoService", "GET", "Get product details"),
    GET_PACKAGE("/api/v1/packages/{packageId}", "junoService", "GET", "Get package details");
}
```

### **URL Generation**
```java
// Simple URL generation
String url = JunoEndpoints.GET_ORDER_DETAILS.getUrl();

// URL with path parameters
String url = JunoEndpoints.GET_ORDER_DETAILS.getUrl(
    Map.of("orderId", "ORD123456")
);

// Using endpoint manager
String url = JunoEndpointManager.getEndpointUrl(
    JunoEndpoints.CREATE_CART
);
```

## 🔧 **Configuration**

### **juno.yml Configuration**
```yaml
juno:
  baseUrls:
    junoService: https://juno.preprod.lenskart.com
    
  authentication:
    defaultCustomerId: "CUST123456"
    sessionTimeout: 3600
    
  defaults:
    country: "IN"
    currency: "INR"
    paymentMethod: "RAZORPAY"
    
  timeouts:
    apiTimeout: 30000
    orderConfirmationTimeout: 120000
    
  validation:
    validateOrderStatus: true
    validatePaymentStatus: true
    validateCustomerDetails: true
```

### **Configuration Usage**
```java
// Get Juno configuration
JunoConfig config = JunoConfigLoader.loadConfig();

// Get base URL
String baseUrl = config.getBaseUrl("junoService");

// Get default values
String defaultCountry = config.getDefaultCountry();
String defaultCurrency = config.getDefaultCurrency();
```

## 🧪 **Testing**

### **Test Categories**

#### **Sanity Tests**
```java
@Test
@TestCategory(TestCategory.Category.SANITY)
public void testCreateOrder() {
    JunoOrderCreationHelper helper = new JunoOrderCreationHelper();
    String orderId = helper.createBasicOrder(customerId, productId);
    assert orderId != null && !orderId.isEmpty();
}
```

#### **Regression Tests**
```java
@Test
@TestCategory(TestCategory.Category.REGRESSION)
public void testCompleteOrderWorkflow() {
    // Test complete order creation workflow
    JunoOrderCreationHelper helper = new JunoOrderCreationHelper();
    
    // Create order with all steps
    String orderId = helper.createCompleteOrder(
        customerId, productId, prescriptionData, 
        shippingAddress, paymentMethod
    );
    
    // Validate order details
    GetOrderDetailsHelper orderHelper = new GetOrderDetailsHelper();
    OrderDetails order = orderHelper.getOrderDetails(orderId);
    
    assert order.getStatus().equals("CONFIRMED");
    assert order.getCustomerId().equals(customerId);
}
```

#### **E2E Tests**
```java
@Test
@TestCategory(TestCategory.Category.E2E)
public void testOrderToDeliveryWorkflow() {
    // Complete end-to-end order workflow
    // Including order creation, processing, and delivery tracking
}
```

### **Running Juno Tests**
```bash
# Run all Juno tests
mvn test -pl juno

# Run specific test categories
mvn test -pl juno -DtestCategory=SANITY

# Run with specific environment
mvn test -pl juno -Denvironment=preprod

# Run specific test class
mvn test -pl juno -Dtest=JunoOrderCreationTest
```

## 📊 **Request Builders**

### **Order Request Builder**
```java
JunoOrderRequestBuilder builder = new JunoOrderRequestBuilder();

// Build create cart request
Map<String, Object> cartRequest = builder
    .withCustomerId(customerId)
    .withProductId(productId)
    .withQuantity(2)
    .withCountry("IN")
    .buildCreateCartRequest();

// Build order confirmation request
Map<String, Object> confirmRequest = builder
    .withCartId(cartId)
    .withPaymentId(paymentId)
    .withShippingAddress(address)
    .buildConfirmOrderRequest();
```

## 🛠️ **Utilities**

### **Order Utilities**
```java
OrderUtil orderUtil = new OrderUtil();

// Generate order reference
String orderRef = orderUtil.generateOrderReference();

// Validate order data
boolean isValid = orderUtil.validateOrderData(orderData);

// Calculate order total
double total = orderUtil.calculateOrderTotal(orderItems);

// Format order for display
String formatted = orderUtil.formatOrderSummary(order);
```

## 🔍 **Exception Handling**

### **Juno Exception States**
```java
public enum JunoExceptionStates {
    AUTHENTICATION_FAILED("AUTH_001", "Customer authentication failed"),
    CART_CREATION_FAILED("CART_001", "Failed to create cart"),
    PRODUCT_NOT_AVAILABLE("PROD_001", "Product not available"),
    PAYMENT_FAILED("PAY_001", "Payment processing failed"),
    ORDER_CONFIRMATION_FAILED("ORD_001", "Order confirmation failed");
}
```

### **Exception Usage**
```
try {
    String orderId = orderHelper.createOrder(orderData);
} catch (JunoException e) {
    if (e.getState() == JunoExceptionStates.PAYMENT_FAILED) {
        // Handle payment failure
        log.error("Payment failed for order: {}", e.getMessage());
    }
}
```

## 📈 **Performance Features**

- **Connection Pooling**: Efficient HTTP connection management
- **Request Caching**: Cache frequently accessed data
- **Async Operations**: Support for asynchronous order processing
- **Retry Mechanisms**: Automatic retry for transient failures
- **Timeout Management**: Configurable timeouts for different operations

## 🔗 **Integration Points**

### **With Other Modules**
- **Commons**: Uses shared utilities and database connections
- **POS**: Shares order models and validation logic
- **SCM**: Integrates for order fulfillment tracking
- **CS**: Provides order data for customer service operations

### **External Services**
- **Payment Gateway**: Razorpay, PayU integration
- **Inventory Service**: Product availability checks
- **Shipping Service**: Delivery address validation
- **Notification Service**: Order status notifications

## 🚀 **Getting Started**

### **1. Add Juno Dependency**
```xml
<dependency>
    <groupId>com.lenskart</groupId>
    <artifactId>juno</artifactId>
    <version>1.0.0</version>
</dependency>
```

### **2. Configure Juno Service**
Update `juno.yml` with your Juno service configuration.

### **3. Create Your First Test**
```java
public class MyJunoTest {
    @Test
    @TestCategory(TestCategory.Category.SANITY)
    public void testOrderCreation() {
        JunoOrderCreationHelper helper = new JunoOrderCreationHelper();
        String orderId = helper.createBasicOrder("CUST123", "PROD456");
        assert orderId != null;
    }
}
```

## 📚 **Examples**

Check the test classes for comprehensive examples:
- **Order Creation**: Complete order workflow examples
- **Cart Management**: Cart operations and validations
- **Customer Operations**: Customer authentication and details
- **Product Management**: Product and package operations
- **Payment Processing**: Payment creation and confirmation

The Juno module provides comprehensive automation capabilities for testing Lenskart's core order management functionality, ensuring reliable and efficient order processing workflows.
