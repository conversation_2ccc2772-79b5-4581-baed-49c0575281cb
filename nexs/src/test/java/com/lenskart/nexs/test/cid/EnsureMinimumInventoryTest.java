package com.lenskart.nexs.test.cid;

import com.lenskart.commons.model.ProductId;
import com.lenskart.nexs.util.CIDUtil;
import com.lenskart.scm.database.ScmDbUtils;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class EnsureMinimumInventoryTest {

    @DataProvider(name = "countryProducts1")
    public Object[][] countryProducts1() {
        ProductId[] allProducts = ProductId.values();
        Object[][] data = new Object[allProducts.length][1];
        for (int i = 0; i < allProducts.length; i++) {
            data[i][0] = allProducts[i];
        }
        return data;
    }


    @Test(dataProvider = "countryProducts1")
    public void addInventory(ProductId product) {
        CIDUtil.ensureMinimumInventory(product, 300);
    }

    @DataProvider(name = "countryProductProvider")
    public Object[][] countryProductProvider() {
        List<Map<String, Object>> data = ScmDbUtils.getLast10DayPIDs(); // Your existing data source

        List<Object[]> flatList = new ArrayList<>();

        for (Map<String, Object> row : data) {
            String country = (String) row.get("lk_country");
            String productIdsStr = (String) row.get("distinct_product_ids");

            if (productIdsStr != null) {
                String[] productIds = productIdsStr.split(",");
                for (String pid : productIds) {
                    flatList.add(new Object[]{country, pid.trim()});
                }
            }
        }

        return flatList.toArray(new Object[0][]);
    }

    @Test(dataProvider = "countryProductProvider")
    public void addLast10DaysPIDsInventory(String country, String pid) {
        CIDUtil.ensureMinimumInventory(country, pid.trim(), 300);
    }

    @Test
    public void addInventoryManually() {
        CIDUtil.ensureMinimumInventory("IN", "131316", 1);
    }
}
