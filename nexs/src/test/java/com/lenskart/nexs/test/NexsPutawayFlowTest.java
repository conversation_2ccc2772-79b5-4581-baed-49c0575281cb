package com.lenskart.nexs.test;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.nexs.helpers.grn.InwardCreatePutawayHelper;
import com.lenskart.nexs.helpers.grn.InwardPutawayPendingHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.po.InwardPoInvoiceProcessHelper;
import com.lenskart.nexs.helpers.po.InwardPoProcessHelper;
import com.lenskart.nexs.helpers.putaway.InwardPutawayCompleteHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class NexsPutawayFlowTest {

    private NexsInwardContext nexsInwardContext;
    private NexsOrderContext nexsOrderContext;

    @BeforeClass
    public void setup() {
        nexsInwardContext = NexsInwardContext.builder().build();
        nexsOrderContext = NexsOrderContext.builder().facilityCode(nexsInwardContext.getFacilityCode()).headers(NexsOrderContext.Headers.builder().build()).build();
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
    }

    @Test(priority = 0)
    public void putawayCreationFlow() {
        InwardPoProcessHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
        InwardPoInvoiceProcessHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
        InwardCreatePutawayHelper inwardCreateputawayHelper = InwardCreatePutawayHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        inwardCreateputawayHelper.test();
        nexsInwardContext = inwardCreateputawayHelper.getNexsInwardContext();
        nexsOrderContext = inwardCreateputawayHelper.getNexsOrderContext();
    }

    @Test(priority = 1, dependsOnMethods = {"putawayCreationFlow"})
    public void putawayPendingFlow() {
        InwardPutawayPendingHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
    }

    @Test(priority = 2, dependsOnMethods = {"putawayPendingFlow"})
    public void putawayCompleteFlow() {
        InwardPutawayCompleteHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();

    }
}


