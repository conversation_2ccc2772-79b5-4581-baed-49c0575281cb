package com.lenskart.nexs.test.ims;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.CidDbUtils;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.helpers.ims.StockInAndOutV2Helper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.util.IMSUtil;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

@TestCategory(TestCategory.Category.SANITY)
@Slf4j
public class ImsJsonEventsTest {
    private NexsOrderContext nexsOrderContext;
    private boolean cidUpdateRequired;

    @BeforeClass
    public void loadEvents() throws IOException {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("IMSEvents.json")) {
            if (is == null) throw new FileNotFoundException("IMSEvents.json not found in classpath");
            String content = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            List<String> events = JsonUtils.getKeysInJsonUsingJsonNodeFieldNames(JsonUtils.convertStringToJson(content).getJSONObject("events").toString(), new ObjectMapper());
            log.info("Loaded Events: {}", events);
        }
    }

    @BeforeMethod
    public void initBarcode() {
        nexsOrderContext = NexsOrderContext.builder()
                .facilityCode(Constants.BHIWADI_WAREHOUSE_FACILITY)
                .productId("131932")
                .legalOwner(Constants.IN_LEGAL_OWNER)
                .locationCode(Constants.TEST_LOCATION)
                .build();
        nexsOrderContext.setBarcode(IMSUtil.createBarcodeEntryInIMS(nexsOrderContext));
    }

    @DataProvider
    public Object[][] eventsList() {
        //   return events.stream().map(event -> new Object[] { event }).toArray(Object[][]::new);

        return new Object[][]{
                { "IQC_GRN_DONE" },
                { "DISCARDED" },
                { "LIQUIDATION" },
                { "RETURN_TRANSFER_DISPATCHED" },
                { "MARK_RECEIVED" },
                { "STORE_EXECUTION" },
                { "RTV_DISPATCHED" },
                { "GATE_PASS_DISPATCHED" },
                { "GATE_PASS_CREATION" },
                { "MARK_BAD_PENDING_APPROVAL" },
                { "UNICOM_STOCK_CORRECTION" },
                { "OTC_DISPATCHED" },
                { "OTC_INVOICED" },
                { "OTC_ALLOCATED" },
                { "STORE_RECEIVED" },
                { "TRANSFER_IN_TRANSIT" },
                { "START_RETIRE" },
                { "MARK_VIRTUAL" },
                { "PUTAWAY_COMPLETE_VIRTUAL" },
                { "RETURNED_PUTAWAY_COMPLETE" },
                { "TRANSFER_DISPATCHED" } // Your currently active test case
        };

    }

    @Test(dataProvider = "eventsList")
    public void validateJsonEventTransition(String operation) throws Exception {
        JSONObject transition = getTransition(operation);
        List<String> keys = JsonUtils.getKeysInJsonUsingJsonNodeFieldNames(transition.toString(), new ObjectMapper());
        Assert.assertEquals(transition.length(), keys.size(), "Mismatch in transition keys count");

        for (String key : keys) {
            log.info("Processing key: {}", key);
            JSONObject transitionState = transition.getJSONObject(key);

            if (isSkippableCase(operation, key)) continue;

            JSONObject currentState = JsonUtils.convertStringToJson(key);
            if (!key.contains("\"condition\":\"DEFAULT\"")) {
                updateIMSBarcodeState(currentState);
            }

            cidUpdateRequired = !JsonUtils.compareJson(currentState, transitionState.getJSONObject("state")) &&
                    transitionState.optBoolean("cidUpdateRequired", false);

            int[] inventoryBefore = getInventoryBeforeCidUpdate(transitionState);
            StockInAndOutV2Helper helper = performStockUpdate(operation, key);

            JSONObject statusFromDB = new JSONObject(ImsDbUtils.getStatusConditionAvailabilityOfBarcode(nexsOrderContext).getFirst());
            AwaitUtils.sleepSeconds(3);
            Assert.assertTrue(JsonUtils.compareJson(statusFromDB, transitionState.getJSONObject("state")));

            if (cidUpdateRequired && helper.getResponse().jsonPath().getInt("data.itemStockUpdateResponseV2List[0].statusCode") == 200) {
                IMSUtil.validateCidUpdatePostconditions(transitionState, inventoryBefore, nexsOrderContext);
            }
        }
    }

    private JSONObject getTransition(String operation) throws IOException {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("IMSEvents.json")) {
            Assert.assertNotNull(is);
            String content = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            return JsonUtils.convertStringToJson(content).getJSONObject("events").getJSONObject(operation).getJSONObject("transition");
        }
    }

    private boolean isSkippableCase(String operation, String key) {
        return operation.equals("CANCELLED") && key.contains("\"condition\":\"DEFAULT\"");
    }

    private void updateIMSBarcodeState(JSONObject data) {
        int count = ImsDbUtils.updateBarcodeStatus(
                data.getString("condition"),
                data.getString("availability"),
                data.getString("status"),
                nexsOrderContext.getBarcode(),
                nexsOrderContext.getLegalOwner()
        );
        Assert.assertEquals(count, 1);
    }

    private int[] getInventoryBeforeCidUpdate(JSONObject transitionState) {
        if (!cidUpdateRequired) return new int[] { -1, -1 };

        int[] inventory = new int[2];
        JSONArray cidUpdates = transitionState.getJSONArray("cidUpdates");

        for (int i = 0; i < cidUpdates.length(); i++) {
            JSONObject update = cidUpdates.getJSONObject(i);
            String cond = update.getString("condition");
            String avail = update.getString("availability");
            String stat = update.getString("status");
            String op = update.getString("cidInventoryOperation");

            try {
                inventory[i] = CidDbUtils.getQuantityFromWarehouseInventory(
                        nexsOrderContext.getProductId(),
                        nexsOrderContext.getFacilityCode(),
                        cond, avail, stat,
                        nexsOrderContext.getLegalOwner()
                );
                inventory[i] += op.equals("INCREMENT") ? 1 : -1;
            } catch (NumberFormatException e) {
                inventory[i] = op.equals("INCREMENT") ? 1 : 0;
            }
        }

        return inventory;
    }

    private StockInAndOutV2Helper performStockUpdate(String operation, String key) {
        String barcode = nexsOrderContext.getBarcode();
        if (key.contains("\"condition\":\"DEFAULT\"")) {
            barcode = IMSUtil.generateUniqueBarcode(nexsOrderContext);
            nexsOrderContext.setBarcode(barcode);
        }

        StockInAndOutV2Helper helper = StockInAndOutV2Helper.builder()
                .barcode(barcode)
                .facility(nexsOrderContext.getFacilityCode())
                .location(nexsOrderContext.getLocationCode())
                .operation(operation)
                .pid(Integer.parseInt(nexsOrderContext.getProductId()))
                .legalOwner(nexsOrderContext.getLegalOwner())
                .updatedBy("Automation")
                .build();
        helper.test();

        int statusCode = helper.getResponse().jsonPath().getInt("data.itemStockUpdateResponseV2List[0].statusCode");
        Assert.assertTrue(statusCode == 200 || statusCode == 201, "Unexpected status code: " + statusCode);

        return helper;
    }
}
