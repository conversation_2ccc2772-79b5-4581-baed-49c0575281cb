package com.lenskart.nexs.test.po;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.helpers.po.StartRetireE2EHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

import static com.lenskart.commons.model.ProductId.SG_EYEGLASSES;
import static com.lenskart.commons.model.ProductId.SG_EYEGLASSES_1;

@TestCategory(TestCategory.Category.SANITY)
public class StartRetireTests {
    NexsOrderContext nexsOrderContext;

    /**
     * RUN THIS TEST ONLY FOR SINGAPORE FACILITY. SYNC IS CONFIGURED FOR SINGAPORE FACILITY ONLY.
     * OTHER FACILITIES WILL FAIL.
     */
    @Test
    public void startRetire() {
        nexsOrderContext = NexsOrderContext
                .builder()
                .headers(NexsOrderContext.Headers.builder().build())
                .productId(SG_EYEGLASSES.getProductId())
                .legalOwner(Constants.SG_LEGAL_OWNER)
                .facilityCode(Constants.SINGAPORE_WAREHOUSE_FACILITY)
                .newProductId(SG_EYEGLASSES_1.getProductId())
                .build();

        String barcode = ImsDbUtils.getGAABarcodes(nexsOrderContext, 1).getFirst().get("barcode").toString();
        nexsOrderContext.setBarcode(barcode);

        StartRetireE2EHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
    }
}
