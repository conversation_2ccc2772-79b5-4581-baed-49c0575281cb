package com.lenskart.nexs.test.cid;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.nexs.helpers.cid.GetConsolidatedInvInfo;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.model.ProductId.IN_EYEGLASSES;

@TestCategory(TestCategory.Category.SANITY)
@Slf4j
public class GetInventoryInfoTest {
    @Test
    public void getInventoryInfo() {
        NexsOrderContext nexsOrderContext = NexsOrderContext
                .builder()
                .productId(IN_EYEGLASSES.getProductId())
                .headers(NexsOrderContext.Headers.builder().build())
                .build();

        GetConsolidatedInvInfo getConsolidatedInvInfo = GetConsolidatedInvInfo.builder()
                .nexsOrderContext(nexsOrderContext)
                .build();
        getConsolidatedInvInfo.test();
        log.info("Available Inventory: {}", getConsolidatedInvInfo.getConsolidatedInvInfo().getAvailableInventory());
    }

}
