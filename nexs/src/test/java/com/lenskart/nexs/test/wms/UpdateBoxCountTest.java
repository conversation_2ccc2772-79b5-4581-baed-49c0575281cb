package com.lenskart.nexs.test.wms;
import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.wms.UpdateBoxCountHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class UpdateBoxCountTest {
    @Test
    public void updateBoxCount() {

        NexsOrderContext nexsOrderContext = NexsOrderContext
                .builder()
                .shippingId("SNXS2260000004448776")
                .boxCount("2")
                .headers(NexsOrderContext.Headers.builder().build())
                .build();

        NexsAuthHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();

        UpdateBoxCountHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
    }
}
