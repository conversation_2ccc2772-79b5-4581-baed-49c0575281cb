package com.lenskart.nexs.test.autoReplenishment;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.model.ScmOrderContext;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

import static com.lenskart.nexs.constants.Constants.AUTOREPLENISHMENT;

@TestCategory(TestCategory.Category.SANITY)
public class AutoReplenishmentTest {
    @Test
    public void testAutoReplenishmentOrderFlow() {
        String facilityCode = "LKS18";
        String productId = "131315";

      // TODO: Replace hardcoded order ID with POS-generated Auto-Replenishment order once integration is ready
        List<Map<String, Object>> orderList = ScmDbUtils.getAutoReplenishmentOrderId(facilityCode, productId);
        Assert.assertFalse(orderList.isEmpty(), "No auto-replenishment order found");
        String dealsOrderId = orderList.getFirst().get("deals_order_id").toString();

        List<Map<String, Object>> entityData = ScmDbUtils.getEntityValueAndPackageId(dealsOrderId);
        Assert.assertFalse(entityData.isEmpty(), "No entity_value found for incrementId");
        String entityValue =  entityData.getFirst().get("entity_value").toString();
        String shippingPackageId = entityData.getFirst().get("shipping_package_id").toString();

        NexsOrderContext nexsOrderContext = NexsOrderContext.builder().incrementId(dealsOrderId).shippingId(shippingPackageId).build();
        String orderPriority = WMSDbUtils.getOrderItemHeaderDetails(nexsOrderContext).getFirst().get("order_priority").toString();

        Assert.assertFalse(orderPriority.isEmpty(), "No order priority found");
        Assert.assertEquals(entityValue, AUTOREPLENISHMENT, "Entity value should be AUTOREPLENISHMENT");
        Assert.assertEquals(orderPriority, "1", "Order priority for auto-replenishment should be P1 (1)");
    }
}
