package com.lenskart.nexs.test.transfers;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.ProductId;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.transfer.TransferEndToEndHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class TransferEndToEndTest
{
    @Test
    public void testE2eFlow() {
        NexsInwardContext nexsInwardContext = NexsInwardContext.builder().build();
        NexsOrderContext.TransferContext context = NexsOrderContext.TransferContext.builder()
                .sourceFacility(Constants.BHIWADI_WAREHOUSE_FACILITY)
                .destFacility(Constants.MANESAR_WAREHOUSE_FACILITY)
                .productId(Integer.valueOf(nexsInwardContext.getProductId()))
                .productDesc("Test Product")
                .quantity(2)
                .availQty("")
                .transferItemId("1")
                .transferType(Constants.TRANSFER_TYPE)
                .legalOwner(Constants.IN_LEGAL_OWNER)
                .build();

        TransferEndToEndHelper.builder()
                .transferContext(context)
                .build()
                .init()
                .orchestrateFlow();

    }
}