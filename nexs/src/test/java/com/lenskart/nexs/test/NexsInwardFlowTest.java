package com.lenskart.nexs.test;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.nexs.helpers.grn.GrnE2EHelper;
import com.lenskart.nexs.helpers.grn.InwardGrnProcessHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.po.InwardPoInvoiceProcessHelper;
import com.lenskart.nexs.helpers.po.InwardPoProcessHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class NexsInwardFlowTest {

    private NexsInwardContext nexsInwardContext;
    private NexsOrderContext nexsOrderContext;

    @BeforeClass
    public void setup() {
        nexsInwardContext = NexsInwardContext.builder().build();
        nexsOrderContext = NexsOrderContext.builder().facilityCode(nexsInwardContext.getFacilityCode()).headers(NexsOrderContext.Headers.builder().build()).build();
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
    }

    @Test(enabled = false)
    public void inwardFlow() {
        GrnE2EHelper.builder().build().test();
    }

    @Test(priority = 0)
    public void inwardPoProcessFlow() {
        InwardPoProcessHelper inwardPoProcessHelper = InwardPoProcessHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        inwardPoProcessHelper.test();
        nexsOrderContext = inwardPoProcessHelper.getNexsOrderContext();
        nexsInwardContext = inwardPoProcessHelper.getNexsInwardContext();

    }

    @Test(priority = 1, dependsOnMethods = {"inwardPoProcessFlow"})
    public void inwardPoInvoiceProcessFlow() {
        InwardPoInvoiceProcessHelper inwardPoInvoiceProcessHelper = InwardPoInvoiceProcessHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        inwardPoInvoiceProcessHelper.test();
        nexsInwardContext = inwardPoInvoiceProcessHelper.getNexsInwardContext();

    }

    @Test(priority = 2, dependsOnMethods = {"inwardPoInvoiceProcessFlow"})
    public void inwardGrnProcessFlow() {
        InwardGrnProcessHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
    }
}


