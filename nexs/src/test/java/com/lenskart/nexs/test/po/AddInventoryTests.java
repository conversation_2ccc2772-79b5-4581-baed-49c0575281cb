package com.lenskart.nexs.test.po;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.ProductId;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.po.AddInventoryE2EHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

import static com.lenskart.commons.model.ProductId.SG_EYEGLASSES;

@TestCategory(TestCategory.Category.SANITY)
public class AddInventoryTests {
    NexsOrderContext  nexsOrderContext;

    /**
     * RUN THIS TEST ONLY FOR SINGAPORE FACILITY. SYNC IS CONFIGURED FOR SINGAPORE FACILITY ONLY.
     * OTHER FACILITIES WILL FAIL.
     */
    @Test
    public void addInventory() {
          nexsOrderContext = NexsOrderContext
                .builder()
                .headers(NexsOrderContext.Headers.builder().build())
                .productId(SG_EYEGLASSES.getProductId())
                .facilityCode(Constants.SINGAPORE_WAREHOUSE_FACILITY)
                .barcode("AAA"+String.valueOf(System.currentTimeMillis()))
                .build();

        AddInventoryE2EHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
    }
}
