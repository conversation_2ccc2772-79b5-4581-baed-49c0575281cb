package com.lenskart.nexs.test.picking;

import com.lenskart.nexs.helpers.picking.DOPickingHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

public class DOPickingTest {

    @Parameters({ "shippingId" })
    @Test
    public void doPicking(@Optional("SNXS2260000004455267") String shippingId) {


        NexsOrderContext  nexsOrderContext = NexsOrderContext
                .builder()
                .shippingId(shippingId)
                .headers(NexsOrderContext.Headers.builder().build())
                .build();

        DOPickingHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
    }
}
