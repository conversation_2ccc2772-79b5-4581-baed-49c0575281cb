package com.lenskart.nexs.test.cyclecount;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.nexs.helpers.cyclecount.CycleCountHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class CycleCount {

    @Test
    public void testCycleCountFlow() {
        NexsOrderContext context = NexsOrderContext.builder()
                .locationCode("NXS2-NXS1-ALL-DEFAULT-TEST-001-01")
                .itemType("ITEM")
                .barcode("zzi5x56Uf")
                .headers(NexsOrderContext.Headers.builder().build())
                .build();

        CycleCountHelper.builder()
                .nexsOrderContext(context)
                .build()
                .test();
    }
}
