<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="NexS Test Suite" verbose="1">
    <listeners>
        <listener class-name="com.lenskart.commons.listeners.ExtentReportListener"/>
        <listener class-name="com.lenskart.commons.listeners.TestCategoryListener"/>
    </listeners>

    <test name="NexS Tests" preserve-order="true">
        <classes>
            <class name="com.lenskart.nexs.test.autoReplenishment.AutoReplenishmentTest"/>
            <class name="com.lenskart.nexs.test.cid.GetInventoryInfoTest"/>
            <class name="com.lenskart.nexs.test.cyclecount.CycleCount"/>
            <class name="com.lenskart.nexs.test.ims.ImsJsonEventsTest"/>
            <class name="com.lenskart.nexs.test.ims.StockInAndOutV2Tests"/>
            <class name="com.lenskart.nexs.test.po.AddInventoryTests"/>
            <class name="com.lenskart.nexs.test.po.StartRetireTests"/>
            <class name="com.lenskart.nexs.test.wms.UpdateBoxCountTest"/>
            <class name="com.lenskart.nexs.test.NexsInwardFlowTest"/>
            <class name="com.lenskart.nexs.test.NexsPutawayFlowTest"/>
            <class name="com.lenskart.nexs.test.transfers.TransferEndToEndTest"/>
        </classes>
    </test>
</suite>