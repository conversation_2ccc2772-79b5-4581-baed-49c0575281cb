<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Do picking" verbose="1" parallel="tests" thread-count="2">
    <listeners>
        <listener class-name="com.lenskart.commons.listeners.ExtentReportListener" />
    </listeners>

    <parameter name="shippingId" value="SNXS2260000004450653" />
    <test name="Do picking">
        <classes>
            <class name="com.lenskart.nexs.test.picking.DOPickingTest" />
        </classes>
    </test>
</suite>