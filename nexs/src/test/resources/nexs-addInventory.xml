<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="IMS Stock Test Suite" verbose="1" parallel="tests" thread-count="2">
    <listeners>
        <listener class-name="com.lenskart.commons.listeners.ExtentReportListener" />
        <listener class-name="com.lenskart.commons.listeners.TestCategoryListener" />
        <listener class-name="com.lenskart.commons.listeners.AnnotationTransformerListener" />
    </listeners>

    <parameter name="pid" value="131932" />
    <parameter name="location" value="test-1-1" />
    <parameter name="facility" value="QNXS2" />
    <parameter name="legalOwner" value="LKIN" />

    <test name="IMS Stock In and Out V2">
        <classes>
            <class name="com.lenskart.nexs.test.ims.StockInAndOutV2Tests" />
        </classes>
    </test>

</suite>