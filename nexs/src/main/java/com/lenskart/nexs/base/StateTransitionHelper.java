package com.lenskart.nexs.base;

import com.lenskart.nexs.model.NexsOrderContext;

/**
 * Base interface for all state transition helpers.
 * Each state transition should have a corresponding helper that implements this interface.
 */
public interface StateTransitionHelper {
    
    /**
     * Execute the state transition logic
     * 
     * @throws RuntimeException if the transition fails
     */
    void executeTransition();
    
    /**
     * Get the order context associated with this helper
     * 
     * @return The order context
     */
    NexsOrderContext getOrderContext();

}
