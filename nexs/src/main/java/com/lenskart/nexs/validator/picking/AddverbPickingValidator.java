package com.lenskart.nexs.validator.picking;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.ProcessingType;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.time.Duration;
import java.util.List;
import java.util.Map;

import static com.lenskart.commons.model.NexsOrderState.IN_TRAY;
import static com.lenskart.nexs.database.PickingDbutils.getpickingDetailsStatus;

@Builder
@Slf4j
public class AddverbPickingValidator implements IValidator {
    NexsOrderContext nexsOrderContext;

    @Override
    public void validateNode() {
        // Add validation logic for AddverbPickingHelper
    }

    @Override
    public void validateDBEntities() {
        if (nexsOrderContext.getIsValidationRequired()) {
            boolean success = AwaitUtils.pollUntil(() ->
                            getpickingDetailsStatus(nexsOrderContext).getFirst().get("status").toString().equals(Constants.PICKED),
                    "Picking status to be picked", Duration.ofSeconds(6), Duration.ofSeconds(3), true);
            Assert.assertTrue(success, "Picking status is not picked");

            if(!ProcessingType.FR0.getCode().equals(nexsOrderContext.getProcessingType())){
                assert WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(IN_TRAY.name());

                nexsOrderContext.getBarcodes().forEach(barcode -> {
                    try {
                        ImsDbUtils.getBarcodeItemDetails(nexsOrderContext, barcode, Constants.GOOD, Constants.ALLOCATED, Constants.IN_TRAY);
                    } catch (Exception e) {
                        log.error("Failed to get barcode item details for barcode: {}", barcode);
                    }
                });
                List<Map<String, Object>> meiSyncDetails = WMSDbUtils.getMeiSyncDetails(nexsOrderContext);
                Assert.assertEquals(meiSyncDetails.getFirst().get("identifier"), "B");
                Assert.assertEquals(meiSyncDetails.getFirst().get("tray_id"), nexsOrderContext.getTrayBarcode());
            }

        }
    }
}
