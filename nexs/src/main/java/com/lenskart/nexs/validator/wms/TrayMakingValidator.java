package com.lenskart.nexs.validator.wms;

import com.lenskart.commons.base.IValidator;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;

@Builder
@Slf4j
public class TrayMakingValidator implements IValidator {
    NexsOrderContext nexsOrderContext;

    @Override
    public void validateNode() {
     // Assert the response from the API call
    }

    @Override
    public void validateDBEntities() {

        if(nexsOrderContext.getIsValidationRequired()){
            nexsOrderContext.getBarcodes().forEach(barcode -> {
                try {
                    ImsDbUtils.getBarcodeItemDetails(nexsOrderContext, barcode, Constants.GOOD, Constants.ALLOCATED, Constants.IN_TRAY);
                } catch (Exception e) {
                    log.error("Failed to get barcode item details for barcode: {}", barcode);
                }
            });
            List<Map<String, Object>> meiSyncDetails = WMSDbUtils.getMeiSyncDetails(nexsOrderContext);
            Assert.assertEquals(meiSyncDetails.getFirst().get("identifier"), "B");
            Assert.assertEquals(meiSyncDetails.getFirst().get("tray_id"), nexsOrderContext.getTrayBarcode());
        }

    }
}
