package com.lenskart.nexs.validator.wms;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.scm.constants.ScmConstants;
import com.lenskart.scm.database.ScmDbUtils;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;

@SuperBuilder
@Slf4j
public class OrderCancellationValidator implements IValidator {
    OrderContext orderContext;
    NexsOrderContext nexsOrderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        log.info("Validating order cancellation in NEXS");

        String orderId = String.valueOf(orderContext.getOrderId());
        String shipmentState = ScmDbUtils.getShipmentState(orderId);
        log.info("Shipment State - {}", shipmentState);

        if (shipmentState == null || shipmentState.isEmpty()) {
            log.warn("Shipment state is empty, skipping validation.");
            return;
        }

        List<String> shippingPackageIds = ScmDbUtils.getShippingPackageId(orderId).stream()
                .map(map -> String.valueOf(map.get("shipping_package_id")))
                .toList();

        log.info("Shipping package IDs: {}", shippingPackageIds);

        if (shippingPackageIds.isEmpty()) {
            log.warn("No shipping package IDs found, skipping validation.");
            return;
        }

        List<Map<String, Object>> statusList = WMSDbUtils.itemStatusByShippingId(shippingPackageIds);

        if (statusList.isEmpty()) {
            log.warn("No status records found in WMS for provided shipping package IDs.");
            return;
        }

        boolean anyCancelled = statusList.stream()
                .map(statusMap -> String.valueOf(statusMap.get("status")))
                .anyMatch(ScmConstants.CANCELLED::equals);

        try {
            Assert.assertTrue(anyCancelled, "At least one order item should be cancelled.");
        } catch (AssertionError e) {
            boolean virtualCancelTasksExist = !ScmDbUtils.getVirtualShipmentCancelTasks(orderId).isEmpty();
            Assert.assertTrue(
                    virtualCancelTasksExist,
                    "Fallback failed: no virtual shipment cancel tasks found for order after cancellation failure."
            );
        }
    }
}
