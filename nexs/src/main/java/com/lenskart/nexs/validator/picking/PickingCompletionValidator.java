package com.lenskart.nexs.validator.picking;

import com.lenskart.commons.base.IValidator;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import static com.lenskart.nexs.database.PickingDbutils.getpickingDetailsStatus;

@Builder
@Slf4j
public class PickingCompletionValidator implements IValidator {
    NexsOrderContext nexsOrderContext;

    @Override
    public void validateNode() {
        // Add validation logic for PickingCompletionHelper
    }

    @Override
    public void validateDBEntities() {
        if(nexsOrderContext.getIsValidationRequired()){
            String pickingStatus = WMSDbUtils.getStatusOfShipment(nexsOrderContext);
            Assert.assertEquals(pickingStatus, "PICKED");

            nexsOrderContext.getBarcodes().forEach(barcode -> {
                try {
                    ImsDbUtils.getBarcodeItemDetails(nexsOrderContext, barcode, Constants.GOOD, Constants.ALLOCATED, Constants.PICKED);
                } catch (Exception e) {
                    log.error("Failed to get barcode item details for barcode: {}", barcode);
                }
            });

             getpickingDetailsStatus(nexsOrderContext).forEach(item -> {
                Assert.assertEquals(item.get("status").toString(), Constants.PICKED);
            });
        }
    }
}
