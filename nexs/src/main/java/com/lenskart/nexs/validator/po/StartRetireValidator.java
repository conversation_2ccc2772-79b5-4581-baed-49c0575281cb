package com.lenskart.nexs.validator.po;

import com.lenskart.commons.base.IValidator;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.database.NexsDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;

@Builder
@Slf4j
public class StartRetireValidator implements IValidator {
    NexsOrderContext nexsOrderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        log.info("Validating Start Retire in DB");
        List<Map<String, Object>> barcodeDetails = NexsDbUtils.getStartRetireBarcodeDetails(nexsOrderContext);
        Assert.assertEquals(barcodeDetails.getFirst().get("new_pid").toString(), nexsOrderContext.getNewProductId());
        Assert.assertEquals(barcodeDetails.getFirst().get("old_pid").toString(), nexsOrderContext.getProductId());
        Assert.assertEquals(barcodeDetails.getFirst().get("barcode").toString(), nexsOrderContext.getBarcode());
        Assert.assertEquals(barcodeDetails.getFirst().get("status").toString(), Constants.DONE);

        List<Map<String, Object>> imsBarcodeDetails = ImsDbUtils.getBarcodeDetails(nexsOrderContext);
        Assert.assertEquals(imsBarcodeDetails.getFirst().get("barcode").toString(), nexsOrderContext.getBarcode());
        Assert.assertEquals(imsBarcodeDetails.getFirst().get("pid").toString(), nexsOrderContext.getNewProductId());
        Assert.assertEquals(imsBarcodeDetails.getFirst().get("condition").toString(), Constants.GOOD);
        Assert.assertEquals(imsBarcodeDetails.getFirst().get("availability").toString(), Constants.AVAILABLE);
        Assert.assertEquals(imsBarcodeDetails.getFirst().get("status").toString(), Constants.AVAILABLE);
        Assert.assertEquals(imsBarcodeDetails.getFirst().get("facility").toString(), nexsOrderContext.getFacilityCode());
    }
}
