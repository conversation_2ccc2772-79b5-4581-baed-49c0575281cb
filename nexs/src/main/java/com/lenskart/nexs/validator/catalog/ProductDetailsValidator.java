package com.lenskart.nexs.validator.catalog;

import com.lenskart.commons.base.IValidator;
import com.lenskart.nexs.helpers.catalog.GetProductAdditionalDetailsHelper;
import com.lenskart.nexs.helpers.catalog.GetProductDetailsHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.Builder;
import org.testng.Assert;

import static com.lenskart.commons.utils.RestUtils.getValueFromResponse;

@Builder
public class ProductDetailsValidator implements IValidator {
    NexsOrderContext nexsOrderContext;
    GetProductDetailsHelper productDetailsHelper;

    @Override
    public void validateNode() {
        if(nexsOrderContext.getIsValidationRequired()){
            Assert.assertEquals(productDetailsHelper.getResponse().getStatusCode(), 200);
            validateDBEntities();
        }
    }

    @Override
    public void validateDBEntities() {
        if(nexsOrderContext.getIsValidationRequired()){
            Assert.assertEquals(getValueFromResponse(productDetailsHelper.getResponse(), "meta.Product Details found").toString(), "Product Details found");
        }
    }
}
