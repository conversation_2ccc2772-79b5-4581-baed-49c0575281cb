package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.GrnRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.GET_BARCODE_PRICE;

@SuperBuilder
public class GetBarcodePriceHelper extends NexsBaseHelper implements ServiceHelper {

    private NexsOrderContext nexsOrderContext;
    private Map<String, Integer> barcodePidMap;
    private String facilityCode;

    private Response response;
    private JSONObject payload;

    @Override
    public ServiceHelper init() {
        payload = GrnRequestBuilder.buildBarcodePriceRequest(barcodePidMap, facilityCode);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(GET_BARCODE_PRICE.getUrl(), null, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}