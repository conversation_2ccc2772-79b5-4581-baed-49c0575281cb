package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.PickingRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.testng.Assert;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_CREATE_PICKING_SUMMARY_DO;

@SuperBuilder
@Slf4j
public class CreatePickingSummaryDOHelper extends NexsBaseHelper implements ServiceHelper {
    String payload;
    NexsOrderContext  nexsOrderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        nexsOrderContext.setFacilityCode(WMSDbUtils.getFacilityCode(nexsOrderContext));
        headers = getHeaders(nexsOrderContext);
        payload = PickingRequestBuilder.createPickingSummaryPayload(nexsOrderContext.getShippingId());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PICKING_CREATE_PICKING_SUMMARY_DO.getUrl(), headers, payload, 200);
        String summaryId = RestUtils.getValueFromResponse(response,"data.id").toString();
        nexsOrderContext.setPickingSummaryId(summaryId);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        Assert.assertTrue(StringUtils.isNotEmpty(nexsOrderContext.getPickingSummaryId()));
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
