package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.GrnRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.SCAN_GRN_BARCODE;

@Slf4j
@SuperBuilder
public class GrnItemBarcodeScanHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    NexsInwardContext nexsInwardContext;
    @Getter
    String itemBarocde;
    JSONObject payload;
    @Getter
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        itemBarocde = "SKY" + GenericUtils.genrateRandomNumericString();
         payload = GrnRequestBuilder.getScanGrnItemBarcodePayload(nexsInwardContext,itemBarocde);
        return this;
    }

    @Override
    public ServiceHelper process() {
        try {
            itemBarocde = "SKY" + GenericUtils.genrateRandomNumericString();
            response = RestUtils.post(SCAN_GRN_BARCODE.getUrl(), headers, payload.toString(), 200);

        }
        catch (Exception e)
        {
            log.info("Failed to scan item barcode: {}, retrying with new barcode",itemBarocde);
            itemBarocde = "SKY" + GenericUtils.genrateRandomNumericString();
            response = RestUtils.post(SCAN_GRN_BARCODE.getUrl(), headers, payload.toString(), 200);
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
