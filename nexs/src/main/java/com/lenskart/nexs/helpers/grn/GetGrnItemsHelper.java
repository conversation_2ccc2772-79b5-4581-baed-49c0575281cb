package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import static com.lenskart.nexs.endpoints.NexsEndpoints.GET_GRN_ITEMS;

@SuperBuilder
public class GetGrnItemsHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    @Getter
    Response response;
    NexsInwardContext nexsInwardContext;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        queryParams = getQueryParamsForGetGrnItemsPayload(nexsInwardContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_GRN_ITEMS.getUrl(), headers, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
