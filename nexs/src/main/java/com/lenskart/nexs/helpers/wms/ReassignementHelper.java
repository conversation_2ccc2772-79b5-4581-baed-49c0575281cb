package com.lenskart.nexs.helpers.wms;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.endpoints.NexsEndpoints;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.WmsRequestBuilder;
import com.lenskart.nexs.validator.wms.ReassignmentValidator;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

@SuperBuilder
@Slf4j
public class ReassignementHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    @Getter
    Response response;
    String selectedFacility;
    JSONObject payload;
    String reassignedFacility;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        if(nexsOrderContext.getFacilityCode().equalsIgnoreCase(Constants.BHIWADI_WAREHOUSE_FACILITY)){
            selectedFacility = Constants.MANESAR_WAREHOUSE_FACILITY;
        }
        else if (nexsOrderContext.getFacilityCode().equalsIgnoreCase(Constants.MANESAR_WAREHOUSE_FACILITY)) {
            selectedFacility = Constants.BHIWADI_WAREHOUSE_FACILITY;
        }
        else {
            selectedFacility = nexsOrderContext.getFacilityCode();
        }
        payload = WmsRequestBuilder.createReassignmentPayload(nexsOrderContext.getShippingId(),selectedFacility);

        return this;

    }

    @Override
    public ServiceHelper process() {
         response = RestUtils.post(NexsEndpoints.WMS_FACILITY_REASSIGNMENT.getUrl(), null, payload.toString(), 200);
        log.info("Reassignment completed for shipping ID: {}", nexsOrderContext.getShippingId());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        log.info("order Reassignment validation for the shipping id {}", nexsOrderContext.getShippingId());
        ReassignmentValidator validator = ReassignmentValidator.builder().nexsOrderContext(nexsOrderContext).reassignementHelper(this).build();
        validator.validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
