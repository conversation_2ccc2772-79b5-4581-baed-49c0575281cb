package com.lenskart.nexs.helpers.consolidation;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CONSOLIDATION_ALLOCATE_ITEM;

@SuperBuilder
@Slf4j
public class AllocateItemHelper extends NexsBaseHelper implements ServiceHelper {
    
    NexsOrderContext nexsOrderContext;
    @Getter
    Response response;
    JSONObject payload;
    String boxCode;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        List<Map<String, Object>> storeConsolidationInfo = WMSDbUtils.getStoreConsolidationInfo(nexsOrderContext);
        
        if (!storeConsolidationInfo.getFirst().get("box_status").equals("ASSIGNED")) {
            assert storeConsolidationInfo.getFirst().get("box_status").equals("UNASSIGNED");
            
            // Get box code from previous scan response or generate one
            if (nexsOrderContext.getBoxCode() == null || nexsOrderContext.getBoxCode().isEmpty()) {
                boxCode = nexsOrderContext.getFacilityCode() + "-" + GenericUtils.genrateRandomNumericString(5);
                nexsOrderContext.setBoxCode(boxCode);
            }
            
            log.info("Allocating item to box code: {}", nexsOrderContext.getBoxCode());
            payload = NexsRequestBuilder.getAllocateItemPayloadForConsolidation(nexsOrderContext);
            response = RestUtils.put(CONSOLIDATION_ALLOCATE_ITEM.getUrl(), headers, payload.toString(), 200);
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        List<Map<String, Object>> storeConsolidationInfo = WMSDbUtils.getStoreConsolidationInfo(nexsOrderContext);
        assert storeConsolidationInfo.getFirst().get("box_status").equals("ASSIGNED");
        assert storeConsolidationInfo.getFirst().get("box_barcode").toString().equals(nexsOrderContext.getBoxCode());
        log.info("Item successfully allocated to box: {}", nexsOrderContext.getBoxCode());
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}