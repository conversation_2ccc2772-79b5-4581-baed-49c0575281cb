package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.fitting.GetFittingDetailResponse;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.validator.fitting.FittingValidator;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import static com.lenskart.commons.model.NexsOrderState.PENDING_CUSTOMIZATION;
import static com.lenskart.nexs.endpoints.NexsEndpoints.FITTING_GET_FITTING_DETAIL;
import static com.lenskart.nexs.endpoints.NexsEndpoints.FITTING_MARK_FITTING_COMPLETE;

@SuperBuilder
@Slf4j
public class FittingHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {
    @Getter
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        logOperationStart("Fitting initiation");
        headers = getHeaders(nexsOrderContext);
        payload = NexsRequestBuilder.getCompleteFittingPayload(nexsOrderContext);
        queryParams = getQueryParamsForFittingDone(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        String fittingId = nexsOrderContext.getFittingId();
        log.info("fittingId - {}", fittingId);

        if (!nexsOrderContext.getIsFrameQcFailed() && WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getLeftLensBarcode()).equals(PENDING_CUSTOMIZATION.name())) {
            response = RestUtils.post(FITTING_GET_FITTING_DETAIL.getUrl(Map.of("fittingId", fittingId)),
                    headers, null, 200);
            GetFittingDetailResponse getFittingDetialResponse = response.as(GetFittingDetailResponse.class);
           try{
               response = RestUtils.post(FITTING_MARK_FITTING_COMPLETE.getUrl(Map.of("fittingId", nexsOrderContext.getFittingId(),
                       "shippingPackageId", nexsOrderContext.getShippingId())), headers, payload.toString(), 200);

           } catch (Exception e) {
               // Make a call without expecting 200 status to get the actual error response
               Response errorResponse = RestUtils.post(FITTING_MARK_FITTING_COMPLETE.getUrl(Map.of("fittingId", fittingId,
                       "shippingPackageId", nexsOrderContext.getShippingId())), headers, queryParams, payload.toString());

               String errorMessage = null;
               try {
                   errorMessage = errorResponse.jsonPath().getString("meta.displayMessage");
               } catch (Exception jsonException) {
                   log.warn("Failed to extract error message from response: {}", jsonException.getMessage());
                   errorMessage = errorResponse.body().asString();
               }
               log.info("Fitting Error message: {}", errorMessage);
           }
        }
        else if (nexsOrderContext.getIsFrameQcFailed() && WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getFrameBarcode()).equals(PENDING_CUSTOMIZATION.name())) {
            logOperationStart("Fitting after qc fail");
            response = RestUtils.post(FITTING_GET_FITTING_DETAIL.getUrl(Map.of("fittingId", fittingId)),
                    headers, null, 200);
            GetFittingDetailResponse getFittingDetialResponse = response.as(GetFittingDetailResponse.class);
            response = RestUtils.post(FITTING_MARK_FITTING_COMPLETE.getUrl(Map.of("fittingId", fittingId,
                    "shippingPackageId", nexsOrderContext.getShippingId())), headers,queryParams, payload.toString(), 200);


        }
            else {
            log.info("Fitting is already completed");
        }
        logOperationComplete("Fitting Completion");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        FittingValidator validator = FittingValidator.builder().nexsOrderContext(nexsOrderContext).fittingHelper(this).build();
        validator.validateNode();
        validator.validateDBEntities();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.PENDING_CUSTOMIZATION;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.CUSTOMIZATION_COMPLETE;
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}
