package com.lenskart.nexs.helpers.jit;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.JitRequestBuilder;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.JIT_RXUSTATUS;

@SuperBuilder
public class RxStatusHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    Response response;
    private JSONObject payload;


    @Override
    public ServiceHelper init() {
        payload = JitRequestBuilder.rxuStatusRequestBuilder(nexsOrderContext, nexsOrderContext.getFittingId(), nexsOrderContext.getStationId());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(JIT_RXUSTATUS.getUrl(), null, payload.toString());
        AwaitUtils.sleepSeconds(60);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {

        init();
        process();
        validate();
        return this;
    }
}
