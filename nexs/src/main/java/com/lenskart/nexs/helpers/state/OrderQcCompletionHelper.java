package com.lenskart.nexs.helpers.state;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.consolidation.ConsolidationHelpers;
import com.lenskart.nexs.helpers.packing.PackingCompletionHelper;
import com.lenskart.nexs.helpers.packing.PackingDetailsHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.orderqc.CompleteQcRequest;
import com.lenskart.nexs.database.WMSDbUtils;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import static com.lenskart.commons.model.NexsOrderState.INVOICED;
import static com.lenskart.commons.model.NexsOrderState.IN_QC;
import static com.lenskart.nexs.endpoints.NexsEndpoints.ORDERQC_COMPLETE_QC;
import static com.lenskart.nexs.endpoints.NexsEndpoints.WMS_GET_COMMENT;


/**
 * Helper class to transition order from IN_QC to QC_DONE state.
 * Handles the business logic for completing quality control process.
 */
@SuperBuilder
@Slf4j
public class OrderQcCompletionHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    private Response response;
    private JSONObject payload;
    private List<CompleteQcRequest> list;
    private List<String> uwItemIds;

    @Override
    public ServiceHelper init() {
        logOperationStart("QC completion Started");
        headers = getHeaders(nexsOrderContext);
        statusCode = nexsOrderContext.getStatusCode();
        list = new ArrayList<>();
        uwItemIds = new ArrayList<>();
        if(WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(IN_QC.name())) {
            nexsOrderContext.getFetchOrderScannedEntityResponse().getOrderQcStatus().getOrderQcStatusList().forEach(items -> {
                log.info("Order QC status: {}", items);
                nexsOrderContext.setBarcode(items.getOrder().getBarcode());
                String barcode = nexsOrderContext.getBarcode();
                nexsOrderContext.setUwItemId(String.valueOf(items.getOrder().getUwItemId()));
                uwItemIds.add(String.valueOf(items.getOrder().getUwItemId()));
                String uwItemId = nexsOrderContext.getUwItemId();
                nexsOrderContext.setTrayId(items.getOrder().getTrayId());
                String trayId = nexsOrderContext.getTrayId();
                CompleteQcRequest request = new CompleteQcRequest();
                request.setBarcode(barcode);
                request.setDamaged(false);
                request.setIncrementId(Integer.parseInt(nexsOrderContext.getIncrementId()));
                request.setPrimaryReason("UNCLASSIFIED");
                request.setShippingPackageId(nexsOrderContext.getShippingId());
                request.setStatus("QCPass");
                request.setIsScanningDone(true);
                request.setFittingId("0");
                request.setOrderItemId(Integer.parseInt(uwItemId));
                request.setTrayId(trayId);
                list.add(request);
            });
            String orderItemHeaderId = String.valueOf(nexsOrderContext.getFetchOrderScannedEntityResponse().getInfoForQCPanel().getOrderResponse().getOrderItemHeaderId());
            nexsOrderContext.setOrderItemHeaderId(orderItemHeaderId);
            String nexsOrderId = String.valueOf(nexsOrderContext.getFetchOrderScannedEntityResponse().getInfoForQCPanel().getOrderResponse().getNexsOrderId());
            nexsOrderContext.setNexsOrderId(nexsOrderId);
            log.info("nexsOrderId {}", nexsOrderId);
            nexsOrderContext.setUwItemIds(uwItemIds);
        }
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {
        if(WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(IN_QC.name())) {
            queryParams = getQueryParamsGetComment(nexsOrderContext.getOrderItemHeaderId(), nexsOrderContext.getNexsOrderId());
            response = RestUtils.get(WMS_GET_COMMENT.getUrl(), headers, queryParams);
            try {
                assert response.getStatusCode() == 200;
            } catch (Exception e) {
                response = RestUtils.get(WMS_GET_COMMENT.getUrl(), headers, queryParams, 201);
            }
            queryParams = getQueryParamsForCompleteQC();
                response = RestUtils.post(ORDERQC_COMPLETE_QC.getUrl(), headers, queryParams, new ObjectMapper().writeValueAsString(list));
                String qcCompleteMessage = RestUtils.getValueFromResponse(response, "meta.displayMessage").toString();
           if(Constants.PO_CREATION_ERROR_MESSAGE.equalsIgnoreCase(qcCompleteMessage)) {
               AwaitUtils.sleepSeconds(15);
               response = RestUtils.post(ORDERQC_COMPLETE_QC.getUrl(), headers, queryParams, new ObjectMapper().writeValueAsString(list), 200);
           }

        }else {
            log.info("QC is already done for this shipment");
        }
        logOperationComplete("Quality control completion");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        logOperationStart("QC completion validation");
        if(nexsOrderContext.getIsConsolidationAndPackingRequired()) {
            /* Store Consolidation and Store Packing */
            AwaitUtils.sleepSeconds(5);
            if (nexsOrderContext.getIsShipToStore() && !nexsOrderContext.getNavChannel().contains(Constants.BULK) &&
                    WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(INVOICED.name())) {
                ConsolidationHelpers.builder().nexsOrderContext(nexsOrderContext).build().test();
            } else {
                // this is added to complete packing
                PackingDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                // this is added to complete packing
                PackingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            }
        }

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return IN_QC;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.QC_DONE;
    }


    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}
