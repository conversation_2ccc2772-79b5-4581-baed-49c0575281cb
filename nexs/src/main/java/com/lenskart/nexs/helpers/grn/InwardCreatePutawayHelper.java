package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.stream.IntStream;

@Getter
@Setter
@SuperBuilder
public class InwardCreatePutawayHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsInwardContext nexsInwardContext;
    NexsOrderContext nexsOrderContext;
    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        GetGrnItemsHelper getGrnItemsHelper = GetGrnItemsHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        getGrnItemsHelper.test();
        String obj= JsonUtils.convertObjectToJsonString(RestUtils.getValueFromResponse(getGrnItemsHelper.response, "data"));
        JSONObject req=new JSONObject(obj);
        CreateGrnHelper createGrn = CreateGrnHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        createGrn.setPayload(req);
        createGrn.test();
        nexsInwardContext.setGrnNumber(RestUtils.getValueFromResponse(createGrn.getResponse(),"result.data.grn_code").toString());
        GetGrnDetailsHelper getGrnDetailsHelper = GetGrnDetailsHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        getGrnDetailsHelper.test();
        nexsInwardContext.setGrnType(RestUtils.getValueFromResponse(getGrnDetailsHelper.getResponse(),"result.data.type").toString());
        nexsInwardContext.setInvoiceLevel(RestUtils.getValueFromResponse(getGrnDetailsHelper.getResponse(),"result.data.invoice_level").toString());
        nexsInwardContext.setLegalOwner(RestUtils.getValueFromResponse(getGrnDetailsHelper.getResponse(),"result.data.legal_owner").toString());
        String obj1 = JsonUtils.convertObjectToJsonString(RestUtils.getValueFromResponse(getGrnDetailsHelper.response, "result.meta"));
        JSONObject meta=new JSONObject(obj1);
        obj1 = JsonUtils.convertObjectToJsonString(RestUtils.getValueFromResponse(getGrnDetailsHelper.response, "result.meta.invoice.items[0]"));
        JSONObject productData=new JSONObject(obj1);
        nexsInwardContext.setProductData(productData);
        AddGrnPidHelper addGrnPidHelper = AddGrnPidHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        addGrnPidHelper.setMeta(meta);
        addGrnPidHelper.test();
        CreateBoxMappingHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();

        ArrayList<String> barcodes = new ArrayList<>();
        IntStream.range(0, Integer.valueOf(nexsInwardContext.getQuantity())).forEach(i -> {
            Response scanItemBarcode = null;
            System.out.println(i);
            try {
                GrnItemBarcodeScanHelper grnItemBarcodeScanHelper = GrnItemBarcodeScanHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
                grnItemBarcodeScanHelper.test();
                scanItemBarcode=grnItemBarcodeScanHelper.getResponse();
                barcodes.add(grnItemBarcodeScanHelper.getItemBarocde());
                AwaitUtils.sleepSeconds(15);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        AwaitUtils.sleepSeconds(15);
        nexsInwardContext.setInwardBarcodes(barcodes);
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
