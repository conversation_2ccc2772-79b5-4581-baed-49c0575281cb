package com.lenskart.nexs.helpers.putaway;

import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.PutawayRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PUTAWAY_CONTINUOUS_SCAN;

@SuperBuilder
public class ContinousScanHelper extends NexsBaseHelper implements ServiceHelper {
    String payload;
    NexsOrderContext nexsOrderContext;
    Response response;
    String barcode;

    @Override
    public ServiceHelper init() {
        payload = PutawayRequestBuilder.continousScanPayload(nexsOrderContext,barcode);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PUTAWAY_CONTINUOUS_SCAN.getUrl(), null, payload, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
