package com.lenskart.nexs.helpers.catalog;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.CatalogRequestBuilder;
import com.lenskart.nexs.validator.catalog.ProductDetailsValidator;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CATALOG_PRODUCT_DETAILS;

@SuperBuilder
@Slf4j
public class GetProductDetailsHelper extends NexsBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    NexsOrderContext nexsOrderContext;
    String stateCode;
    String supplierId;
    JSONObject payload;
    NexsInwardContext nexsInwardContext;

    @Override
    public ServiceHelper init() {
        payload = CatalogRequestBuilder.getProductDetailsPayload(Integer.parseInt(nexsInwardContext.getProductId()), nexsInwardContext.getStateCode(), nexsInwardContext.getVendorCode());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(CATALOG_PRODUCT_DETAILS.getUrl(), null, payload.toString());
        JSONObject jsonObject = new JSONObject(response.getBody().asString());
        JSONObject hsnTaxRates = jsonObject.getJSONObject("data").getJSONObject(nexsInwardContext.getProductId()).getJSONObject("hsn_tax_rates");
        nexsInwardContext.setHsnClassification(RestUtils.getValueFromResponse(response, "data." + nexsInwardContext.getProductId() + ".hsn_classification").toString());
        nexsInwardContext.setTaxInfo(hsnTaxRates);
        nexsInwardContext.setVendorUnitCostPrice(response.jsonPath().getDouble("data." + nexsInwardContext.getProductId() + ".unit_price"));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        log.info("Product Details validation for the ProductID {}", nexsInwardContext.getProductId());
        ProductDetailsValidator validator = ProductDetailsValidator.builder().nexsOrderContext(nexsOrderContext)
                .productDetailsHelper(this).build();
        validator.validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
