package com.lenskart.nexs.helpers.wms;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.WmsRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.WMS_ITEM_FULLFILLABILITY;

@SuperBuilder
@Slf4j
public class FullfillabilityHelper extends NexsBaseHelper implements ServiceHelper {
    JSONObject payload;
    NexsOrderContext nexsOrderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = WmsRequestBuilder.getFullfillabilityPayload(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(WMS_ITEM_FULLFILLABILITY.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
