package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import java.util.Map;
import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_LENS_PICKING_DETAILS;

@SuperBuilder
public class PickingDetailsHelper extends NexsBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }
    @Override
    public ServiceHelper process() {
        response = RestUtils.get(PICKING_LENS_PICKING_DETAILS.getUrl(Map.of("fittingId",nexsOrderContext.getFittingId())),headers, null,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }
}
