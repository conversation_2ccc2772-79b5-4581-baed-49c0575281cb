package com.lenskart.nexs.helpers.catalog;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.CatalogRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CATALOG_PRODUCT_PRICE;

@SuperBuilder
public class ProductPriceHelper extends NexsBaseHelper implements ServiceHelper {

    NexsOrderContext nexsOrderContext;
    NexsInwardContext nexsInwardContext;
    @Getter
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = CatalogRequestBuilder.getProductPricePayload(nexsInwardContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(CATALOG_PRODUCT_PRICE.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
