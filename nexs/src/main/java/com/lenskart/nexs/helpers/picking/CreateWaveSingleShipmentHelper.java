package com.lenskart.nexs.helpers.picking;


import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;


import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_ADDVERB_CREATE_WAVE_SINGLESHIPMENT;


@SuperBuilder
public class CreateWaveSingleShipmentHelper extends NexsBaseHelper implements ServiceHelper {


    Response response;
    NexsOrderContext nexsOrderContext;


    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        nexsOrderContext.setPickingType(Constants.FR0_WAVE);
        queryParams = getQueryParamsForCreateWaveSingleShipment(nexsOrderContext);
        return this;
    }


    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PICKING_ADDVERB_CREATE_WAVE_SINGLESHIPMENT.getUrl(),null,queryParams,null,200);
        return this;
    }


    @Override
    public ServiceHelper validate() {


        return this;
    }
    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }


}
