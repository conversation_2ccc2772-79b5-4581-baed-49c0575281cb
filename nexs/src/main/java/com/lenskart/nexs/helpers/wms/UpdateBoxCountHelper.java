package com.lenskart.nexs.helpers.wms;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.requestBuilder.WmsRequestBuilder;
import io.restassured.response.Response;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;

import static com.lenskart.nexs.endpoints.NexsEndpoints.WMS_UPDATE_BOX_COUNT;

@Slf4j
@SuperBuilder
public class UpdateBoxCountHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    JSONArray payload;
    private Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        nexsOrderContext.setBoxCount("2");
        payload = WmsRequestBuilder.UpdateBoxCountPayload(nexsOrderContext.getShippingId(), nexsOrderContext.getBoxCount());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(WMS_UPDATE_BOX_COUNT.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }


}
