package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.PORequestBuilder;
import com.lenskart.nexs.validator.po.AddInventoryValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PO_ADD_INVENTORY;

@SuperBuilder
public class AddInventoryHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    Response response;
    String payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = PORequestBuilder.addInventoryPayload(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response= RestUtils.post(PO_ADD_INVENTORY.getUrl(), headers, payload, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        AddInventoryValidator validator = AddInventoryValidator.builder().nexsOrderContext(nexsOrderContext).build();
        validator.validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
