package com.lenskart.nexs.helpers.consolidation;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CONSOLIDATION_SCAN_ITEM_IN_PACKING;
import static com.lenskart.nexs.endpoints.NexsEndpoints.CONSOLIDATION_SCAN_ITEM_V2;

@SuperBuilder
@Slf4j
public class ScanItemInPackingHelper extends NexsBaseHelper implements ServiceHelper {
    
    NexsOrderContext nexsOrderContext;
    @Getter
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = NexsRequestBuilder.getScanItemPayloadForConsolidation(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (!nexsOrderContext.getFacilityCode().equals(Constants.BHIWADI_WAREHOUSE_FACILITY) &&
                !nexsOrderContext.getFacilityCode().equals(Constants.UAE_WAREHOUSE_FACILITY)) {
            // For other facilities, call V2 endpoint first
            response = RestUtils.post(CONSOLIDATION_SCAN_ITEM_V2.getUrl(), headers, payload.toString(), 200);

            // Then call the packing endpoint
        }
        response = RestUtils.post(CONSOLIDATION_SCAN_ITEM_IN_PACKING.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        String storeCode = RestUtils.getValueFromResponse(response, "data.packingHeaderItem.storeCode").toString();
        nexsOrderContext.setStoreCode(storeCode);
        log.info("Item scanned in packing for store code: {}", storeCode);
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}