package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.catalog.GetProductDetailsHelper;
import com.lenskart.nexs.helpers.catalog.ProductPriceHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.po.*;
import com.lenskart.nexs.helpers.vendor.VendorSearchHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.stream.IntStream;


@SuperBuilder
@Slf4j
public class GrnE2EHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsOrderContext nexsOrderContext;
    NexsInwardContext nexsInwardContext;
    NexsOrderContext.Headers nexsOrderContextHeader;
    Response response;

    @Override
    public ExecutionHelper init() {
        nexsInwardContext = NexsInwardContext.builder().build();
        nexsOrderContext = NexsOrderContext.builder().facilityCode(nexsInwardContext.getFacilityCode()).headers(NexsOrderContext.Headers.builder().build()).build();
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        log.info("GRN E2E Flow");
        VendorSearchHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
        GetProductDetailsHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
        ProductPriceHelper productPriceHelper = ProductPriceHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        productPriceHelper.test();
        response = productPriceHelper.getResponse();
        nexsInwardContext.setCgstRate(response.jsonPath().getDouble("data.hsn_tax_info.cgst_rate"));
        nexsInwardContext.setIgstRate(response.jsonPath().getDouble("data.hsn_tax_info.igst_rate"));
        nexsInwardContext.setSgstRate(response.jsonPath().getDouble("data.hsn_tax_info.sgst_rate"));
        nexsInwardContext.setUgstRate(response.jsonPath().getDouble("data.hsn_tax_info.ugst_rate"));
        nexsInwardContext.setPriceWithTaxes(response.jsonPath().getDouble("data.price_with_taxes"));

        if (nexsInwardContext.isUiFlowEnabled()) {
            GeneratePoNumHelper.builder().nexsOrderContext(nexsOrderContext).nexsInwardContext(nexsInwardContext).build().test();
            SubmitPoHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
            try {
                SubmitForApprovalHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
            } catch (AssertionError e) {
                AwaitUtils.sleepSeconds(30);
                SubmitForApprovalHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
            }
            try {
                PoApproveRejectHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
            } catch (AssertionError e) {
                AwaitUtils.sleepSeconds(30);
                PoApproveRejectHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
            }
        } else {
            GeneratePoDetailsHelper generatePoDetailsHelper = GeneratePoDetailsHelper.builder().nexsInwardContext(nexsInwardContext).build();
            generatePoDetailsHelper.test();
            nexsInwardContext.setPoNumber(RestUtils.getValueFromResponse(generatePoDetailsHelper.getResponse(), "data").toString());
        }
        nexsInwardContext.setVendorInvNo("INV" + nexsInwardContext.getPoNumber());
        GetPoInvoiceItemHelper getPoInvoiceItemHelper = GetPoInvoiceItemHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        getPoInvoiceItemHelper.test();
        nexsInwardContext.setVendorUnitCostPriceWithTax(getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].vendor_unit_cost_price"));
        nexsInwardContext.setCgstPer(getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].cgst_per"));
        nexsInwardContext.setIgstPer(getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].igst_per"));
        nexsInwardContext.setSgstPer(getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].sgst_per"));
        nexsInwardContext.setUgstPer(getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].ugst_per"));
        nexsInwardContext.setFlatPer(getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].flat_per"));
        SavePoInvoiceHelper savePoInvoiceHelper = SavePoInvoiceHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        savePoInvoiceHelper.test();
        AwaitUtils.sleepSeconds(30);
        GetInvoiceProcessingStatusHelper getInvoiceProcessingStatusHelper = GetInvoiceProcessingStatusHelper.builder().nexsOrderContext(nexsOrderContext).build();
        getInvoiceProcessingStatusHelper.setRequest(RestUtils.getValueFromResponse(savePoInvoiceHelper.getResponse(), "data").toString());
        getInvoiceProcessingStatusHelper.test();
        nexsInwardContext.setInvoiceRefNum(getInvoiceProcessingStatusHelper.getResponse().jsonPath().getString("data.infoMsg[0]").substring(22, 27));

        GetGrnItemsHelper getGrnItemsHelper = GetGrnItemsHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        getGrnItemsHelper.test();
        String obj = JsonUtils.convertObjectToJsonString(RestUtils.getValueFromResponse(getGrnItemsHelper.response, "data"));
        JSONObject req = new JSONObject(obj);
        CreateGrnHelper createGrn = CreateGrnHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        createGrn.setPayload(req);
        createGrn.test();
        nexsInwardContext.setGrnNumber(RestUtils.getValueFromResponse(createGrn.getResponse(), "result.data.grn_code").toString());
        log.info("GRN Number: {}", nexsInwardContext.getGrnNumber());
        GetGrnDetailsHelper getGrnDetailsHelper = GetGrnDetailsHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        getGrnDetailsHelper.test();
        nexsInwardContext.setGrnType(RestUtils.getValueFromResponse(getGrnDetailsHelper.getResponse(), "result.data.type").toString());
        nexsInwardContext.setInvoiceLevel(RestUtils.getValueFromResponse(getGrnDetailsHelper.getResponse(), "result.data.invoice_level").toString());
        nexsInwardContext.setLegalOwner(RestUtils.getValueFromResponse(getGrnDetailsHelper.getResponse(), "result.data.legal_owner").toString());
        String obj1 = JsonUtils.convertObjectToJsonString(RestUtils.getValueFromResponse(getGrnDetailsHelper.response, "result.meta"));
        log.info("Meta: {}", obj1);
        JSONObject meta = new JSONObject(obj1);
        obj1 = JsonUtils.convertObjectToJsonString(RestUtils.getValueFromResponse(getGrnDetailsHelper.response, "result.meta.invoice.items[0]"));
        log.info(" Product Data: {}", obj1);
        JSONObject productData = new JSONObject(obj1);
        nexsInwardContext.setProductData(productData);
        AddGrnPidHelper addGrnPidHelper = AddGrnPidHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        addGrnPidHelper.setMeta(meta);
        addGrnPidHelper.test();
        CreateBoxMappingHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();

        ArrayList<String> barcodes = new ArrayList<>();
        IntStream.range(0, Integer.valueOf(nexsInwardContext.getQuantity())).forEach(i -> {
            Response scanItemBarcode = null;
            System.out.println(i);
            try {
                GrnItemBarcodeScanHelper grnItemBarcodeScanHelper = GrnItemBarcodeScanHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
                grnItemBarcodeScanHelper.test();
                scanItemBarcode = grnItemBarcodeScanHelper.getResponse();
                barcodes.add(grnItemBarcodeScanHelper.getItemBarocde());
                AwaitUtils.sleepSeconds(30);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        AwaitUtils.sleepSeconds(30);
        try {
            CloseGrnHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
        } catch (AssertionError e) {
            CloseGrnHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
        }


        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
