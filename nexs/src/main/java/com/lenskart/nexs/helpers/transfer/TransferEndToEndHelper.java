package com.lenskart.nexs.helpers.transfer;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.NexsOrderContext.TransferContext;
import com.lenskart.nexs.requestBuilder.TransfersRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.time.LocalDateTime;

@Slf4j
@SuperBuilder
public class TransferEndToEndHelper extends NexsBaseHelper implements ExecutionHelper {

    private TransferContext transferContext;
    private NexsOrderContext nexsOrderContext;
    private Response response;

    @Override
    public ExecutionHelper init() {

        // Setting source facility
        transferContext.setFacilityCode(transferContext.getSourceFacility());

        // Auth for source facility
        nexsOrderContext = NexsOrderContext.builder()
                .facilityCode(transferContext.getFacilityCode())
                .headers(NexsOrderContext.Headers.builder().build())
                .build();

        NexsAuthHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();

        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        //availQty check if present
        if (transferContext.getAvailQty() != null && !transferContext.getAvailQty().isEmpty()) {
            try {
                int availQty = Integer.parseInt(transferContext.getAvailQty());
                if (transferContext.getQuantity() > availQty) {
                    log.warn("Requested quantity {} exceeds available quantity {}",
                            transferContext.getQuantity(), availQty);
                    transferContext.setQuantity(availQty);
                }
            } catch (NumberFormatException e) {
                log.warn("availQty '{}' is not a valid number. Skipping availability check.",
                        transferContext.getAvailQty());
            }
        } else {
            log.info("No availQty provided. Proceeding with quantity: {}", transferContext.getQuantity());
        }

        // Create Transfer
        JSONObject payload = TransfersRequestBuilder.createTransferPayload(transferContext);

        CreateTransferHelper createTransfer = CreateTransferHelper.builder()
                .transferContext(transferContext)
                .nexsOrderContext(nexsOrderContext)
                .payload(payload.toString())
                .build();
        createTransfer.test();
        response = createTransfer.getResponse();

        String transferCode = response.jsonPath().getString("data.transferCode");
        transferContext.setTransferCode(transferCode);
        log.info("Created transfer with code: {}", transferCode);

        // Scan Barcode
        int quantity = transferContext.getQuantity();

        for (int i = 0; i < quantity; i++) {
            ScanBarcodeHelper.builder()
                    .transferContext(transferContext)
                    .nexsOrderContext(nexsOrderContext)
                    .build()
                    .test();
        }

        // Change Status
        ChangeTransferStatusHelper.builder()
                .transferContext(transferContext)
                .nexsOrderContext(nexsOrderContext)
                .status(transferContext.getTransferStatus())
                .build()
                .test();

        //Wait
        AwaitUtils.sleepSeconds(360);

        //Auth for destination facility
        nexsOrderContext = NexsOrderContext.builder()
                .facilityCode(transferContext.getDestFacility())
                .headers(NexsOrderContext.Headers.builder().build())
                .build();

        NexsAuthHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();

        //Transfer Receive
        TransferReceiveHelper.builder()
                .transferContext(transferContext)
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
