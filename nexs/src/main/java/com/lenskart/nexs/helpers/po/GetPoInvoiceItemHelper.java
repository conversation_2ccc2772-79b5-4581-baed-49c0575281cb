package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PO_INVOICE_ITEMS;

@SuperBuilder
public class GetPoInvoiceItemHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    NexsInwardContext nexsInwardContext;
    @Getter
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        queryParams = getQueryParamsForPoInvoicePayload(nexsInwardContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(PO_INVOICE_ITEMS.getUrl(), headers, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
