package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.PickingRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_SCAN_LENS;
@SuperBuilder
public class LensScanHelper extends NexsBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    NexsOrderContext nexsOrderContext;
    String payload,barcode;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = PickingRequestBuilder.lensScanPayload(nexsOrderContext.getFittingId(),nexsOrderContext.getBarcode());
        return this;
    }
    @Override
    public ServiceHelper process() {

        response = RestUtils.put(PICKING_SCAN_LENS.getUrl(),headers, payload,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }

}
