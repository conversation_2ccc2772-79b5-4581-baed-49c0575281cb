package com.lenskart.nexs.helpers.transfer;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.NexsOrderContext.TransferContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.nexs.endpoints.NexsEndpoints.TRANSFER_CHANGE_STATUS;

@SuperBuilder
@Slf4j
public class ChangeTransferStatusHelper extends NexsBaseHelper implements ServiceHelper {

    private TransferContext transferContext;
    private NexsOrderContext nexsOrderContext;
    private String status;
    private Response response;

    @Override
    public ServiceHelper init() {
        log.info("Changing status of transferCode: {} to {}",
                transferContext.getTransferCode(), status);
        headers = getHeadersWithAuth(nexsOrderContext);
        queryParams = getQueryParamsTransferStatus(
                transferContext.getTransferCode(),
                status);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(
                TRANSFER_CHANGE_STATUS.getUrl(),
                headers,
                queryParams,
                200
        );
        log.info("Transfer status changed to {}", status);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}