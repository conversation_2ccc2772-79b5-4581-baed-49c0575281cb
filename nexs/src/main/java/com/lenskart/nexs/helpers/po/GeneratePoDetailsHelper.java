package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.requestBuilder.po.POrequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.GENERATE_PO_DETAILS;

@SuperBuilder
public class GeneratePoDetailsHelper extends NexsBaseHelper implements ServiceHelper {
   NexsInwardContext nexsInwardContext;
    JSONObject payload;
    @Getter
    Response response;
    @Override
    public ServiceHelper init() {
          POrequestBuilder.getItemsPayload(nexsInwardContext);
         payload = POrequestBuilder.getGeneratePoDetailsPayload(nexsInwardContext);
        return this;
    }

    @Override
    public ServiceHelper process() {

        response = RestUtils.post(GENERATE_PO_DETAILS.getUrl(), null, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
