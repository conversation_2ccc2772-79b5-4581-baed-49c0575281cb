package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.PickingRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_COMPLETE_LENS_PICKING;

@SuperBuilder
public class LensSummaryCompleteHelper extends NexsBaseHelper implements ServiceHelper {
    Response response;
    NexsOrderContext nexsOrderContext;
    String payload,barcode;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = PickingRequestBuilder.lensCompletePayload(nexsOrderContext);
        return this;
    }
    @Override
    public ServiceHelper process() {
        response = RestUtils.put(PICKING_COMPLETE_LENS_PICKING.getUrl(Map.of("pickingSummaryId",nexsOrderContext.getPickingSummaryId())),headers, payload,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }
}
