package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_GET_PICKING_DEATIALS;
@SuperBuilder
public class GetPickingDetailsHelper extends NexsBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    NexsOrderContext nexsOrderContext;
    String id;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(PICKING_GET_PICKING_DEATIALS.getUrl(Map.of("pickingSummaryId", nexsOrderContext.getSummary_id())), headers, null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;

    }
}
