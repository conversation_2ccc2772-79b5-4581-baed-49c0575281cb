package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.lkauth.NexsLogoutHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@SuperBuilder
@Slf4j
public class DOPickingHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsOrderContext nexsOrderContext;
    @Getter
    List<String> doPickedBarcodes;

    @Override
    public ExecutionHelper init() {
//        setting DO orders required details
        Map<String, Object> fetchedDoOrderDetails = WMSDbUtils.getDoShipmentDetails(nexsOrderContext).get(0);
        nexsOrderContext.setShippingId(fetchedDoOrderDetails.get("shipping_package_id").toString());
        nexsOrderContext.setNavChannel(fetchedDoOrderDetails.get("nav_channel").toString());
        nexsOrderContext.setNexsOrderId(fetchedDoOrderDetails.get("nexs_order_id").toString());
        nexsOrderContext.setIsShipToStore(false);
        log.info("Do order Details: {}", fetchedDoOrderDetails);
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
//        since  NexsAuthHelper  calling in Do test class commenting login and logout,
        /* Authenticate User */
//        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        CreatePickingSummaryDOHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        GetPickingSummaryDOHelper getPickingSummaryDOHelper = GetPickingSummaryDOHelper.builder().nexsOrderContext(nexsOrderContext).build();
        getPickingSummaryDOHelper.test();

        PickBarcodeForDOHelper pickBarcodeForDOHelper=PickBarcodeForDOHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .getPickingSummaryDOHelper(getPickingSummaryDOHelper)
                .build();
        pickBarcodeForDOHelper.test();
        doPickedBarcodes=pickBarcodeForDOHelper.getAllPickedBarcodes();

        /* Logout User */
//        NexsLogoutHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
