package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_CLOSE_PICKING_SUMMARY;

@Slf4j
@SuperBuilder
public class ClosePickingSummaryHelper extends NexsBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    NexsOrderContext nexsOrderContext;


    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }
    @Override
    public ServiceHelper process() {
        response = RestUtils.put(PICKING_CLOSE_PICKING_SUMMARY.getUrl(Map.of("id",nexsOrderContext.getSummary_id())),headers, null,null);
        return this;
    }


    @Override
    public ServiceHelper validate() {
        return this;
    }
    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }
}
