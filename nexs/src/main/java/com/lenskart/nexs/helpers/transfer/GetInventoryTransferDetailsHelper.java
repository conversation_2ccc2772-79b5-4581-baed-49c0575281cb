package com.lenskart.nexs.helpers.transfer;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.nexs.endpoints.NexsEndpoints.TRANSFER_GET_TRANSFER_DETAILS;


@SuperBuilder
public class GetInventoryTransferDetailsHelper extends NexsBaseHelper implements ServiceHelper {

    private NexsOrderContext nexsOrderContext;
    private Response response;
    private String transferCode;
    private int pageNumber;
    private int pageSize;
    private boolean barcodeRequired;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        queryParams = getQueryParamsForGetTransferDetails(transferCode, barcodeRequired, pageNumber, pageSize);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(TRANSFER_GET_TRANSFER_DETAILS.getUrl(), headers, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

