package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.ProcessingType;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.EMSDbUtils;
import com.lenskart.nexs.database.NexsDbUtils;
import com.lenskart.nexs.helpers.consolidation.ConsolidationHelpers;
import com.lenskart.nexs.helpers.ems.EmsTriggerExceptionHelper;
import com.lenskart.nexs.helpers.jit.JITStatusUpdateHelper;
import com.lenskart.nexs.helpers.jit.RxStatusHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.lkauth.NexsLogoutHelper;
import com.lenskart.nexs.helpers.orderqc.OrderQcHoldAndFailHelper;
import com.lenskart.nexs.helpers.packing.PackingCompletionHelper;
import com.lenskart.nexs.helpers.packing.PackingDetailsHelper;
import com.lenskart.nexs.helpers.state.*;
import com.lenskart.nexs.helpers.wms.GetShipmentsHelper;
import com.lenskart.nexs.helpers.wms.NexsOrderDetailsHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.jit.JITStatusUpdateModel;
import com.lenskart.scm.helpers.fds.EinvoiceDetailsHelper;
import com.lenskart.scm.helpers.lsm.ChangeCourierHelper;
import com.lenskart.scm.helpers.vsm.ManulJItProcessHelper;
import com.lenskart.scm.model.ScmOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;


@SuperBuilder
@Slf4j
public class NexsOrderCompletionHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsOrderContext nexsOrderContext;
    JITStatusUpdateModel jitStatusUpdateModel;
    ScmOrderContext scmOrderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        /* Authenticate User */
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Get Shipments */
        GetShipmentsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();


        if (nexsOrderContext.getShipmentIds() != null && !nexsOrderContext.getShipmentIds().isEmpty()) {
            log.info("Processing {} shipments: {}", nexsOrderContext.getShipmentIds().size(), nexsOrderContext.getShipmentIds());

            // Process each shipment
            for (String shipmentId : nexsOrderContext.getShipmentIds()) {
                log.info("Processing shipment ID: {}", shipmentId);

                // Set the current shipment ID
                nexsOrderContext.setShippingId(shipmentId);

                // Process this shipment
                processShipment();
            }
        } else if (nexsOrderContext.getShippingId() != null) {
            processShipment();
        } else {
            log.error("No shipment IDs found to process");
        }

        /* Logout User */
        NexsLogoutHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        return this;
    }

    /**
     * Process a single shipment with the current shipment ID in the context
     */
    private void processShipment() {
        /* Order Details */
        NexsOrderDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        if(scmOrderContext==null){
            scmOrderContext = ScmOrderContext.builder().shippingId(nexsOrderContext.getShippingId()).incrementId(nexsOrderContext.getIncrementId()).facilityCode(nexsOrderContext.getFacilityCode()).build();
        }
//        scmOrderContext = ScmOrderContext.builder().shippingId(nexsOrderContext.getShippingId()).incrementId(nexsOrderContext.getIncrementId()).facilityCode(nexsOrderContext.getFacilityCode()).build();


        if(nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("MANUAL_JIT")){
            ManulJItProcessHelper.builder().scmOrderContext(scmOrderContext).build().test();
            log.info("Waiting for 15 seconds for EMS kafka consumer to process the MANUAL_JIT order ");
            AwaitUtils.sleepSeconds(15);
            List<String> barcodes = scmOrderContext.getBarcodes();
            nexsOrderContext.setBarcodes(barcodes);
            String stockinbarcode = barcodes.get(0);
            nexsOrderContext.setLeftLensBarcode(stockinbarcode);
            log.info("stockinbarcode: {}", stockinbarcode);
            String grnNO = NexsDbUtils.getGrnNoForJitInward(nexsOrderContext, stockinbarcode).getFirst().get("grn_code").toString();
            List<Map<String, Object>> exceptionIds = EMSDbUtils.getExceptiondId(nexsOrderContext, grnNO);
            if(exceptionIds.isEmpty() || exceptionIds == null){
                log.info("No exception found for grnNO: {}", grnNO);
            }
            else {
                for (Map<String, Object> map : exceptionIds) {
                    String exceptionId = map.get("id").toString();
                    log.info("retring for the ems exception for EmsException id :{}",exceptionId);
                    for (int i = 0; i < 4; i++) {
                        EmsTriggerExceptionHelper.builder().nexsOrderContext(nexsOrderContext).exceptionId(exceptionId).build().test();

                    }
                }

            }
            NexsOrderDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        }
        if(nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("AUTO_JIT")){
         RxStatusHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            NexsOrderDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
          nexsOrderContext.setIsFramePickingEnabled(false);
         if (nexsOrderContext.getIsAddverbPicking())
             AddverbPickingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
         else
             PickingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();


         TrayMakingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

         // Initialize JITStatusUpdateModel with default values
         jitStatusUpdateModel = JITStatusUpdateModel.builder().build();
         JITStatusUpdateHelper.builder().nexsOrderContext(nexsOrderContext).jitStatusUpdateModel(jitStatusUpdateModel).build().test();
         TrayMakingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
         NexsOrderDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
         nexsOrderContext.setIsFramePickingEnabled(true);
         nexsOrderContext.setIsLensPickingEnabled(false);
            if (nexsOrderContext.getIsAddverbPicking())
                AddverbPickingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            else
                PickingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

            nexsOrderContext.setIsLensPickingEnabled(false);
            NexsOrderDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        }
        if(nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("MANUAL_JIT")){
            log.info("Facility Code MANUAL_JIT : {}", nexsOrderContext.getFacilityCode());
            if(Constants.BHIWADI_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode()) || Constants.UAE_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode())){
                TrayMakingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                NexsOrderDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                log.info(" Tray making completed for  Code MANUAL_JIT : {}", nexsOrderContext.getFacilityCode());
                nexsOrderContext.setIsFramePickingEnabled(true);
                nexsOrderContext.setIsLensPickingEnabled(false);
                if (nexsOrderContext.getIsAddverbPicking())
                    AddverbPickingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                else
                    PickingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

                log.info("Frame picking completed for  Code MANUAL_JIT : {}", nexsOrderContext.getFacilityCode());
            }
            if(Constants.MANESAR_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode()) || Constants.SINGAPORE_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode())){
                NexsOrderDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                if (nexsOrderContext.getIsAddverbPicking())
                    AddverbPickingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                else
                    PickingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

            }


        }
        if (nexsOrderContext.getIsAddverbPicking() && !nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("AUTO_JIT") &&!nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("MANUAL_JIT"))
            AddverbPickingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        else if (!nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("MANUAL_JIT")){
            PickingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        }



        if (nexsOrderContext.getProcessingType().equals(ProcessingType.FR1.getCode()) ||
                nexsOrderContext.getProcessingType().equals(ProcessingType.FR2.getCode())) {
            /* Tray Making */
            TrayMakingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            /* MEI In */
            if(nexsOrderContext.getIsFrameQcFailed() && !nexsOrderContext.getIsLensQcFail()) {
                log.info("MEI Out .. initiation after qc fail");
                MeiOutHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                /* Fitting */
                FittingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            }else {
                log.info("MEI ..... In initiation");
                MeiInHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                /* MEI Out */
                MeiOutHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                /* Fitting */
                FittingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            }
        }

        /* QC Initiation */
        OrderQcInitiationHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        /* QC hold and QC fail */
        if(nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("MANUAL_JIT") && nexsOrderContext.getIsLensQcFail()){
            nexsOrderContext.setIsQcHold(true);
            OrderQcHoldAndFailHelper.builder()
                    .nexsOrderContext(nexsOrderContext)
                    .build()
                    .test();
            nexsOrderContext.setIsQcHold(false);
            nexsOrderContext.setIsQcFail(true);
            OrderQcHoldAndFailHelper.builder()
                    .nexsOrderContext(nexsOrderContext)
                    .build().test();
            return ;
        }else {
            OrderQcCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        }


        if(nexsOrderContext.getIsEInvoiceRequired()){
            EinvoiceDetailsHelper.builder().scmOrderContext(scmOrderContext).build().test();
        }

        /* Store Consolidation and Store Packing */
        if (nexsOrderContext.getIsShipToStore() && !nexsOrderContext.getNavChannel().contains(Constants.BULK)) {
            ConsolidationHelpers.builder().nexsOrderContext(nexsOrderContext).build().test();
        }

        /* Packing Details */
        PackingDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Packing Completion */
        PackingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Print Invoice */
        if (nexsOrderContext.getNavChannel().contains(Constants.DTC) || nexsOrderContext.getNavChannel().contains(Constants.BULK))
            PrintInvoiceHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* changeCourier logic */
        if (nexsOrderContext.getIsCourierChangeRequired()) {
            scmOrderContext.setCourierCode(Constants.DEFAULT_COURIER);
            ChangeCourierHelper.builder().scmOrderContext(scmOrderContext).build().test();
        }

        /* Print Shipment */
        PrintShipmentHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Manifest Scan */
        ManifestHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Manifest Close */
        DispatchHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
    public static void main(String[] args) {
        NexsOrderContext  nexsOrderContext = NexsOrderContext
                .builder()
                .shippingId("SQNXS226000004451081")
                .headers(NexsOrderContext.Headers.builder().build())
              //  .isFrameQcFailed(true)
                .isLensQcFail(true)
                .build();
        NexsOrderCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
       // nexsOrderContext.setIsFrameQcFailed(false);
        nexsOrderContext.setIsLensQcFail(false);
      //  ScmOrderContext.builder().isQcFailOrder(true).build();
        ScmOrderContext scmOrderContext = ScmOrderContext.builder().isQcFailOrder(true).shippingId(nexsOrderContext.getShippingId()).incrementId(nexsOrderContext.getIncrementId()).facilityCode(nexsOrderContext.getFacilityCode()).build();
        NexsOrderCompletionHelper.builder().nexsOrderContext(nexsOrderContext).scmOrderContext(scmOrderContext).build().test();
    }

}
