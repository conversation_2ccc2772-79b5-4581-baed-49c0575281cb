package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.lkauth.NexsLogoutHelper;
import com.lenskart.nexs.helpers.putaway.ContinousScanHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class StartRetireE2EHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsOrderContext nexsOrderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    /**
     * RUN THIS TEST ONLY FOR SINGAPORE FACILITY. SYNC IS CONFIGURED FOR SINGAPORE FACILITY ONLY.
     * OTHER FACILITIES WILL FAIL.
     */
    @Override
    public ExecutionHelper orchestrateFlow() {
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        StartRetireHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        if (!nexsOrderContext.getFacilityCode().equals(Constants.SINGAPORE_WAREHOUSE_FACILITY))
            ContinousScanHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        NexsLogoutHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }


}
