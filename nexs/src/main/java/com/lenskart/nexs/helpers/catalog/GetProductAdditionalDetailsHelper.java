package com.lenskart.nexs.helpers.catalog;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import io.restassured.response.Response;
import static com.lenskart.nexs.endpoints.NexsEndpoints.CATALOG_PRODUCT_ADDITIONAL_DETAILS;

@SuperBuilder
public class GetProductAdditionalDetailsHelper extends NexsBaseHelper implements ServiceHelper {
     NexsOrderContext nexsOrderContext;
     Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        queryParams = getQueryParamsForGetProductDetails(nexsOrderContext.getProductId());
        return this;
    }

    @Override
    public ServiceHelper process() {
         response = RestUtils.get(CATALOG_PRODUCT_ADDITIONAL_DETAILS.getUrl(), headers, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
