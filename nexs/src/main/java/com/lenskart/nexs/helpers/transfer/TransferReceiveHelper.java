package com.lenskart.nexs.helpers.transfer;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.NexsOrderContext.TransferContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.TRANSFER_RECEIVE;

@SuperBuilder
@Slf4j
public class TransferReceiveHelper extends NexsBaseHelper implements ServiceHelper {

    private TransferContext transferContext;
    private NexsOrderContext nexsOrderContext;

    @Getter
    private Response response;

    @Override
    public ServiceHelper init() {
        log.info("Calling Transfer Receive API for transferCode: {}", transferContext.getTransferCode());
        headers = getHeadersWithAuth(nexsOrderContext);
        queryParams = getQueryParamsForTransferReceive(
                transferContext.getTransferType(),
                transferContext.getDestFacility()
        );
        return this;
    }

    @Override
    public ServiceHelper process() {
        log.info("Transfer Receive API call");
        String url = TRANSFER_RECEIVE.getUrl(Map.of("transfer_code", transferContext.getTransferCode()));
        response = RestUtils.get(url, headers, queryParams, 200);
        log.info("Transfer Receive response received for transferCode: {} with status: {}",
                transferContext.getTransferCode(), response.getStatusCode());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}