package com.lenskart.nexs.helpers.cid;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.endpoints.NexsEndpoints;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.ims.response.ConsolidatedInvInfo;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CID_GET_INVENTORY_INFO;

@SuperBuilder
@Slf4j
@Getter
public class GetConsolidatedInvInfo extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    Response response;
    ConsolidatedInvInfo consolidatedInvInfo;

    @Override
    public ServiceHelper init() {
        statusCode =200;
        headers = getHeadersFacilityCode(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(CID_GET_INVENTORY_INFO.getUrl(Map.of("productId", String.valueOf(nexsOrderContext.getProductId()))),
                headers, null, 200);
         consolidatedInvInfo = parseResponse(RestUtils.getValueFromResponse(response, "data"),
                ConsolidatedInvInfo.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
