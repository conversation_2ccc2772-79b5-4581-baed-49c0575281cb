package com.lenskart.nexs.helpers.transfer;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.TransfersRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.TRANSFER_CREATE;

@SuperBuilder
@Slf4j

    public class CreateTransferHelper extends NexsBaseHelper implements ServiceHelper {

        private NexsOrderContext.TransferContext transferContext;
        private String payload;
        private NexsOrderContext nexsOrderContext;
        @Getter
        private Response response;


        @Override
        public ServiceHelper init() {
            headers = getHeaders(nexsOrderContext);
            return this;
        }

        @Override
        public ServiceHelper process() {
            JSONObject payload = TransfersRequestBuilder.createTransferPayload(transferContext);
            response = RestUtils.post(TRANSFER_CREATE.getUrl(), headers, payload.toString(), 200);
            String transferCode = RestUtils.getValueFromResponse(response,"data.transferCode").toString();
            transferContext.setTransferCode(transferCode);
            nexsOrderContext.setTransferCode(transferCode);
            log.info("Transfer created successfully with transferCode: {}", transferCode);
            return this;
        }

        @Override
        public ServiceHelper validate() {
            return this;
        }

        @Override
        public ServiceHelper test() {
            init();
            process();
            validate();
            return this;
        }
    }