package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.PickingRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_DO_CLOSE_PICKING;

@SuperBuilder
@Slf4j
public class DoClosePickingHelper extends NexsBaseHelper implements ServiceHelper {
    String payload;
    NexsOrderContext nexsOrderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = PickingRequestBuilder.DoClosePickingPayload(nexsOrderContext.getShippingId());
        log.info("ClosePicking payload:{}", payload);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PICKING_DO_CLOSE_PICKING.getUrl(), headers, payload, 200);
        log.info("ClosePicking response:{}", response);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        log.info("Validating picking status for shipping ID: {}", nexsOrderContext.getShippingId());
        List<Map<String, Object>> statusList = WMSDbUtils.getShipmentStatus(nexsOrderContext);

        statusList.forEach(item -> {
            String status = item.get("status").toString();
            log.info("Shipment status: {}", status);
            Assert.assertEquals(status, "PICKED",
                    "Shipment should not be in IN_PICKING state after closing picking summary");
        });

        log.info("Validation successful - shipment is no longer in picking state");
        return this;
    }

    @Override
    public ServiceHelper test() {
        return init().process().validate();
    }

}

