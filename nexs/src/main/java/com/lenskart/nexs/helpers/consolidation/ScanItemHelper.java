package com.lenskart.nexs.helpers.consolidation;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CONSOLIDATION_SCAN_ITEM;

@SuperBuilder
@Slf4j
public class ScanItemHelper extends NexsBaseHelper implements ServiceHelper {
    
    NexsOrderContext nexsOrderContext;
    @Getter
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = NexsRequestBuilder.getScanItemPayloadForConsolidation(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (nexsOrderContext.getFacilityCode().equals(Constants.BHIWADI_WAREHOUSE_FACILITY) ||
                nexsOrderContext.getFacilityCode().equals(Constants.UAE_WAREHOUSE_FACILITY)) {
            try {
                response = RestUtils.post(CONSOLIDATION_SCAN_ITEM.getUrl(), headers, payload.toString(), 200);
            } catch (Exception e) {
                log.warn("First attempt failed, retrying after 5 seconds...");
                AwaitUtils.sleepSeconds(5);
                response = RestUtils.post(CONSOLIDATION_SCAN_ITEM.getUrl(), headers, payload.toString(), 200);
            }
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        if (response != null) {
            String displayMessage = RestUtils.getValueFromResponse(response, "meta.displayMessage").toString();
            assert displayMessage.equals("Success");
            
            String shipmentId = RestUtils.getValueFromResponse(response, "data.childItems[0].itemBarCode").toString();
            assert shipmentId.equals(nexsOrderContext.getShippingId());
            log.info("Scan item validation successful for shipment: {}", shipmentId);
        }
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}