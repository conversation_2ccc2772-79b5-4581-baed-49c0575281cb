package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_VIEW_ORDER;

@SuperBuilder
public class PickingViewHelper extends NexsBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        queryParams = getQueryParamsForViewOrder(nexsOrderContext.getBarcode());
        return this;
    }
    @Override
    public ServiceHelper process() {
        response = RestUtils.get(PICKING_VIEW_ORDER.getUrl(),headers, queryParams,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }
}
