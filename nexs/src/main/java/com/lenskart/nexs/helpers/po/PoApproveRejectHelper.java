package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.po.POrequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PO_APPROVE_REJECT;

@SuperBuilder
public class PoApproveRejectHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    NexsInwardContext nexsInwardContext;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = POrequestBuilder.getApproveRejectPayload(nexsInwardContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        Response response = RestUtils.put(PO_APPROVE_REJECT.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
