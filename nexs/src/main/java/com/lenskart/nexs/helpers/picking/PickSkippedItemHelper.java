package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.api.ImsService;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_PICK_ITEM;
@SuperBuilder
public class PickSkippedItemHelper extends NexsBaseHelper implements ServiceHelper {
    Response response;
    NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }
    @Override
    public ServiceHelper process() {
        String barcode = ImsService.fetchBarcodeForGivenPid(Constants.AVAILABLE, Constants.GOOD, true, nexsOrderContext.getFacilityCode(), nexsOrderContext.getProductId(), Constants.AVAILABLE);
        nexsOrderContext.setBarcode(barcode);
        response = RestUtils.post(PICKING_PICK_ITEM.getUrl(Map.of("wmsOrderItemId",nexsOrderContext.getWmsOrderId(),"barcode",nexsOrderContext.getBarcode())),headers, null,null);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }
}
