package com.lenskart.nexs.helpers.transfer;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.database.NexsDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.NexsOrderContext.TransferContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.TRANSFER_SCAN_BARCODE;

@SuperBuilder
@Slf4j
public class ScanBarcodeHelper extends NexsBaseHelper implements ServiceHelper {
    private TransferContext transferContext;
    private NexsOrderContext nexsOrderContext;
    private Response response;

    @Override
    public ServiceHelper init() {
        // Fetch barcodes from IMS-GRN join
        NexsOrderContext context = NexsOrderContext.builder()
                .productId(String.valueOf(transferContext.getProductId()))
                .facilityCode(transferContext.getSourceFacility())
                .legalOwner(transferContext.getLegalOwner())
                .build();

        List<Map<String, Object>> barcodeList = NexsDbUtils.getTransferBarcodes(context, 10);

        if (barcodeList.isEmpty()) {
            throw new RuntimeException("No valid barcode found for product and facility");
        }

        String barcodeToScan = barcodeList.getFirst().get("barcode").toString();
        transferContext.setBarcode(barcodeToScan);
        nexsOrderContext.setBarcode(barcodeToScan);

        log.info("Using barcode: {}", barcodeToScan);

        headers = getHeaders(nexsOrderContext);
        queryParams = getQueryParamsForScanBarcode(
                barcodeToScan,
                "",
                transferContext.getTransferCode(),
                false
        );

        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(
                TRANSFER_SCAN_BARCODE.getUrl(),
                headers,
                queryParams,
                "",
                200
        );
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}