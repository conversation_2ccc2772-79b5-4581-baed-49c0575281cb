package com.lenskart.nexs.helpers.picking;


import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;


import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_ADDVERB_CREATE_WAVE;
@SuperBuilder

public class CreateWaveHelper extends NexsBaseHelper implements ServiceHelper {
    Response response;
    public NexsOrderContext nexsOrderContext;


    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        nexsOrderContext.setPickingType(Constants.FR1_WAVE);
        queryParams = getQueryParamsForCreateWave(nexsOrderContext.getPickingType(), Constants.WAVE_SIZE,Constants.DATA_SOURCE);
        return this;
    }


    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PICKING_ADDVERB_CREATE_WAVE.getUrl(),null, queryParams,null);
        String waveId = RestUtils.getValueFromResponse(response, "waveId").toString();
        nexsOrderContext.setWaveId(waveId);
        return this;
    }


    @Override
    public ServiceHelper validate() {


        return this;
    }
    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }


}

