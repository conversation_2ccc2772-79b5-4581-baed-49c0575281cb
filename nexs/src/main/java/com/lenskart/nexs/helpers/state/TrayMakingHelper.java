package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.validator.wms.TrayMakingValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;

import static com.lenskart.commons.model.NexsOrderState.PICKED;
import static com.lenskart.nexs.endpoints.NexsEndpoints.WMS_CREATE_TRAY;

/**
 * Helper class to transition order from PICKED to IN_TRAY Status.
 * Handles the business logic for completing the picking process.
 */

@SuperBuilder
@Slf4j
public class TrayMakingHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    Response response;
    JSONObject payload;
    String trayBarcode;
    String frameBarcode;

    @Override
    public ServiceHelper init() {
        logOperationStart("Tray Making Started");
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        String trayBarcode = generateUniqueTrayBarcode();

        if (WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(PICKED.name())
                && !nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("AUTO_JIT")
                && !nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("MANUAL_JIT")) {

            nexsOrderContext.setTrayBarcode(trayBarcode);
            for (String barcode : nexsOrderContext.getBarcodes()) {
                nexsOrderContext.setBarcode(barcode);
                if (barcode != null && ImsDbUtils.getBarcodeDetails(nexsOrderContext).getFirst().get("status").equals(PICKED.name())) {
                    payload = NexsRequestBuilder.getCreateTrayPayload(nexsOrderContext, barcode, trayBarcode);
                    response = RestUtils.post(WMS_CREATE_TRAY.getUrl(), headers, payload.toString(), 200);
                }
            }
        } else if ((WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(PICKED.name()) || WMSDbUtils.getStatusOfLens(nexsOrderContext).equals(PICKED.name()))
                && !nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("AUTO_JIT")
                && (Constants.MANESAR_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode())
                || Constants.SINGAPORE_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode()))) {
            nexsOrderContext.setTrayBarcode(trayBarcode);
            for (String barcode : nexsOrderContext.getBarcodes()) {
                nexsOrderContext.setBarcode(barcode);
                if (barcode != null && ImsDbUtils.getBarcodeDetails(nexsOrderContext).getFirst().get("status").equals(PICKED.name())) {
                    payload = NexsRequestBuilder.getCreateTrayPayload(nexsOrderContext, barcode, trayBarcode);
                    response = RestUtils.post(WMS_CREATE_TRAY.getUrl(), headers, payload.toString(), 200);
                }
            }

        } else if ((WMSDbUtils.getStatusOfLens(nexsOrderContext).equals(PICKED.name()) || WMSDbUtils.getStatusOfLens(nexsOrderContext).equals("PRODUCTION_DONE"))
                && (nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("AUTO_JIT") || nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("MANUAL_JIT"))
                && (Constants.BHIWADI_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode()) || Constants.UAE_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode()))) {
            nexsOrderContext.setTrayBarcode(trayBarcode);
            for (String barcode : nexsOrderContext.getBarcodes()) {
                if (barcode != null) {
                    payload = NexsRequestBuilder.getCreateTrayPayload(nexsOrderContext, barcode, trayBarcode);
                    response = RestUtils.post(WMS_CREATE_TRAY.getUrl(), headers, payload.toString(), 200);
                }
            }
        } else {
            trayBarcode = WMSDbUtils.getTrayIdForLens(nexsOrderContext);
            nexsOrderContext.setTrayBarcode(trayBarcode);
            for (String barcode : nexsOrderContext.getBarcodes()) {
                if (barcode != null) {
                    payload = NexsRequestBuilder.getCreateTrayPayload(nexsOrderContext, barcode, trayBarcode);
                    response = RestUtils.post(WMS_CREATE_TRAY.getUrl(), headers, payload.toString());
                }
            }
        }

        logOperationComplete("Tray Making Completed");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        TrayMakingValidator validator = TrayMakingValidator.builder().nexsOrderContext(nexsOrderContext).build();
        validator.validateNode();
        validator.validateDBEntities();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return PICKED;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.IN_TRAY;
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }

        public static String generateUniqueTrayBarcode() {
            String trayBarcode;
            List<Map<String, Object>> trayDetails;

            int maxRetries = 10; // to prevent infinite loop
            int attempts = 0;

            while (attempts < maxRetries) {
                trayBarcode = "T" + GenericUtils.genrateRandomNumericString(5);
                trayDetails = WMSDbUtils.getTrayDetails(trayBarcode);

                if (trayDetails.isEmpty()) {
                    // Tray does not exist; return tray ID
                    return trayBarcode;
                }

                attempts++;
            }

            throw new RuntimeException("Could not generate a unique tray ID after " + maxRetries + " attempts.");

        }


}
