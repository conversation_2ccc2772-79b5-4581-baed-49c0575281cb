package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.api.ImsService;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Generated;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.Collections;
import java.util.Map;
import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_SCAN_BARCODE;

@SuperBuilder
public class PickingScanBarcodeHelper extends NexsBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    NexsOrderContext nexsOrderContext;
    @Getter
    String barcode;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }
    @Override
    public ServiceHelper process() {
        barcode = ImsService.fetchBarcodeForGivenPid(Constants.AVAILABLE, Constants.GOOD, true, nexsOrderContext.getFacilityCode(), nexsOrderContext.getProductId(), Constants.AVAILABLE);
        response = RestUtils.post(PICKING_SCAN_BARCODE.getUrl(Map.of("picklistId",nexsOrderContext.getPicklistId(),"barcode",barcode)),headers,null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }
}
