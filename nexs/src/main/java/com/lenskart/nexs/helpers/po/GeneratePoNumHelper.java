package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.nexs.endpoints.NexsEndpoints.GENERATE_PO_NUM;


@SuperBuilder
public class GeneratePoNumHelper  extends NexsBaseHelper implements ServiceHelper {
    Response response;
    NexsOrderContext nexsOrderContext;
    NexsInwardContext nexsInwardContext;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response= RestUtils.get(GENERATE_PO_NUM.getUrl(), headers, null, 200);
        String po_num = RestUtils.getValueFromResponse(response, "data").toString();
        nexsInwardContext.setPoNumber(po_num);
        return this;
    }


    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return null;
    }
}
