package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.GET_INVOICE_PROCESSING_STATUS;

@SuperBuilder
public class GetInvoiceProcessingStatusHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    @Getter
    Response response;
    @Getter
    @Setter
    String request;
    Map<String, Object> QueryParams;


    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        queryParams = getQueryParamsForGetInvoiceProcessingStatusPayload(request);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_INVOICE_PROCESSING_STATUS.getUrl(), headers, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
