package com.lenskart.nexs.helpers.cyclecount;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.cycleCount.CycleCountScanLocationRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CYCLE_COUNT_SCAN_ITEM;

@SuperBuilder
@Slf4j
public class CycleCountScanItemHelper extends NexsBaseHelper implements ServiceHelper {
    private NexsOrderContext nexsOrderContext;
    JSONObject payload;
    private Response response;

    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = CycleCountScanLocationRequestBuilder.getCycleCountItemPayload(nexsOrderContext.getBarcode(),nexsOrderContext.getLocationCode());
        log.info("Payload for Cycle Count Scan Item: {}", payload.toString());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(CYCLE_COUNT_SCAN_ITEM.getUrl(),headers,payload.toString(),200);
        log.info("Response Status Code: {}", response.getStatusCode());
        log.info("Response Body: {}", response.getBody().asString());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        RestUtils.validateStatusCode(response,200);
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
