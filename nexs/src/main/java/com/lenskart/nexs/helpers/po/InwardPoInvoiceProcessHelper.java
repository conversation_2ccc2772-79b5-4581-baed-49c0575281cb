package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class InwardPoInvoiceProcessHelper extends NexsBaseHelper implements ExecutionHelper {

    NexsOrderContext nexsOrderContext;
    @Getter
    @Setter
    NexsInwardContext nexsInwardContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        nexsInwardContext.setVendorInvNo("INV"+ nexsInwardContext.getPoNumber());
        GetPoInvoiceItemHelper getPoInvoiceItemHelper = GetPoInvoiceItemHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        getPoInvoiceItemHelper.test();
        nexsInwardContext.setVendorUnitCostPriceWithTax( getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].vendor_unit_cost_price"));
        nexsInwardContext.setCgstPer( getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].cgst_per"));
        nexsInwardContext.setIgstPer( getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].igst_per"));
        nexsInwardContext.setSgstPer( getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].sgst_per"));
        nexsInwardContext.setUgstPer( getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].ugst_per"));
        nexsInwardContext.setFlatPer( getPoInvoiceItemHelper.getResponse().jsonPath().getDouble("data.invoice_items[0].flat_per"));
        SavePoInvoiceHelper savePoInvoiceHelper = SavePoInvoiceHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        savePoInvoiceHelper.test();
        AwaitUtils.sleepSeconds(15);
        GetInvoiceProcessingStatusHelper getInvoiceProcessingStatusHelper = GetInvoiceProcessingStatusHelper.builder().nexsOrderContext(nexsOrderContext).build();
        getInvoiceProcessingStatusHelper.setRequest(RestUtils.getValueFromResponse(savePoInvoiceHelper.getResponse(),"data").toString());
        getInvoiceProcessingStatusHelper.test();
        nexsInwardContext.setInvoiceRefNum(getInvoiceProcessingStatusHelper.getResponse().jsonPath().getString("data.infoMsg[0]").substring(22, 27));
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
