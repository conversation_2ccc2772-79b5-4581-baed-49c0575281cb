package com.lenskart.nexs.helpers.picking;


import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import com.lenskart.nexs.database.PickingDbutils;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;


import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_ADDVERB_WAVE_DETAILS;

@Slf4j
@SuperBuilder
public class FetchWaveDetailsHelper extends NexsBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    NexsOrderContext nexsOrderContext;
    String groupId;

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(PICKING_ADDVERB_WAVE_DETAILS.getUrl(Map.of("waveId", nexsOrderContext.getWaveId())), null, null);
        String status = RestUtils.getValueFromResponse(response, "wave.status").toString();
        nexsOrderContext.setStatus(status);
        if (status.equals("DONE")) {
            String groupId = response.jsonPath().getString("waveDetails[0].groupId");
            nexsOrderContext.setGroupId(groupId);

        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
