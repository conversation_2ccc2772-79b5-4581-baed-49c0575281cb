package com.lenskart.nexs.helpers.ems;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.nexs.endpoints.NexsEndpoints.EMS_EXCEPTION_TRIGGER;

@SuperBuilder
public class EmsTriggerExceptionHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    String exceptionId;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        nexsOrderContext.setExceptionId(exceptionId);
        queryParams = getQueryParamsForGetExceptionId(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        Response response = RestUtils.post(EMS_EXCEPTION_TRIGGER.getUrl(), headers, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
