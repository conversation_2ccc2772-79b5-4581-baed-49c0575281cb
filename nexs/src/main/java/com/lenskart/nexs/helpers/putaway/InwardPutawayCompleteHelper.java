package com.lenskart.nexs.helpers.putaway;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.nexs.database.PutawayDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class InwardPutawayCompleteHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsInwardContext nexsInwardContext;
    NexsOrderContext nexsOrderContext;

    @Override
    public ExecutionHelper init() {
        String barcode = nexsInwardContext.getInwardBarcodes().get(0);
        nexsOrderContext.setPutawayId(PutawayDbUtils.getPutawayIDForBarcode(nexsOrderContext,barcode).getFirst().get("putaway_code").toString());
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        for (String barcode : nexsInwardContext.getInwardBarcodes()) {
            ContinousScanHelper.builder().nexsOrderContext(nexsOrderContext).barcode(barcode).build().test();
        }
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
