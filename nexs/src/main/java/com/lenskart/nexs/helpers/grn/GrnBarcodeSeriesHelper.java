package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.nexs.endpoints.NexsEndpoints.GRN_SCAN_ITEM_BARCODE_SERIES;

@SuperBuilder
public class GrnBarcodeSeriesHelper extends NexsBaseHelper implements ServiceHelper{
    Response response;
    NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GRN_SCAN_ITEM_BARCODE_SERIES.getUrl(), headers, null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
