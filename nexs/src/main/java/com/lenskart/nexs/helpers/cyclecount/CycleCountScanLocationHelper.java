package com.lenskart.nexs.helpers.cyclecount;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.cycleCount.CycleCountScanLocationRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CYCLE_COUNT_SCAN_LOCATION;

@SuperBuilder
@Slf4j
public class CycleCountScanLocationHelper extends NexsBaseHelper implements ServiceHelper {
    private NexsOrderContext nexsOrderContext;
    JSONObject payload;
    private Response response;

    @Override
    public ServiceHelper init() {
        NexsAuthHelper authHelper = NexsAuthHelper.builder()
                .nexsOrderContext(nexsOrderContext).build();
        authHelper.test();
        headers = getHeaders(nexsOrderContext);
        payload = CycleCountScanLocationRequestBuilder.getCycleCountPayload(nexsOrderContext.getLocationCode(),nexsOrderContext.getItemType());
        log.info("Payload for Cycle Count scan location: {}", payload.toString());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(CYCLE_COUNT_SCAN_LOCATION.getUrl(),headers,payload.toString(),200);
        log.info("Response Status Code: {}", response.getStatusCode());
        log.info("Response Body: {}", response.getBody().asString());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        RestUtils.validateStatusCode(response,200);
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
