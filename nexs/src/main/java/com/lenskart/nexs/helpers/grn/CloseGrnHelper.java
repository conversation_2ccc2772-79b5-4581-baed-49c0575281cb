package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CLOSE_GRN;

@SuperBuilder
public class CloseGrnHelper extends NexsBaseHelper implements ServiceHelper {
    Response response;
    NexsOrderContext nexsOrderContext;
    NexsInwardContext nexsInwardContext;


    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
         response = RestUtils.put(CLOSE_GRN.getUrl(Map.of("grnCode", nexsInwardContext.getGrnNumber())), headers, null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;

    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }


}