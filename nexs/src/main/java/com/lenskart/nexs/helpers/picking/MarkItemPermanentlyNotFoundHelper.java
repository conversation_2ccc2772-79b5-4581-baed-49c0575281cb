package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_MARK_ITEM_PERMANENT_NOT_FOUND;

@SuperBuilder
public class MarkItemPermanentlyNotFoundHelper extends NexsBaseHelper implements ServiceHelper {
    Response response;
    NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        queryParams = getQueryParamsForSkip(nexsOrderContext.getPicklistId());
        return this;
    }
    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PICKING_MARK_ITEM_PERMANENT_NOT_FOUND.getUrl(),headers, queryParams,null);
        return this;
    }

    @Override
    public ServiceHelper validate() {

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }
}
