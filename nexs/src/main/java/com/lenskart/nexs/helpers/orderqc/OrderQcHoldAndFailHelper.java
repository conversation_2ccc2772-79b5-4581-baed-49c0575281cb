package com.lenskart.nexs.helpers.orderqc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.EMSDbUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.NexsOrderCompletionHelper;
import com.lenskart.nexs.helpers.ems.EmsTriggerExceptionHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.orderqc.CompleteQcRequest;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.testng.Assert;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.lenskart.commons.model.NexsOrderState.IN_QC;
import static com.lenskart.nexs.constants.Constants.QC_FAIL_REASON;
import static com.lenskart.nexs.endpoints.NexsEndpoints.ORDERQC_COMPLETE_QC;

@SuperBuilder
@Slf4j
public class OrderQcHoldAndFailHelper extends NexsBaseHelper implements ServiceHelper {
    Response response;
    JSONObject payload;
    List<CompleteQcRequest> list;
    NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        statusCode = nexsOrderContext.getStatusCode();
        list = new ArrayList<>();


        if (WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(IN_QC.name()) ||
                WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(Constants.QC_HOLD) ||
                WMSDbUtils.getStatusOfLens(nexsOrderContext).equals(Constants.QC_HOLD)) {
            nexsOrderContext.getFetchOrderScannedEntityResponse().getOrderQcStatus().getOrderQcStatusList().forEach(items -> {
                Object item_types = WMSDbUtils.getWmsDetails(items.getOrder().getUwItemId().toString()).getFirst().get("item_type");
                String item_type = item_types.toString();
                log.info("Order QC status: {}", items);
                nexsOrderContext.setBarcode(items.getOrder().getBarcode());
                String barcode = nexsOrderContext.getBarcode();
                nexsOrderContext.setUwItemId(String.valueOf(items.getOrder().getUwItemId()));
                String uwItemId = nexsOrderContext.getUwItemId();
                nexsOrderContext.setTrayId(items.getOrder().getTrayId());
                String trayId = nexsOrderContext.getTrayId();
                CompleteQcRequest request = new CompleteQcRequest();
                request.setBarcode(barcode);
                request.setDamaged(false);
                request.setIncrementId(Integer.parseInt(nexsOrderContext.getIncrementId()));
                request.setPrimaryReason("UNCLASSIFIED");
                request.setShippingPackageId(nexsOrderContext.getShippingId());
                request.setIsScanningDone(true);
                request.setFittingId("0");
                request.setOrderItemId(Integer.parseInt(uwItemId));
                request.setTrayId(trayId);
                request.setReason(QC_FAIL_REASON);
                request.setReasonId("1");
                request.setReasonName(QC_FAIL_REASON);
                request.setSecondaryReason(QC_FAIL_REASON);
                if((item_type.equalsIgnoreCase("FRAME") || item_type.equalsIgnoreCase("SUNGLASS")) && nexsOrderContext.getIsFrameQcFailed()) {
                    if (nexsOrderContext.getIsQcHold() == Boolean.TRUE) {
                        request.setStatus("QCHold");
                        request.setBarcodeStatus("IN_QC");
                    } else if (nexsOrderContext.getIsQcFail() == Boolean.TRUE) {
                        nexsOrderContext.setItemType(item_type);
                        request.setStatus("QCFailed");
                        request.setBarcodeStatus("QC_HOLD");
                    }
                }else if((item_type.equalsIgnoreCase("RIGHTLENS") || item_type.equalsIgnoreCase("LEFTLENS")) && nexsOrderContext.getIsLensQcFail()){
                    if (nexsOrderContext.getIsQcHold() == Boolean.TRUE) {
                        request.setStatus("QCHold");
                        request.setBarcodeStatus("IN_QC");
                    } else if (nexsOrderContext.getIsQcFail() == Boolean.TRUE) {
                        nexsOrderContext.setItemType(item_type);
                        request.setStatus("QCFailed");
                        request.setBarcodeStatus("QC_HOLD");
                    }
                }
                else{
                    request.setStatus("QCPass");
                    request.setBarcodeStatus("IN_QC");
                }
                list.add(request);
            });

            String orderItemHeaderId = String.valueOf(nexsOrderContext.getFetchOrderScannedEntityResponse().getInfoForQCPanel().getOrderResponse().getOrderItemHeaderId());
            nexsOrderContext.setOrderItemHeaderId(orderItemHeaderId);
            String nexsOrderId = String.valueOf(nexsOrderContext.getFetchOrderScannedEntityResponse().getInfoForQCPanel().getOrderResponse().getNexsOrderId());
            nexsOrderContext.setNexsOrderId(nexsOrderId);
            log.info("nexsOrderId {}", nexsOrderId);
            log.info("JsonUtils.convertObjectToJsonString(list) :{}"+JsonUtils.convertObjectToJsonString(list));
        }
        return this;
    }

    @Override
    public ServiceHelper process() {
        queryParams = getQueryParamsForCompleteQC();
        response = RestUtils.post(ORDERQC_COMPLETE_QC.getUrl(), headers, queryParams, JsonUtils.convertObjectToJsonString(list));
        String errorMessage = RestUtils.getValueFromResponse(response, "meta.displayMessage").toString();
        if(Constants.PO_CREATION_ERROR_MESSAGE.equalsIgnoreCase(errorMessage)) {
            AwaitUtils.sleepSeconds(20);
            response = RestUtils.post(ORDERQC_COMPLETE_QC.getUrl(), headers, queryParams, JsonUtils.convertObjectToJsonString(list),200);
        }
        if(nexsOrderContext.getIsQcFail() == Boolean.TRUE){
            boolean success = AwaitUtils.pollUntil(
                    () -> {
                        List<Map<String, Object>> EMSDetails = EMSDbUtils.getDetails(nexsOrderContext);
                        String emsId = EMSDetails.getFirst().get("id").toString();
                        EmsTriggerExceptionHelper.builder().nexsOrderContext(nexsOrderContext).exceptionId(emsId).build().test();
                        String currentStatus = EMSDetails.getFirst().get("status").toString();
                        log.info("Polling EMS status: {}", currentStatus);
                        nexsOrderContext.setEmsStatus(currentStatus);
                        return "FINISHED".equals(currentStatus);
                    },
                    "Wait for order to move to in_picking",
                    Duration.ofSeconds(120),
                    Duration.ofSeconds(15),
                    true
            );
            List<Map<String, Object>> EMSDetails = EMSDbUtils.getDetails(nexsOrderContext);
            String emsExceptionIDStatus = EMSDetails.getFirst().get("status").toString();
            Assert.assertEquals(emsExceptionIDStatus, "FINISHED","EMS status is not FINISHED, found: " + emsExceptionIDStatus);
            if (success){
                if( nexsOrderContext.getIsFrameQcFailed()) {
                    String status =WMSDbUtils.getStatusOfShipment(nexsOrderContext);
                    Assert.assertEquals(status, "IN_PICKING", " Frame status is not in picking, found: " + status);
                } else if(nexsOrderContext.getIsLensQcFail() && !nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("AUTO_JIT") &&  !nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("MANUAL_JIT")){
                    String status =WMSDbUtils.getStatusOfLens(nexsOrderContext);
                    Assert.assertEquals(status, "IN_PICKING", " Lens status is not in picking, found: " + status);
                } else if(nexsOrderContext.getIsLensQcFail() &&((nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("AUTO_JIT") || nexsOrderContext.getOrderProcessingType().equalsIgnoreCase("MANUAL_JIT")))){
                    String status =WMSDbUtils.getStatusOfLens(nexsOrderContext);
                    Assert.assertEquals(status, "JIT_PROCESSING", " Lens status is not in picking, found: " + status);
                }
                else {
                    throw new RuntimeException("Order is either reassigned or not in IN_PICKING state");
                }
            }
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
