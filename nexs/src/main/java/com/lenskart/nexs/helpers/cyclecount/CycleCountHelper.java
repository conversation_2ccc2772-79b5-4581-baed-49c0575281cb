package com.lenskart.nexs.helpers.cyclecount;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@SuperBuilder
@Slf4j
public class CycleCountHelper extends NexsBaseHelper implements ServiceHelper {
    private NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        // Step 1: Scan Location
        CycleCountScanLocationHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();

        // Step 2: Scan Item
        CycleCountScanItemHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();

        // Step 3: Scan Complete
        CycleCountScanCompleteHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();

        return this;
    }

    @Override
    public ServiceHelper validate() {
        // Optional: Consolidated validation logic
        return this;
    }

    @Override
    public ServiceHelper test() {
        return init()
                .process()
                .validate();
    }
}
