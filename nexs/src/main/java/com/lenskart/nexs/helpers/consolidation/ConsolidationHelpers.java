package com.lenskart.nexs.helpers.consolidation;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@SuperBuilder
@Slf4j
public class ConsolidationHelpers extends NexsBaseHelper implements ServiceHelper {
    
    NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
        log.info("Starting consolidation process for facility: {}", nexsOrderContext.getFacilityCode());
        return this;
    }

    @Override
    public ServiceHelper process() {
        // Step 1: Scan Item (only for specific facilities)
        if (nexsOrderContext.getFacilityCode().equals(Constants.BHIWADI_WAREHOUSE_FACILITY) ||
                nexsOrderContext.getFacilityCode().equals(Constants.UAE_WAREHOUSE_FACILITY)) {
            
            ScanItemHelper scanItemHelper = ScanItemHelper.builder()
                    .nexsOrderContext(nexsOrderContext)
                    .build();
            scanItemHelper.test();
            
            // Extract box code from scan response if available
            if (scanItemHelper.getResponse() != null) {
                try {
                    String boxCode = RestUtils.getValueFromResponse(scanItemHelper.getResponse(), "data.boxBarCode").toString();
                    if (boxCode != null && !boxCode.isEmpty()) {
                        nexsOrderContext.setBoxCode(boxCode);
                        log.info("Box code from scan API: {}", boxCode);
                    }
                } catch (Exception e) {
                    log.debug("No box code found in scan response, will generate one if needed");
                }
            }
            
            // Step 2: Allocate Item (if needed)
            AllocateItemHelper.builder()
                    .nexsOrderContext(nexsOrderContext)
                    .build()
                    .test();
        }
        
        // Step 3: Scan Item in Packing
        ScanItemInPackingHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
        
        // Step 4: Order Flush
        OrderFlushHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
        
        return this;
    }

    @Override
    public ServiceHelper validate() {
        log.info("Consolidation process completed successfully for store: {}", nexsOrderContext.getStoreCode());
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}