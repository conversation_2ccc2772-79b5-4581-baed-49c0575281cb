package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_CREATE_SUMMARY;
@SuperBuilder
public class CreateSummaryHelper extends NexsBaseHelper implements ServiceHelper {
    @Getter
    Response response;
    NexsOrderContext nexsOrderContext;
    String location, category;


    @Override
    public ServiceHelper init() {

       nexsOrderContext.setRePickStatus("");
       nexsOrderContext.setShipmentType("null");
       nexsOrderContext.setLocationType("null");

       headers = getHeaders(nexsOrderContext);
       queryParams = getQueryParamsForCreateSummary(nexsOrderContext.getRePickStatus(),nexsOrderContext.getShipmentType(),nexsOrderContext.getLocationType());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PICKING_CREATE_SUMMARY.getUrl(Map.of("location", location, "category", category)), headers, queryParams,null);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }

}
