package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.catalog.GetProductDetailsHelper;
import com.lenskart.nexs.helpers.catalog.ProductPriceHelper;
import com.lenskart.nexs.helpers.vendor.VendorSearchHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class InwardPoProcessHelper extends NexsBaseHelper implements ExecutionHelper {

    @Getter
    @Setter
    NexsOrderContext nexsOrderContext;
    @Getter
    @Setter
    NexsInwardContext nexsInwardContext;
    Response responce;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        VendorSearchHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
        GetProductDetailsHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
        ProductPriceHelper productPriceHelper = ProductPriceHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build();
        productPriceHelper.test();
        responce = productPriceHelper.getResponse();
        nexsInwardContext.setCgstRate(responce.jsonPath().getDouble("data.hsn_tax_info.cgst_rate"));
        nexsInwardContext.setIgstRate(responce.jsonPath().getDouble("data.hsn_tax_info.igst_rate"));
        nexsInwardContext.setSgstRate(responce.jsonPath().getDouble("data.hsn_tax_info.sgst_rate"));
        nexsInwardContext.setUgstRate(responce.jsonPath().getDouble("data.hsn_tax_info.ugst_rate"));
        nexsInwardContext.setPriceWithTaxes(responce.jsonPath().getDouble("data.price_with_taxes"));

        if(nexsInwardContext.isUiFlowEnabled()){
            GeneratePoNumHelper.builder().nexsOrderContext(nexsOrderContext).nexsInwardContext(nexsInwardContext).build().test();
            SubmitPoHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
            try {
                SubmitForApprovalHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
            }catch(AssertionError e){
                AwaitUtils.sleepSeconds(30);
                SubmitForApprovalHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
            }
            try {
                PoApproveRejectHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
            }catch(AssertionError e){
                AwaitUtils.sleepSeconds(30);
                PoApproveRejectHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
            }
        }
        else{
            GeneratePoDetailsHelper generatePoDetailsHelper = GeneratePoDetailsHelper.builder().nexsInwardContext(nexsInwardContext).build();
            generatePoDetailsHelper.test();
            nexsInwardContext.setPoNumber(RestUtils.getValueFromResponse(generatePoDetailsHelper.getResponse(),"data").toString());
        }
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
