package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.api.ImsService;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.validator.picking.PickingCompletionValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_DYNAMIC_PICKING;


/**
 * Helper class to transition order from IN_PICKING to PICKED state.
 * Handles the business logic for completing the picking process.
 */
@SuperBuilder
@Slf4j
public class PickingCompletionHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    JSONObject payload;
    Response response;
    List<String> barcodes;

    @Override
    public ServiceHelper init() {
        logOperationStart("Picking completion Started");
        headers = getHeaders(nexsOrderContext);
        barcodes = new ArrayList<>();
        return this;
    }

    @Override
    public ServiceHelper process() {
        log.info("Picking completion process {} " + nexsOrderContext.getOrderItemResponses());
        nexsOrderContext.getOrderItemResponses().forEach(orderItemResponse -> {
            log.info(orderItemResponse.getStatus());
            if (orderItemResponse.getStatus().equals("IN_PICKING")) {
                nexsOrderContext.setProductId(String.valueOf(orderItemResponse.getProduct_id()));
                log.info("productID {}", nexsOrderContext.getProductId());
                String barcode = ImsService.fetchBarcodeForGivenPid(Constants.AVAILABLE, Constants.GOOD, true, nexsOrderContext.getFacilityCode(), nexsOrderContext.getProductId(), Constants.AVAILABLE);
                nexsOrderContext.setBarcode(barcode);
                if(orderItemResponse.getItemType().toString().equals("LEFTLENS") && nexsOrderContext.getIsLensPickingEnabled())
                    nexsOrderContext.setLeftLensBarcode(barcode);
                if(orderItemResponse.getItemType().toString().equals("FRAME") && nexsOrderContext.getIsFramePickingEnabled())
                    nexsOrderContext.setFrameBarcode(barcode);
                if(orderItemResponse.getItemType().toString().equals("RIGHTLENS") && nexsOrderContext.getIsLensPickingEnabled())
                    nexsOrderContext.setRightLensBarcode(barcode);
                log.info("LeftLensBarcode {}", nexsOrderContext.getLeftLensBarcode());
                try {
                    response = RestUtils.post(PICKING_DYNAMIC_PICKING.getUrl(Map.of("orderItemId", String.valueOf(orderItemResponse.getId()),
                            "barcode", nexsOrderContext.getBarcode())), headers, null, 200);
                    barcodes.add(barcode);
                } catch (Exception e) {
                    log.info(response.asPrettyString());
                    if (response.jsonPath().getString("meta.message")
                            .contains("Barcode is already to assigned to another item")) {
                        barcode = ImsService.fetchBarcodeForGivenPid(Constants.AVAILABLE, Constants.GOOD, true, nexsOrderContext.getFacilityCode(), nexsOrderContext.getProductId(), Constants.AVAILABLE, "3");
                        nexsOrderContext.setFrameBarcode(barcode);
                        nexsOrderContext.setBarcode(barcode);
                        response = RestUtils.post(PICKING_DYNAMIC_PICKING.getUrl(Map.of("orderItemId", String.valueOf(orderItemResponse.getId()),
                                "barcode", nexsOrderContext.getFrameBarcode())), headers, null, 200);
                        barcodes.add(barcode);
                    }
                }
            } else if (orderItemResponse.getStatus().equals("CREATED") && !nexsOrderContext.getOrderProcessingType().equals("AUTO_JIT")) {
                log.info("Order item is in CREATED state it should be in IN_PICKING state {}" + nexsOrderContext.getOrderProcessingType());
                throw new RuntimeException("Order item is in CREATED state it should be in IN_PICKING state");
            } else {
                if(orderItemResponse.getItemType().toString().equals("LEFTLENS") && nexsOrderContext.getIsLensPickingEnabled())
                    nexsOrderContext.setLeftLensBarcode(orderItemResponse.getBarcode());
                if(orderItemResponse.getItemType().toString().equals("FRAME") && nexsOrderContext.getIsFramePickingEnabled())
                    nexsOrderContext.setFrameBarcode(orderItemResponse.getBarcode());
                if(orderItemResponse.getItemType().toString().equals("RIGHTLENS") && nexsOrderContext.getIsLensPickingEnabled())
                    nexsOrderContext.setRightLensBarcode(orderItemResponse.getBarcode());
                nexsOrderContext.setBarcode(orderItemResponse.getBarcode());
                barcodes.add(orderItemResponse.getBarcode());
            }
        });
        log.info("Barcodes {}", barcodes);
        nexsOrderContext.setBarcodes(barcodes);
        if(barcodes.isEmpty()){
            throw new RuntimeException("Unable to pick the products");
        }
        logOperationComplete("Picking completion");
        return this;
    }

    @Override
    public ServiceHelper validate() {
       log.info("Picking completion validation");
        PickingCompletionValidator validator = PickingCompletionValidator.builder().nexsOrderContext(nexsOrderContext).build();
        validator.validateNode();
        validator.validateDBEntities();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.IN_PICKING;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.PICKED;
    }


    @Override
    public void executeTransition() {
        validateTransition();
        test();

    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}
