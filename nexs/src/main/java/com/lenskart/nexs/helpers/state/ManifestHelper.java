package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.scm.database.ScmQueries;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.*;

@SuperBuilder
@Slf4j
public class ManifestHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {
    Response response;
    JSONObject payload;
    String shippingProvider;

    @Override
    public ServiceHelper init() {
        logOperationStart("Manifest initiation");
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals("AWB_CREATED") ||
                WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals("INVOICED")) {
            response = RestUtils.get(MANIFEST_FETCH_CHANNEL.getUrl(), headers, null, 200);
            String channelType = nexsOrderContext.getIsShipToStore() ? "STS" : "STC";
            nexsOrderContext.setChannelType(channelType);

            queryParams = Map.of("page", "0", "size", "1000");


            response = RestUtils.get(MANIFEST_FETCH_SHIPPING_PROVIDER.getUrl(Map.of("channel", nexsOrderContext.getChannelType())),
                    headers, queryParams, 200);

            JSONArray shippingProviderArray = new JSONObject(response.asString()).getJSONArray("data");

            shippingProvider = null;
            for (int i = 0; i < shippingProviderArray.length(); i++) {
                if (nexsOrderContext.getShippingProviderCode()
                        .equals(shippingProviderArray.getJSONObject(i).getString("shippingProviderCode"))) {
                    shippingProvider = shippingProviderArray.getJSONObject(i).getString("shippingProvider");
                    break;
                }
            }
            nexsOrderContext.setShippingProvider(shippingProvider);
            log.info("Shipping provider code: {}", nexsOrderContext.getShippingProviderCode());
            log.info("Shipping provider: {}", nexsOrderContext.getShippingProvider());

            payload = NexsRequestBuilder.getSaveManifestPayload(nexsOrderContext);
            response = RestUtils.post(MANIFEST_SAVE.getUrl(), headers, payload.toString(), 200);
            nexsOrderContext.setManifestId((String) RestUtils.getValueFromResponse(response, "data.manifestId"));
            log.info("Manifest id: {}", nexsOrderContext.getManifestId());


            response = RestUtils.get(MANIFEST_FETCH_MANIFEST.getUrl(Map.of("manifestNumber", nexsOrderContext.getManifestId())),
                    headers, null, 200);
            AwaitUtils.sleepSeconds(6);
            response = RestUtils.post(MANIFEST_ADD_SHIPMENT.getUrl(Map.of("manifestNumber", nexsOrderContext.getManifestId(),
                    "awbNumber", nexsOrderContext.getShippingId())), headers, null, 200);
            AwaitUtils.sleepSeconds(3);
        } else {
            log.info("Shipment is already in manifest");
            List<Map<String, Object>> manifestId = MySQLQueryExecutor
                    .executeQuery(Cluster.SCM_CLUSTER.getClusterName(),
                            Constants.MANIFEST_DB,
                            ScmQueries.GET_MANIFEST_ID,
                            nexsOrderContext.getShippingId());
            nexsOrderContext.setManifestId(manifestId.getFirst().get("shipping_manifest_id").toString());
        }
        logOperationStart("Manifest Completion");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.AWB_CREATED;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.READY_TO_SHIP;
    }


    @Override
    public void executeTransition() {
        validateTransition();
        test();

    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}
