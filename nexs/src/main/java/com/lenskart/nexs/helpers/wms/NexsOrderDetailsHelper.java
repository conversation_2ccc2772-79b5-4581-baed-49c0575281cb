package com.lenskart.nexs.helpers.wms;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.PickingQueries;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.wms.enums.OrderItemType;
import com.lenskart.nexs.wms.response.order.OrderDetailsResponse;
import com.lenskart.scm.database.ScmDbUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;


import static com.lenskart.commons.model.PowerTypes.CONTACT_LENS;
import static com.lenskart.nexs.endpoints.NexsEndpoints.WMS_ORDER_DETAILS_OVERVIEW;
import static com.lenskart.nexs.endpoints.NexsEndpoints.WMS_ORDER_DETAILS_WITH_ID;

@SuperBuilder
@Slf4j
public class NexsOrderDetailsHelper extends NexsBaseHelper implements ServiceHelper {

    NexsOrderContext nexsOrderContext;
    OrderContext orderContext;
    Response response;
    NexsOrderContext.Headers nexsOrderContextHeader;
    OrderDetailsResponse orderDetailsResponse;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        statusCode = nexsOrderContext.getStatusCode();
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (nexsOrderContext.getShippingId().isEmpty()) {
            throw new RuntimeException("Shipping id cannot be null or empty");
        } else {
            queryParams = getQueryParams(nexsOrderContext.getShippingId());
            response = RestUtils.get(WMS_ORDER_DETAILS_OVERVIEW.getUrl(), headers, queryParams, 200);
            String shipmentStatus = response.jsonPath().getString("data.orderItemHeaderResponse.shipmentResponse.status");
            if (shipmentStatus != null && shipmentStatus.equalsIgnoreCase("DISPATCHED")) {
                nexsOrderContext.setShipmentStatus(shipmentStatus);
            }
            List<Object> shipmentLists = response.jsonPath().getList("data.shipmentLists");
            for (int i = 0; i < shipmentLists.size(); i++) {
                String shipmentId = response.jsonPath().getString("data.shipmentLists[" + i + "].shipmentId");
                if (shipmentId.equals(nexsOrderContext.getShippingId())) {
                    String awbNo = response.jsonPath().getString("data.shipmentLists[" + i + "].awbNo");
                    nexsOrderContext.setAwdNumber((awbNo == null || awbNo.trim().isEmpty()) ? "" : awbNo);
                    nexsOrderContext.setFacilityCode(response.jsonPath().getString("data.shipmentLists[" + i + "].facility"));
                    break;
                }
                nexsOrderContextHeader.setFacilityCode(nexsOrderContext.getFacilityCode());
            }

            log.info("AWB number: {}", nexsOrderContext.getAwdNumber());
            log.info("Facility code: {}", nexsOrderContext.getFacilityCode());

            response = RestUtils.get(WMS_ORDER_DETAILS_WITH_ID.getUrl(Map.of("shippingPackageId", nexsOrderContext.getShippingId())), headers, null, 200);

            orderDetailsResponse = parseResponse(RestUtils.getValueFromResponse(response, "data"), OrderDetailsResponse.class);
            nexsOrderContext.setIncrementId(String.valueOf(orderDetailsResponse.getIncrementId()));
            nexsOrderContext.setNexsOrderId(String.valueOf(orderDetailsResponse.getNexsOrderId()));
            nexsOrderContext.setIsShipToStore(orderDetailsResponse.getOrderItemHeaderResponse().getShipToStoreRequired());
            nexsOrderContext.setNavChannel(orderDetailsResponse.getNavChannel());
            nexsOrderContext.setWmsOrderId(orderDetailsResponse.getOrderItemHeaderResponse().getWmsOrderCode());
            nexsOrderContext.setLegalOwner(orderDetailsResponse.getOrderItemHeaderResponse().getLegalOwner());
            if (String.valueOf(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemType()).equalsIgnoreCase(String.valueOf(OrderItemType.JIT))) {
                String orderSubType = String.valueOf(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemSubType());
                if (orderSubType.equalsIgnoreCase("NONE") || orderSubType.contains("AUTO")) {
                    nexsOrderContext.setOrderProcessingType("AUTO_JIT");
                } else {
                    nexsOrderContext.setOrderProcessingType("MANUAL_JIT");
                }
            } else {
                nexsOrderContext.setOrderProcessingType(String.valueOf(OrderItemType.REGULAR));
            }
            log.info("orderProcessingType: {}", nexsOrderContext.getOrderProcessingType());
            List<String> itemTypes = response.jsonPath().getList("data.orderItemHeaderResponse.orderItemResponses.itemType", String.class);
            nexsOrderContext.setIsLoyaltyItemPresent(itemTypes.contains("LOYALTY"));

            if (orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().getFirst().getItemType().toString().equals(CONTACT_LENS.name())) {
                nexsOrderContext.setProductId(String.valueOf(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().getFirst().getPower().getShellId()));
                nexsOrderContext.setProcessingType(String.valueOf(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().getFirst().getProcessingType()));
                nexsOrderContext.setFittingId(String.valueOf(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().getFirst().getFittingId()));
                nexsOrderContext.setTrayBarcode(String.valueOf(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().getFirst().getLocationId()));
            } else {
                orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().forEach(orderItemResponse -> {
                    if (orderItemResponse.getItemType().toString().equals("FRAME") || orderItemResponse.getItemType().toString().equals("SUNGLASS")
                            || orderItemResponse.getItemType().toString().equals("CONTACT_LENS_SOLUTION")) {
                        nexsOrderContext.setProductId(String.valueOf(orderItemResponse.getProduct_id()));
                        nexsOrderContext.setProcessingType(String.valueOf(orderItemResponse.getProcessingType()));
                        nexsOrderContext.setFittingId(String.valueOf(orderItemResponse.getFittingId()));
                        nexsOrderContext.setTrayBarcode(String.valueOf(orderItemResponse.getLocationId()));

                    } else {
                        log.info("Item type is not in the above if condition {}", orderItemResponse.getItemType());
                        log.info("check the code and add the item type");
                    }

                });
                //       nexsOrderContext.setProductId(String.valueOf(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().getFirst().getProduct_id()));
            }
            if (Objects.nonNull(orderContext)) {
                List<OrderContext.ProductList> productLists = orderContext.getProductLists();
                for (OrderContext.ProductList productList : productLists) {
                    if (productList.getProductId() != null) {
                        if (productList.getProductId().equals(nexsOrderContext.getProductId())) {
                            productList.setShippingPackageId(nexsOrderContext.getShippingId());
                            productList.setFacilityCode(nexsOrderContext.getFacilityCode());
                        }
                    } else if (nexsOrderContext.getNavChannel().contains(Constants.BULK)) {
                        productList.setShippingPackageId(nexsOrderContext.getShippingId());
                        productList.setFacilityCode(nexsOrderContext.getFacilityCode());

                    }
                }
                orderContext.setProductLists(productLists);
            }

            nexsOrderContext.setOrderItemResponses(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses());

            nexsOrderContext.setBarcode(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().getFirst().getBarcode());
            nexsOrderContext.setShipmentStatus(orderDetailsResponse.getStatus());

            if (Objects.nonNull(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().getFirst().getCustomFields().getCustomFields().get("MANGETNTO_ITEM_ID")))
                nexsOrderContext.setMagentoItemId(Integer.parseInt(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().getFirst().getCustomFields().getCustomFields().get("MANGETNTO_ITEM_ID")));
            else
                nexsOrderContext.setMagentoItemId(Integer.parseInt(ScmDbUtils.getMagentoItemId(nexsOrderContext.getShippingId())));


            log.info("Increment id: {}", nexsOrderContext.getIncrementId());
            log.info("Nexs order id: {}", nexsOrderContext.getNexsOrderId());
            log.info("Is ship to store: {}", nexsOrderContext.getIsShipToStore());
            log.info("Nav channel: {}", nexsOrderContext.getNavChannel());
            log.info("Processing type: {}", nexsOrderContext.getProcessingType());
            log.info("WMS order id: {}", nexsOrderContext.getWmsOrderId());
            log.info("Legal owner: {}", nexsOrderContext.getLegalOwner());
            log.info("Is loyalty item present: {}", nexsOrderContext.getIsLoyaltyItemPresent());
            log.info("Barcode: {}", nexsOrderContext.getBarcode());
            log.info("Fitting id: {}", nexsOrderContext.getFittingId());
            log.info("Magento Item Id: {}", nexsOrderContext.getMagentoItemId());
            log.info("Product Id: {}", nexsOrderContext.getProductId());
            log.info("Product Id: {}", nexsOrderContext.getFacilityCode());

            List<Map<String, Object>> pickingStatus = MySQLQueryExecutor
                    .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                            Constants.PICKING_DB,
                            PickingQueries.GET_PICKING_STATUS,
                            nexsOrderContext.getShippingId());
            boolean isAddverbPicking = false;
            try {
                isAddverbPicking = "3".equals(pickingStatus.getFirst().get("status").toString());
            } catch (Exception e) {
                isAddverbPicking = false;
            }
            nexsOrderContext.setIsAddverbPicking(isAddverbPicking);
            log.info("Is Addverb Picking: {}", nexsOrderContext.getIsAddverbPicking());
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}
