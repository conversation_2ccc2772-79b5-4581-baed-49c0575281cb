package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.database.WMSDbUtils;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.testng.Assert;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

import static com.lenskart.nexs.endpoints.NexsEndpoints.WMS_EXPORT_PDF;

@SuperBuilder
@Slf4j
public class PrintInvoiceHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    Response response;
    List<String> terminalStatus;

    @Override
    public ServiceHelper init() {
        logOperationStart("Print Invoice Started");
        headers = getHeaders(nexsOrderContext);
        terminalStatus = Arrays.asList("READY_TO_SHIP", "RETURNED", "CANCELLED");
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {
        if (!terminalStatus.contains(WMSDbUtils.getStatusOfShipment(nexsOrderContext))) {
            if (nexsOrderContext.getNavChannel().contains(Constants.DTC) || nexsOrderContext.getNavChannel().contains(Constants.BULK) || nexsOrderContext.getNavChannel().contains(Constants.OTC)) {
                queryParams = getQueryParamsForInvoice(nexsOrderContext.getShippingId());

                // Use AwaitUtils polling approach to wait for successful invoice PDF export
                boolean success = AwaitUtils.retryOperation(
                        () -> {
                            try {
                                log.debug("Attempting to export invoice PDF for shipping ID: {}", nexsOrderContext.getShippingId());
                                response = RestUtils.get(WMS_EXPORT_PDF.getUrl(), headers, queryParams);

                                if (response.statusCode() == HttpStatus.SC_OK) {
                                    log.info("✅ Invoice PDF export successful for shipping ID: {}", nexsOrderContext.getShippingId());
                                    return true;
                                } else {
                                    log.warn("Invoice PDF export failed with status: {} for shipping ID: {}",
                                            response.statusCode(), nexsOrderContext.getShippingId());
                                    return false;
                                }
                            } catch (Exception e) {
                                log.warn("Exception during invoice PDF export attempt: {}", e.getMessage());
                                return false;
                            }
                        },
                        "Invoice PDF export Retry",
                        4, // max attempts
                        Duration.ofSeconds(7) // 20 second poll interval
                );

                if (!success) {
                    log.error("❌ Invoice PDF export failed after polling timeout for shipping ID: {}",
                            nexsOrderContext.getShippingId());
                    Assert.fail("Invoice PDF export failed after multiple attempts for shipping ID: " +
                            nexsOrderContext.getShippingId());
                }
            }
        }
        logOperationComplete("Print Invoice Completed");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.QC_DONE;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.INVOICED;
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}
