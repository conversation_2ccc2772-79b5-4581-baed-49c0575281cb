package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.constants.Constants;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.state.PrintInvoiceHelper;
import com.lenskart.nexs.helpers.wms.GetShipmentsHelper;
import com.lenskart.nexs.helpers.wms.NexsOrderDetailsHelper;
import com.lenskart.nexs.manager.OrderStateTransitionManager;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;


/**
 * Main orchestrator for NEXS order state management.
 * Similar to JunoOrderCreationHelper but for state transitions.
 * Provides a high-level interface for managing order state transitions.
 */

@SuperBuilder
@Slf4j
public class NexsOrderStateHelper extends NexsBaseHelper implements ExecutionHelper {

    private OrderContext orderContext;
    private NexsOrderContext nexsOrderContext;
    private NexsOrderState targetState;
    private boolean sequentialTransition;
    List<OrderContext.ProductList> productLists;

    @Override
    public ExecutionHelper init() {

        // get this for moving the particular item to a particular state
        if (Objects.nonNull(orderContext)) {
            productLists = orderContext.getProductLists();
        }

        /* Authenticate User */
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Get Shipments */
        GetShipmentsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        if (nexsOrderContext.getShipmentIds() != null && !nexsOrderContext.getShipmentIds().isEmpty()) {
            log.info("Processing {} shipments: {}", nexsOrderContext.getShipmentIds().size(), nexsOrderContext.getShipmentIds());

            // Process each shipment
            for (String shipmentId : nexsOrderContext.getShipmentIds()) {
                log.info("Processing shipment ID: {}", shipmentId);

                // Set the current shipment ID
                nexsOrderContext.setShippingId(shipmentId);

                NexsOrderDetailsHelper.builder()
                        .nexsOrderContext(nexsOrderContext)
                        .orderContext(orderContext)
                        .build()
                        .test();
                if (Constants.ORDER_DISPATCHED.equalsIgnoreCase(nexsOrderContext.getShipmentStatus())) {
                    log.info("validating print invoice for OTC order {}",nexsOrderContext.getIncrementId());
                    PrintInvoiceHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
                   log.info(nexsOrderContext.getNavChannel()," Order is  dispatched, skipping state transition");
                    continue;
                }

                if (!nexsOrderContext.getIsLoyaltyItemPresent()) {
                    // Get the product id and shipment id package
                    productLists = orderContext.getProductLists();
                    log.info("New productLists is: {}", JsonUtils.convertObjectToJsonString(productLists));
                    for (OrderContext.ProductList productList : productLists) {

                        if (Objects.nonNull(productList.getShippingPackageId()) && (productList.getItemId() == nexsOrderContext.getMagentoItemId())) {
                            if (Objects.nonNull(productList.getFinalState()))
                                transitionToFinalState(productList.getFinalState());
                            else
                                transitionToFinalState(NexsOrderState.CREATED);
                            // once the order is transitioned to the final state, reset the state for other shipmentId
                            nexsOrderContext.setCurrentState(NexsOrderState.IN_PICKING);
                        }
                    }
                }
            }
        } else {
            throw new RuntimeException("No shipment id or increment id found in order context");
        }

        return this;
    }


    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }

    /**
     * Transition order to the next state in sequence
     *
     * @return Updated NexsOrderStateManager instance
     */
    public NexsOrderStateHelper transitionToNextState() {
        log.info("Transitioning order {} with shipment Id {} to next state", nexsOrderContext.getIncrementId(), nexsOrderContext.getShippingId());

        try {
            nexsOrderContext = OrderStateTransitionManager.transitionToNextState(nexsOrderContext);
            log.info("Order {} with shipment id {} successfully transitioned to: {}",
                    nexsOrderContext.getIncrementId(), nexsOrderContext.getShippingId(), nexsOrderContext.getCurrentState().getDisplayName());
        } catch (Exception e) {
            log.error("Failed to transition order {} to next state: {}", nexsOrderContext.getIncrementId(), e.getMessage());
            throw e;
        }

        return this;
    }

    /**
     * Transition order to a specific state
     *
     * @param state The target state
     * @return Updated NexsOrderStateManager instance
     */
    public NexsOrderStateHelper transitionToState(NexsOrderState state) {
        log.info("Transitioning order {} to state: {}", nexsOrderContext.getIncrementId(), state.getDisplayName());

        try {
            nexsOrderContext = OrderStateTransitionManager.transitionToState(nexsOrderContext, state);
            log.info("Order {} successfully transitioned to: {}",
                    nexsOrderContext.getIncrementId(), nexsOrderContext.getCurrentState().getDisplayName());
        } catch (Exception e) {
            log.error("Failed to transition order {} to state {}: {}",
                    nexsOrderContext.getIncrementId(), state.getDisplayName(), e.getMessage());
            throw e;
        }

        return this;
    }

    /**
     * Transition order through multiple states to reach final state
     *
     * @param finalState The final target state
     * @return Updated NexsOrderStateManager instance
     */
    public NexsOrderStateHelper transitionToFinalState(NexsOrderState finalState) {
        log.info("Transitioning order {} with shipment Id {} sequentially to final state: {}",
                nexsOrderContext.getIncrementId(), nexsOrderContext.getShippingId(), finalState.getDisplayName());

        try {
            nexsOrderContext = OrderStateTransitionManager.transitionToStateSequentially(nexsOrderContext, finalState);
            log.info("Order {} with shipment id {} successfully reached final state: {}",
                    nexsOrderContext.getIncrementId(), nexsOrderContext.getShippingId(), nexsOrderContext.getCurrentState().getDisplayName());
        } catch (Exception e) {
            log.error("Failed to transition order {} to final state {}: {}",
                    nexsOrderContext.getIncrementId(), finalState.getDisplayName(), e.getMessage());
            throw e;
        }

        return this;
    }

    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }


}
