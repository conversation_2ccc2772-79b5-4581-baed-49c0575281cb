package com.lenskart.nexs.helpers.vendor;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.endpoints.NexsEndpoints;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.nexs.endpoints.NexsEndpoints.VENDOR_SEARCH;

/**
 * Helper class for Vendor Search operations
 * Provides functionality to search for vendors using various criteria
 *
 * <AUTHOR>
 */
@SuperBuilder
@Slf4j
public class VendorSearchHelper extends NexsBaseHelper implements ServiceHelper {

    NexsOrderContext nexsOrderContext;
    @Getter
    Response response;
    String facilityCode;
    NexsInwardContext nexsInwardContext;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        queryParams = getQueryParamsForSearchVendor(nexsInwardContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(VENDOR_SEARCH.getUrl(), headers, queryParams, 200);
        nexsInwardContext.setStateCode(RestUtils.getValueFromResponse(response, "data[0].state_code").toString());
        nexsInwardContext.setCurrency(RestUtils.getValueFromResponse(response, "data[0].currency").toString());
        log.info("Vendor search completed. Status code: {}", response.statusCode());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}