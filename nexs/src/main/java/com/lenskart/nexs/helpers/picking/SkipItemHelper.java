package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.database.PickingDbutils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.testng.Assert;

import java.util.Map;

import static com.lenskart.nexs.constants.Constants.SKIP_REASON;
import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_SKIP;

@SuperBuilder
public class SkipItemHelper extends NexsBaseHelper implements ServiceHelper {
    Response response;
    NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }
    @Override
    public ServiceHelper process() {
        response = RestUtils.put(PICKING_SKIP.getUrl(Map.of("picklistId",nexsOrderContext.getPicklistId(),"reason",SKIP_REASON)),headers, null,null);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        String picklistid =nexsOrderContext.getPicklistId();
        nexsOrderContext.setPicklistId(picklistid);
        String status = PickingDbutils.getPickingDetails(nexsOrderContext).toString();
        Assert.assertEquals(status, "TEMP_NOT_FOUND");
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;


    }

}
