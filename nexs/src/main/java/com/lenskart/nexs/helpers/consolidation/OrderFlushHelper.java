package com.lenskart.nexs.helpers.consolidation;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CONSOLIDATION_ORDER_FLUSH;

@SuperBuilder
@Slf4j
public class OrderFlushHelper extends NexsBaseHelper implements ServiceHelper {
    
    NexsOrderContext nexsOrderContext;
    @Getter
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = NexsRequestBuilder.getOrderFlushPayloadForConsolidation(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        log.info("Waiting 5 seconds before flushing order...");
        AwaitUtils.sleepSeconds(5);
        
        try {
            response = RestUtils.put(CONSOLIDATION_ORDER_FLUSH.getUrl(), headers, payload.toString(), 200);
        } catch (Exception e) {
            log.warn("First flush attempt failed, retrying after 5 seconds...");
            AwaitUtils.sleepSeconds(5);
            response = RestUtils.put(CONSOLIDATION_ORDER_FLUSH.getUrl(), headers, payload.toString(), 200);
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        List<Map<String, Object>> storeConsolidationInfo = WMSDbUtils.getStoreConsolidationInfo(nexsOrderContext);
        log.info("Store consolidation info after flush: {}", storeConsolidationInfo);
        
        // Uncomment when ready to validate flush status
        // assert storeConsolidationInfo.getFirst().get("box_status").equals("FLUSHED");
        
        log.info("Order flush completed successfully");
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}