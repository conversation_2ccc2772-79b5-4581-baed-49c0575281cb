package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.GrnRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.ADD_GRN_PID;

@SuperBuilder
public class AddGrnPidHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    NexsInwardContext nexsInwardContext;
    @Getter
    Response response;
    @Setter
    JSONObject meta;
    JSONObject payload;


    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = GrnRequestBuilder.getAddGrnPidPay(nexsInwardContext, meta);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(ADD_GRN_PID.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
