package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.po.POrequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;
import static com.lenskart.nexs.endpoints.NexsEndpoints.PO_SUBMIT_FOR_APPROVAL;


@SuperBuilder
public class SubmitForApprovalHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    NexsInwardContext nexsInwardContext;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        POrequestBuilder.getItemsPayload(nexsInwardContext);
         payload = POrequestBuilder.getPoSumbitPayload(nexsInwardContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        Response response = RestUtils.put(PO_SUBMIT_FOR_APPROVAL.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
