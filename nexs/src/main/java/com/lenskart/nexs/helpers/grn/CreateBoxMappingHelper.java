package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.GrnRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CREATE_BOX_MAPPING;

@SuperBuilder
public class CreateBoxMappingHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    NexsInwardContext nexsInwardContext;
    JSONObject payload;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        nexsInwardContext.setBoxBarcode("BOX" + GenericUtils.genrateRandomNumericString());
        payload = GrnRequestBuilder.getCreateBoxMappingPayload(nexsInwardContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(CREATE_BOX_MAPPING.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
