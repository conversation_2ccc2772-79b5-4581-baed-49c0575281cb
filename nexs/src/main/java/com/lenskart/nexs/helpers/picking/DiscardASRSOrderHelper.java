package com.lenskart.nexs.helpers.picking;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.PICKING_DISCARD_ASRS_ORDER;

@SuperBuilder
public class DiscardASRSOrderHelper extends NexsBaseHelper implements ServiceHelper{

    Response response;
    NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
     NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PICKING_DISCARD_ASRS_ORDER.getUrl(Map.of("shippingPackageId",nexsOrderContext.getShippingId())),headers,null);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
