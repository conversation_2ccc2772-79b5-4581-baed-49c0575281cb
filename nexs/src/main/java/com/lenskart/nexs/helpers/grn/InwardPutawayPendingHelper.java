package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsInwardContext;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class InwardPutawayPendingHelper extends NexsBaseHelper implements ExecutionHelper {

    NexsInwardContext nexsInwardContext;
    NexsOrderContext nexsOrderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        try {
            CloseGrnHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
        }catch (AssertionError e)
        {
            CloseGrnHelper.builder().nexsInwardContext(nexsInwardContext).nexsOrderContext(nexsOrderContext).build().test();
        }
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
