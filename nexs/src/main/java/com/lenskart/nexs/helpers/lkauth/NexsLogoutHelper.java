package com.lenskart.nexs.helpers.lkauth;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.LKAUTH_LOGOUT;

@SuperBuilder
@Slf4j
public class NexsLogoutHelper extends NexsBaseHelper implements ServiceHelper {

    Response response;
    JSONObject payload;
    NexsOrderContext nexsOrderContext;
    NexsOrderContext.Headers nexsOrderContextHeader;

    @Override
    public ServiceHelper init() {
        headers = getLogoutHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(LKAUTH_LOGOUT.getUrl(), headers, null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
