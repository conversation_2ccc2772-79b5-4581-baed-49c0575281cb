package com.lenskart.nexs.util;

import com.lenskart.commons.constants.Constants;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.nexs.database.CidDbUtils;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.helpers.ims.StockInAndOutV2Helper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.scm.database.ScmDbUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.testng.Assert;

import java.util.List;
import java.util.Map;

@Slf4j
public class IMSUtil {

    public static String createBarcodeEntryInIMS(NexsOrderContext nexsOrderContext) {
        String count, barcode;
        String operation = "EXTERNAL_RECEIVE_GOOD_TRANSFER";
        barcode = generateUniqueBarcode(nexsOrderContext);
        StockInAndOutV2Helper.builder()
                .barcode(barcode)
                .facility(nexsOrderContext.getFacilityCode())
                .location(nexsOrderContext.getLocationCode())
                .operation(operation)
                .pid(Integer.parseInt(nexsOrderContext.getProductId()))
                .legalOwner(nexsOrderContext.getLegalOwner())
                .updatedBy("Automation")
                .build()
                .test();
        return barcode;
    }

    public static JSONObject GAA() {
        return new JSONObject(Map.of(
                "condition", "GOOD",
                "status", "AVAILABLE",
                "availability", "AVAILABLE"
        ));
    }


    public static void assertStockAdjustmentUpdate(String pid, String legalOwner)  {
        AwaitUtils.sleepSeconds(4);
        int ss1 = CidDbUtils.getQuantityFromWarehouseBlockedInventoryBasedOnFacilities(legalOwner, pid, Constants.STOCK_ADJUSTMENT_FACILITIES);
        int ss2  = CidDbUtils.getQuantityFromWarehouseInventoryBasedOnFacilities(legalOwner, pid, Constants.STOCK_ADJUSTMENT_FACILITIES);

        int stockAdjustmentCalculatedInventory = ss2-ss1;
        log.info("stockAdjustmentCalculatedInventory - {}", stockAdjustmentCalculatedInventory);
        String stockAdjustmentInventory = ScmDbUtils.getQuantityFromStockAdjustment(pid).getFirst().get("qty").toString();
        log.info("stockAdjustmentCalculatedInventory - {}", stockAdjustmentCalculatedInventory);
        log.info("stockAdjustmentInventory - {}", stockAdjustmentInventory);
        Assert.assertEquals(Integer.valueOf(stockAdjustmentCalculatedInventory),
                Integer.valueOf(stockAdjustmentInventory));
        Assert.assertTrue(Math.abs(stockAdjustmentCalculatedInventory
                - Integer.parseInt(stockAdjustmentInventory)) <= 1);
    }

    public static String generateUniqueBarcode(NexsOrderContext nexsOrderContext) {
        String barcode;
        do {
            barcode = RandomStringUtils.randomNumeric(5) + RandomStringUtils.randomAlphabetic(5).toUpperCase();
            nexsOrderContext.setBarcode(barcode);
        } while (!ImsDbUtils.getBarcodeCount(nexsOrderContext).getFirst().get("count(*)").toString().equals("0"));
        log.info("Generated Barcode : {}", barcode);
        return barcode;
    }

    public static void validateCidUpdatePostconditions(JSONObject transitionState, int[] expectedInventory, NexsOrderContext nexsOrderContext) {
        JSONArray cidUpdates = transitionState.getJSONArray("cidUpdates");
        AwaitUtils.sleepSeconds(4);

        for (int i = 0; i < cidUpdates.length(); i++) {
            JSONObject update = cidUpdates.getJSONObject(i);
            String cond = update.getString("condition");
            String stat = update.getString("status");
            String avail = update.getString("availability");

            if (expectedInventory[i] < 0) continue;

            int actual = CidDbUtils.getQuantityFromWarehouseInventory(
                    nexsOrderContext.getProductId(),
                    nexsOrderContext.getFacilityCode(),
                    cond, avail, stat,
                    nexsOrderContext.getLegalOwner()
            );

            Assert.assertEquals(actual, expectedInventory[i], "Mismatch in CID inventory for index " + i);
        }

        if (JsonUtils.compareJson(IMSUtil.GAA(), transitionState.getJSONObject("state"))) {
            int updatedStoreFrontInventory = CidDbUtils.getQuantityFromStorefrontInventory(nexsOrderContext.getProductId(), nexsOrderContext.getLegalOwner());
            log.info("Updated Storefront Inventory = {}", updatedStoreFrontInventory);
            IMSUtil.assertStockAdjustmentUpdate(nexsOrderContext.getProductId(), nexsOrderContext.getLegalOwner());
        }
    }


}
