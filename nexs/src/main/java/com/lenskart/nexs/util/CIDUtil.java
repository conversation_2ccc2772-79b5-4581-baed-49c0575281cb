package com.lenskart.nexs.util;

import com.lenskart.commons.model.ProductId;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.cid.GetConsolidatedInvInfo;
import com.lenskart.nexs.helpers.ims.AddGAAInventoryHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.stream.IntStream;

import static com.lenskart.nexs.util.CommonNexsUtil.legalOwnerMapping;

@Slf4j
public class CIDUtil {

    private static final Set<String> EXCLUDED_PRODUCT_IDS = Set.of("19873", "19872", "128269", "303961", "208251","216617");


    public static void ensureMinimumInventory(ProductId product, int requiredInventory) {
        String country = product.name().split("_")[0];
        ensureMinimumInventoryInternal(country, product.getProductId(), requiredInventory);
    }

    public static void ensureMinimumInventory(String country, String productId, int requiredInventory) {
        ensureMinimumInventoryInternal(country, productId, requiredInventory);
    }

    private static void ensureMinimumInventoryInternal(String country, String productId, int requiredInventory) {
        if (EXCLUDED_PRODUCT_IDS.contains(productId)) {
            log.info("Skipping inventory addition for excluded product ID: {}", productId);
            return;
        }

        List<String> facilities = switch (country) {
            case "IN" -> List.of("NXS2", "QNXS2");
            case "SG", "ID", "TH" -> List.of("SGNXS1");
            case "AE", "SA" -> List.of("UAE1");
            case "US" -> List.of("NXS2");
            default -> List.of();
        };

        if (facilities.isEmpty()) {
            log.warn("No facilities configured for country: {}", country);
            return;
        }

        for (String facility : facilities) {
            long availableInventory = fetchInventory(productId, facility);
            log.info("Facility: {}, Available Inventory: {}, Required: {}", facility, availableInventory, requiredInventory);

            if (availableInventory < requiredInventory) {
                int inventoryToAdd = (int) (requiredInventory - availableInventory);
                log.info("Adding {} units to facility {} to meet required inventory", inventoryToAdd, facility);
                addInventory(facility, productId, inventoryToAdd);
            } else {
                log.info("No inventory addition needed for facility {}", facility);
            }
        }
    }

    private static void addInventory(String facility, String productId, int count) {
        IntStream.range(0, count)
                .parallel()
                .forEach(i -> AddGAAInventoryHelper.builder()
                        .location(Constants.TEST_LOCATION)
                        .facility(facility)
                        .pid(Integer.parseInt(productId))
                        .legalOwner(legalOwnerMapping(facility))
                        .build()
                        .test());
    }

    private static long fetchInventory(String productId, String facility) {
        NexsOrderContext context = NexsOrderContext.builder()
                .productId(productId)
                .facilityCode(facility)
                .build();

        GetConsolidatedInvInfo invInfo = GetConsolidatedInvInfo.builder()
                .nexsOrderContext(context)
                .build();

        invInfo.test();
        return invInfo.getConsolidatedInvInfo().getAvailableInventory();
    }
}