package com.lenskart.nexs.requestBuilder;

import org.json.JSONObject;
import com.lenskart.nexs.model.NexsOrderContext;
import org.json.JSONArray;

public class TransfersRequestBuilder {
    public static JSONObject ScanBarcodePayload(String barcode, String boxBarcode, String unicomTransferCode, String transferCode) {
        JSONObject payload = new JSONObject();
        payload.put("barcode", barcode);
        payload.put("box_barcode", boxBarcode);
        payload.put("unicom_transfer_code", unicomTransferCode);
        payload.put("transfer_code", transferCode);
        return payload;
    }

    public static JSONObject createTransferPayload(NexsOrderContext.TransferContext transferContext) {
        JSONObject payload = new JSONObject();
        payload.put("transferCode", JSONObject.NULL);
        payload.put("sourceFacility", transferContext.getSourceFacility());
        payload.put("destFacility", transferContext.getDestFacility());

        JSONArray itemsArray = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("productId", transferContext.getProductId());
        item.put("productDesc", transferContext.getProductDesc());
        item.put("quantity", transferContext.getQuantity());
        item.put("avail_qty", transferContext.getAvailQty());
        item.put("id", transferContext.getTransferItemId());
        item.put("isDisable", transferContext.getIsDisable());

        itemsArray.put(item);
        payload.put("items", itemsArray);

        return payload;
    }
}

