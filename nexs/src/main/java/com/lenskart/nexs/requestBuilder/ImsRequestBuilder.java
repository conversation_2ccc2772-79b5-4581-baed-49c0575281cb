package com.lenskart.nexs.requestBuilder;

import com.lenskart.nexs.ims.request.StockRequestV2;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;

import java.util.List;

public class ImsRequestBuilder {

    public static UpdateStocksRequestV2 stockInAndOutV2(String barcode, String facility, String location, String operation,
                                                        int pid, String legalOwner, String updatedBy) {
        var stockItem = new StockRequestV2();
        stockItem.setBarcode(barcode);
        stockItem.setFacility(facility);
        stockItem.setLocation(location);
        stockItem.setPid(pid);
        stockItem.setLegalOwner(legalOwner);
        stockItem.setUpdatedBy(updatedBy);

        var request = new UpdateStocksRequestV2();
        request.setStockRequestV2List(List.of(stockItem));
        request.setOperation(operation);
        return request;
    }
}
