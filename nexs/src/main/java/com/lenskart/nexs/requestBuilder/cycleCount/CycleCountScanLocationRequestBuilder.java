package com.lenskart.nexs.requestBuilder.cycleCount;

import org.json.JSONObject;

public class CycleCountScanLocationRequestBuilder {
    public static JSONObject getCycleCountPayload(String loctionCode, String itemType) {
        JSONObject payload = new JSONObject();
        payload.put("locationCode", loctionCode);
        payload.put("scanType", itemType);
        return payload;
    }

    public static JSONObject getCycleCountItemPayload(String barcode, String loctionCode) {
        JSONObject payload = new JSONObject();
        payload.put("locationCode", loctionCode);
        payload.put("barcode", barcode);
        return payload;
    }
}
