package com.lenskart.nexs.requestBuilder;

import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import org.json.JSONObject;

public class PutawayRequestBuilder {
    public static String continousScanPayload(NexsOrderContext nexsOrderContext,String barcode) {
        JSONObject request = new JSONObject();
        request.put("barcode", barcode);
        if(Constants.BHIWADI_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode())){
            request.put("locationBarcode", Constants.BHIWADI_WAREHOUSE_PUTAWAY_LOCATION);
        }
        if(Constants.MANESAR_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode())){
            request.put("locationBarcode", Constants.MANESAR_WAREHOUSE_PUTAWAY_LOCATION);
        }
        if(Constants.UAE_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode())){
            request.put("locationBarcode", Constants.UAE_WAREHOUSE_PUTAWAY_LOCATION);
        }
        if(Constants.SINGAPORE_WAREHOUSE_FACILITY.equalsIgnoreCase(nexsOrderContext.getFacilityCode())){
            request.put("locationBarcode", Constants.SINGAPORE_WAREHOUSE_PUTAWAY_LOCATION);
        }
        request.put("putawayId", nexsOrderContext.getPutawayId());
        return request.toString();
    }
}
