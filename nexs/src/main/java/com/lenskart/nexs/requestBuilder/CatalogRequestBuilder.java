package com.lenskart.nexs.requestBuilder;

import com.lenskart.nexs.model.NexsInwardContext;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class CatalogRequestBuilder {

    public static JSONObject getProductDetailsPayload(int productId, String stateCode, String supplierId) {
        JSONObject request = new JSONObject();
        List<Integer> productIds = new ArrayList<>();
        productIds.add(productId);
        request.put("product_ids", productIds);
        request.put("state_code", stateCode);
        request.put("supplier_id", supplierId);
        return request;
    }
    public static JSONObject getProductPricePayload(NexsInwardContext nexsInwardContext) {
        JSONObject request = new JSONObject();
        request.put("currency", nexsInwardContext.getCurrency());
        request.put("facility_state_code", nexsInwardContext.getStateCode());
        request.put("product_id", nexsInwardContext.getProductId());
        request.put("quantity", nexsInwardContext.getQuantity());
        request.put("vendor_code", nexsInwardContext.getVendorCode());
        request.put("vendor_unit_cost_price", nexsInwardContext.getVendorUnitCostPrice());
        request.put("hsn_tax_info", nexsInwardContext.getTaxInfo());
        return request;
    }

}
