package com.lenskart.nexs.requestBuilder;

import com.lenskart.nexs.model.NexsOrderContext;
import org.json.JSONArray;
import org.json.JSONObject;

public class PickingRequestBuilder {

    public static String createPickingSummaryPayload(String shippingPackageID) {
        JSONObject payload = new JSONObject();
        payload.put("shippingPackageID", shippingPackageID);
        return payload.toString();
    }

    public static String DoClosePickingPayload(String shippingPackageID) {
        JSONObject payload = new JSONObject();
        payload.put("shippingPackageID", shippingPackageID);
        return payload.toString();

    }
    public static String lensScanPayload(String fittingId,String barcode) {
        JSONObject payload = new JSONObject();
        payload.put("fittingId", fittingId);
        payload.put("barCode", barcode);
        return payload.toString();

    }
    public static String lensCompletePayload(NexsOrderContext nexsOrderContext) {
        JSONArray payload = new JSONArray();
        for(String  pickingDetailsId : nexsOrderContext.getPickingDetailsIds()) {
            JSONObject payload1 = new JSONObject();
            payload1.put("pickingDetailsId", pickingDetailsId);
            payload1.put("status", "IN_TRAY");
            payload1.put("trayId", nexsOrderContext.getTrayId());
            payload.put(payload1);
        }
        return payload.toString();

    }
}
