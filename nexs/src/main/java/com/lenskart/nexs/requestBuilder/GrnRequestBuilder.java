package com.lenskart.nexs.requestBuilder;

import com.lenskart.nexs.model.NexsInwardContext;
import org.json.JSONObject;
import java.util.Map;

public class GrnRequestBuilder
{
    public static JSONObject buildBarcodePriceRequest(Map<String, Integer> barcodePidMap, String facilityCode) {
        return new JSONObject()
                .put("barcode_pid", new JSONObject(barcodePidMap))
                .put("facility_code", facilityCode);
    }
    public static JSONObject getAddGrnPidPay(NexsInwardContext nexsInwardContext, JSONObject meta) {

        JSONObject pidObj = new JSONObject();
        pidObj.put("pid", nexsInwardContext.getProductId());
        pidObj.put("estimatedQuantity", 0);
        JSONObject request = new JSONObject();
        request.put("grnCode", nexsInwardContext.getGrnNumber());
        request.put("type", nexsInwardContext.getGrnType());
        request.put("invoice_level", nexsInwardContext.getInvoiceLevel() );
        request.put("pid", pidObj);
        request.put("meta", meta);
        return request;
    }
    public static JSONObject getCreateBoxMappingPayload(NexsInwardContext nexsInwardContext) {
        JSONObject request = new JSONObject();
        request.put("barcode", nexsInwardContext.getBoxBarcode());
        request.put("grn_code", nexsInwardContext.getGrnNumber() );
        request.put("pid", nexsInwardContext.getProductId());
        return request;
    }
    public static JSONObject getScanGrnItemBarcodePayload(NexsInwardContext nexsInwardContext, String itemBarocde) {
        JSONObject request = new JSONObject();
        request.put("barcode", itemBarocde);
        request.put("grn_code", nexsInwardContext.getGrnNumber() );
        request.put("pid", nexsInwardContext.getProductId());
        request.put("lot_no", "");
        request.put("qc_status", "pass");
        request.put("expiry_date_threshold", 0);
        request.put("po_id", nexsInwardContext.getPoNumber());
        request.put("invoice_id", nexsInwardContext.getVendorInvNo() );
        request.put("vendor_code", nexsInwardContext.getVendorCode());
        request.put("unicom_grn_code", "null");
        request.put("invoice_ref_number", nexsInwardContext.getInvoiceRefNum());
        request.put("poQuantity", Integer.valueOf(nexsInwardContext.getQuantity()));
        request.put("legal_owner", nexsInwardContext.getLegalOwner());
        request.put("qc_pass_box_barcode", nexsInwardContext.getBoxBarcode());
        request.put("type", nexsInwardContext.getGrnType());
        request.put("invoice_level", nexsInwardContext.getInvoiceLevel());
        request.put("sampling_percent", 0);
        request.put("product", nexsInwardContext.getProductData());
        return request;
    }
}
