package com.lenskart.nexs.requestBuilder;
import com.lenskart.nexs.model.NexsOrderContext;
import org.json.JSONArray;
import org.json.JSONObject;
public class WmsRequestBuilder {
    public static JSONObject createReassignmentPayload(String shippingPackageID,String selectedfacility) {
        JSONObject payload = new JSONObject();
        payload.put("shippingPackageId", shippingPackageID);
        payload.put("selectedFacility", selectedfacility);
        return payload;
    }
    public static JSONObject getFullfillabilityPayload(NexsOrderContext nexsOrderContext) {
        JSONObject payload = new JSONObject();
        payload.put("nexsOrderId", nexsOrderContext.getNexsOrderId());
        payload.put("isFullfillable", 1);
        return payload;
    }

    public static JSONArray UpdateBoxCountPayload(String shippingPackageID, String boxCount) {
        JSONArray payloadArray = new JSONArray();
        JSONObject payload = new JSONObject();
        payload.put("shippingPackageID", shippingPackageID);
        payload.put("boxCount", boxCount);
        payloadArray.put(payload);
        return payloadArray;
    }
}

