package com.lenskart.nexs.requestBuilder;

import com.lenskart.nexs.model.ProductPriceModel;
import org.json.JSONObject;

public class ProductPriceBuilder {
    public static String ProductPriceModel(String currency, String facilityStateCode,
                                           ProductPriceModel.HsnTaxInfo hsnTaxInfo, String vendorUnitCostPrice,
                                           String productId, String quantity, String vendorCode) {
        // for hsn_code creating new jsonObject
        JSONObject hsnJson = new JSONObject();
        hsnJson.put("hsnCode", hsnTaxInfo.getHsnCode());
        hsnJson.put("stateCode", hsnTaxInfo.getStateCode());
        hsnJson.put("igstPer", hsnTaxInfo.getIgstPer());
        hsnJson.put("cgstPer", hsnTaxInfo.getCgstPer());
        hsnJson.put("sgstPer", hsnTaxInfo.getSgstPer());
        hsnJson.put("ugstPer", hsnTaxInfo.getUgstPer());
        hsnJson.put("flatPer", hsnTaxInfo.getFlatPer());
        hsnJson.put("igstRate", hsnTaxInfo.getIgstRate());
        hsnJson.put("cgstRate", hsnTaxInfo.getCgstRate());
        hsnJson.put("sgstRate", hsnTaxInfo.getSgstRate());
        hsnJson.put("ugstRate", hsnTaxInfo.getUgstRate());

        JSONObject payload = new JSONObject();
        payload.put("currency", currency);
        payload.put("facilityStateCode", facilityStateCode);
        payload.put("hsnTaxInfo", hsnJson);
        payload.put("vendorUnitCostPrice", vendorUnitCostPrice);
        payload.put("productId", productId);
        payload.put("quantity", quantity);
        payload.put("vendorCode", vendorCode);

        return payload.toString();
    }
}
