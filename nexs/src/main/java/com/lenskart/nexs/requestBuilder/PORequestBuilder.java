package com.lenskart.nexs.requestBuilder;

import com.lenskart.nexs.model.NexsOrderContext;
import org.json.JSONObject;

public class PORequestBuilder {
    public static String addInventoryPayload(NexsOrderContext nexsOrderContext) {
        JSONObject payload = new JSONObject();
        payload.put("barcode", nexsOrderContext.getBarcode());
        payload.put("productId", nexsOrderContext.getProductId());
        payload.put("facilityCode", nexsOrderContext.getFacilityCode());
        return payload.toString();
    }

    public static String startRetirePayload(NexsOrderContext nexsOrderContext) {
        JSONObject payload = new JSONObject();
        payload.put("barcode", nexsOrderContext.getBarcode());
        payload.put("facilityCode", nexsOrderContext.getFacilityCode());
        payload.put("newPid", nexsOrderContext.getNewProductId());
        payload.put("oldPid", nexsOrderContext.getProductId());
        return payload.toString();
    }
}
