package com.lenskart.nexs.requestBuilder.po;

import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.nexs.model.NexsInwardContext;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;

public class POrequestBuilder {

    public static JSONObject getPoSumbitPayload(NexsInwardContext nexsInwardContext) {
        JSONObject request = new JSONObject();
        request.put("po_num", nexsInwardContext.getPoNumber());
        request.put("po_type", nexsInwardContext.getPoType());
        request.put("vendor_id", nexsInwardContext.getVendorCode());
        request.put("shipment_type", nexsInwardContext.getShipmentType());
        request.put("shipping_date", GenericUtils.currentDatePlus(1));
        request.put("expiry_date", GenericUtils.currentDatePlus(4));
        request.put("version", 1);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDateTime = dateFormat.format(new Date());
        request.put("current_timestamp", formattedDateTime);
        request.put("items", nexsInwardContext.getItems());

        return request;
    }
    public static void getItemsPayload(NexsInwardContext nexsInwardContext) {
        JSONArray items = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("id", 0);
        item.put("product_id", nexsInwardContext.getProductId());
        item.put("quantity", nexsInwardContext.getQuantity());
        item.put("vendor_unit_cost_price", nexsInwardContext.getVendorUnitCostPrice());
        item.put("cgst_rate", nexsInwardContext.getCgstRate());
        item.put("igst_rate", nexsInwardContext.getIgstRate());
        item.put("sgst_rate", nexsInwardContext.getSgstRate());
        item.put("ugst_rate", nexsInwardContext.getUgstRate());
        item.put("price_with_taxes", nexsInwardContext.getPriceWithTaxes());
        item.put("hsn_classification", nexsInwardContext.getHsnClassification());
        item.put("enabled", true);
        items.put(item);
        nexsInwardContext.setItems(items);
    }

    public static JSONObject getApproveRejectPayload(NexsInwardContext nexsInwardContext) {
        JSONObject request = new JSONObject();
        request.put("po_num", nexsInwardContext.getPoNumber() );
        request.put("status", nexsInwardContext.getStatus());
        request.put("reason", "");
        return request;
    }
    public static JSONObject getGeneratePoDetailsPayload(NexsInwardContext nexsInwardContext) {
        JSONObject request = new JSONObject();
        request.put("po_type", nexsInwardContext.getPoType());
        request.put("vendor_id", nexsInwardContext.getVendorCode());
        request.put("shipping_date", GenericUtils.currentDatePlus(1));
        request.put("expiry_date", GenericUtils.currentDatePlus(4));
        request.put("version", 1);
        request.put("facility_code", nexsInwardContext.getFacilityCode());
        request.put("items", nexsInwardContext.getItems());
        JSONObject obj = new JSONObject();
        obj.put("create_po_model",request );
        return obj;
    }
    public static JSONArray getSaveInvoiceitemsPayload(NexsInwardContext nexsInwardContext) {
        JSONArray items = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("id", "");
        item.put("product_id", nexsInwardContext.getProductId());
        item.put("quantity", Integer.parseInt(nexsInwardContext.getQuantity()));
        item.put("vendor_unit_cost_price", nexsInwardContext.getVendorUnitCostPriceWithTax());
        item.put("cgst_per", nexsInwardContext.getCgstPer());
        item.put("igst_per", nexsInwardContext.getIgstPer());
        item.put("sgst_per", nexsInwardContext.getSgstPer());
        item.put("ugst_per", nexsInwardContext.getUgstPer());
        item.put("flat_per", nexsInwardContext.getFlatPer());
        items.put(item);
        return items;
    }
    public static JSONObject getSaveInvoicePayload(NexsInwardContext nexsInwardContext) {

        JSONObject request = new JSONObject();
        request.put("invoice_ref_no", "");
        request.put("vendor_invoice_number", nexsInwardContext.getVendorInvNo());
        request.put("po_num", nexsInwardContext.getPoNumber());
        request.put("invoice_date", GenericUtils.currentDatePlus(0));
        request.put("version", 0);
        request.put("b2b_invoice_date",JSONObject.NULL);
        request.put("bill_of_entry_amount", JSONObject.NULL);
        request.put("bill_of_entry_date", JSONObject.NULL);
        request.put("bill_of_entry_number", JSONObject.NULL);
        request.put("send_to_party", JSONObject.NULL);
        request.put("handover_party", JSONObject.NULL);
        JSONArray items = getSaveInvoiceitemsPayload(nexsInwardContext);
        request.put("items", items);
        return request;
    }
}
