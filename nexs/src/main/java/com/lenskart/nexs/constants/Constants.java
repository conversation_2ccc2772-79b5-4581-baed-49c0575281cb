package com.lenskart.nexs.constants;

public class
Constants {
    public static final String X_LENSKART_APP_ID = "nexs_search";
    public static final String GOOD = "GOOD";
    public static final String AVAILABLE = "AVAILABLE";
    public static final String ALLOCATED = "ALLOCATED";
    public static final String IN_TRAY = "IN_TRAY";
    public static final String PENDING_CUSTOMIZATION = "PENDING_CUSTOMIZATION";
    public static final String CUSTOMIZATION_COMPLETE = "CUSTOMIZATION_COMPLETE";
    public static final String ASSIGNED = "ASSIGNED";
    public static final String PICKED = "PICKED";
    public static final String UAE_LEGAL_OWNER = "LKAE";
    public static final String IN_LEGAL_OWNER = "LKIN";
    public static final String SG_LEGAL_OWNER = "LKSG";
    public static final String BULK = "Bulk";
    public static final String DTC = "DTC";
    public static final String OTC = "OTC";
    public static final String WMS_DB = "wms";
    public static final String MANIFEST_DB = "manifest";
    public static final String PICKING_DB = "picking";
    public static final String NEXS_DB = "nexs";
    public static final String EMS_DB = "ems";
    public static final String IMS_DB = "nexs_ims";
    public static final String CID_DB = "nexs_cid";
    public static final String PUTAWAY_DB = "putawaydb";
    public static final String ORDER_SENSIE_DB = "order_sensei";
    public static final String GRN_QC_PASS = "GRN_QC_PASS";
    public static final String PUTAWAY_PENDING = "PUTAWAY_PENDING";
    public static final String PUTAWAY_COMPLETE = "PUTAWAY_COMPLETE";
    public static final String EDGING = "EDGING";
    public static final String DONE = "DONE";
    public static final String TEST_LOCATION = "test-1-1";
    public static final String WAVE_SIZE = "1";
    public static final String FR1_WAVE ="FR1_V3";
    public static final String FR0_WAVE ="FR0_V1";
    public static final String DATA_SOURCE = "DB";
    public static final String QC_FAIL_REASON = "Cut Lens - Wrong powers fitted";
    public static final String QC_HOLD = "QC_HOLD";
    public static final String IN_QC = "IN_QC";
    public static final String AUTOREPLENISHMENT = "AUTOREPLENISHMENT";
    public static final String TRANSFER_TYPE = "NRGP";
    public static final String PO_CREATION_ERROR_MESSAGE = "PO IS NOT CREATED FOR ORDER, WE HAVE TRIGGERED THE PO. PLEASE CHECK AFTER SOMETIME";
    public static final String FITTING_ERROR_MESSAGE ="There are multiple trays, please scan with tray Id";
    public static final String EYEFRAME ="EYEFRAME";
    public static final String SUNGLASS= "SUNGLASS";
    public static final String FR1_CATEGORY ="FR1-NON_JIT-NDD";
    public static final String FR0_NDD_CATEGORY ="FR0-NDD";
    public static final String PICKLIST_CREATED="PICKLIST_CREATED";
    public static final String  NEXS_ORDER_ID ="nexs_order_id";
    public static final String FR1_PROCESSING_TYPE ="FR1";
    public static final String FR0_CATEGORY ="FR0";
    public static final String SKIP_REASON="Not Found";
    public static final String DEFAULT_COURIER="NXSSTORE";



    //PREPROD_WAREHOUSE_FACILITY
    public static final String BHIWADI_WAREHOUSE_FACILITY = "QNXS2";
    public static final String MANESAR_WAREHOUSE_FACILITY = "NXS2";
    public static final String UAE_WAREHOUSE_FACILITY = "UAE1";
    public static final String SINGAPORE_WAREHOUSE_FACILITY = "SGNXS1";
    public static final String CONTACTS_CART_FACILITY = "PBR01";

    //PREPROD_WAREHOUSE_FACILITY_PUTAWAY_LOCATION FOR PUTAWAY COMPLETION
    public static final String BHIWADI_WAREHOUSE_PUTAWAY_LOCATION = "QNXS2-ALL-DEFAULT-001-01-34";
    public static final String MANESAR_WAREHOUSE_PUTAWAY_LOCATION = "NXS2-1-1";
    public static final String UAE_WAREHOUSE_PUTAWAY_LOCATION = "UAE1-1-1";
    public static final String SINGAPORE_WAREHOUSE_PUTAWAY_LOCATION = "SGNXS1-SMT-01-01-01-002";



}

