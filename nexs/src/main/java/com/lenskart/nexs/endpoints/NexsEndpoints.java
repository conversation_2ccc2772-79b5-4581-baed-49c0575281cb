package com.lenskart.nexs.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum NexsEndpoints implements BaseEndpoint {

    //Picking Service Endpoints
    PICKING_GET_LOCATION_CATEGORIES("/nexs/order/picking/location/categories", "nexsService"),
    PICKING_SUMMARY("/nexs/order/picking/summary/{$location}/{$categories}/standard", "nexsService"),
    PICKING_CLOSE_PICKING_SUMMARY("/nexs/order/picking/complete/{$id}", "nexsService"),
    PICKING_DISPLAY_PICKLIST("/nexs/order/picking/{$pickingSummaryId}", "nexsService"),
    PICKING_SCAN_BARCODE("/nexs/order/picking/scan/{$picklistId}/{$barcode}", "nexsService"),
    PICKING_VIEW_ORDER("/nexs/order/picking/view/order", "nexsService"),
    PICKING_VALIDATE_FRAME("/nexs/order/picking/validate/frame", "nexsService"),
    PICKING_LENS_PICKING_DETAILS("/nexs/order/picking/details/{$fittingId}", "nexsService"),
    PICKING_SCAN_LENS("/nexs/order/picking/lens/scan", "nexsService"),
    PICKING_COMPLETE_LENS_PICKING("/nexs/order/picking/v2/lens/summary/{$pickingSummaryId}/complete", "nexsService"),
    PICKING_GET_WAVE_DETAILS("/nexs/order/picking/addverb/wave/{$waveId}", "nexsService"),
    PICKING_DISCARD_ASRS_ORDER("/nexs/order/picking/supervisor/discard/asrs/Order/{$shippingPackageId}", "nexsService"),
    PICKING_SYNC_ORDER_TO_ES("/nexs/order/picking/order/item/syncToES", "nexsService"),
    PICKING_DYNAMIC_PICKING("/nexs/order/picking/pick/orderItemId/{$orderItemId}/{$barcode}", "nexsService"),
    PICKING_ADDVERB_CREATE_WAVE("/nexs/order/picking/addverb/wave", "nexsService"),
    PICKING_ADDVERB_PICK_ITEM("/nexs/order/picking/addverb/pick/item", "nexsService"),
    PICKING_ADDVERB_COMPLETE("/nexs/order/picking/addverb/complete", "nexsService"),
    PICKING_CHANGE_ES_STATUS("/nexs/order/picking/supervisor/es/change/status", "nexsService"),
    PICKING_CREATE_PICKING_SUMMARY_DO("/nexs/order/picking/DO/createPickingSummary", "nexsService"),
    PICKING_PICK_BARCODE_DO("/nexs/order/picking/DO/pickBarcode", "nexsService"),
    PICKING_GET_PICKING_SUMMARY_DO("/nexs/order/picking/DO/getPickingSummary", "nexsService"),
    PICKING_DO_CLOSE_PICKING("/nexs/order/picking/DO/closePicking","nexsService"),
    PICKING_ADDVERB_CREATE_WAVE_SINGLESHIPMENT ("nexs/order/picking/addverb/sync/singleShipment","nexsService"),
    PICKING_ADDVERB_WAVE_DETAILS ("nexs/order/picking/addverb/wave/{$waveId}","nexsService"),
    PICKING_CREATE_SUMMARY("/nexs/order/picking/v2/summary/{$location}/{$category}", "nexsService"),
    PICKING_GET_PICKING_DEATIALS("nexs/order/picking/{$pickingSummaryId}","nexsService"),
    PICKING_SKIP("nexs/order/picking/skip/{$picklistId}/{$reason}", "nexsService"),
    PICKING_MARK_ITEM_PERMANENT_NOT_FOUND("nexs/order/picking/supervisor/markItemPermanentlyNotFound", "nexsService"),
    PICKING_PICK_ITEM("/nexs/order/picking/pick/orderItemId/{$wmsOrderItemId}/{$barcode}", "nexsService"),




    //Packing Service Endpoints
    PACKING_GET_ITEM_DETAIL_BY_ID("/nexs/packing/getItemDetailById", "nexsService"),
    PACKING_COMPLETE_PACKING("/nexs/packing/completePacking", "nexsService"),
    PACKING_PACKING_SLIP("/nexs/packing/label/pdf", "nexsService"),

    //OrderQC Service Endpoints
    ORDERQC_FETCH_ORDER_ENTITY_TYPE("/nexs/api/orderqc/fetch/order/entityType", "nexsService"),
    ORDERQC_COMPLETE_QC("/nexs/api/orderqc/completeQc", "nexsService"),

    //WMS Service Endpoints
    WMS_GET_COMMENT("/nexs/wms/api/v1/comment/get", "nexsService"),
    WMS_PRINT_SHIPMENT("/nexs/wms/api/v1/shipment/print", "nexsService"),
    WMS_PRINT_SHIPMENT_MP("/nexs/wms/api/v1/shipment/mporder/print", "nexsService"),
    WMS_CREATE_TRAY("/nexs/wms/api/v1/tray/create", "nexsService"),
    WMS_LOCATION_SCAN("/nexs/wms/api/v1/location/scan", "nexsService"),
    WMS_EXPORT_PDF("/nexs/wms/api/v1/exportPdf", "nexsService"),
    WMS_ADD_VSM_COMMENT("/nexs/wms/api/v1/vsmComment/add", "nexsService"),
    WMS_REASSIGN_SHIPMENT("/nexs/wms/api/v1/shipment/reassign", "nexsService"),
    WMS_ORDER_DETAILS("/nexs/wms/api/v1/order/details/header", "nexsService"),
    WMS_ORDER_DETAILS_WITH_ID("/nexs/wms/api/v1/order/details/id/{$shippingPackageId}", "nexsService"),
    WMS_HAND_EDGING_SCAN("/nexs/wms/api/v1/hand/edging/scan", "nexsService"),
    WMS_FETCH_INVOICE("/nexs/wms/api/v1/invoice/fetch", "nexsService"),
    WMS_ORDER_DETAILS_OVERVIEW("/nexs/wms/api/v1/order/details/overview", "nexsService"),
    WMS_FACILITY_REASSIGNMENT("/nexs/wms/api/v1/shipment/reassign", "nexsService"),
    WMS_UPDATE_BOX_COUNT("/nexs/wms/api/v1/update/shipment/box/count", "nexsService"),
    WMS_ITEM_FULLFILLABILITY("/nexs/wms/api/v1/update/item/fullfillablity", "nexsService"),

    //Fitting Service Endpoints
    FITTING_GET_FITTING_DETAIL("nexs/fitting/getFittingDetail/{$fittingId}", "nexsService"),
    FITTING_MARK_FITTING_COMPLETE("/nexs/fitting/markFittingComplete/{$fittingId}/{$shippingPackageId}", "nexsService"),

    //IMS Service Endpoints
    IMS_STOCK_IN_AND_OUT("/nexs/api/ims/stockInAndOut","nexsService"),
    IMS_FETCH_BARCODE_ITEM_DETAILS("/nexs/api/ims/fetchBarcodeItemDetails","nexsService"),
    IMS_FETCH_INVENTORY_INFO("/nexs/api/ims/fetchInventoryInfo","nexsService"),
    IMS_GET_CONSOLIDATED_INV_INFO("/nexs/api/ims/getConsolidatedInvInfo/{$productId}","nexsService"),
    IMS_STOCK_IN_AND_OUT_V2("/nexs/api/ims/stockInAndOutV2","nexsService"),
    IMS_UPDATE_BARCODE_STATUS("/nexs/api/ims/update/{$id}","nexsService"),
    IMS_UPDATE_BARCODE_DESTINATION("/nexs/api/ims/update/barcodeDestination","nexsService"),
    IMS_UPDATE_LOCATION("/nexs/api/ims/update/location","nexsService"),
    IMS_STOCK_IN_OUT_AND_RELEASE("/nexs/api/ims/stockInOutAndRelease","nexsService"),

    //LK Auth Service Endpoints
    LKAUTH_LOGIN("/v1/user/login", "nexsService"),
    LKAUTH_LOGOUT("/v1/user/logout", "nexsService"),
    LKAUTH_FORGOT_PASSWORD("/v1/forgot/password/{$email}", "nexsService"),

    //Manifest Service Endpoints
    MANIFEST_FETCH_CHANNEL("/nexs/manifest/api/v1/fetch/channel", "nexsService"),
    MANIFEST_FETCH_SHIPPING_PROVIDER("/nexs/manifest/api/v1/fetch/shippingProvider/{$channel}", "nexsService"),
    MANIFEST_SAVE("/nexs/manifest/api/v1/save", "nexsService"),
    MANIFEST_FETCH_MANIFEST("/nexs/manifest/api/v1/fetch/{$manifestNumber}", "nexsService"),
    MANIFEST_ADD_SHIPMENT("/nexs/manifest/api/v1/add/shipment/{$manifestNumber}/{$awbNumber}", "nexsService"),
    MANIFEST_CLOSE("/nexs/manifest/api/v1/close/{$manifestNumber}", "nexsService"),

    //Consolidation Service Endpoints
    CONSOLIDATION_SCAN_ITEM("/nexs-consolidation/api/v1/scan/item", "nexsService"),
    CONSOLIDATION_OPTIONS("/nexs-consolidation/api/v1/options", "nexsService"),
    CONSOLIDATION_ALLOCATE_ITEM("/nexs-consolidation/api/v1/allocate/item", "nexsService"),
    CONSOLIDATION_ORDER_FLUSH("/nexs-consolidation/packing/api/v1/order/flush", "nexsService"),
    CONSOLIDATION_SCAN_ITEM_IN_PACKING("/nexs-consolidation/packing/api/v1/scan/item", "nexsService"),
    CONSOLIDATION_SCAN_ITEM_V2("nexs-consolidation/api/v1/scan/v2/item", "nexsService"),

    //JIT Service Endpoints
    JIT_RXUSTATUS("/rxustatus","jitService"),
    JIT_LENSLAB_STATUS_UPDATE("/jit/lenslab/status-update","jitService"),

    //CATALOG Service Endpoints
    CATALOG_PRODUCT_ADDITIONAL_DETAILS("/nexs/api/catalog/v1/product/additionalDetails", "nexsService"),
    CATALOG_PRODUCT_DETAILS("/nexs/api/catalog/v1/product", "nexsService"),
    CATALOG_PRODUCT_PRICE("/nexs/api/catalog/v1/productPrice", "nexsService"),

    //Transfer Service Endpoints
    TRANSFER_GET_TRANSFER_DETAILS("/nexs/api/v1/iwt/inventoryTransfer/getTransferDetails","nexsService"),
    TRANSFER_SCAN_BARCODE("/nexs/api/v1/iwt/inventoryTransfer/scanBarcode","nexsService"),
    TRANSFER_CREATE("nexs/api/v1/iwt/inventoryTransfer/createTransfer","nexsService"),
    TRANSFER_RECEIVE("nexs/api/v1/iwt/gate-pass/get-details/{transfer_code}","nexsService"),
    TRANSFER_CHANGE_STATUS("/nexs/api/v1/iwt/inventoryTransfer/changeTransferStatus", "nexsService"),


    //Purchase Order Service Endpoints
    GENERATE_PO_NUM("/nexs/api/po/v1/generatePoNum","nexsService"),
    GENERATE_PO_DETAILS("nexs/api/po/v1/createPoDetails","nexsService"),
    GET_AVERAGE_UNIT_PRICE("/internal/nexs/api/po/v1/getAverageUnitPrice/{$productId}/{$countryCode}","poService"),
    PO_ADD_INVENTORY("/nexs/api/po/v1/single/addInventory","nexsService"),
    PO_START_RETIRE("/nexs/api/po/v1/single/startRetire","nexsService"),
    PO_APPROVE_REJECT("/nexs/api/po/v1/approveorreject", "nexsService"),
    PO_HOLD("/nexs/api/po/v1/hold", "nexsService"),
    PO_UNHOLD("/nexs/api/po/v1/unhold", "nexsService"),
    PO_CLOSE("/nexs/api/po/v1/close", "nexsService"),
    PO_GET_ORDER("/nexs/api/po/v1/order", "nexsService"),
    PO_PRINT("/nexs/api/export/v1/pdf/po", "nexsService"),
    PO_EXPORT_DETAIL("/nexs/api/export/v1/poItemDetails/exportCSV", "nexsService"),
    PO_EXPORT_LISTING("/export/purchaseOrder", "nexsService"),
    PO_SEARCH("/search/purchaseOrder", "nexsService"),
    PO_SUGGESTION("/suggestions/purchaseOrder", "nexsService"),
    PO_SAVE("/nexs/api/po/v1/submit", "nexsService"),
    PO_GET_PROCESSING_STATUS("/nexs/api/po/v1/processingstatus", "nexsService"),
    PO_SUBMIT_FOR_APPROVAL("/nexs/api/po/v1/submitforapproval", "nexsService"),
    PO_AMEND("/nexs/api/po/v1/amend", "nexsService"),
    PO_ITEM_LIST("/nexs/api/po/v1/po_itemlist", "nexsService"),
    PO_BULK_APPROVE_REJECT("/nexs/api/po/v1/bulkApproveorreject", "nexsService"),
    PO_INVOICE_ITEMS("nexs/api/invoice/v1/items/creation", "nexsService"),
    SAVE_PO_INVOICE("nexs/api/invoice/v1/save", "nexsService"),
    GET_INVOICE_PROCESSING_STATUS("nexs/api/invoice/v1/processingstatus", "nexsService"),

    //Vendor Service Endpoints
    VENDOR_SEARCH("/nexs/api/vendor/v1/search", "nexsService"),

    //GRN service endpoints
    CLOSE_GRN("nexs/api/grn/v1/master/close/{$grnCode}", "nexsService"),
    GET_BARCODE_PRICE("/nexs/api/grn/v1/get/barcode/price", "nexsService"),
    GRN_SCAN_ITEM_BARCODE_SERIES("/nexs/api/grn/v1/stock-in/barcode-series","nexsService"),
    GET_GRN_ITEMS("/nexs/api/invoice/v1/items/grn", "nexsService"),
    CREATE_GRN("nexs/api/grn/v1/master/create", "nexsService"),
    GET_GRN_SUMMARY("nexs/api/grn/v1/master/get/{$grnCode}", "nexsService"),
    ADD_GRN_PID("/nexs/api/grn/v1/master/add/grn-pid", "nexsService"),
    CREATE_BOX_MAPPING("nexs/api/grnentities/v1/box-mapping/create", "nexsService"),
    SCAN_GRN_BARCODE("nexs/api/grn/v1/scan/item-barcode","nexsService"),

    //EMS service endpoints
    EMS_EXCEPTION_TRIGGER("/nexs-ems/triggerException","nexsService"),

    //Putaway Service endpoints
    PUTAWAY_CONTINUOUS_SCAN("/putaway/v1/continousScan", "nexsService"),

    //CID Service Endpoints
    CID_GET_INVENTORY_INFO("nexs/cid/api/v1/warehouseInventory/getConsolidatedInvInfo/{$productId}","nexsService"),

    //Cycle Count service Endpoints
    CYCLE_COUNT_SCAN_LOCATION("/nexs-cycle-count/api/v1/scan/location","nexsService"),
    CYCLE_COUNT_SCAN_ITEM("/nexs-cycle-count/api/v1/scan/item","nexsService"),
    CYCLE_COUNT_SCAN_COMPLETE("/nexs-cycle-count/api/v1/location/complete","nexsService");

    private final String endpoint;
    private final String serviceName;

    NexsEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    @Override
    public String getUrl() {
        return NexsEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return NexsEndpointManager.getEndpointUrl(this, pathParams);
    }

}

