package com.lenskart.nexs.config;

import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility class to load Nexs configurations from YAML files
 */
@Slf4j
public class NexsConfigLoader {
    // Configuration cache
    private static final Map<String, NexsConfig> configCache = new ConcurrentHashMap<>();

    // Default values
    private static final String DEFAULT_CONFIG_PATH = "nexs.yml";
    private static final String DEFAULT_ENVIRONMENT = "preprod";

    /**
     * Loads the Juno configuration from the default YAML file
     *
     * @return JunoConfig object with configuration details
     */
    public static NexsConfig loadConfig() {
        return loadConfig(DEFAULT_CONFIG_PATH);
    }

    /**
     * Loads the Juno configuration from a specified YAML file
     *
     * @param configPath Path to the YAML file
     * @return JunoConfig object with configuration details
     */
    public static NexsConfig loadConfig(String configPath) {
        // Return cached config if available
        if (configCache.containsKey(configPath)) {
            return configCache.get(configPath);
        }

        try {
            Yaml yaml = new Yaml();

            // Try to load from classpath first
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(configPath);

            // If not found in classpath, try as a file path
            if (inputStream == null) {
                try {
                    inputStream = new FileInputStream(configPath);
                } catch (IOException e) {
                    log.warn("Could not find configuration file: {}", configPath);
                    NexsConfig defaultConfig = createDefaultConfig();
                    configCache.put(configPath, defaultConfig);
                    return defaultConfig;
                }
            }

            // Load the YAML file as a map
            Map<String, Object> yamlMap = yaml.load(inputStream);
            inputStream.close();

            if (yamlMap == null || yamlMap.isEmpty()) {
                log.warn("Empty or invalid configuration file: {}", configPath);
                NexsConfig defaultConfig = createDefaultConfig();
                configCache.put(configPath, defaultConfig);
                return defaultConfig;
            }

            // Create a new NexsConfig object
            NexsConfig config = new NexsConfig();

            // Process each environment in the YAML file
            for (Map.Entry<String, Object> entry : yamlMap.entrySet()) {
                String envName = entry.getKey();

                if (entry.getValue() instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> envConfig = (Map<String, Object>) entry.getValue();

                    // Create a new environment configuration
                    NexsConfig.EnvironmentConfig environmentConfig = new NexsConfig.EnvironmentConfig();

                    // Process base URLs if available
                    if (envConfig.containsKey("baseUrls") && envConfig.get("baseUrls") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, String> baseUrls = (Map<String, String>) envConfig.get("baseUrls");
                        environmentConfig.setBaseUrls(baseUrls);
                    }

                    // Process credentials if available
                    if (envConfig.containsKey("credentials") && envConfig.get("credentials") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> credentialsMap = (Map<String, Object>) envConfig.get("credentials");
                        
                        Map<String, NexsConfig.SerialNoConfig> credentials = new HashMap<>();
                        
                        // Process each serial number entry
                        for (Map.Entry<String, Object> credEntry : credentialsMap.entrySet()) {
                            String serialNumber = credEntry.getKey();
                            
                            if (credEntry.getValue() instanceof Map) {
                                @SuppressWarnings("unchecked")
                                Map<String, String> credDetails = (Map<String, String>) credEntry.getValue();
                                
                                NexsConfig.SerialNoConfig serialNoConfig = new NexsConfig.SerialNoConfig();
                                serialNoConfig.setUsername(credDetails.get("username"));
                                serialNoConfig.setPassword(credDetails.get("password"));
                                
                                credentials.put(serialNumber, serialNoConfig);
                                log.debug("Loaded credentials for serial number: {}", serialNumber);
                            }
                        }
                        
                        environmentConfig.setCredentials(credentials);
                        log.info("Loaded {} credential entries", credentials.size());
                    }

                    // Add the environment configuration to the NexsConfig
                    config.getEnvironments().put(envName, environmentConfig);
                }
            }

            // Cache the config
            configCache.put(configPath, config);

            log.info("Loaded Nexs configuration from: {}", configPath);
            return config;

        } catch (Exception e) {
            log.error("Failed to load Nexs configuration: {}", e.getMessage(), e);
            NexsConfig defaultConfig = createDefaultConfig();
            configCache.put(configPath, defaultConfig);
            return defaultConfig;
        }
    }

    /**
     * Creates a default Nexs configuration
     *
     * @return Default NexsConfig
     */
    private static NexsConfig createDefaultConfig() {
        NexsConfig config = new NexsConfig();

        // Create default environment configuration
        NexsConfig.EnvironmentConfig environmentConfig = new NexsConfig.EnvironmentConfig();

        // Add default base URLs
        Map<String, String> baseUrls = new HashMap<>();
        baseUrls.put("sessionService", "https://api-gateway.nexs.preprod.lenskart.com");
        baseUrls.put("testService", "https://jsonplaceholder.typicode.com");
        environmentConfig.setBaseUrls(baseUrls);

        // Add the environment configuration to the NexsConfig
        config.getEnvironments().put(DEFAULT_ENVIRONMENT, environmentConfig);

        return config;
    }

    /**
     * Clears the configuration cache
     */
    public static void clearCache() {
        configCache.clear();
        log.info("Configuration cache cleared");
    }

    /**
     * Get the default environment name
     *
     * @return Default environment name
     */
    public static String getDefaultEnvironment() {
        return DEFAULT_ENVIRONMENT;
    }

    /**
     * Get the default configuration path
     *
     * @return Default configuration path
     */
    public static String getDefaultConfigPath() {
        return DEFAULT_CONFIG_PATH;
    }
}
