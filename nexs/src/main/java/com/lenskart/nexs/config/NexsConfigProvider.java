package com.lenskart.nexs.config;

import com.lenskart.commons.config.ConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Configuration provider for NEXS module that wraps NexsConfigRegistry
 * to implement the standard ConfigProvider interface.
 */
@Slf4j
public class NexsConfigProvider implements ConfigProvider {
    
    private final NexsConfigRegistry nexsRegistry;
    
    // Singleton instance
    private static volatile NexsConfigProvider instance;
    
    /**
     * Private constructor for singleton pattern
     */
    private NexsConfigProvider() {
        this.nexsRegistry = NexsConfigRegistry.getInstance();
    }
    
    /**
     * Gets the singleton instance of NexsConfigProvider
     *
     * @return The singleton instance
     */
    public static NexsConfigProvider getInstance() {
        if (instance == null) {
            synchronized (NexsConfigProvider.class) {
                if (instance == null) {
                    instance = new NexsConfigProvider();
                }
            }
        }
        return instance;
    }
    
    @Override
    public String getBaseUrl(String serviceName) {
        try {
            return nexsRegistry.getBaseUrl(serviceName);
        } catch (Exception e) {
            log.error("Error getting base URL for service {} from NEXS registry: {}", 
                serviceName, e.getMessage());
            return null;
        }
    }
    
    @Override
    public Map<String, String> getAllBaseUrls() {
        try {
            return nexsRegistry.getAllBaseUrls();
        } catch (Exception e) {
            log.error("Error getting all base URLs from NEXS registry: {}", e.getMessage());
            return Map.of();
        }
    }
    
    @Override
    public void refresh() {
        try {
            nexsRegistry.refresh();
            log.info("NEXS configuration refreshed successfully");
        } catch (Exception e) {
            log.error("Error refreshing NEXS configuration: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isInitialized() {
        return nexsRegistry != null;
    }
    
    @Override
    public String getProviderName() {
        return "NexsConfigProvider";
    }
    
    /**
     * Gets the underlying NEXS registry for direct access if needed
     *
     * @return The NexsConfigRegistry instance
     */
    public NexsConfigRegistry getNexsRegistry() {
        return nexsRegistry;
    }
}
