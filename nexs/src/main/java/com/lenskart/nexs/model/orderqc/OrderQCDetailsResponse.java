package com.lenskart.nexs.model.orderqc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.wms.enums.ProcessingType;
import com.lenskart.nexs.wms.response.qc.CustomOptions;
import com.lenskart.nexs.wms.response.qc.Product;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderQCDetailsResponse {
    private Long id;
    private Integer uwItemId;
    private int incrementId;
    private int itemId;
    private int productId;
    private String barcode;
    private int parentUw;
    private int stockout;
    private Product product;
    private CustomOptions customOptions;
    private Date qcTime;
    private String poId;
    private Date poDate;
    private String isFulfillable;
    private String status;
    private String fitting;
    private Date createdAt;
    private String shippingPackageId;
    private String splOrderFlag;
    private String fitterName;
    private boolean qcHold;
    private int inventoryCount;
    private String facilityCode;
    private String wmsSynStatus;
    private String wmsOrderCode;
    private String wmsShipmentStatus;
    private String productDeliveryType;
    @Builder.Default
    private Boolean shipToStoreRequired = false;
    @Builder.Default
    private Boolean isLocalFittingRequired = false;
    private String navChannel;
    private short qtyOrdered;
    private BigDecimal grandTotal;
    private String extCustomerId;
    private BigDecimal orderGrandTotal;
    private Date orderCreatedAt;
    private byte storeId;
    private String fittingId;
    private String trayId;
    private Date expiryDate;
    private Boolean isExpired;
    private String caseImage;
    private String displayAdditionalText;
    private ProcessingType processingType;

}
