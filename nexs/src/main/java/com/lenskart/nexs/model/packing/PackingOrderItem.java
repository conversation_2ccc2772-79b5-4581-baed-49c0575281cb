package com.lenskart.nexs.model.packing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;


@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PackingOrderItem {
    public String barcode;
    public Integer fittingId;
    public Integer productId;
    public Integer incrementId;
    public String shipmentId;
    public String status;
    public Integer itemId;
    public String expectedDispatchDate;
    public String unicomOrderCode;
    public Integer uwItemId;
    public Product product;
    public Power power;
    public Object fitterName;
    public Boolean qcHold;
    public String facilityCode;
    public String navChannel;
    public Boolean delayOrderCard;
    public Boolean tokaiCard;
    public Boolean exchangeOrderHolded;
    public Object customText;
    public String itemType;
}
