package com.lenskart.nexs.model.orderqc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InfoForQCPanel {
    private String highValue;
    private Date orderCreatedAt;
    private Map<String,Boolean> isReturnOrder;
    private Date unciomSyncTime;
    private String source;
    private String isFranchiseOrderShipToCustomer;
    private boolean franchiseShipToCustomerNoInvoice;
    private String country;
    private OrderResponse orderResponse;
    private Map<String,Boolean> jitOrders;
    private Map<String,Integer> qcFailCount;
    private boolean webB2BOrder;
    private String lkFacilityCode;
    private String awb;
    private String shipmentLabel;
    private int printShipment;
    private String shipmentType;
    private boolean leftLensHandEdgingRequired;
    private boolean rightLensHandEdgingRequired;
    private String expectedDeliveryDate;
    //  QcFailHistory qcFailHistory;
}

