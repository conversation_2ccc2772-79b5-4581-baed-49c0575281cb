package com.lenskart.nexs.model.fitting;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetFittingDetailResponse {
    private DataGetFittingDetail data;
    private Object meta;

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DataGetFittingDetail {
        private Order order;
        @JsonProperty("scanned_order_item")
        private ScannedOrderItem scannedOrderItem;

        @AllArgsConstructor
        @NoArgsConstructor
        @Builder
        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Order {
            @JsonProperty("order_type")
            private String orderType;
            @JsonProperty("country_code")
            private String countryCode;
            @JsonProperty("ext_customer_id")
            private String extCustomerId;
            @JsonProperty("ship_to_customer")
            private Boolean shipToCustomer;
            @JsonProperty("increment_id")
            private Boolean incrementId;
        }

        @AllArgsConstructor
        @NoArgsConstructor
        @Builder
        @Data
        public static class ScannedOrderItem {

            @JsonProperty("shipping_package_id")
            private String shippingPackageId;
            @JsonProperty("fitting_id")
            private Integer fittingId;
            @JsonProperty("fulfillable_type")
            private String fulfillableType;
            @JsonProperty("fitting_type")
            private String fittingType;
            @JsonProperty("product_id")
            private Integer productId;
            @JsonProperty("product_image")
            private String productImage;
            @JsonProperty("alternate_product_images")
            private List<String> alternateProductImages;
            @JsonProperty("lens_package")
            private String lensPackage;
            @JsonProperty("lens_details")
            private LensDetails lensDetails;
            @JsonProperty("frame_tray_id")
            private String frameTrayId;
            @JsonProperty("left_lens_tray_id")
            private String leftLensTrayId;
            @JsonProperty("right_lens_tray_id")
            private String rightLensTrayId;
            @JsonProperty("frame_barcode")
            private String frameBarcode;
            @JsonProperty("left_lens_barcode")
            private String leftLensBarcode;
            @JsonProperty("right_lens_barcode")
            private String rightLensBarcode;
            @JsonProperty("left_lens_orderItemId")
            private Integer leftLensOrderItemId;
            @JsonProperty("right_lens_orderItemId")
            private Integer rightLensOrderItemId;
            @JsonProperty("frame_orderItemId")
            private Integer frameOrderItemId;
            @JsonProperty("rimless_bar_productId")
            private String rimlessBarProductId;
        }

        @AllArgsConstructor
        @NoArgsConstructor
        @Builder
        @Data
        public static class LensDetails {
            @JsonProperty("right_lens")
            public LensOptions rightLens;
            @JsonProperty("left_lens")
            public LensOptions leftLens;
        }

        @AllArgsConstructor
        @NoArgsConstructor
        @Builder
        @Data
        public static class LensOptions {
            private String sph;
            private String cyl;
            private String axis;
            private String ap;
            private String pd;
            private String fh;
            private String width;
            private String height;
            private String dbl;
            private String ed;
        }
    }
}
