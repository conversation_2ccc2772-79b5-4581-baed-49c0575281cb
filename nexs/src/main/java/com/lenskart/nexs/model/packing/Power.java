package com.lenskart.nexs.model.packing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class Power {
    private Integer orderId;
    private Integer productId;
    private Integer shellId;
    private String rightLens;
    private String axis;
    private String sph;
    private String lensHeight;
    private String lensWidth;
    private String cyl;
    private String ap;
    private String packageData;
    private String lensPackageType;
    private String patientDOB;
    private String patientName;
    private Object patientDrName;
    private Object gender;
    private Object patientComments;
    private String pd;
    private String relatedProduct;
    private String bottomDistance;
    private String edgeDistance;
    private String effectiveDia;
    private Object nearPD;
    private String topDistance;
    private String updatedAt;
    private Integer packagePrice;
    private String customerAxis;
    private Object customerSPH;
    private Object customerCYL;
    private Object webPackage;
    private String coatingOid;
    private Object coatingName;
    private Object lensId;
    private Object lensIndex;
    private Object lensName;
    private Object lensType;
    private Object type;
    private Object withAp;
    private Object vendorPackageName;
    private Object brand;
    private Object isActive;
    private Object coating;
}
