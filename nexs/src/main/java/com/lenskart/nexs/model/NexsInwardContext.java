package com.lenskart.nexs.model;

import lombok.Builder;
import lombok.Data;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

@Data
@Builder
public class NexsInwardContext {

    @Builder.Default
    String facilityCode="NXS2";
    String poNumber;
    String vendorInvNo;
    @Builder.Default
    String vendorCode="WHOLESALE01";
    @Builder.Default
    String productId="148248";
    @Builder.Default
    String quantity="3";
    String stateCode;
    String currency;
    JSONObject taxInfo;
    JSONArray items;
    double vendorUnitCostPrice;
    String hsnClassification;

    double igstRate;
    double cgstRate;
    double sgstRate;
    double ugstRate;
    double priceWithTaxes;
    double vendorUnitCostPriceWithTax;
    String invoiceRefNum;
    String grnNumber;
    String grnType;
    String invoiceLevel;
    String legalOwner;
    String boxBarcode;
    List<String> inwardBarcodes;

    double cgstPer;
    double igstPer;
    double sgstPer;
    double ugstPer;
    double flatPer;
    JSONObject productData;
    @Builder.Default
    String poType="BULK";
    @Builder.Default
    String shipmentType="AIR";
    @Builder.Default
    String status="APPROVED";
    @Builder.Default
    boolean isUiFlowEnabled=false;

}
