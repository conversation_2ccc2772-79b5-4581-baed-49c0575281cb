package com.lenskart.nexs.model;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ProductPriceModel {
    private String currency;
    private String facilityStateCode;
    private HsnTaxInfo hsnTaxInfo; // this is for nested object payload, creating class below

    @Data
    @Builder
    public static class HsnTaxInfo {
        private String hsnCode;
        private String stateCode;
        private String igstPer;
        private String cgstPer;
        private String sgstPer;
        private String ugstPer;
        private String flatPer;
        private String igstRate;
        private String cgstRate;
        private String sgstRate;
        private String ugstRate;
        private String flatRate;
    }

    private String productId;
    private String quantity;
    private String vendorCode;
    private String vendorUnitCostPrice;

}
