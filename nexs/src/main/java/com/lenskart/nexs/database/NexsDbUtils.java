package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;

import java.util.List;
import java.util.Map;

public class NexsDbUtils {
    public static List<Map<String, Object>> getStartRetireBarcodeDetails(NexsOrderContext nexsOrderContext) {
        return  MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.NEXS_DB,
                        NexsDbQueries.GET_START_RETIRE_BARCODE_DETAILS,
                        nexsOrderContext.getBarcode());
    }

    public static List<Map<String, Object>> getTransferBarcodes(NexsOrderContext nexsOrderContext, int limit) {
        return MySQLQueryExecutor.executeQuery(
                Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.NEXS_DB,
                NexsDbQueries.GET_TRANSFER_BARCODES,
                nexsOrderContext.getProductId(), nexsOrderContext.getFacilityCode() , nexsOrderContext.getLegalOwner(), limit
        );
    }

    public static List<Map<String, Object>> getGrnNoForJitInward(NexsOrderContext nexsOrderContext, String barcode ) {
        return MySQLQueryExecutor.executeQuery(
                Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.NEXS_DB,
                NexsDbQueries.GET_GRN_NO_FOR_JIT_INWARD,
                barcode
        );
    }
}
