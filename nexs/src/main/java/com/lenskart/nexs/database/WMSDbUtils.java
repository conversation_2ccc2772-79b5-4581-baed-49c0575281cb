package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import java.util.List;
import java.util.Map;

public class WMSDbUtils {

    public static String getStatusOfShipment(NexsOrderContext nexsOrderContext) {
        List<Map<String, Object>> itemTypeForStatus = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.ITEM_TYPE_FOR_STATUS,
                        nexsOrderContext.getShippingId());

        List<Map<String, Object>> itemStatus = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.ITEM_STATUS,
                        itemTypeForStatus.getFirst().get("item_type"), nexsOrderContext.getNexsOrderId(),
                        nexsOrderContext.getShippingId());

        return itemStatus.getFirst().get("status").toString();
    }
    public static String getStatusOfLens(NexsOrderContext nexsOrderContext) {

        List<Map<String, Object>> itemTypeForStatus = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.LENS_STATUS,
                        nexsOrderContext.getShippingId());

        return itemTypeForStatus.getFirst().get("status").toString();
    }
    public static String getTrayIdForLens(NexsOrderContext nexsOrderContext) {

        List<Map<String, Object>> itemTypeForStatus = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.TRAY_BARCODE,
                        nexsOrderContext.getShippingId());

        return itemTypeForStatus.getFirst().get("location_id").toString();
    }


    public static String getStausBasedOnBarcode(NexsOrderContext nexsOrderContext, String barcode) {
        List<Map<String, Object>> leftLensStatus = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.GET_ORDER_ITEM_STATUS,
                        barcode, nexsOrderContext.getTrayBarcode());
        return leftLensStatus.getFirst().get("status").toString();
    }

    public static List<Map<String, Object>> getMeiSyncDetails(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.MEI_SYNC_DETAILS, nexsOrderContext.getNexsOrderId(), nexsOrderContext.getFittingId());
    }

    public static List<Map<String, Object>> getFittingStatus(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.FITTING_STATUS, nexsOrderContext.getFittingId());
    }

    public static String getFacilityCode(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.GET_FACILITY_CODE,
                nexsOrderContext.getShippingId()).getFirst().get("facility_code").toString();
    }

    public static List<Map<String, Object>> getShipmentStatus(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.DISTINCT_STATUS,
                nexsOrderContext.getShippingId());
    }

    public static List<Map<String, Object>> itemStatusByShippingId(List<String> shippingIds) {
        return MySQLQueryExecutor.executeQuery(
                Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.ITEM_STATUS_BY_SHIPPING_ID,
                String.join(",", shippingIds) // single CSV param
        );
    }

    public static String getReassignStatus(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.REASSIGNMENT_STATUS_BY_SHIPPING_ID,
                nexsOrderContext.getShippingId()).getFirst().get("status").toString();
    }
    public static List<Map<String, Object>> getNexsOrderId(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB, WMSQueries.GET_NEXS_ORDER_ID,
                nexsOrderContext.getShippingId());
    }

    public static List<Map<String, Object>> getOrderItemHeaderDetails(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.GET_ORDER_ITEM_HEADER,
                nexsOrderContext.getShippingId());
    }
    public static List<Map<String, Object>> getWmsDetails( String orderItemId) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.GET_ITEM_TYPE,
                orderItemId
        );
    }

    public static List<Map<String, Object>> getStoreConsolidationInfo(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.STORE_CONSOLIDATION_INFO,
                nexsOrderContext.getShippingId());
    }

    public static List<Map<String, Object>> getDoShipmentDetails(NexsOrderContext nexsOrderContext) {
        List<Map<String, Object>> fetchedDoOrderDetails = MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.GET_DO_SHIPMENT_DETAILS,
                nexsOrderContext.getIncrementId());
        return fetchedDoOrderDetails;
    }

    public static List<Map<String, Object>> getLatestBulkOrder() {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.GET_LATEST_BULK_ORDER);
    }
    public static List<Map<String, Object>> getTrayDetails(String trayId) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.GET_LOCATION_ID_DETAILS,trayId);
    }
    public static List<Map<String, Object>> getLatestOrder( ) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                WMSQueries.GET_LATEST_ORDER);
    }
}
