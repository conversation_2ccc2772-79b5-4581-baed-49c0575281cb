package com.lenskart.nexs.database;

public class NexsDbQueries {
    public static final String GET_START_RETIRE_BARCODE_DETAILS = "select * from nexs.start_retire_barcode_details where barcode=?";
    public static final String GET_GRN_NO_FOR_JIT_INWARD = "select * from grn_items where barcode =? ;";
    public static final String GET_TRANSFER_BARCODES = "SELECT ims.barcode FROM nexs_ims.barcode_item ims INNER JOIN nexs.grn_items grn ON ims.barcode = grn.barcode WHERE ims.pid = ? AND ims.condition = 'GOOD' AND ims.availability = 'AVAILABLE' AND ims.status = 'AVAILABLE' AND ims.facility = ? AND ims.legal_owner = ? AND grn.pid = ims.pid AND grn.facility = ims.facility ORDER BY grn.created_at DESC LIMIT ?";
}
