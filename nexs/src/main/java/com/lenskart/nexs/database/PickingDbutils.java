package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
@Slf4j
public class PickingDbutils {

    public static List<Map<String, Object>> getpickingDetailsStatus (NexsOrderContext nexsOrderContext) {
        return  MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PICKING_DB,
                        PickingQueries.PICKING_DETAILS_STATUS,
                        nexsOrderContext.getShippingId());
    }
    public static List<Map<String, Object>> getShippingPackageIdsByGroupId(NexsOrderContext nexsOrderContext) {
       return  MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PICKING_DB,
                        PickingQueries.GET_SHIPPING_PACKAGE_ID_BY_GROUP_ID,
                        nexsOrderContext.getGroupId());


    }

    public static String getPickingDetails(NexsOrderContext nexsOrderContext){
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PICKING_DB,
                        PickingQueries.GET_DETAILS,
                        nexsOrderContext.getPicklistId()).getFirst().get("status").toString();
    }
    public static String getOrderItemId(NexsOrderContext nexsOrderContext){
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PICKING_DB,
                        PickingQueries.GET_DETAILS,
                        nexsOrderContext.getPicklistId()).getFirst().get("wms_order_item_id").toString();
    }

    public static String getProductIdByPicklistId(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.PICKING_DB,
                PickingQueries.GET_DETAILS,
                nexsOrderContext.getPicklistId()).getFirst().get("product_id").toString();
    }
    public static String getShipmentId(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PICKING_DB,
                        PickingQueries.GET_DETAILS,
                        nexsOrderContext.getPicklistId()).getFirst().get("shipment_id").toString();
    }
    public static String getFittingId(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PICKING_DB,
                        PickingQueries.GET_FITTING_ID,
                        nexsOrderContext.getShippingId()).getFirst().get("fitting_id").toString();
    }
    public static String getLensSummaryId(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PICKING_DB,
                        PickingQueries.GET_DETAILS,
                        nexsOrderContext.getPicklistId()).getFirst().get("picking_summary_id").toString();
    }

}
