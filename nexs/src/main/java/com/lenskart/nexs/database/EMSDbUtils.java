package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;

import java.util.List;
import java.util.Map;

public class EMSDbUtils{
    public static  List<Map<String, Object>> getDetails(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.WMS_DB,
                EMSQueries.EMSDetails,
                nexsOrderContext.getShippingId());
    }
    public static  List<Map<String, Object>> getExceptiondId(NexsOrderContext nexsOrderContext,String grnNo) {
        return MySQLQueryExecutor.executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                Constants.EMS_DB,
                EMSQueries.GET_EXCEPTION_ID,
                grnNo);
    }
}
