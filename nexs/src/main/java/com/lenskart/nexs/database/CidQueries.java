package com.lenskart.nexs.database;

public class CidQueries {
    public static String GET_QUANTITY_FROM_WAREHOUSE_INVENTORY = "select quantity from nexs_cid.warehouse_inventory where pid=? and facility =? and `condition`=? and availability=? and status=? and legal_owner=? and location_type='DEFAULT'";
    public static String GET_QUANTITY_FROM_STOREFRONT_INVENTORY = "select quantity from nexs_cid.storefront_inventory where pid=? and legal_owner=?;";
    public static String GET_QUANTITY_FROM_WAREHOUSE_BLOCKED_INVENTORY_BASED_ON_FACILITIES = "select sum(quantity) from nexs_cid.warehouse_blocked_inventory where legal_owner=? and FIND_IN_SET(facility, ?) group by pid having pid=?;";
    public static String GET_QUANTITY_FROM_WAREHOUSE_INVENTORY_BASED_ON_FACILITIES = "select sum(quantity) from nexs_cid.warehouse_inventory where legal_owner=? and pid = ? and `condition` = 'GOOD' and `availability` = 'AVAILABLE' and `status` = 'AVAILABLE' and FIND_IN_SET(facility, ?) AND location_type = 'DEFAULT';";




}
