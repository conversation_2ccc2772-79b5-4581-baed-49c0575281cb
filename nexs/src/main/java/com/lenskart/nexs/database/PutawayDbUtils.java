package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;

import java.util.List;
import java.util.Map;

public class PutawayDbUtils {
    public static List<Map<String, Object>> getPutawayIDForBarcode(NexsOrderContext nexsOrderContext,String barcode) {
        return  MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PUTAWAY_DB,
                        PutawayQueries.GET_PUTAWAY_ITEM_DETAILS_FOR_BAROCDE,
                        barcode);


    }
}
