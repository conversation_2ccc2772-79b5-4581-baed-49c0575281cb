package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.Assert;

import java.util.List;
import java.util.Map;

public class ImsDbUtils {
    public static void getBarcodeItemDetails(NexsOrderContext nexsOrderContext, String barcode, String condition, String availability,
                                             String status) {
        List<Map<String, Object>> barcodeItemDetails = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.IMS_DB,
                        ImsQueries.GET_BARCODE_ITEM_DETAILS,
                        barcode, nexsOrderContext.getLegalOwner());
        Assert.assertEquals(barcodeItemDetails.getFirst().get("condition").toString(), condition);
        Assert.assertEquals(barcodeItemDetails.getFirst().get("availability").toString(), availability);
        Assert.assertEquals(barcodeItemDetails.getFirst().get("status").toString(), status);
    }

    public static List<Map<String, Object>> getGAABarcodes(NexsOrderContext nexsOrderContext, int limit) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.IMS_DB,
                        ImsQueries.GET_GAA_BARCODES,
                        nexsOrderContext.getProductId(),nexsOrderContext.getFacilityCode(),nexsOrderContext.getLegalOwner(),limit);
    }
    public static List<Map<String, Object>> getBarcodeDetails(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.IMS_DB,
                        ImsQueries.GET_BARCODE_ITEM_DETAILS_BY_BARCODE,
                        nexsOrderContext.getBarcode());
    }

    public static List<Map<String, Object>> getBarcodeCount(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.IMS_DB,
                        ImsQueries.GET_BARCODE_COUNT,
                        nexsOrderContext.getBarcode(),nexsOrderContext.getLegalOwner());
    }

    public static int updateBarcodeStatus(String condition, String availability, String status, String barcode, String legalOwner) {
        return MySQLQueryExecutor
                .executeUpdate(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.IMS_DB,
                        ImsQueries.UPDATE_BARCODE_STATUS,
                       condition, availability, status, barcode, legalOwner);
    }

    public static List<Map<String, Object>> getStatusConditionAvailabilityOfBarcode(NexsOrderContext nexsOrderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.IMS_DB,
                        ImsQueries.GET_STATUS_CONDITION_AVAILABILITY_OF_BARCODE,
                        nexsOrderContext.getBarcode(),nexsOrderContext.getLegalOwner());
    }
}
