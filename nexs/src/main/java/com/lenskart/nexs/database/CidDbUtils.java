package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class CidDbUtils {
    public static int getQuantityFromWarehouseInventory(String pid, String facilityCode, String condition,
                       String availability, String status, String legalOwner) {
        List<Map<String, Object>> result = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.CID_DB,
                        CidQueries.GET_QUANTITY_FROM_WAREHOUSE_INVENTORY,
                        pid, facilityCode, condition, availability, status, legalOwner);
       return (int) result.getFirst().get("quantity");
    }

    public static int getQuantityFromStorefrontInventory(String pid, String legalOwner) {
        List<Map<String, Object>> result = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.CID_DB,
                        CidQueries.GET_QUANTITY_FROM_STOREFRONT_INVENTORY,
                        pid, legalOwner);
        return (int) result.getFirst().get("quantity");
    }
    public static int getQuantityFromWarehouseBlockedInventoryBasedOnFacilities(String legalOwner, String pid, List<String> facilities) {
        List<Map<String, Object>> result = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.CID_DB,
                        CidQueries.GET_QUANTITY_FROM_WAREHOUSE_BLOCKED_INVENTORY_BASED_ON_FACILITIES,
                        legalOwner, String.join(",", facilities), pid);
        BigDecimal quantity = (BigDecimal) result.getFirst().get("sum(quantity)");
        return quantity != null ? quantity.intValue() : 0;
    }
    public static int getQuantityFromWarehouseInventoryBasedOnFacilities(String legalOwner, String pid, List<String> facilities) {
        List<Map<String, Object>> result = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.CID_DB,
                        CidQueries.GET_QUANTITY_FROM_WAREHOUSE_INVENTORY_BASED_ON_FACILITIES,
                        legalOwner, pid, String.join(",", facilities));
        BigDecimal quantity = (BigDecimal) result.getFirst().get("sum(quantity)");
        return quantity != null ? quantity.intValue() : 0;
    }
}
