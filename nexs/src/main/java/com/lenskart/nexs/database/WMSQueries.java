package com.lenskart.nexs.database;

public class WMSQueries {

    public static final String ITEM_TYPE_FOR_STATUS = "select distinct item_type from wms.order_items where shipping_package_id=? and `item_type` not in ('RIGHTLENS','LEFTLENS','LOYALTY')";
    public static final String ITEM_STATUS = "select distinct `status` from wms.order_items where item_type=? and nexs_order_id=? and shipping_package_id=?";
    public static final String LENS_STATUS = "select distinct `status` from wms.order_items where `item_type` in ('RIGHTLENS','LEFTLENS')  and  shipping_package_id=?";
    public static final String TRAY_BARCODE = "select distinct `location_id` from wms.order_items where `item_type` in ('RIGHTLENS','LEFTLENS')  and  shipping_package_id=?  limit 1";
    public static final String GET_ORDER_ITEM_STATUS = "select `status` from order_items where `barcode`= ? and `location_id` = ?";
    public static final String MEI_SYNC_DETAILS = "select * from wms.mei_sync where nexs_order_id =? and  fitting_id=?";
    public static final String FITTING_STATUS = "select `fitting_status` from wms.fitting_detail where fitting_id=?";
    public static final String GET_FACILITY_CODE = "select distinct facility_code from wms.order_items where shipping_package_id=?";
    public static final String DISTINCT_STATUS = "select distinct `status` from wms.order_items where shipping_package_id=?";
    public static final String ITEM_STATUS_BY_SHIPPING_ID = "SELECT status FROM wms.order_items WHERE FIND_IN_SET(shipping_package_id, ?)";
    public static final String REASSIGNMENT_STATUS_BY_SHIPPING_ID = "select * from wms.shipment_reassignment where shipping_package_id in (?);";
    public static final String GET_ORDER_ITEM_HEADER = "select * from order_item_header where `shipping_package_id`=?";
    public static final String GET_NEXS_ORDER_ID = "select distinct nexs_order_id from wms.order_items where `fitting_id`=?";
    public static final String STORE_CONSOLIDATION_INFO = "select * from store_order_consolidation where child_id=?";
    public static final String GET_ITEM_TYPE = "select distinct item_type from wms.order_items where order_item_id=?";
    public static final String GET_DO_SHIPMENT_DETAILS = "SELECT  oi.* FROM orders o JOIN order_items oi ON oi.nexs_order_id = o.id WHERE o.increment_id = ?";
    public static final String GET_LATEST_BULK_ORDER = "select * from order_items where channel='FranchiseBulk' order by created_at desc limit 10";
    public static final String GET_LOCATION_ID_DETAILS ="select * from order_items where location_id=?";
    public static final String GET_LATEST_ORDER = "select * from order_items where channel='CUSTOM' and nav_channel='WebDTC'and status='IN_PICKING' order by created_at desc limit 10";

}
