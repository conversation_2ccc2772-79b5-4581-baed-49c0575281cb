package com.lenskart.nexs.database;

public class PickingQueries {

    public static String GET_PICKING_STATUS = "select distinct `status` from picking.picklist_order_item where `shipment_id`=?";
    public static String WAVE_ID = "select distinct(picking_summary_id) from picking.picking_detail where shipment_id=?";
    public static String ORDER_ITEM_ID = "select id,product_id from picking.picking_detail where `shipment_id`=?";
    public static String PICKING_DETAILS_STATUS = "select `status` from picking.picking_detail where shipment_id =?";
    public static String GET_SHIPPING_PACKAGE_ID_BY_GROUP_ID = "select distinct(shipment_id) from picking.picking_detail where group_id=?";
    public static String GET_DETAILS ="select * from picking.picking_detail where id=?";
    public static String GET_FITTING_ID ="select fitting_id from picking.picking_detail where shipment_id=?";


}
