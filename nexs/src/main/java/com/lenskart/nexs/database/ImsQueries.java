package com.lenskart.nexs.database;

public class ImsQueries {
    public static String GET_BARCODE_ITEM_DETAILS = "select * from nexs_ims.barcode_item where barcode=? and legal_owner=?;";
    public static String GET_GAA_BARCODES = "select `barcode` from nexs_ims.barcode_item where pid=? and `condition`='GOOD' and `availability`='AVAILABLE' and `status`='AVAILABLE' and facility=? and legal_owner=? limit ?;";
    public static String GET_BARCODE_ITEM_DETAILS_BY_BARCODE = "select * from nexs_ims.barcode_item where barcode=?;";
    public static String GET_BARCODE_COUNT = "select count(*) from nexs_ims.barcode_item where barcode=? and legal_owner=?;";
    public static String UPDATE_BARCODE_STATUS = "UPDATE nexs_ims.barcode_item set `condition`=? , `availability`=? , `status`=? where `barcode`=? and legal_owner=?;";
    public static String GET_STATUS_CONDITION_AVAILABILITY_OF_BARCODE = "select `condition`,`availability`, `status` from nexs_ims.barcode_item where barcode=? and legal_owner=?;";


}
