package com.lenskart.pos.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.commons.utils.DateUtils;
import com.lenskart.pos.model.PosContext;
import org.testng.annotations.DataProvider;
import java.util.List;

import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;

public class PosDataProvider {

    @DataProvider(name = "marginContext")
    public Object[][] marginContext() {
        return new Object[][]{

                {

                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .startDate(DateUtils.formatDateToString(DateUtils.getFirstDayOfCurrentMonth()))
                                        .endDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth()))
                                        .country(Countries.IN)
                                        .storeId("35")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                                .build()

                }
        };
    }
}

