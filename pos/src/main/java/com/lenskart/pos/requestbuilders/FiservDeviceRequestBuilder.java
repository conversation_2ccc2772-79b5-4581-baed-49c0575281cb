package com.lenskart.pos.requestbuilders;

import com.lenskart.pos.model.PosContext;
import org.json.JSONObject;

public class FiservDeviceRequestBuilder {
    public static String fiservDeviceInsertPayload(PosContext.FiservDeviceInsertMapper mapper) {
        JSONObject payload = new JSONObject();
        payload.put("colorCode", mapper.getColorCode());
        payload.put("deviceColor", mapper.getDeviceColor());
        payload.put("serialNumber",mapper.getSerialNumber());
        return payload.toString();
    }

    public static String fiservDeviceUpdatePayload(PosContext.FiservDeviceUpdateMapper mapper) {
        JSONObject payload = new JSONObject();
        payload.put("colorCode", mapper.getColorCode());
        payload.put("deviceColor", mapper.getDeviceColor());
        payload.put("serialNumber",mapper.getSerialNumber());
        return payload.toString();
    }
}
