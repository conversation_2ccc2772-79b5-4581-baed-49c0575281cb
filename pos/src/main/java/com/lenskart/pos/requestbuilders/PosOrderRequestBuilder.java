package com.lenskart.pos.requestbuilders;

import com.lenskart.commons.model.ItemType;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.juno.schema.v2.common.LensOnlyDetails;
import com.lenskart.juno.schema.v2.common.LensOnlyImageUrl;
import com.lenskart.juno.schema.v2.common.Price;
import com.lenskart.pos.config.PosConfig;
import com.lenskart.pos.model.*;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.lenskart.commons.constants.Constants.LENS_ONLY_PRODUCTS;
import static com.lenskart.commons.model.ProductTypes.CONTACT_LENS;
import static com.lenskart.commons.model.ProductTypes.INSURANCE;


public class PosOrderRequestBuilder {

    public static JSONObject createPayloadForSession(PosConfig.PosUser user) {
        JSONObject payload = new JSONObject();
        payload.put("userName", user.getUsername());
        payload.put("password", user.getPassword());
        return payload;
    }


    public static AddToCartRequest createPayloadForAddCart(OrderContext.ProductList product) {

        AddToCartRequest request = AddToCartRequest.builder()
                .posMisc(Map.of("jitRuleIdNew", "66e960898a14043075a8bbfb", "isAddedViaDeepLink", false))
                .quantity(product.getQuantity())
                .customer(new JSONObject())
                .productId(Integer.parseInt(product.getProductId()))
                .barCode(product.getBarCode())
                .showLiquidationDiscount(false)
                .addOns("")
                .additionalProducts(new ArrayList<>())
                .build();
        if ("CONTACT_LENS".equalsIgnoreCase(product.getProductType().name())) {
            PrescriptionData.EyeData left = new PrescriptionData.EyeData();
            PrescriptionData.EyeData right = new PrescriptionData.EyeData();
            request.setQuantity(2);
            left.setSph("-0.50");
            left.setBoxes("1");

            right.setSph("-0.50");
            right.setBoxes("1");

            PrescriptionData prescriptionData = new PrescriptionData();
            prescriptionData.setLeft(left);
            prescriptionData.setRight(right);
//            JSONObject left = new JSONObject();
//            left.put("sph", "-0.50");
//            left.put("boxes", "1");
//            JSONObject right = new JSONObject();
//            right.put("sph", "-0.50");
//            right.put("boxes", "1");

        //            JSONObject prescription = new JSONObject();
        //            prescription.put("left", left);
        //            prescription.put("right", right);
        //            prescription.put("recordedAt", 0);

            System.out.println("Setting prescription for contact lens product...");
            request.setPrescription(prescriptionData);
            request.setStoreInventory(0);

            return request;
        } else if (product.getProductType().equals(INSURANCE)) {
            request.setComboSwitcherProducts(new ArrayList<>());
            request.setStoreInventory(0); // optional
            return request;

        } else if (product.getItemType().equals(ItemType.LOCAL_FITTING)) {

            request.setPackageId(product.getPackageId());
            request.setStoreInventory(2);
            PowerTypes powerType = product.getPowerType();
            if (powerType != null) {
                request.setPowerType(powerType.getDisplayName());
            }

            return request;

        } else if (product.getItemType().equals(ItemType.LENS_ONLY_LOCAL_FITTING_IN_COCO)) {
            request.setPackageId(product.getPackageId());
            request.setStoreInventory(2);
            request.setPowerType("SINGLE_VISION");
            LensOnlyDetails lensOnlyDetails =  new LensOnlyDetails();
            lensOnlyDetails.setBrand("Vincent Chase");
            lensOnlyDetails.setFrameAge("Less than 6 months old");

            Price price = new Price();
            price.setValue(BigDecimal.valueOf(100));
            price.setCurrencyCode("INR");
            lensOnlyDetails.setPrice(price);

            LensOnlyImageUrl imageUrls = new LensOnlyImageUrl();
            imageUrls.setFront("https://pos-static.lenskart.com/106/lens_only/image_1754389943547.jpeg");
            imageUrls.setSide("https://pos-static.lenskart.com/106/lens_only/image_1754389943547.jpeg");
            lensOnlyDetails.setImageUrls(imageUrls);
            request.setLensOnlyDetails(lensOnlyDetails);
            return request;

        }else if (product.getItemType().equals(ItemType.OTC)) {
            request.setPackageId(product.getPackageId());
            request.setStoreInventory(1);

            PowerTypes powerType = product.getPowerType();
            if (powerType != null) {
                request.setPowerType(powerType.getDisplayName());
            }

            request.setComboSwitcherProducts(new ArrayList<>());
            return request;
        } else {
            request.setPackageId(product.getPackageId());

            PowerTypes powerType = product.getPowerType();
            if (powerType != null) {
                request.setPowerType(powerType.getDisplayName());
            }

            request.setComboSwitcherProducts(new ArrayList<>());
            return request;
        }
    }


    public static List<Map<String, Object>> createItemsPayload(CartResponse cartResponse, OrderContext orderContext) {
        List<Map<String, Object>> items = new ArrayList<>();

        if (cartResponse.getItems() != null && !cartResponse.getItems().isEmpty()) {
            Map<String, Object> lensOnlyDetails = new HashMap<>();
            for (Object itemObj : cartResponse.getItems()) {
                // Convert Object to Map for easier access
                Map<String, Object> item = (Map<String, Object>) itemObj;
                if (LENS_ONLY_PRODUCTS.contains( String.valueOf(item.get("productId")))) {
                    lensOnlyDetails.put("brand", "Vincent Chase");
                    lensOnlyDetails.put("frameAge", "More than 1 year old");

                    Map<String, String> imageUrls = new HashMap<>();
                    imageUrls.put("front", "https://pos-static.lenskart.com/106/lens_only/image_1754389943547.jpeg");
                    imageUrls.put("side", "https://pos-static.lenskart.com/106/lens_only/image_1754389943547.jpeg");

                    lensOnlyDetails.put("imageUrls", imageUrls);

                    Map<String, Object> price = new HashMap<>();
                    price.put("currencyCode", "INR");
                    price.put("value", 500);

                    lensOnlyDetails.put("price", price);
                }

                // Extract required fields
                int quantity = (int) item.getOrDefault("quantity", 1);
                String productTypeValue = (String) item.getOrDefault("classification", "eyeframe");
                // *** Add this condition to override quantity for contact_lens ***
                if ("contact_lens".equalsIgnoreCase(productTypeValue)) {
                    quantity = 2;
                }
                String productId = String.valueOf(item.get("productId"));
//                String productTypeValue = (String) item.getOrDefault("classification", "eyeframe");

                // Extract prescription type from cart response
                String prescriptionType = null;
                boolean isPowerRequired = true;

                // First check if prescription type exists in the prescription object
                if (item.containsKey("prescription") && item.get("prescription") != null) {
                    Map<String, Object> prescriptionData = (Map<String, Object>) item.get("prescription");
                    if (prescriptionData.containsKey("type")) {
                        prescriptionType = (String) prescriptionData.get("type");

                        // Check if it's zero_power
                        if ("zero_power".equalsIgnoreCase(prescriptionType)) {
                            isPowerRequired = false;
                        }
                    }
                }

                // Check if powerRequired is explicitly set
                if (item.containsKey("powerRequired")) {
                    Object powerRequiredObj = item.get("powerRequired");
                    if (powerRequiredObj instanceof Boolean) {
                        isPowerRequired = (Boolean) powerRequiredObj;
                    } else if (powerRequiredObj instanceof Number) {
                        isPowerRequired = ((Number) powerRequiredObj).intValue() == 1;
                    } else if (powerRequiredObj instanceof String) {
                        isPowerRequired = "1".equals(powerRequiredObj) ||
                                "true".equalsIgnoreCase((String) powerRequiredObj) ||
                                "POWER_REQUIRED".equalsIgnoreCase((String) powerRequiredObj);
                    }
                }

                // Extract options data
                List<Map<String, Object>> options = new ArrayList<>();
                if (item.containsKey("options") && item.get("options") != null) {
                    List<Object> optionsList = (List<Object>) item.get("options");
                    for (Object optObj : optionsList) {
                        Map<String, Object> option = (Map<String, Object>) optObj;
                        String oid = (String) option.get("oid");
                        String type = (String) option.get("type");

                        // If prescription type is not set yet, get it from options
                        if (prescriptionType == null) {
                            prescriptionType = type;

                            // Check if it's zero_power
                            if ("zero_power".equalsIgnoreCase(prescriptionType)) {
                                isPowerRequired = false;
                            }
                        }

                        options.add(Map.of(
                                "oid", oid,
                                "type", type
                        ));
                    }
                }

                // If prescription type is still null, try to determine from product type
                if (prescriptionType == null) {
                    // Try to determine based on product type and power required flag
                    if (!isPowerRequired) {
                        prescriptionType = "zero_power";
                    } else if (productTypeValue.equalsIgnoreCase("eyeframe")) {
                        prescriptionType = "single_vision";
                    } else if (productTypeValue.equalsIgnoreCase("sunglasses")) {
                        prescriptionType = "sunglasses";
                    } else {
                        // Last resort fallback
                        prescriptionType = "single_vision";
                    }
                }

                // Create prescription data
                Map<String, Object> prescription = new HashMap<>();
                prescription.put("type", prescriptionType);
                prescription.put("category", productTypeValue);

                // Determine delivery option based on item type from data provider
                String deliveryOption = determineDeliveryOption(productId, orderContext);

                // Create the item map
                Map<String, Object> itemMap = new HashMap<>();
                itemMap.put("deliveryOption", deliveryOption);
                itemMap.put("quantity", quantity);
                itemMap.put("productTypeValue", productTypeValue);
                itemMap.put("productId", productId);
                itemMap.put("options", options);
                itemMap.put("prescription", prescription);
                itemMap.put("powerRequired", isPowerRequired ? 1 : 0);
                itemMap.put("lensOnlyDetails", lensOnlyDetails);
                itemMap.put("lensOnlyBarcode", orderContext.getProductLists().getFirst().getLensOnlyBarcode());

                items.add(itemMap);
            }
        }

        return items;
    }

    // Determine delivery option based on item type from data provider

    private static String determineDeliveryOption(String productId, OrderContext orderContext) {

        return orderContext.getProductLists().stream()
                .filter(product -> productId.equals(product.getProductId()))
                .findFirst()
                .map(product -> {
                    ItemType itemType = product.getItemType();
                    if (itemType != null) {
                        return switch (itemType) {
                            case LOCAL_FITTING, LENS_ONLY_LOCAL_FITTING_IN_COCO -> "LOCAL_FITTING";
                            case PLANT_FITTINGS, PLANT_FITTINGS_WITH_GATEPASS -> "PLANT_FITTING";
                            default -> "STANDARD";
                        };
                    }
                    return "STANDARD";
                })
                .orElse("STANDARD");
    }

    public static OrderRequest createPayloadForOrderPayment(OrderContext orderContext, CartResponse cartResponse) {
        List<Map<String, Object>> itemsList = createItemsPayload(cartResponse, orderContext);
        GiftVoucher voucher = createVoucher(cartResponse);

        LensOnly lensonly = new LensOnly();
        boolean isLensOnly = false;

        for(Object itemObj : cartResponse.getItems()){
            Map<String, Object> item = (Map<String, Object>) itemObj;
            if (LENS_ONLY_PRODUCTS.contains(String.valueOf(item.get("productId")))) {
                System.out.println("THIS IS A LENS ONLY PRODUCT");
                lensonly.setIsLensOnlyOrder(true);
                lensonly.setFrameSource("0");
                lensonly.setIndemnityFlag("0");
                isLensOnly = true;
            }
        }

        // Calculate delivery date (current date + 2 days in milliseconds)
        long deliveryDateMillis = System.currentTimeMillis() + (2 * 24 * 60 * 60 * 1000);

        OrderRequest request = OrderRequest.builder()
                .items(Collections.unmodifiableList(itemsList))
                .deliveryOption("STANDARD")
                .isNewLensOnlyFlow(isLensOnly)
                .loggedInUserId("106")
                .powerSkipped(1)
                .frameSelector(Map.of(
                        "id", "1519756",
                        "name", "Automation User"
                ))
                .communication(Map.of(
                        "phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode(),
                        "mobileNumber", orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber(),
                        "firstName", "Automation User",
                        "lastName", "User"
                ))
                .orderBeforePayment(false)
                .customerComment("")
                .deliveryDate(deliveryDateMillis)
                .shipToCust(orderContext.getShipToCust())
                .deliveryTat("Estimated Delivery Date : " + new SimpleDateFormat("dd-MMM-yyyy").format(new Date(deliveryDateMillis)))
                .cartId(Integer.parseInt(String.valueOf(orderContext.getCartId())))
                .customer(Map.of(
                        "firstName", "Automation",
                        "phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode(),
                        "email", "<EMAIL>",
                        "lastName", "User",
                        "gender", "male",
                        "telephone", orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber()
                ))
                .paymentDetails(Map.of(
                        "authCodePo", "",
                        "prepaidAmount", orderContext.getFinalOrderAmount(),
                        "paymentMethod", isLensOnly ? "medibuddy": "offlinecash",
                        "referenceId", "" // Added referenceId
                )).giftVoucher(voucher).lensonly(lensonly)
                .build();
        return request;
    }

    public static JSONObject createPayloadForDeliveryEstimate(OrderContext orderContext) {
        JSONObject estimatePayload = new JSONObject();
        estimatePayload.put("trueLastPiece", false);
        estimatePayload.put("lastPiece", true);
        estimatePayload.put("lensOnly", false);
        estimatePayload.put("shippingCountry", orderContext.getCountryCodeMapper().getCountry());
        estimatePayload.put("deliveryOptions", Arrays.asList("LOCAL_FITTING", "STANDARD"));
        estimatePayload.put("pincode", orderContext.getCountryCodeMapper().getPinCode());

        return estimatePayload;
    }

    public static JSONObject createPayloadForCartItemUpdate(OrderContext orderContext, CartResponse cartResponse) {
        JSONObject cartItemUpdatePayload = new JSONObject();

        // Create inventory object
        JSONObject inventory = new JSONObject();
        inventory.put("posStoreInventoryId", "2");

        // Create item object
        JSONObject item = new JSONObject();
        item.put("barCode", "");
        item.put("lensOnlyBarcode", orderContext.getProductLists().get(0).getLensOnlyBarcode());
        item.put("deliveryOption", "LOCAL_FITTING");
        item.put("wasPowerWiseLensPackageInventoryAvailable", true);
            Map<String, Object> cartItem = (Map<String, Object>) cartResponse.getItems().get(0);
            item.put("id",cartItem.get("id"));
        item.put("inventory", inventory);

        // Add item to items array
        JSONArray itemsArray = new JSONArray();
        itemsArray.put(item);

        // Populate payload
        cartItemUpdatePayload.put("items", itemsArray);
        cartItemUpdatePayload.put("shippingCountry", orderContext.getCountryCodeMapper().getCountry());
        cartItemUpdatePayload.put("pinCode", orderContext.getCountryCodeMapper().getPinCode());
        cartItemUpdatePayload.put("shipToCustomerRecommendation", false);
        cartItemUpdatePayload.put("shipToCust", false);

        return cartItemUpdatePayload;
    }

    public static JSONObject createGiftVoucherPayload(OrderContext orderContext){
        JSONObject giftVoucherPayload = new JSONObject();
        giftVoucherPayload.put("customerEmail", "<EMAIL>");
        giftVoucherPayload.put("phoneCode", "+91");
        giftVoucherPayload.put("customerMobile", orderContext.getPhoneNumber());
        return giftVoucherPayload;
    }

    public static JSONObject createCartsLocalFittingPayload(OrderContext orderContext){
        JSONObject cartsLocalFittingPayload = new JSONObject();
        // Empty arrays for itemIds and bufferItemIds
        cartsLocalFittingPayload.put("itemIds", new JSONArray());
        cartsLocalFittingPayload.put("bufferItemIds", new JSONArray());
        return cartsLocalFittingPayload;
    }

    public static JSONObject createPayloadForStoreId(OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        payload.put("store_id", orderContext.getPosStoreMapper().getStoreId());
        return payload;
    }

    public static GiftVoucher createVoucher(CartResponse cartResponse){

        String gvCode = StringUtils.EMPTY;
        Optional<com.lenskart.pos.model.Discount> gv = cartResponse.getTotals().getDiscounts()
                .stream().filter(i -> "gv".equalsIgnoreCase(i.getType())).findFirst();
        if(gv.isPresent()) {
            gvCode = gv.get().getCode();
        }
        GiftVoucher giftVoucher = new GiftVoucher();
        giftVoucher.setVoucherCode(gvCode);
        return giftVoucher;
    }

    public static ExchangeItemRequest createExchangeRequest(OrderContext orderContext) {

        return ExchangeItemRequest.builder()
                .items(List.of(ExchangeItemRequest.ItemData.builder()
                        .id(String.valueOf(818907768))  // handle this hardcoding
                        .productId(String.valueOf(93870)) // handle this hardcoding
                        .reasons((ExchangeItemRequest.Reasons) List.of(ExchangeItemRequest.Reasons.builder()
                                .primaryReasonId(2003) // handle this hardcoding
                                .primaryReasonText("Fit uncomfort") // handle this hardcoding
                                .secondaryReasonId(206)
                                .secondaryReasonText("Frame fit uncomfortable uneven alignement issue")
                                .type("RETURN")
                                .build()))
                        .qcStatus("Pass")
                        .isPackageAssigned(false)
                        .build()))
                .build();


    }

    public static CreateReturnRefundRequest createRefundRequest(OrderContext orderContext) {

        CreateReturnRefundRequest request = CreateReturnRefundRequest.builder()
                .items(List.of(ExchangeItemRequest.ItemData.builder()
                        .id(String.valueOf(818907768))  // handle this hardcoding
                        .productId(String.valueOf(93870)) // handle this hardcoding
                        .reasons((ExchangeItemRequest.Reasons) List.of(ExchangeItemRequest.Reasons.builder()
                                .primaryReasonId(2003) // handle this hardcoding
                                .primaryReasonText("Fit uncomfort") // handle this hardcoding
                                .secondaryReasonId(206)
                                .secondaryReasonText("Frame fit uncomfortable uneven alignement issue")
                                .type("RETURN")
                                .build()))
                        .qcStatus("Pass")
                        .isPackageAssigned(false)
                        .build()))
                .facilityCode("ST47")
                .salesmanPhoneNumber("8878894293")
                .orderId(String.valueOf(1311402333))
                .salesmanName("Rajesh Kumar Mishra")
                .returnMethod("store_return")
                .isCallbackRequiredToSalesman(true)
                .build();
        return request;


    }
}
