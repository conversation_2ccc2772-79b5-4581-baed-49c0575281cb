package com.lenskart.pos.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.model.ItemType;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.Constats;
import groovy.util.logging.Slf4j;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static com.lenskart.pos.constants.DbConstants.POS_DB;

@lombok.extern.slf4j.Slf4j
@Slf4j
public class PosDbUtils {
    public static String getBulkDealsOrderId(String filName) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(),
                        Constats.POS_DB,
                        PosDbQueries.GET_BUlk_DEALS_ORDER_ID,
                        filName)
                .getFirst().get("deals_order_id").toString();
    }
    public static List<Map<String, Object>> getOrderDetails(OrderContext orderContext) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.POS_CLUSTER.getClusterName(),
                        POS_DB,
                        FranchiseOrders.ORDER_DETAILS,
                        orderContext.getOrderId());
    }
    public static List<Map<String, Object>> getItemTypeByIncrementId(OrderContext orderContext) {
        return MySQLQueryExecutor.executeQuery(
                Cluster.POS_CLUSTER.getClusterName(),
                POS_DB,
                FranchiseOrders.ITEM_TYPE_BY_INCREMENT_ID,
                orderContext.getOrderId());
    }
    public static List<Map<String, Object>> getKafkaDetailsByIncrementId(OrderContext orderContext) {
        return MySQLQueryExecutor.executeQuery(
                Cluster.POS_CLUSTER.getClusterName(),
                POS_DB,
                FranchiseOrders.KAFKA_QUEUE_STATUS_BY_INCREMENT_ID,
                orderContext.getOrderId());
    }



    public static List<Map<String, Object>> getBulkAutoReplenishment(OrderContext orderContext) {
        boolean isLocalOrOtc = orderContext.getProductLists().stream()
                .map(OrderContext.ProductList::getItemType)
                .anyMatch(itemType ->
                        itemType != null &&
                                (itemType.equals(ItemType.LOCAL_FITTING) || itemType.equals(ItemType.OTC)));

        if (!isLocalOrOtc) {
            log.info("Skipping Bulk Auto Replenishment query: No LOCAL_FITTING or OTC items found");

            Map<String, Object> dummy = new HashMap<>();
            dummy.put("pos_order_id", -1);
            dummy.put("status", "SKIPPED");
            return List.of(dummy);
        }

        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Sleep interrupted: Bulk Auto Replenish thread: {}", e.getMessage());
        }

        return MySQLQueryExecutor.executeQuery(
                Cluster.POS_CLUSTER.getClusterName(),
                POS_DB,
                BulkAutoReplenishment.BULK_AUTO_REPLENISHMENT);
    }



}