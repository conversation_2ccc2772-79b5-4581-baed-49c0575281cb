package com.lenskart.pos.database;

public class FranchiseOrders {
    public static String ORDER_DETAILS = "select dealskart_order_id from POS.franchise_orders where dealskart_order_id =?";
    public static String ITEM_TYPE_BY_INCREMENT_ID= "SELECT foi.item_type FROM POS.franchise_order_items foi JOIN POS.franchise_orders fo ON fo.increment_id = foi.increment_id  WHERE fo.dealskart_order_id = ?;";
    public static String KAFKA_QUEUE_STATUS_BY_INCREMENT_ID = "SELECT kqs.pos_increment_id FROM POS.kafka_queue_status kqs JOIN POS.franchise_orders fo ON fo.increment_id = kqs.pos_increment_id WHERE fo.dealskart_order_id = ?";
}
