package com.lenskart.pos.validator;

import com.fasterxml.jackson.databind.JsonNode;
import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.ItemType;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.pos.database.PosDbUtils;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
//import org.bson.Document;
import org.json.JSONObject;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;
@SuperBuilder
@Slf4j
public class OrderValidator implements IValidator {

    private final OrderContext orderContext;

    public OrderValidator(OrderContext orderContext) {
        this.orderContext = orderContext;
    }

    @Override
    public void validateNode() {
        if (orderContext == null) {
            throw new IllegalArgumentException("OrderContext cannot be null.");
        }

        // Skip Order ID validation for bulk orders
        if (isBulkOrder()) {
//            log.info("Skipping Order ID validation for bulk order");
            return;
        }

        int orderId = orderContext.getOrderId();
        if (orderId == 0) {
            throw new IllegalArgumentException("Order ID is missing in OrderContext.");
        }

    }


    // Check if the order context contains bulk order items

    private boolean isBulkOrder() {
        if (orderContext.getProductLists() == null || orderContext.getProductLists().isEmpty()) {
            return false;
        }

        return orderContext.getProductLists().stream()
                .anyMatch(product -> product.getItemType() == ItemType.BULK_ORDER);
    }


    @Override
    public void validateDBEntities() {
        // Skip DB validation for bulk orders
        if (isBulkOrder()) {
//            log.info("Skipping DB validation for bulk order");
            return;
        }

        validateOrderIdInDb();
        validateItemTypeByIncrementId();
        validateBulkAutoReplenishment();
        validateKafkaQueueStatusByIncrementId();

    }

    private void validateOrderIdInDb() {
        List<Map<String, Object>> orderDetails = PosDbUtils.getOrderDetails(orderContext);

        if (orderDetails == null || orderDetails.isEmpty()) {
            throw new IllegalStateException("No records found in franchise_orders for orderId: " + orderContext.getOrderId());
        }

        Map<String, Object> dbOrder = orderDetails.get(0);
        Object dbOrderId = dbOrder.get("dealskart_order_id");
        if (dbOrderId == null) {
            throw new IllegalStateException("dealskart_order_id is missing in the franchise_orders DB record.");
        }
    }

    private void validateItemTypeByIncrementId() {

        List<Map<String, Object>> itemTypeByIncrementId = PosDbUtils.getItemTypeByIncrementId(orderContext);
        if (itemTypeByIncrementId == null) {
            throw new IllegalStateException("item_type is missing in franchise_order_items for incrementId: " + itemTypeByIncrementId.get(0));
        }

    }

    private void validateKafkaQueueStatusByIncrementId() {
        boolean kafkaEntryExists = AwaitUtils.waitUntil(() -> {
                    List<Map<String, Object>> kafkaDetailsByIncrementId = PosDbUtils.getKafkaDetailsByIncrementId(orderContext);
                    return kafkaDetailsByIncrementId != null && !kafkaDetailsByIncrementId.isEmpty();
                },
                "Kafka queue entry for orderId: " + orderContext.getOrderId(),
                AwaitUtils.LONG_TIMEOUT);

        if (!kafkaEntryExists) {
            throw new IllegalStateException("No Kafka queue entry found in POS.kafka_queue_status for orderId: " + orderContext.getOrderId());
        }
    }

    private void validateBulkAutoReplenishment() {
        final Map<String, Object>[] latestRecordHolder = new Map[1];

        boolean found = AwaitUtils.retryOperation(
                () -> {
                    try {
                        log.debug("Attempting to fetch auto replenishment record from bulk_order table");
                        List<Map<String, Object>> bulkResult = PosDbUtils.getBulkAutoReplenishment(orderContext);
                        if (bulkResult != null && !bulkResult.isEmpty()) {
                            latestRecordHolder[0] = bulkResult.get(0);
                            log.info("Found auto replenishment record: {}", latestRecordHolder[0]);
                            return true;
                        } else {
                            log.warn("No records found in bulk_order table yet. Retrying...");
                            return false;
                        }
                    } catch (Exception e) {
                        log.warn("Exception while fetching bulk_order record: {}", e.getMessage());
                        return false;
                    }
                },
                "Fetch Auto Replenishment Record Retry",
                5, // max attempts
                Duration.ofSeconds(4) // 1 second poll interval
        );

        Map<String, Object> latestRecord = latestRecordHolder[0];

        if (!found || latestRecord == null) {
            log.warn("No auto replenishment records found in bulk_order table after retries. Skipping validation.");
            return;
        }

        // For non-OTC and non-LF order handling
        if ("SKIPPED".equals(latestRecord.get("status"))) {
            log.info("Skipping bulk auto replenishment validation: Marked as SKIPPED in result.");
            return;
        }

        Object uploadedFileObj = latestRecord.get("uploaded_file");
        if (uploadedFileObj == null || !(uploadedFileObj instanceof String uploadedFile) || !uploadedFile.contains("auto")) {
            log.warn("Latest bulk_order record does not contain an 'auto' replenishment file. Skipping validation.");
            return;
        }

        log.info("Bulk auto replenishment record validated successfully.");
    }


}




