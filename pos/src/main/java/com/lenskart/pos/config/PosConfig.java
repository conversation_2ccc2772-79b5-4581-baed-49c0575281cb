package com.lenskart.pos.config;


import com.lenskart.commons.model.Countries;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Configuration class for the Pos module
 */
@Data
@NoArgsConstructor
public class PosConfig {

    // Environment configurations
    private Map<String, EnvironmentConfig> environments = new HashMap<>();

    /**
     * Gets the configuration for a specific environment
     *
     * @param environment Environment name (e.g., "preprod", "prod")
     * @return Environment configuration
     */
    public EnvironmentConfig getEnvironment(String environment) {
        return environments.get(environment);
    }

    /**
     * Configuration for a specific environment
     */
    @Data
    @NoArgsConstructor
    public static class EnvironmentConfig {
        // Base URLs for different services
        private Map<String, String> baseUrls = new HashMap<>();

        // Country-specific POS user configurations
        private Map<String, CountryPosConfig> countries = new HashMap<>();

        /**
         * Gets the base URL for a specific service
         *
         * @param serviceName Service name
         * @return Base URL for the service
         */
        public String getBaseUrl(String serviceName) {
            return baseUrls.get(serviceName);
        }

        /**
         * Gets the country-specific POS configuration
         *
         * @param countryCode Country code (e.g., "IN", "SG", "US")
         * @return Country POS configuration
         */
        public CountryPosConfig getCountryConfig(String countryCode) {
            return countries.get(countryCode);
        }

        /**
         * Gets all country configurations
         *
         * @return Map of all country configurations
         */
        public Map<String, CountryPosConfig> getAllCountryConfigs() {
            return new HashMap<>(countries);
        }
    }

    /**
     * Country-specific POS configuration
     */
    @Data
    @NoArgsConstructor
    public static class CountryPosConfig {
        // Country information
        private Countries countryCode;
        private String countryName;

        // Default settings for this country
        private String defaultCurrency;
        private String defaultLanguage;
        private String defaultTimezone;

        // POS users for this country
        private List<PosUser> posUsers;

        /**
         * Get POS user by username
         *
         * @param username Username to search for
         * @return POS user if found, null otherwise
         */
        public PosUser getPosUser(String username) {
            if (posUsers == null || username == null) {
                return null;
            }

            return posUsers.stream()
                    .filter(user -> username.equals(user.getUsername()))
                    .findFirst()
                    .orElse(null);
        }

        /**
         * Get POS user by store ID
         *
         * @param storeId Store ID to search for
         * @return POS user if found, null otherwise
         */
        public PosUser getPosUserByStoreId(String storeId) {
            if (posUsers == null || storeId == null) {
                return null;
            }

            return posUsers.stream()
                    .filter(user -> storeId.equals(user.getStoreId()))
                    .findFirst()
                    .orElse(null);
        }

        /**
         * Get all active POS users
         *
         * @return List of active POS users
         */
        public List<PosUser> getActivePosUsers() {
            if (posUsers == null) {
                return List.of();
            }

            return posUsers.stream()
                    .filter(PosUser::isActive)
                    .toList();
        }
    }

    /**
     * POS User configuration
     */
    @Data
    @NoArgsConstructor
    public static class PosUser {
        // User identification
        private String username;
        private String password;
        private String userId;

        // Store information
        private String storeId;
        private boolean active = true;

        /**
         * Check if user is valid for authentication
         *
         * @return true if user is active and has required fields, false otherwise
         */
        public boolean isValidForAuth() {
            return active &&
                   username != null && !username.trim().isEmpty() &&
                   password != null && !password.trim().isEmpty() &&
                   storeId != null && !storeId.trim().isEmpty();
        }
    }
}
