package com.lenskart.pos.config;

import com.lenskart.commons.config.ConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Configuration provider for POS module that wraps PosConfigRegistry
 * to implement the standard ConfigProvider interface.
 */
@Slf4j
public class PosConfigProvider implements ConfigProvider {
    
    private final PosConfigRegistry posRegistry;
    
    // Singleton instance
    private static volatile PosConfigProvider instance;
    
    /**
     * Private constructor for singleton pattern
     */
    private PosConfigProvider() {
        this.posRegistry = PosConfigRegistry.getInstance();
    }
    
    /**
     * Gets the singleton instance of PosConfigProvider
     *
     * @return The singleton instance
     */
    public static PosConfigProvider getInstance() {
        if (instance == null) {
            synchronized (PosConfigProvider.class) {
                if (instance == null) {
                    instance = new PosConfigProvider();
                }
            }
        }
        return instance;
    }
    
    @Override
    public String getBaseUrl(String serviceName) {
        try {
            return posRegistry.getBaseUrl(serviceName);
        } catch (Exception e) {
            log.error("Error getting base URL for service {} from POS registry: {}", 
                serviceName, e.getMessage());
            return null;
        }
    }
    
    @Override
    public Map<String, String> getAllBaseUrls() {
        try {
            return posRegistry.getAllBaseUrls();
        } catch (Exception e) {
            log.error("Error getting all base URLs from POS registry: {}", e.getMessage());
            return Map.of();
        }
    }
    
    @Override
    public void refresh() {
        try {
            posRegistry.refresh();
            log.info("POS configuration refreshed successfully");
        } catch (Exception e) {
            log.error("Error refreshing POS configuration: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isInitialized() {
        return posRegistry != null;
    }
    
    @Override
    public String getProviderName() {
        return "PosConfigProvider";
    }
    
    /**
     * Gets the underlying POS registry for direct access if needed
     *
     * @return The PosConfigRegistry instance
     */
    public PosConfigRegistry getPosRegistry() {
        return posRegistry;
    }
}
