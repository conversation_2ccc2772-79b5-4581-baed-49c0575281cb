package com.lenskart.pos.config;

import com.lenskart.commons.model.Countries;
import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility class to load Pos configurations from YAML files
 */
@Slf4j
public class PosConfigLoader {
    // Configuration cache
    private static final Map<String, PosConfig> configCache = new ConcurrentHashMap<>();

    // Default values
    private static final String DEFAULT_CONFIG_PATH = "pos.yml";
    private static final String DEFAULT_ENVIRONMENT = "preprod";

    /**
     * Loads the Juno configuration from the default YAML file
     *
     * @return JunoConfig object with configuration details
     */
    public static PosConfig loadConfig() {
        return loadConfig(DEFAULT_CONFIG_PATH);
    }

    /**
     * Loads the Juno configuration from a specified YAML file
     *
     * @param configPath Path to the YAML file
     * @return JunoConfig object with configuration details
     */
    public static PosConfig loadConfig(String configPath) {
        // Return cached config if available
        if (configCache.containsKey(configPath)) {
            return configCache.get(configPath);
        }

        try {
            Yaml yaml = new Yaml();

            // Try to load from classpath first
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(configPath);

            // If not found in classpath, try as a file path
            if (inputStream == null) {
                try {
                    inputStream = new FileInputStream(configPath);
                } catch (IOException e) {
                    log.warn("Could not find configuration file: {}", configPath);
                    PosConfig defaultConfig = createDefaultConfig();
                    configCache.put(configPath, defaultConfig);
                    return defaultConfig;
                }
            }

            // Load the YAML file as a map
            Map<String, Object> yamlMap = yaml.load(inputStream);
            inputStream.close();

            if (yamlMap == null || yamlMap.isEmpty()) {
                log.warn("Empty or invalid configuration file: {}", configPath);
                PosConfig defaultConfig = createDefaultConfig();
                configCache.put(configPath, defaultConfig);
                return defaultConfig;
            }

            // Create a new PosConfig object
            PosConfig config = new PosConfig();

            // Process each environment in the YAML file
            for (Map.Entry<String, Object> entry : yamlMap.entrySet()) {
                String envName = entry.getKey();

                if (entry.getValue() instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> envConfig = (Map<String, Object>) entry.getValue();

                    // Create a new environment configuration
                    PosConfig.EnvironmentConfig environmentConfig = new PosConfig.EnvironmentConfig();

                    // Process base URLs if available
                    if (envConfig.containsKey("baseUrls") && envConfig.get("baseUrls") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, String> baseUrls = (Map<String, String>) envConfig.get("baseUrls");
                        environmentConfig.setBaseUrls(baseUrls);
                    }

                    // Process country configurations if available
                    if (envConfig.containsKey("countries") && envConfig.get("countries") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> countriesData = (Map<String, Object>) envConfig.get("countries");
                        Map<String, PosConfig.CountryPosConfig> countryConfigs = processCountryConfigurations(countriesData);
                        environmentConfig.setCountries(countryConfigs);
                    }

                    // Add the environment configuration to the PosConfig
                    config.getEnvironments().put(envName, environmentConfig);
                }
            }

            // Cache the config
            configCache.put(configPath, config);

            log.info("Loaded Pos configuration from: {}", configPath);
            return config;

        } catch (Exception e) {
            log.error("Failed to load Pos configuration: {}", e.getMessage(), e);
            PosConfig defaultConfig = createDefaultConfig();
            configCache.put(configPath, defaultConfig);
            return defaultConfig;
        }
    }

    /**
     * Process country configurations from YAML data
     *
     * @param countriesData Map containing country configuration data
     * @return Map of country code to CountryPosConfig
     */
    @SuppressWarnings("unchecked")
    private static Map<String, PosConfig.CountryPosConfig> processCountryConfigurations(Map<String, Object> countriesData) {
        Map<String, PosConfig.CountryPosConfig> countryConfigs = new HashMap<>();

        for (Map.Entry<String, Object> countryEntry : countriesData.entrySet()) {
            String countryCode = countryEntry.getKey();

            if (countryEntry.getValue() instanceof Map) {
                Map<String, Object> countryData = (Map<String, Object>) countryEntry.getValue();
                PosConfig.CountryPosConfig countryConfig = new PosConfig.CountryPosConfig();

                // Set basic country information
                countryConfig.setCountryCode(Countries.valueOf(countryCode.toUpperCase()));
                countryConfig.setCountryName((String) countryData.get("countryName"));
                countryConfig.setDefaultCurrency((String) countryData.get("defaultCurrency"));
                countryConfig.setDefaultLanguage((String) countryData.get("defaultLanguage"));
                countryConfig.setDefaultTimezone((String) countryData.get("defaultTimezone"));

                // Process POS users
                if (countryData.containsKey("posUsers") && countryData.get("posUsers") instanceof List) {
                    List<Object> posUsersData = (List<Object>) countryData.get("posUsers");
                    List<PosConfig.PosUser> posUsers = processPosUsers(posUsersData);
                    countryConfig.setPosUsers(posUsers);
                }

                countryConfigs.put(countryCode, countryConfig);
            }
        }

        return countryConfigs;
    }

    /**
     * Process POS users from YAML data
     *
     * @param posUsersData List containing POS user data
     * @return List of PosUser objects
     */
    @SuppressWarnings("unchecked")
    private static List<PosConfig.PosUser> processPosUsers(List<Object> posUsersData) {
        List<PosConfig.PosUser> posUsers = new ArrayList<>();

        for (Object userObj : posUsersData) {
            if (userObj instanceof Map) {
                Map<String, Object> userData = (Map<String, Object>) userObj;
                PosConfig.PosUser posUser = new PosConfig.PosUser();

                // Set user identification
                posUser.setUsername((String) userData.get("username"));
                posUser.setPassword((String) userData.get("password"));
                posUser.setUserId((String) userData.get("userId"));

                // Set store information
                posUser.setStoreId((String) userData.get("storeId"));

                // Set status
                if (userData.containsKey("active")) {
                    posUser.setActive((Boolean) userData.get("active"));
                }
                posUsers.add(posUser);
            }
        }

        return posUsers;
    }

    /**
     * Creates a default Pos configuration
     *
     * @return Default PosConfig
     */
    private static PosConfig createDefaultConfig() {
        PosConfig config = new PosConfig();

        // Create default environment configuration
        PosConfig.EnvironmentConfig environmentConfig = new PosConfig.EnvironmentConfig();

        // Add default base URLs
        Map<String, String> baseUrls = new HashMap<>();
        baseUrls.put("sessionService", "https://api-gateway.pos.preprod.lenskart.com");
        baseUrls.put("testService", "https://jsonplaceholder.typicode.com");
        environmentConfig.setBaseUrls(baseUrls);

        // Add the environment configuration to the PosConfig
        config.getEnvironments().put(DEFAULT_ENVIRONMENT, environmentConfig);

        return config;
    }

    /**
     * Clears the configuration cache
     */
    public static void clearCache() {
        configCache.clear();
        log.info("Configuration cache cleared");
    }

    /**
     * Get the default environment name
     *
     * @return Default environment name
     */
    public static String getDefaultEnvironment() {
        return DEFAULT_ENVIRONMENT;
    }

    /**
     * Get the default configuration path
     *
     * @return Default configuration path
     */
    public static String getDefaultConfigPath() {
        return DEFAULT_CONFIG_PATH;
    }
}
