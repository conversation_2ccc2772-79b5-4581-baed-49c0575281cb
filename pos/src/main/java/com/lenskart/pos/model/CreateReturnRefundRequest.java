package com.lenskart.pos.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@Data
@SuperBuilder
public class CreateReturnRefundRequest extends ExchangeItemRequest {

    private String returnMethod;
    private Object pickupAddress;
    private String facilityCode;
    private String orderId;
    private String returnSource;

    private Object kyc;

    /**
     * This value is added to the request by pos in case of insurance claim request
     */
    @JsonProperty("storeEmail")
    private String storeEmail;
    private Object nexusOrderResponse;

    private String salesmanName = "";
    private String salesmanPhoneNumber = "";
    @JsonProperty("isCallbackRequiredToSalesman")
    private boolean isCallbackRequiredToSalesman;
    private String storeFacilityCode;
    private Boolean enforceRefundAtStore = Boolean.FALSE;
    private List<Map<Integer, Integer>> uwMagentoMap;
    private Object productBarcodeBean;
}
