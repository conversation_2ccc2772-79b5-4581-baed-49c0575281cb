package com.lenskart.pos.model;


import com.lenskart.juno.schema.product.BuyPackageOfferDetail;
import com.lenskart.juno.schema.product.HighlightTag;
import com.lenskart.juno.schema.product.Images;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PackageDetail {

    private String name;
    private String sku;
    private String id;
    private String brand_image_url;
    ArrayList<Object> specifications;
    private String label;
    private String subtitle;
    private ArrayList<Object> addons;
    List<Object> deliveryOptions;
    ArrayList<Object> specificationList;
    ArrayList<Object> subPowerType;
    private Object recommendationLabel1;
    ArrayList<Object> offer;
    private Object power;
    ArrayList<Object> prices;
    Object power_recommendation;
    Object sort_order;
    private Object recommendationLabel2;
    private String warranty;
    private String offer_text;
    /**
     * Exchange Price Group ID
     */
    private String group;
    private boolean remove;
    private Boolean isRodenStockEligible;
    private boolean removePackage;

    private Boolean showNewCoatingUi;

    private String  identifier;

    List<HighlightTag>  highlightTags;
    private BuyPackageOfferDetail offerDetails;

    List<Images> images;

    private String posVideoLink;
    private String posVideoThumbnailImage;
    private int installmentPrice;
    private int tenure;
}
