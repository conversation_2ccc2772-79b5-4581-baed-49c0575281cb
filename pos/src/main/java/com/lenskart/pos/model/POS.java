package com.lenskart.pos.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum POS {


    IN_MUMBAI_STORE("STR_MUM_001", "Mumbai Store", "LKS18"),
    IN_FOFO_STORE("STR_FOFO", "Bangalore FOFO Store", "ST05" ),
    IN_ADMIN_STORE("STR_ADMIN", "Delhi Admin Store", "Admin"),
    SG_PREPROD_STORE("STR_SG_003", "Singapore Preprod Store", "SGD1"),
    AE_PREPROD_STORE("STR_LKST833", "Dubai Preprod Store", "LKST833"),
    KSA_PREPROD_STORE("STR_LKSTSA001", "KSA Preprod Store", "LKSTSA001"),
    ID_PREPROD_STORE("STR_LKST01", "Indonesia Preprod Store", "LKST01"),
    TH_PREPROD_STORE("STR_LKSTTH01", "Thailand Preprod Store", "LKSTTH01");

    private final String storeId;
    private final String storeName;
    private final String storeCode;

}