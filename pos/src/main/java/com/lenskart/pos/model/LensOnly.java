package com.lenskart.pos.model;

import lombok.*;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor

public class LensOnly {
    private String frameSource;
    private String indemnityFlag;
    private boolean isLensOnlyOrder;

    public String getFrameSource() {
        return frameSource;
    }

    public void setFrameSource(String frameSource) {
        this.frameSource = frameSource;
    }

    public String getIndemnityFlag() {
        return indemnityFlag;
    }

    public void setIndemnityFlag(String indemnityFlag) {
        this.indemnityFlag = indemnityFlag;
    }

    public boolean isIsLensOnlyOrder() {
        return isLensOnlyOrder;
    }

    public void setIsLensOnlyOrder(boolean isLensOnlyOrder) {
        this.isLensOnlyOrder = isLensOnlyOrder;
    }
}