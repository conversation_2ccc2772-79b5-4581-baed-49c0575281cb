package com.lenskart.pos.model;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.juno.schema.v2.cart.BarcodeMapping;
import com.lenskart.juno.schema.v2.cart.RimlessBar;
import com.lenskart.juno.schema.v2.common.LensOnlyDetails;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Builder
@Data
public class AddToCartRequest {

    // TODO - get rid of these models use the models exposed by the service
    private Object customer;
    private Map<String, Object> posMisc; // send jirRuleId, isAddedViaDeepLink, productVariantDetail inside posmic only
    private Long itemId;
    private Long orderId;
    private String exchangeMethod;
    private Boolean showLiquidationDiscount = false;
    private Boolean liquidationFlag;
    private Integer percentageDiscount;
    private String pincode;
    private List<Long> additionalProducts;
    private Object bankDetails;


    // Cart Items
    private String addOns;
    private String barCode;
    private BarcodeMapping barcodeMapping;
    private String isAutoDiscountEnabled;
    private String packageId;
    private String powerType;
    private int productId;
    private int quantity;
    private PrescriptionData prescription;
    @JsonIgnore
    private List<Integer> relatedProductIds;
    private String isBothEye = "N";
    private String subscriptionId;
    private String tierName;
    private int storeInventory;
    private String storeType;
    private String facilityCode;
    @Deprecated
    @JsonProperty("hasOptions")
    private Boolean hasOptions = false;

    private List<String> comboSwitcherProducts;
    private LensOnlyDetails lensOnlyDetails;

    private Boolean autoAdd = false;

    private Boolean isSbrtEnabled = false;

    private Boolean isNexsStore = false;

    private Boolean isFreebie=false;
    private RimlessBar rimlessBar;
    private Boolean isExpressCheckout;
    private Boolean setTatForUpsellItems=false;
    private Map<String, Object> posMetadata = new HashMap<>();
}
