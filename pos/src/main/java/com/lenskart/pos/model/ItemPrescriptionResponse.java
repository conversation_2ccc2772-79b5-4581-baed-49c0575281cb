package com.lenskart.pos.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ItemPrescriptionResponse {
    @JsonProperty("itemPrescriptions")
    private List<PrescriptionData> data;

    @JsonProperty("success")
    private boolean success;

    @JsonProperty("message")
    private String message;
}
