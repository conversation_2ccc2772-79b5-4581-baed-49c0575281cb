package com.lenskart.pos.model;

import com.lenskart.juno.schema.product.HighlightTag;
import com.lenskart.juno.schema.product.SpecificationGroups;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierPackageResponse {

    Object id;
    Object label;
    Object display;
    Object additional_text;
    ArrayList<Object> specificationGroups;
    ArrayList<PackageDetail> packages;
    List<SpecificationGroups> newSpecificationGroups;
    List<HighlightTag>  masterHighlightTags;
    private Boolean isPreOrder;


}
