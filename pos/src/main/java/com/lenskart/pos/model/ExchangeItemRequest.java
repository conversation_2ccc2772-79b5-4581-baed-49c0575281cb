package com.lenskart.pos.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
public class ExchangeItemRequest {

    private String currencyCode; // use Countries.getCurrencyCode()
    private List<ItemData> items;
    private List<RefundVerify> refundVerifyData;
    private Double orderRefundedAmount;
    private String source = "POS";

    @Builder
    @Data
    public static class ItemData {

        private String id; // magento item id
        private String productId;
        private String returnId;
        private boolean needApproval;
        private String needApprovalStatus;
        private String qcStatus;
        @JsonProperty("qcComments")
        private String qcComments;
        private Reasons reasons;
        private String refundMethod;
        private String exchangeMethod;
        private String orderId;
        private int orderFranchiseId;
        private int franchiseId;
        @JsonProperty("isPackageAssigned")
        private boolean isPackageAssigned;
        private String createdAt;

        /**
         * If set to true, the return request is to start a insurance claim
         */
        @JsonProperty("claimInsurance")
        private boolean claimInsurance;
    }

    @Builder
    @Data
    public static class RefundVerify {

        private String magentoItemId;
        private Double itemPrice;
        private Double itemRefundedAmount;
        private Double amountToRefund;
        private Double lkCashAmountToRefund;
        private Double lkCashPlusAmountToRefund;

    }

    @Builder
    @Data
    public static class Reasons {

        private String type;
        private Number primaryReasonId;
        private Number secondaryReasonId;
        private String additionalComments;
        private String primaryReasonText;
        private String secondaryReasonText;

    }


}
