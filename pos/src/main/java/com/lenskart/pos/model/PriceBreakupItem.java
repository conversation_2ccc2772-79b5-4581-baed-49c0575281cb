package com.lenskart.pos.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PriceBreakupItem {

    private String id;

    private String label;

    private String message;

    private BigDecimal value;

    private String type;

    private boolean goldDiscountPrice;


}
