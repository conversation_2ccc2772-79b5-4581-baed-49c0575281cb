package com.lenskart.pos.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TotalAmount {
    private static final long serialVersionUID = 6274156662927346658L;
    private String currencyCode;
    private List<com.lenskart.pos.model.Discount> discounts;
    private float shipping;
    private float subTotal;
    // private List<Tax> taxes;
    private float total;
    private float totalDiscount;
    private float totalTax;
    @JsonIgnore
    private float storeCredit;
    @JsonIgnore
    private float exchangeAmount;
    @JsonIgnore
    private float coupon;
    @JsonIgnore
    private float implicit;
    @JsonIgnore
    private float lkCash;
    @JsonIgnore
    private float lkCashPlus;
    @JsonIgnore
    private float giftVoucher;
    @JsonIgnore
    private float prepaid;
    @JsonIgnore
    private float giftCard;
    @JsonIgnore
    private float insuranceBenefit;
    @JsonIgnore
    private float payableAmount;

    public float getPayableAmount() {
        return (total + storeCredit + exchangeAmount + giftCard);
    }
}
