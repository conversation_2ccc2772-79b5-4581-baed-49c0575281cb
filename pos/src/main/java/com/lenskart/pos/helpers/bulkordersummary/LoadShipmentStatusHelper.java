package com.lenskart.pos.helpers.bulkordersummary;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.pos.endpoints.PosEndpoints.LOAD_SHIPMENT_STATUS;

@Slf4j
@SuperBuilder
public class LoadShipmentStatusHelper extends PosBaseHelper implements ServiceHelper {
    OrderContext orderContext;
    Response response;

    public ServiceHelper init(){
        headers = getHeadersWithApiClient(orderContext);
        return this;
    }

    // calls api using the service class present under the respective api package
    public ServiceHelper process(){
        response = RestUtils.post(LOAD_SHIPMENT_STATUS.getUrl(), headers, null, 200);
//        log.info(JsonUtils.convertObjectToJsonString(response.asPrettyString()));
        return this;
    }
    // uses validator class for validation
    public ServiceHelper validate(){
        return this;
    }

    // orchestrator for executing the flows
    public ServiceHelper test(){
        init();
        process();
        validate();
        return this;
    }
}
