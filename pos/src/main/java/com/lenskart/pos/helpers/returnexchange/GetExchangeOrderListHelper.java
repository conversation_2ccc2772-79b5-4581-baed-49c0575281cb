package com.lenskart.pos.helpers.returnexchange;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.*;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_EXCHANGEORDER_LIST;

@Slf4j
@SuperBuilder
public class GetExchangeOrderListHelper extends PosBaseHelper implements ServiceHelper{

      OrderContext orderContext;
      Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        queryParams = getQueryParamsForExchangeList(orderContext, 3);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_EXCHANGEORDER_LIST.getUrl(), headers, queryParams, 200 );
        log.info(JsonUtils.convertObjectToJsonString(response.asPrettyString()));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}
