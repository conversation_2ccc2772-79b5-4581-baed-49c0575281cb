package com.lenskart.pos.helpers.bulkordersummary;


import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_ALREADY_RECEIVED_SHIPMENTS;

@Slf4j
@SuperBuilder
public class GetAlreadyReceivedShipmentsHelper extends PosBaseHelper implements ServiceHelper {
    OrderContext orderContext;
    Response response;

    public ServiceHelper init(){
        headers = getHeadersWithApiClient(orderContext);
        queryParams = getQueryParamsWithUserId1(15, 1);
        return this;
}

    // calls api using the service class present under the respective api package
    public ServiceHelper process(){
        response = RestUtils.get(GET_ALREADY_RECEIVED_SHIPMENTS.getUrl(), headers, queryParams, 200);
        return this;
}

    // uses validator class for validation
    public ServiceHelper validate(){
    return this;
}

    // orchestrator for executing the flows
    public ServiceHelper test(){
    init();
    process();
    validate();
    return this;
}
}
