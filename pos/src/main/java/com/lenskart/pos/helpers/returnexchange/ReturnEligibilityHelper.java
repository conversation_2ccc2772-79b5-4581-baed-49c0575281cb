package com.lenskart.pos.helpers.returnexchange;


import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import com.lenskart.pos.model.ExchangeItemRequest;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import static com.lenskart.pos.endpoints.PosEndpoints.RETURN_ACTION;

@Slf4j
@SuperBuilder
public class ReturnEligibilityHelper extends PosBaseHelper implements ServiceHelper {
    OrderContext orderContext;
    ExchangeItemRequest exchangeItemRequest;
    String orderItemId;
    Response response;

    public ServiceHelper init() {
        headers = getHeadersWithApiClient(orderContext);
        exchangeItemRequest = PosOrderRequestBuilder.createExchangeRequest(orderContext);
        return this;
    }

    public ServiceHelper process() {
        response = RestUtils.post(RETURN_ACTION.getUrl(Map.of("orderItem",orderItemId)), headers,
                JsonUtils.convertObjectToJsonString(exchangeItemRequest), 200);
        return this;
    }

    public ServiceHelper validate() {
        return this;
    }



    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
