package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static com.lenskart.commons.constants.Constants.BARCODE_ID;
import static com.lenskart.commons.constants.Constants.PRODUCT_ID;
import static com.lenskart.pos.endpoints.PosEndpoints.GET_BARCODE_DETAILS;
import static com.lenskart.pos.endpoints.PosEndpoints.VALIDATE_BARCODE_DETAILS;

@SuperBuilder
@Slf4j
public class ValidateBarCodeHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    private static final int MAX_BARCODE_RETRY_ATTEMPTS = 3;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {

        if (orderContext.getProductLists().isEmpty()) {
            throw new RuntimeException("No products added to the order context");
        } else {
            List<OrderContext.ProductList> productLists = orderContext.getProductLists();
            for (OrderContext.ProductList productList : productLists) {
                validateBarcodeWithRetry(productList);
            }
            return this;
        }
    }

    private void validateBarcodeWithRetry(OrderContext.ProductList productList) {
        int attempts = 0;
        boolean isValidBarcode = false;

        while (attempts < MAX_BARCODE_RETRY_ATTEMPTS && !isValidBarcode) {
            attempts++;
            log.info("Validating barcode attempt {} for product {}: {}", attempts, productList.getProductId(), productList.getBarCode());

            try {
                response = RestUtils.get(VALIDATE_BARCODE_DETAILS.getUrl(Map.of(PRODUCT_ID, productList.getProductId(),
                                BARCODE_ID, productList.getBarCode())),
                        headers, null);

                if (response.getStatusCode() == 200 && (boolean) RestUtils.getValueFromResponse(response, "success")) {
                    log.info("Barcode validation successful for product {}: {}", productList.getProductId(), productList.getBarCode());
                    isValidBarcode = true;
                } else if (response.getStatusCode() == 428) {
                    log.warn("Barcode {} is already in use (428) for product {}. Fetching next barcode...",
                            productList.getBarCode(), productList.getProductId());
                    fetchNextBarcode(productList);
                } else {
                    log.warn("Barcode validation failed with status {} for product {}: {}. Response: {}",
                            response.getStatusCode(), productList.getProductId(), productList.getBarCode(),
                            response.getBody().asString());
                    fetchNextBarcode(productList);
                }

            } catch (RuntimeException e) {
                if (e.getMessage().contains("428") || e.getMessage().contains("already in use")) {
                    log.warn("Barcode {} is already in use for product {}. Fetching next barcode...",
                            productList.getBarCode(), productList.getProductId());
                    fetchNextBarcode(productList);
                } else {
                    throw e;
                }
            }
        }

        if (!isValidBarcode) {
            throw new RuntimeException("Failed to find a valid barcode for product " + productList.getProductId() +
                    " after " + MAX_BARCODE_RETRY_ATTEMPTS + " attempts");
        }
    }

    private void fetchNextBarcode(OrderContext.ProductList productList) {
        try {
            log.info("Fetching new barcode for product: {}", productList.getProductId());

            Response barcodeResponse = RestUtils.get(GET_BARCODE_DETAILS.getUrl(Map.of(PRODUCT_ID, productList.getProductId())),
                    headers, null, 200);

            Map<String, List<String>> barcodeDetails = barcodeResponse.as(Map.class);
            List<String> barcodeList = barcodeDetails.get(productList.getProductId());

            if (barcodeList == null || barcodeList.isEmpty()) {
                throw new RuntimeException("No barcodes available for product id: " + productList.getProductId());
            }

            // Find the barcode that's different from the current one
            String currentBarcode = productList.getBarCode();
            String nextBarcode = null;

            int currentIndex = barcodeList.indexOf(currentBarcode);
            if (currentIndex != -1 && currentIndex + 1 < barcodeList.size()) {
                nextBarcode = barcodeList.get(currentIndex + 1);
            }

            if (nextBarcode != null) {
                productList.setBarCode(nextBarcode);
                log.info("Updated barcode for product {} from {} to {}",
                        productList.getProductId(), currentBarcode, nextBarcode);
            } else {
                throw new RuntimeException("No alternative barcode found for product id: " + productList.getProductId() +
                        ". Available barcodes: " + barcodeList.size());
            }

        } catch (Exception e) {
            log.error("Failed to fetch next barcode for product {}: {}", productList.getProductId(), e.getMessage());
            throw new RuntimeException("Failed to fetch next barcode for product " + productList.getProductId(), e);
        }
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
