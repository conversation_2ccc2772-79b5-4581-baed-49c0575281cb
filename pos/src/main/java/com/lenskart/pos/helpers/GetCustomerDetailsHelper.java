package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.schema.v2.customer.Customer;
import com.lenskart.pos.config.PosConfig;
import com.lenskart.pos.config.PosConfigRegistry;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.commons.constants.Constants.CUSTOMER;
import static com.lenskart.pos.endpoints.PosEndpoints.GET_CUSTOMER_DETAILS;
import static com.lenskart.pos.endpoints.PosEndpoints.V1_AUTH;

@SuperBuilder
@Getter
public class GetCustomerDetailsHelper extends PosBaseHelper implements ServiceHelper {


    private JSONObject payload;
    private OrderContext orderContext;
    private Response response;
    OrderContext.Headers orderContextHeader;
    Customer customer;
    PosConfigRegistry registry;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        registry = PosConfigRegistry.getInstance();
        headers = getHeaders(orderContext);
        PosConfig.PosUser user = registry.getPosUserByStoreId(orderContext.getPosStoreMapper().getCountry().name(),
                orderContext.getPosStoreMapper().getStoreId());
        queryParams = getQueryParams(orderContext);
        payload = PosOrderRequestBuilder.createPayloadForSession(user);
        orderContextHeader = orderContext.getHeaders();
        return this;
    }

    @Override
    public ServiceHelper process() {

        /* Fetch session Token */
        response = RestUtils.post(V1_AUTH.getUrl(), headers, payload.toString(), 200);
        orderContextHeader.setPosSessionToken((String) RestUtils.getValueFromResponse(response, "sessionToken"));

        headers = getHeadersWithSessionToken(orderContext);
        response = RestUtils.get(GET_CUSTOMER_DETAILS.getUrl(Map.of("phoneNumber", orderContext.getPhoneNumber())),
                headers, queryParams, 200);
        customer = parseResponse(RestUtils.getValueFromResponse(response, CUSTOMER), Customer.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
