package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static com.lenskart.commons.constants.Constants.PRODUCT_ID;
import static com.lenskart.pos.endpoints.PosEndpoints.GET_BARCODE_DETAILS;

@SuperBuilder
@Slf4j
public class FetchBarCodeHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    private static int barcodeIndex = 0;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (orderContext.getProductLists().isEmpty()) {
            throw new RuntimeException("No products added to the order context");
        } else {
            List<OrderContext.ProductList> productLists = orderContext.getProductLists();
            for (OrderContext.ProductList productList : productLists) {
               boolean success = AwaitUtils.retryOperation(() -> {
                    try {
                        response = RestUtils.get(GET_BARCODE_DETAILS.getUrl(Map.of(PRODUCT_ID, productList.getProductId())),
                                headers, null, 200);
                        return response.statusCode() == 200;
                    } catch (Exception e) {
                        return false;
                    }
                },
                        "Retry Fetch Barcode",
                        3,
                        AwaitUtils.DEFAULT_TIMEOUT
                );
               if(success){
                   log.info("Barcode fetched successfully");
               }else {
                   throw new RuntimeException("Failed to fetch barcode after retries");
               }
//                response = RestUtils.get(GET_BARCODE_DETAILS.getUrl(Map.of(PRODUCT_ID, productList.getProductId())),
//                        headers, null, 200);
                Map<String, List<String>> barcodeDetails = response.as(Map.class);

                List<String> barcodeList = barcodeDetails.get(productList.getProductId());
                if (barcodeList == null || barcodeList.isEmpty()) {
                    throw new RuntimeException("No barcodes found for product id: " + productList.getProductId());
                } else {
                    // Use the next available barcode from the list
                    int indexToUse = barcodeIndex % barcodeList.size();
                    productList.setBarCode(barcodeList.get(indexToUse));
                    log.info("Selected barcode {} (index {}) for product {}",
                            barcodeList.get(indexToUse), indexToUse, productList.getProductId());
                    barcodeIndex++; // Increment for next product
                }
            }
            return this;
        }
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
