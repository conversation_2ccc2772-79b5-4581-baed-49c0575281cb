package com.lenskart.pos.helpers.detailedcount;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_DC_SUMMARY;

@SuperBuilder
public class GetDcCycleSummaryHelper extends PosBaseHelper implements ServiceHelper {

    Response response;
    OrderContext orderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_DC_SUMMARY.getUrl(Map.of("franchiseID", String.valueOf(orderContext.getPosStoreMapper().getFranchiseId()))), headers, null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
