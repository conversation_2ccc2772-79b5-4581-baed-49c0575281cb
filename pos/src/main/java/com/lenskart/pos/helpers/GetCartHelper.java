package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.CartResponse;
import com.lenskart.pos.model.Discount;
import com.lenskart.pos.model.PriceBreakupItem;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_CART;

@SuperBuilder
@Slf4j
@Getter
public class GetCartHelper extends PosBaseHelper implements ServiceHelper {

    private OrderContext orderContext;
    private Response response;
    private CartResponse cartResponse;

    @Override
    public ServiceHelper init() {
        statusCode = 200; // Initialize statusCode to 200
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_CART.getUrl(), headers, null, 200);
        cartResponse = parseResponse(response.asPrettyString(), CartResponse.class);
        log.info("++++++++++++cartResponse of get cart++++++++" + cartResponse);
        BigDecimal value = BigDecimal.valueOf(cartResponse.getTotals().getPayableAmount());
        orderContext.setFinalOrderAmount(value);
        log.info("Final order amount in getcart: " + orderContext.getFinalOrderAmount());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}