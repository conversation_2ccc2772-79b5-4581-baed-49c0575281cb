package com.lenskart.pos.helpers.nso;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import com.lenskart.pos.model.POS;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.pos.endpoints.PosEndpoints.STORE_LIST;
@Slf4j
@SuperBuilder
public class StoreListHelper extends PosBaseHelper implements ServiceHelper {

    private OrderContext orderContext;
    Response response;


    public ServiceHelper init(){
        int userId = 6;
        headers = getHeadersWithSessionToken(orderContext);
        queryParams = getQueryParamsWithUserId(userId, 15, 1, POS.IN_MUMBAI_STORE.getStoreId());
        return this;
    }


    // calls api using the service class present under the respective api package
    public ServiceHelper process(){
        response = RestUtils.get(STORE_LIST.getUrl(), headers, queryParams, 200);
        log.info(JsonUtils.convertObjectToJsonString(response.asPrettyString()));
        return this;
    }

    // uses validator class for validation
    public ServiceHelper validate(){
    return this;
    }

    // orchestrator for executing the flows
    public ServiceHelper test(){
        init();
        process();
        validate();
    return this;
    }}