package com.lenskart.pos.helpers.order;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.helpers.*;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class LensOnlyLocalFittingHelper extends PosBaseHelper implements ExecutionHelper {

    private OrderContext orderContext;
    JSONObject payload;
    Response response;


    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        /* Authenticate User */
        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* upload image */
        UploadImageHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Search for product and get package id */
        GetProductAndPackageHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        CreateCartHelper cartHelper = CreateCartHelper.builder()
                .orderContext(orderContext)
                .build();
        cartHelper.test();

        /* Apply Gift voucher to cart */
        ApplyGiftVoucherHelper gvHelper = ApplyGiftVoucherHelper.builder()
                .orderContext(orderContext)
                .build();
        gvHelper.test();
        if(gvHelper.getCartResponse() != null) {
            cartHelper.setCartResponse(gvHelper.getCartResponse());
        }

        ValidateLensOnlyBarcodeHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Remove auto-added items from cart */
        RemoveAutoAddedItemsHelper removeAutoAddedItemsHelper = RemoveAutoAddedItemsHelper.builder()
                .orderContext(orderContext)
                .cartResponse(cartHelper.getCartResponse())
                .build();
        removeAutoAddedItemsHelper.test();

        /* Get prescription data */
        GetPrescriptionHelper getPrescriptionHelper = GetPrescriptionHelper.builder()
                .orderContext(orderContext)
                .build();
        getPrescriptionHelper.test();

        /* Add prescription to cart only if prescription data is available */
        if (getPrescriptionHelper.getSelectedPrescription() != null) {
            AddPrescriptionToCartHelper.builder()
                    .orderContext(orderContext)
                    .prescriptionData(getPrescriptionHelper.getSelectedPrescription())
                    .build()
                    .test();
        }

        DeliveryEstimateHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        CartsLocalFittingHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        CartsItemUpdateHelper.builder()
                .orderContext(orderContext)
                .cartResponse(cartHelper.getCartResponse())
                .build()
                .test();

        /* Create Order Payment with updated cart */
        CreateOrderPaymentHelper.builder()
                .orderContext(orderContext)
                .cartHelper(cartHelper)
                .cartResponse(removeAutoAddedItemsHelper.getUpdatedCartResponse())
                .build()
                .test();
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
