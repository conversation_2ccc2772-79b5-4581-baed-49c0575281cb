package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.pos.endpoints.PosEndpoints.VALIDATE_LENS_ONLY_BARCODE;

@SuperBuilder
public class ValidateLensOnlyBarcodeHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    String barcode;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        long base = 53000000;
        long range = 10000000;
        long uniqueNumber = base + (System.currentTimeMillis() % range);
        barcode = String.valueOf(uniqueNumber);
        queryParams = getQueryParamsWithBarCode(orderContext, barcode);
        orderContext.getProductLists().get(0).setLensOnlyBarcode(barcode);
        return this;
    }

    @Override
    public ServiceHelper process() {

        response = RestUtils.get(VALIDATE_LENS_ONLY_BARCODE.getUrl(), headers, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
