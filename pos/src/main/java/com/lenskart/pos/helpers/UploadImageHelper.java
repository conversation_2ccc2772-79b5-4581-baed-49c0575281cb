package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

import static com.lenskart.pos.endpoints.PosEndpoints.UPLOAD_IMAGE;


@SuperBuilder
@Slf4j
@Getter
public class UploadImageHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    String filePath;
    String fileName;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        fileName = null;
        // Set the file path to the CSV file
        if (filePath == null) {
            try {
                InputStream inputStream = getClass().getClassLoader().getResourceAsStream("Lens_only_image1.jpg");
                if (inputStream == null) {
                    throw new RuntimeException("image file not found in classpath");
                }
                File tempfile = File.createTempFile("Lens_only_image1", ".jpg");
                Files.copy(inputStream, tempfile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                filePath = tempfile.getAbsolutePath();
                fileName = tempfile.getName();
            } catch (IOException e) {
                throw new RuntimeException("Failed to upload the file", e);
            }
        }
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.postWithMultipartFile(
                UPLOAD_IMAGE.getUrl(), headers, filePath, "file", 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
