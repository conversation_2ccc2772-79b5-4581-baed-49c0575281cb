package com.lenskart.pos.helpers.storefinance;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.pos.endpoints.PosEndpoints.MONTHLY_MARGIN;

@Slf4j
@SuperBuilder
public class PosMonthlyMarginHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    public ServiceHelper init(){
        headers = getHeadersWithSessionToken(orderContext);
        queryParams=getQueryParamsForMonthlyMargin(orderContext);
        return this;
    }
    public ServiceHelper process(){
        response= RestUtils.post(MONTHLY_MARGIN.getUrl(),headers,queryParams,null,200);

        return this;
    }
    public ServiceHelper validate(){
        return this;
    }
    public ServiceHelper test(){
        init();
        process();
        validate();
        return this;
    }
}
