package com.lenskart.pos.helpers.storeops;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import lombok.experimental.SuperBuilder;
import io.restassured.response.Response;

import java.util.Map;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_SALESMAN_LIST;

@SuperBuilder
public class GetSalesmanListHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext  orderContext;
    Response response;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_SALESMAN_LIST.getUrl(Map.of("franchiseID", String.valueOf(orderContext.getPosStoreMapper().getFranchiseId()))),headers, queryParams,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
