package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.database.PosDbUtils;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

import static com.lenskart.pos.endpoints.PosEndpoints.BULK_ORDER_UPLOAD;


@SuperBuilder
@Slf4j
@Getter
public class OrderUploadHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    String filePath;
    String fileName;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithPosSessionToken(orderContext);
        fileName = null;
        // Set the file path to the CSV file
        if (filePath == null) {
            try {
                InputStream inputStream = getClass().getClassLoader().getResourceAsStream("bulk_order_upload1.csv");
                if (inputStream == null) {
                    throw new RuntimeException("CSV file not found in classpath");
                }
                File tempfile = File.createTempFile("bulk_upload", ".csv");
                Files.copy(inputStream, tempfile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                filePath = tempfile.getAbsolutePath();
                fileName = tempfile.getName();
            } catch (IOException e) {
                throw new RuntimeException("Failed to load or write CSV file", e);
            }
        }
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.postWithMultipartFile(
                BULK_ORDER_UPLOAD.getUrl(), headers, filePath, "file", 200);

        AwaitUtils.sleepSeconds(20);
        String dealsOrderId = PosDbUtils.getBulkDealsOrderId(fileName);
        orderContext.setOrderId(Integer.parseInt(dealsOrderId));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
