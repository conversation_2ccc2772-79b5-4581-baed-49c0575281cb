package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.AddToCartRequest;
import com.lenskart.pos.model.CartResponse;
import com.lenskart.pos.model.PriceBreakupItem;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

import static com.lenskart.pos.endpoints.PosEndpoints.ADD_TO_CART;

@SuperBuilder
@Slf4j
@Getter
@Setter
public class CreateCartHelper extends PosBaseHelper implements ServiceHelper {

    AddToCartRequest payload;
    OrderContext orderContext;
    Response response;
    CartResponse cartResponse;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (orderContext.getProductLists().isEmpty()) {
            throw new RuntimeException("No products added to the order context");
        } else {
            List<OrderContext.ProductList> productLists = orderContext.getProductLists();
            for (OrderContext.ProductList productList : productLists) {
                payload = PosOrderRequestBuilder.createPayloadForAddCart(productList);
                log.info("payload is " + payload);
                response = RestUtils.post(ADD_TO_CART.getUrl(), headers, JsonUtils.convertObjectToJsonString(payload), 200);
                log.info("-------------response is -------------" +response);
                orderContext.setCartId( (int) RestUtils.getValueFromResponse(response, "cartId"));
                cartResponse = parseResponse(response.asPrettyString(), CartResponse.class);
                log.info("-----------The cart Response is ------------------ " + cartResponse);
                BigDecimal value = BigDecimal.valueOf(cartResponse.getTotals().getPayableAmount());
                orderContext.setFinalOrderAmount(value);
                log.info("Final order amount in createcart: " + orderContext.getFinalOrderAmount());
            }
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
