package com.lenskart.pos.helpers.returnexchange;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import com.lenskart.pos.model.PosContext;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_RETURN_REASONS;


@lombok.extern.slf4j.Slf4j
@Slf4j
@SuperBuilder
public class GetReturnReasonHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    PosContext posContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        queryParams = getQueryParamsForReturnReasons(posContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_RETURN_REASONS.getUrl(), headers , queryParams , 200);
        log.info(JsonUtils.convertObjectToJsonString(response.asPrettyString()));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return null;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
