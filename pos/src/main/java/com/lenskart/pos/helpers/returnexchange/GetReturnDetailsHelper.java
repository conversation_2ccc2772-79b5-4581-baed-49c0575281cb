package com.lenskart.pos.helpers.returnexchange;


import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_RETURN_DETAILS;

@Slf4j
@SuperBuilder
public class GetReturnDetailsHelper extends PosBaseHelper implements ServiceHelper {
    OrderContext orderContext;
    Response response;
    long returnId;
    long itemId;
    long orderId;

    public ServiceHelper init() {
        headers = getHeadersWithApiClient(orderContext);
        queryParams = getQueryParamsForReturnDetails(returnId, itemId, orderId);
        return this;
    }

    public ServiceHelper process() {
        response = RestUtils.get(GET_RETURN_DETAILS.getUrl(), headers, queryParams, 200);
        return this;
    }

    public ServiceHelper validate() {
        return this;
    }



    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
