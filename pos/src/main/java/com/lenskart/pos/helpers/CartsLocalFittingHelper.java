package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.pos.endpoints.PosEndpoints.APPLY_GIFT_VOUCHER_TO_CART;
import static com.lenskart.pos.endpoints.PosEndpoints.CARTS_LOCAL_FITTING;

@SuperBuilder
public class CartsLocalFittingHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    JSONObject cartsLocalFittingPayload;
    @Override
    public ServiceHelper init() {

        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        cartsLocalFittingPayload = PosOrderRequestBuilder.createCartsLocalFittingPayload(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(
                CARTS_LOCAL_FITTING.getUrl(),
                headers,
                cartsLocalFittingPayload.toString(),
                200
        );
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
