package com.lenskart.pos.helpers.fiserv;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;


import static com.lenskart.pos.endpoints.PosEndpoints.GET_FISERV_DEVICE_DETAILS;
@SuperBuilder
public class GetFiservDeviceDetailsHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_FISERV_DEVICE_DETAILS.getUrl(),headers,queryParams,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
