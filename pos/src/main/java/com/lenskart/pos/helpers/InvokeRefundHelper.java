package com.lenskart.pos.helpers;


import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.CreateReturnRefundRequest;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.pos.endpoints.PosEndpoints.INVOKE_REFUND;

@Slf4j
@SuperBuilder
public class InvokeRefundHelper extends PosBaseHelper implements ServiceHelper {
    OrderContext orderContext;
    CreateReturnRefundRequest createReturnRefundRequest;
    String orderItemId;
    Response response;

    public ServiceHelper init() {
        headers = getHeadersWithApiClient(orderContext);
        createReturnRefundRequest = PosOrderRequestBuilder.createRefundRequest(orderContext);
        return this;
    }

    public ServiceHelper process() {
        response = RestUtils.post(INVOKE_REFUND.getUrl(), headers,
                JsonUtils.convertObjectToJsonString(createReturnRefundRequest), 200);
        return this;
    }

    public ServiceHelper validate() {
        return this;
    }



    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
