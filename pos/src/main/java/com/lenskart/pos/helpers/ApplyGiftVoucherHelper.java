package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.CartResponse;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.commons.constants.Constants.CART_ID;
import static com.lenskart.pos.endpoints.PosEndpoints.APPLY_GIFT_VOUCHER_TO_CART;
import static com.lenskart.pos.endpoints.PosEndpoints.GET_DELIVERY_ESTIMATES;

@Getter
@SuperBuilder
public class ApplyGiftVoucherHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    JSONObject giftVoucherPayload;

    CartResponse cartResponse;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        giftVoucherPayload = PosOrderRequestBuilder.createGiftVoucherPayload(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(
                APPLY_GIFT_VOUCHER_TO_CART.getUrl(Map.of("gift_voucher", "MDBY-TEST-12345")),
                headers,
                giftVoucherPayload.toString(),
                200
        );

        cartResponse = parseResponse(response.asPrettyString(), CartResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
