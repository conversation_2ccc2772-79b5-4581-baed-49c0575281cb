package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.commons.constants.Constants.CART_ID;
import static com.lenskart.pos.endpoints.PosEndpoints.GET_DELIVERY_ESTIMATES;

@SuperBuilder
@Getter
public class DeliveryEstimateHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    JSONObject estimatePayload;

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        estimatePayload = PosOrderRequestBuilder.createPayloadForDeliveryEstimate(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {

        response = RestUtils.post(
            GET_DELIVERY_ESTIMATES.getUrl(Map.of(CART_ID, String.valueOf(orderContext.getCartId()))),
            headers,
            estimatePayload.toString(),
            200
        );
        
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
