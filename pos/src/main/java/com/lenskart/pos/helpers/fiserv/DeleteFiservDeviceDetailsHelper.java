package com.lenskart.pos.helpers.fiserv;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import com.lenskart.pos.model.PosContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.pos.endpoints.PosEndpoints.DELETE_FISERV_DEVICE_DETAILS;

@SuperBuilder
public class DeleteFiservDeviceDetailsHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    PosContext posContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        String deviceSerialNumber = posContext.getFiservDeviceUpdateMapper().getSerialNumber();
        queryParams = getQueryParamsForDeleteFiservDeviceDetails(deviceSerialNumber);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(DELETE_FISERV_DEVICE_DETAILS.getUrl(),headers,queryParams,null);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
