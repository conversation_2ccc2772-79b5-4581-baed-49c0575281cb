package com.lenskart.pos.helpers.order;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.helpers.AuthenticationHelper;
import com.lenskart.pos.helpers.OrderUploadHelper;
import com.lenskart.pos.helpers.PosBaseHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class BulkOrderHelper extends PosBaseHelper implements ExecutionHelper {

    private OrderContext orderContext;
    JSONObject payload;
    Response response;



    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        OrderUploadHelper ordrerUploadHelper = OrderUploadHelper.builder()
                .orderContext(orderContext)
                .build();
                ordrerUploadHelper .test();



        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
