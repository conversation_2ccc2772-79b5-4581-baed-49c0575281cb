package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.CartResponse;
import io.restassured.response.Response;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lenskart.pos.endpoints.PosEndpoints.DELETE_ITEMS_FROM_CART;

@SuperBuilder
@Slf4j
@Getter
public class RemoveAutoAddedItemsHelper extends PosBaseHelper implements ServiceHelper {

    private OrderContext orderContext;
    private Response response;
    private CartResponse cartResponse;
    private CartResponse updatedCartResponse;
    @Builder.Default
    private List<String> removedItemIds = new ArrayList<>();

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        // Ensure removedItemIds is initialized
        if (removedItemIds == null) {
            removedItemIds = new ArrayList<>();
        }
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (cartResponse == null || cartResponse.getItems() == null || cartResponse.getItems().isEmpty()) {
            log.info("Cart is empty or null, nothing to remove");
            return this;
        }

        // Identify auto-added items
        for (Object itemObj : cartResponse.getItems()) {
            Map<String, Object> item = (Map<String, Object>) itemObj;

            // Check if item is auto-added
            if (item.containsKey("isAutoAdded") && Boolean.TRUE.equals(item.get("isAutoAdded"))) {
                String itemId = String.valueOf(item.get("id"));
                log.info("Found auto-added item with ID: {}", itemId);
                removedItemIds.add(itemId);

                // Create payload for removing the item
                Map<String, Object> payload = new HashMap<>();
                payload.put("quantity", 1); // Remove 1 quantity

                // Remove the item from cart using POST
                response = RestUtils.post(
                        DELETE_ITEMS_FROM_CART.getUrl(Map.of("itemId", itemId)),
                        headers,
                        JsonUtils.convertObjectToJsonString(payload),
                        200
                );

                if (response.getStatusCode() != 200) {
                    log.error("Failed to remove auto-added item with ID: {}. Status code: {}",
                            itemId, response.getStatusCode());
                } else {
                    log.info("Successfully removed auto-added item with ID: {}", itemId);
                }
            }
        }

        // If any items were removed, fetch the updated cart
        if (!removedItemIds.isEmpty()) {
            log.info("Removed {} auto-added items from cart", removedItemIds.size());

            // Get updated cart response
            GetCartHelper getCartHelper = GetCartHelper.builder()
                    .orderContext(orderContext)
                    .build();
            getCartHelper.test();

            updatedCartResponse = getCartHelper.getCartResponse();
        } else {
            log.info("No auto-added items found in cart");
            updatedCartResponse = cartResponse;
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        if (updatedCartResponse != null) {
            // Verify that auto-added items were removed
            for (String removedItemId : removedItemIds) {
                boolean stillExists = false;

                if (updatedCartResponse.getItems() != null) {
                    for (Object itemObj : updatedCartResponse.getItems()) {
                        Map<String, Object> item = (Map<String, Object>) itemObj;
                        String itemId = String.valueOf(item.get("id"));

                        if (itemId.equals(removedItemId)) {
                            stillExists = true;
                            break;
                        }
                    }
                }

                if (stillExists) {
                    log.warn("Auto-added item with ID: {} still exists in cart after removal", removedItemId);
                }
            }
        }
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}