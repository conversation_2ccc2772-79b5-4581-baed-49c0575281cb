package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.CartResponse;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.pos.endpoints.PosEndpoints.CARTS_ITEM_UPDATE;

@SuperBuilder
public class CartsItemUpdateHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    JSONObject cartItemUpdatePayload;
    CartResponse cartResponse;

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        cartItemUpdatePayload = PosOrderRequestBuilder.createPayloadForCartItemUpdate(orderContext,cartResponse);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(CARTS_ITEM_UPDATE.getUrl(), headers, cartItemUpdatePayload.toString(),200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
