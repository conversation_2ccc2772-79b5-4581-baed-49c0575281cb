package com.lenskart.pos.helpers.storefinance;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.helpers.PosBaseHelper;
import com.lenskart.pos.model.PosContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_INVOICE;


@SuperBuilder
public class GetInvoiceNumerWithTypeHelper extends PosBaseHelper implements ServiceHelper {
    OrderContext orderContext;
    PosContext posContext;
    Response response;

    public ServiceHelper init(){
        headers = getHeaders(orderContext);
        queryParams=getQueryParamsWithInvoiceType(posContext);
        return this;
    }
    public ServiceHelper process(){
        response= RestUtils.get(GET_INVOICE.getUrl(),headers,queryParams,200);
        return this;
    }
    public ServiceHelper validate(){
        return this;
    }
    public ServiceHelper test(){
        init();
        process();
        validate();
        return this;
    }
}
