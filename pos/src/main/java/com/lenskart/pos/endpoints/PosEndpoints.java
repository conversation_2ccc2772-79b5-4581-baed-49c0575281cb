package com.lenskart.pos.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import com.lenskart.juno.schema.v2.inventory.Inventory;
import lombok.Getter;

import java.util.Map;

@Getter
public enum PosEndpoints implements BaseEndpoint {

    V1_AUTH("/v1/auth", "posService"),
    V2_SESSIONS("/v2/sessions", "junoService"),

    GET_PRODUCT_DETAILS("/v2/products/{$productID}", "webService"),
    GET_PACKAGE_DETAILS("/v1/products/{$productID}/packages", "webService"),
    DELETE_ITEMS_FROM_CART("/v1/carts/items/{itemId}/quantity/remove", "webService"),
    GET_CART("/v1/carts", "webService"),
    ADD_TO_CART("/v1/carts", "webService"),
    PLACE_ORDER("/v1/order", "webService"),
    GET_CUSTOMER_DETAILS("/v1/customers/{$phoneNumber}", "webService"),
    GET_BARCODE_DETAILS("/v1/products/{$productID}/barcodes", "webService"),
    VALIDATE_BARCODE_DETAILS("/v1/products/{$productID}/validate-barcode/{$barcodeID}", "webService"),
    FETCH_PRESCRIPTION("v1/prescription/customer", "webService"),
    GET_DELIVERY_ESTIMATES("/v1/carts/item/{$cartID}/estimate", "webService"),
    VALIDATE_LENS_ONLY_BARCODE("/v1/products/validate-lensonly-barcode", "webService"),
    GET_CUSTOMER_PROFILE("/v1/customer/guest/profile",  "webService"),
    ADD_PRESCRIPTION_TO_CART("/v1/carts/item/prescription/{itemId}", "webService"),
    CARTS_ITEM_UPDATE("v1/carts/item/update","webService"),
    APPLY_GIFT_VOUCHER_TO_CART("v1/carts/giftvoucher/{gift_voucher}","webService"),
    CARTS_LOCAL_FITTING("v1/carts/localfitting", "webService"),

    //PosWeb service Api's
    BULK_ORDER_UPLOAD("/v1/bulk", "posService"),
    UPLOAD_IMAGE("/v2/upload-image", "posService"),
    GET_EXCHANGEORDER_LIST("/v2/orderDetails" , "posService"),
    STORE_LIST("/list", "posStore"),
    SALESMEN_DETAILS("v1/franchises/salesmen/{salesmenId}","posService"),
    GET_FISERV_DEVICE_DETAILS("/fiserv/getFiservDeviceDetails", "posService"),
    GET_SALESMAN_LIST("/v1/franchises/{$franchiseID}/salesmens", "posService"),
    GET_ORDERSUMMARY_ITEMS_LIST("/v1/order/summary/items","posService"),
    UPDATE_FISERV_DEVICE_DETAILS("/fiserv/updateFiservDeviceDetail","posService"),
    GET_ORDER_DETAILS("/v1/order/summary/{$incrementId}/itemDetails","posService"),
    GET_RETURN_REASONS("/v1/return/reasons","posService"),
    INSERT_FISERV_DEVICE_DETAILS("/fiserv/insertFiservDeviceData","posService"),
    DELETE_FISERV_DEVICE_DETAILS("/fiserv/deleteFiserveDeviceDetail","posService"),

    //Inventory service APIs
    GET_DC_SUMMARY("/detailCount/store/summary","inventoryService"),
    LOAD_SHIPMENT_STATUS("/v2/bulkOrderSummary/loadShipmentStatusForAll", "orderService"),
    NAV_COMMISSION("/v1/jobs/nav/commission","posService"),
    MONTHLY_MARGIN("/v1/jobs/nav/margin/createMarginInvoice","posService"),
    GET_INVOICE("/v1/jobs/generate","posService"),
    GET_ALREADY_RECEIVED_SHIPMENTS("/v2/bulkOrderSummary/getAlreadyReceivedShipments", "orderService"),
    GET_RETURN_DETAILS("/v2/item/return-details","posService"),
    RETURN_ACTION("/v2/return/order/{$orderItem}/returnActions","posService"),
    INVOKE_REFUND("/v2/return/item","posService");

    private final String endpoint;
    private final String serviceName;

    PosEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }



    @Override
    public String getUrl() {
        return PosEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return PosEndpointManager.getEndpointUrl(this, pathParams);
    }

}
