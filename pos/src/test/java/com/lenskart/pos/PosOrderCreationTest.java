package com.lenskart.pos;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.dataprovider.PosDataProvider;
import com.lenskart.pos.helpers.storefinance.PosMonthlyMarginHelper;
import com.lenskart.pos.helpers.storefinance.ProcessStoreCommissionPayoutHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;


@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class PosOrderCreationTest {
    @Test(dataProviderClass = PosDataProvider.class, dataProvider = "marginContext")
    public void TestNavCommission(OrderContext orderContext) {
        ProcessStoreCommissionPayoutHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }
    @Test(dataProviderClass = PosDataProvider.class, dataProvider = "marginContext")
    public void testMonthlyMargin(OrderContext orderContext) {
        PosMonthlyMarginHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = PosDataProvider.class, dataProvider = "marginContext")
    public void testNavCommission(OrderContext orderContext) {
        ProcessStoreCommissionPayoutHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }

}

