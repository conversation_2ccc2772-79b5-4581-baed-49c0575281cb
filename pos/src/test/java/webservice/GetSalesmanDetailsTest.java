package webservice;

import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.helpers.AuthenticationHelper;
import com.lenskart.pos.helpers.storeops.GetSalesmanDetailsHelper;
import com.lenskart.pos.model.POS;
import org.testng.annotations.Test;

public class GetSalesmanDetailsTest {
    @Test
    public void GetSalesmanDetails() {
        OrderContext orderContext = OrderContext.builder()
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .country(Countries.IN)
                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                        .salesmenId(1567453)
                        .build())
                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                .build();

        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        GetSalesmanDetailsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();


    }

}
