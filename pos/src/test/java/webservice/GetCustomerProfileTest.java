package webservice;

import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.ItemType;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.helpers.AuthenticationHelper;
import com.lenskart.pos.helpers.GetCustomerProfileHelper;
import com.lenskart.pos.model.POS;
import org.testng.annotations.Test;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;

public class GetCustomerProfileTest{
    @Test
    public void CustomerProfile() {

        OrderContext orderContext = OrderContext.builder()
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId("148248")
                                .powerType(ZERO_POWER)
                                .itemType(ItemType.DTC)
                                .isPrescriptionRequired(false)
                                .build()))
                .phoneNumber("8660038279")
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("560075")
                        .build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .country(Countries.IN)
                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                        .build())
                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                .build();

        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
        GetCustomerProfileHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }
}
