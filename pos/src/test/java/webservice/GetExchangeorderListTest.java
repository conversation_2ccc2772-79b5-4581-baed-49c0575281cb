package webservice;

import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.ItemType;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.helpers.AuthenticationHelper;
import com.lenskart.pos.helpers.returnexchange.GetExchangeOrderListHelper;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.pos.model.POS;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;

@Slf4j
public class GetExchangeorderListTest {
@Test
    public void ExchangeList() {

        OrderContext orderContext = OrderContext.builder()
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId("148248")
                                .powerType(ZERO_POWER)
                                .itemType(ItemType.DTC)
                                .isPrescriptionRequired(false)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("560075")
                        .build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .country(Countries.IN)
                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                        .build())
                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                .build();

        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
        PosOrderCreationHelper posOrderCreationHelper = PosOrderCreationHelper
                .builder()
                .orderContext(orderContext)
                .build();
        posOrderCreationHelper.test();

        log.info("Order ID - {}", orderContext.getOrderId());

        GetExchangeOrderListHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}
