# POS Module

## Overview

The **POS Module** provides comprehensive automation testing capabilities for Lenskart's Point of Sale (POS) system, which handles in-store order management, cart operations, barcode validation, prescription management, and order processing for retail locations.

## 🏗️ **Architecture**

### **Core Components**

- **In-Store Order Management**: Complete POS order lifecycle automation
- **Cart Operations**: POS-specific cart management and operations
- **Barcode Management**: Barcode validation and processing
- **Prescription Handling**: In-store prescription creation and validation
- **Image Upload**: Product and prescription image management
- **Country-Specific Operations**: Multi-country POS support

## 📁 **Package Structure**

```
com.lenskart.pos/
├── config/              # POS-specific configuration management
├── endpoints/           # POS API endpoints and endpoint manager
├── exceptions/          # POS-specific exception states
├── helpers/             # Service helper classes for POS operations
│   └── order/           # Order-specific helper classes
├── model/               # POS-specific data models
└── requestbuilders/     # Request builders for POS API calls
```

## 🔧 **Key Features**

### **1. Complete POS Order Lifecycle**

#### **POS Order Creation Workflow**
```java
PosOrderCreationHelper orderHelper = new PosOrderCreationHelper();

// Create complete POS order
String orderId = orderHelper.createCompleteOrder(
    customerId, 
    productBarcode, 
    prescriptionData, 
    storeLocation,
    paymentMethod
);
```

#### **Individual POS Order Steps**
```java
// 1. Authentication
AuthenticationHelper authHelper = new AuthenticationHelper();
String sessionToken = authHelper.authenticateUser(username, password, country);

// 2. Validate Barcode
ValidateBarCodeHelper barcodeHelper = new ValidateBarCodeHelper();
boolean isValid = barcodeHelper.validateBarcode(barcode, storeId);

// 3. Create Cart
CreateCartHelper cartHelper = new CreateCartHelper();
String cartId = cartHelper.createCart(customerId, barcode, quantity);

// 4. Upload Images
UploadImageHelper imageHelper = new UploadImageHelper();
String imageId = imageHelper.uploadPrescriptionImage(prescriptionImage);

// 5. Create Payment
CreateOrderPaymentHelper paymentHelper = new CreateOrderPaymentHelper();
String paymentId = paymentHelper.createPayment(cartId, paymentDetails);

// 6. Upload Order
OrderUploadHelper uploadHelper = new OrderUploadHelper();
String orderId = uploadHelper.uploadOrder(cartId, paymentId);
```

### **2. Barcode Management**

#### **Barcode Validation**
```java
ValidateBarCodeHelper barcodeHelper = new ValidateBarCodeHelper();

// Validate product barcode
boolean isValidProduct = barcodeHelper.validateBarcode("PROD123456", storeId);

// Validate lens-only barcode
ValidateLensOnlyBarcodeHelper lensHelper = new ValidateLensOnlyBarcodeHelper();
boolean isValidLens = lensHelper.validateLensBarcode("LENS789012");

// Fetch barcode details
FetchBarCodeHelper fetchHelper = new FetchBarCodeHelper();
BarcodeDetails details = fetchHelper.fetchBarcodeDetails("PROD123456");
```

### **3. Cart Management**

#### **POS Cart Operations**
```
CreateCartHelper cartHelper = new CreateCartHelper();

// Create cart with barcode
String cartId = cartHelper.createCartWithBarcode(customerId, barcode, quantity);

// Get cart details
GetCartHelper getCartHelper = new GetCartHelper();
CartResponse cart = getCartHelper.getCart(cartId);

// Remove auto-added items
RemoveAutoAddedItemsHelper removeHelper = new RemoveAutoAddedItemsHelper();
removeHelper.removeAutoAddedItems(cartId);
```

### **4. Customer and Product Management**

#### **Customer Operations**
```java
GetCustomerDetailsHelper customerHelper = new GetCustomerDetailsHelper();

// Get customer details
CustomerDetails customer = customerHelper.getCustomerDetails(customerId);

// Search customer by phone
CustomerDetails customer = customerHelper.searchCustomerByPhone(phoneNumber);
```

#### **Product and Package Operations**
```java
GetProductAndPackageHelper productHelper = new GetProductAndPackageHelper();

// Get product details by barcode
ProductDetails product = productHelper.getProductByBarcode(barcode);

// Get package details
PackageDetails packageInfo = productHelper.getPackageDetails(packageId);

// Get supplier package information
SupplierPackageResponse supplierPackage = productHelper.getSupplierPackage(supplierId);
```

### **5. Prescription Management**

#### **Prescription Operations**
```java
GetPrescriptionHelper prescriptionHelper = new GetPrescriptionHelper();

// Get prescription details
PrescriptionDetails prescription = prescriptionHelper.getPrescription(prescriptionId);

// Validate prescription
boolean isValid = prescriptionHelper.validatePrescription(prescriptionData);

// Upload prescription image
UploadImageHelper imageHelper = new UploadImageHelper();
String imageId = imageHelper.uploadPrescriptionImage(prescriptionFile);
```

### **6. Delivery and Estimation**

#### **Delivery Operations**
```java
DeliveryEstimateHelper deliveryHelper = new DeliveryEstimateHelper();

// Get delivery estimate
DeliveryEstimate estimate = deliveryHelper.getDeliveryEstimate(
    productId, customerAddress, storeLocation
);

// Validate delivery address
boolean isValidAddress = deliveryHelper.validateDeliveryAddress(address);
```

## 🌐 **API Endpoints**

### **POS Endpoints**
```java
public enum PosEndpoints implements BaseEndpoint {
    // Authentication
    LOGIN("/api/v1/auth/login", "posService", "POST", "POS user authentication"),
    
    // Barcode Management
    VALIDATE_BARCODE("/api/v1/barcode/validate", "posService", "POST", "Validate product barcode"),
    FETCH_BARCODE("/api/v1/barcode/{barcode}", "posService", "GET", "Fetch barcode details"),
    
    // Cart Management
    CREATE_CART("/api/v1/cart/create", "posService", "POST", "Create POS cart"),
    GET_CART("/api/v1/cart/{cartId}", "posService", "GET", "Get cart details"),
    ADD_TO_CART("/api/v1/cart/{cartId}/add", "posService", "POST", "Add item to cart"),
    
    // Order Management
    UPLOAD_ORDER("/api/v1/order/upload", "posService", "POST", "Upload POS order"),
    GET_ORDER("/api/v1/order/{orderId}", "posService", "GET", "Get order details"),
    
    // Customer Management
    GET_CUSTOMER("/api/v1/customer/{customerId}", "posService", "GET", "Get customer details"),
    SEARCH_CUSTOMER("/api/v1/customer/search", "posService", "POST", "Search customer"),
    
    // Product Management
    GET_PRODUCT("/api/v1/product/{productId}", "posService", "GET", "Get product details"),
    GET_PACKAGE("/api/v1/package/{packageId}", "posService", "GET", "Get package details"),
    
    // Image Upload
    UPLOAD_IMAGE("/api/v1/image/upload", "posService", "POST", "Upload prescription image"),
    
    // Delivery
    DELIVERY_ESTIMATE("/api/v1/delivery/estimate", "posService", "POST", "Get delivery estimate");
}
```

## 🔧 **Configuration**

### **pos.yml Configuration**
```yaml
pos:
  baseUrls:
    posService: https://pos.preprod.lenskart.com
    
  # Country-specific POS user configurations
  users:
    IN:
      - username: "pos_user_in_1"
        password: "PosPass123!"
        storeId: "STORE_IN_001"
        storeName: "Mumbai Central"
      - username: "pos_user_in_2"
        password: "PosPass456!"
        storeId: "STORE_IN_002"
        storeName: "Delhi CP"
        
    SG:
      - username: "pos_user_sg_1"
        password: "PosPassSG123!"
        storeId: "STORE_SG_001"
        storeName: "Orchard Road"
        
    AE:
      - username: "pos_user_ae_1"
        password: "PosPassAE123!"
        storeId: "STORE_AE_001"
        storeName: "Dubai Mall"
        
  defaults:
    sessionTimeout: 3600
    uploadTimeout: 60000
    imageMaxSize: 5242880  # 5MB
    
  validation:
    validateBarcode: true
    validateCustomer: true
    validatePrescription: true
    
  features:
    enableImageUpload: true
    enableBarcodeValidation: true
    enableDeliveryEstimate: true
```

### **Configuration Usage**
```java
// Get POS configuration
PosConfig config = PosConfigLoader.loadConfig();

// Get country-specific user
PosUser user = config.getPosUser("IN", 0); // First user for India

// Get base URL
String baseUrl = config.getBaseUrl("posService");

// Get store information
String storeId = user.getStoreId();
String storeName = user.getStoreName();
```

## 🧪 **Testing**

### **Test Categories**

#### **Sanity Tests**
```java
@Test
@TestCategory(TestCategory.Category.SANITY)
public void testPosAuthentication() {
    AuthenticationHelper authHelper = new AuthenticationHelper();
    String token = authHelper.authenticateUser("pos_user_in_1", "PosPass123!", "IN");
    assert token != null && !token.isEmpty();
}

@Test
@TestCategory(TestCategory.Category.SANITY)
public void testBarcodeValidation() {
    ValidateBarCodeHelper helper = new ValidateBarCodeHelper();
    boolean isValid = helper.validateBarcode("PROD123456", "STORE_IN_001");
    assert isValid;
}
```

#### **Regression Tests**
```java
@Test
@TestCategory(TestCategory.Category.REGRESSION)
public void testCompletePosOrderWorkflow() {
    PosOrderCreationHelper helper = new PosOrderCreationHelper();
    
    // Create complete POS order
    String orderId = helper.createCompleteOrder(
        customerId, barcode, prescriptionData, 
        storeLocation, paymentMethod
    );
    
    // Validate order
    assert orderId != null && !orderId.isEmpty();
    
    // Verify order details
    GetOrderDetailsHelper orderHelper = new GetOrderDetailsHelper();
    OrderDetails order = orderHelper.getOrderDetails(orderId);
    assert order.getStatus().equals("UPLOADED");
}
```

### **Running POS Tests**
```bash
# Run all POS tests
mvn test -pl pos

# Run specific test categories
mvn test -pl pos -DtestCategory=SANITY

# Run with specific country
mvn test -pl pos -Dcountry=IN

# Run specific test class
mvn test -pl pos -Dtest=PosOrderCreationTest
```

## 📊 **Data Models**

### **POS-Specific Models**
```java
// Cart Response Model
public class CartResponse {
    private String cartId;
    private String customerId;
    private List<CartItem> items;
    private PriceBreakup priceBreakup;
    private String status;
}

// Order Request Model
public class OrderRequest {
    private String cartId;
    private String paymentId;
    private String storeId;
    private String userId;
    private DeliveryDetails delivery;
}

// Package Detail Model
public class PackageDetail {
    private String packageId;
    private String packageName;
    private String supplierId;
    private List<String> includedItems;
    private double price;
}
```

## 🛠️ **Request Builders**

### **POS Order Request Builder**
```java
PosOrderRequestBuilder builder = new PosOrderRequestBuilder();

// Build cart creation request
Map<String, Object> cartRequest = builder
    .withCustomerId(customerId)
    .withBarcode(barcode)
    .withQuantity(1)
    .withStoreId(storeId)
    .buildCreateCartRequest();

// Build order upload request
Map<String, Object> orderRequest = builder
    .withCartId(cartId)
    .withPaymentId(paymentId)
    .withStoreId(storeId)
    .withDeliveryDetails(deliveryDetails)
    .buildOrderUploadRequest();
```

## 🔍 **Exception Handling**

### **POS Exception States**
```java
public enum PosExceptionStates {
    AUTHENTICATION_FAILED("POS_001", "POS user authentication failed"),
    INVALID_BARCODE("POS_002", "Invalid or unrecognized barcode"),
    CART_CREATION_FAILED("POS_003", "Failed to create POS cart"),
    IMAGE_UPLOAD_FAILED("POS_004", "Failed to upload prescription image"),
    ORDER_UPLOAD_FAILED("POS_005", "Failed to upload POS order"),
    STORE_NOT_FOUND("POS_006", "Store not found or inactive");
}
```

## 🌍 **Multi-Country Support**

### **Country-Specific Features**
- **India (IN)**: Full POS functionality with prescription management
- **Singapore (SG)**: POS operations with local compliance
- **UAE (AE)**: POS operations with regional customizations

### **Country-Specific Testing**
```java
@Test
@TestCategory(TestCategory.Category.SANITY)
public void testIndianPosOperations() {
    // Test India-specific POS functionality
    PosConfig config = PosConfigLoader.loadConfig();
    PosUser indianUser = config.getPosUser("IN", 0);
    
    AuthenticationHelper authHelper = new AuthenticationHelper();
    String token = authHelper.authenticateUser(
        indianUser.getUsername(), 
        indianUser.getPassword(), 
        "IN"
    );
    
    assert token != null;
}
```

## 🚀 **Getting Started**

### **1. Add POS Dependency**
```xml
<dependency>
    <groupId>com.lenskart</groupId>
    <artifactId>pos</artifactId>
    <version>1.0.0</version>
</dependency>
```

### **2. Configure POS Service**
Update `pos.yml` with your POS service configuration and user credentials.

### **3. Create Your First Test**
```java
public class MyPosTest {
    @Test
    @TestCategory(TestCategory.Category.SANITY)
    public void testPosLogin() {
        AuthenticationHelper helper = new AuthenticationHelper();
        String token = helper.authenticateUser("pos_user_in_1", "PosPass123!", "IN");
        assert token != null;
    }
}
```

## 📚 **Examples**

Check the test classes for comprehensive examples:
- **POS Authentication**: Multi-country user authentication
- **Barcode Operations**: Barcode validation and processing
- **Cart Management**: POS cart creation and management
- **Order Processing**: Complete POS order workflow
- **Image Upload**: Prescription and product image handling
- **Customer Management**: Customer search and details

The POS module provides comprehensive automation capabilities for testing Lenskart's Point of Sale system, ensuring reliable in-store order processing and customer service operations across multiple countries.
