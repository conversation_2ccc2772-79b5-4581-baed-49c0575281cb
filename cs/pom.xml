<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lenskart</groupId>
        <artifactId>be-automation</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>cs</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <lombok.version>1.18.30</lombok.version>
        <commons.version>1.0.0</commons.version>
        <scm.version>1.0.0</scm.version>

    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>core</artifactId>
            <version>8.0.16</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>order-metadata</artifactId>
            <version>0.0.32</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>return-common</artifactId>
            <version>0.1.27</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>refund-common</artifactId>
            <version>0.0.1.SPRINT-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>commons</artifactId>
            <version>${commons.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>scm</artifactId>
            <version>${scm.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>nexs</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>

        <!--Tracking Middleware Dependency-->
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>tracking-middlware</artifactId>
            <version>1.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>juno</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>pos</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>

        <!-- Explicit Jackson dependencies to ensure version alignment -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- Lombok Annotation Processor -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <release>21</release>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire.version}</version>
                <configuration>
                    <!-- Configure TestNG listeners -->
                    <properties>
                        <property>
                            <name>usedefaultlisteners</name>
                            <value>false</value>
                        </property>
                        <property>
                            <name>listener</name>
                            <value>com.lenskart.commons.listeners.ExtentReportListener,com.lenskart.commons.listeners.TestCategoryListener</value>
                        </property>
                    </properties>
                    <!-- Use default TestNG XML file for CS module -->
                    <suiteXmlFiles>
                        <suiteXmlFile>src/test/resources/testng.xml</suiteXmlFile>
                    </suiteXmlFiles>
                    <!-- Skip tests by default, but allow override from command line -->
                    <skipTests>${skipTests}</skipTests>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.surefire</groupId>
                        <artifactId>surefire-testng</artifactId>
                        <version>${surefire.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>