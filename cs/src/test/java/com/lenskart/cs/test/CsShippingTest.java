package com.lenskart.cs.test;

import com.lenskart.cs.helpers.ShippingEstimateHelper;
import com.lenskart.cs.model.CsOrderContext;

import com.lenskart.cs.dataprovider.CsDataProvider;
import lombok.extern.slf4j.Slf4j;

import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
public class CsShippingTest {


    @Test(dataProviderClass = CsDataProvider.class, dataProvider = "shippingEstimate")
    public void verifyShippingEstimate(CsOrderContext.ShippingEstimate csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(csOrderContext));

        ShippingEstimateHelper.builder()
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }
}

