package com.lenskart.cs.test;

import com.lenskart.cs.dataprovider.CsDataProvider;
import org.testng.annotations.Test;
import com.lenskart.cs.helpers.CrmStoreLocatorHelper;
import com.lenskart.cs.model.CsOrderContext;

public class CrmStoreLocatorTest {
    @Test(dataProviderClass = CsDataProvider.class, dataProvider = "crmNearestStoreProvider")
    public void verifyCrmNearestStore(CsOrderContext.ShippingEstimate shippingEstimate) {

        CrmStoreLocatorHelper.builder()
                .shippingEstimate(shippingEstimate)
                .build()
                .test();
    }
}
