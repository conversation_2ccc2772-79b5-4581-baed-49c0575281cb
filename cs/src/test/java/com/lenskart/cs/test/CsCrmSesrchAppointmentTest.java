package com.lenskart.cs.test;

import com.lenskart.cs.dataprovider.CsDataProvider;
import com.lenskart.cs.helpers.CrmSearchAppiontmentHelper;
import com.lenskart.cs.model.CsOrderContext;
import org.testng.annotations.Test;

public class CsCrmSesrchAppointmentTest {

    @Test(dataProviderClass = CsDataProvider.class, dataProvider = "crmSearchAppointmentProvider")
    public void verifyCrmSearchAppointment(CsOrderContext.CrmSearchAppointmentContext crmSearchAppointmentContext) {

        CrmSearchAppiontmentHelper.builder()
                .crmSearchAppointmentContext(crmSearchAppointmentContext)
                .build()
                .test();
    }
}
