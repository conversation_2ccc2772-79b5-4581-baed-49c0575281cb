package com.lenskart.cs.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.cs.model.CsOrderContext;
import org.testng.annotations.DataProvider;

public class CsDataProvider {

    @DataProvider (name = "shippingEstimate")
    public Object[][] shippingEstimateContext() {
        return new Object[][]{

                {
                        CsOrderContext.ShippingEstimate.builder().source_facility("").pincode(121001).shipping_country(Countries.IN).build()
                }
        };
    }
    @DataProvider(name = "crmNearestStoreProvider")
    public Object[][] crmNearestStoreProvider() {
        return new Object[][]{
                {
                        CsOrderContext.ShippingEstimate.builder()
                                .pincode(560038) //Bangalore
                                .build()
                },
                {
                        CsOrderContext.ShippingEstimate.builder()
                                .pincode(110001)  // Delhi
                                .build()
                },
                {
                        CsOrderContext.ShippingEstimate.builder()
                                .pincode(400001)  // Mumbai
                                .build()
                },
                {
                        CsOrderContext.ShippingEstimate.builder()
                                .pincode(600001)  // Chennai
                                .build()
                },
                {
                        CsOrderContext.ShippingEstimate.builder()
                                .pincode(700001)  // Kolkata
                                .build()
                }
        };
    }
    @DataProvider(name = "crmSearchAppointmentProvider")
    public Object[][] crmSearchAppointmentProvider() {
        return new Object[][]{
                {
                        CsOrderContext.CrmSearchAppointmentContext.builder()
                                .storeCode("LKST764")
                                .build()
                },
                {
                        CsOrderContext.CrmSearchAppointmentContext.builder()
                                .storeCode("ST113")
                                .build()
                }
        };
    }
}
