package com.lenskart.cs.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

@Slf4j
public class ChecksumUtil {


    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";

    // store the secret in config
    public static String createChecksum(String secret, String body) {
        String result = null;
        try {

            // get a hmac_sha256 key from the raw secret bytes
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(), HMAC_SHA256_ALGORITHM);

            // get a hmac_sha256 Mac instance and initialize with the signing key
            Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            mac.init(signingKey);

            // compute the hmac on input data bytes
            byte[] rawHmac = mac.doFinal(body.getBytes());

            // base64-encode the hmac
            result = Base64.encodeBase64String(rawHmac).toLowerCase();

        } catch (Exception e) {
            log.error("Error creating checksum: {}", e.getMessage());
        }
        return result;
    }

}
