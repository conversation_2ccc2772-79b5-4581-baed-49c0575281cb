package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.AuthenticationResponse;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ItemResolution;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.ITEM_RESOLUTION_RECEIVING;


@SuperBuilder
public class ItemResolutionHelper extends CsBaseHelper implements ServiceHelper {


    String payload;
    OrderContext orderContext;
    Response response;
    ItemResolution itemResolution;
    CsOrderContext csOrderContext;
    NexsOrderContext nexsOrderContext;
    ItemSearchByOrderFacilityHelper itemSearchHelper;
    AuthenticationResponse authenticationResponse;

    @Override
    public ServiceHelper init() {
        headers = getTrackingValidatorHeaders(orderContext,csOrderContext,authenticationResponse.getUserName());
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.itemResolutionModel(orderContext,csOrderContext,nexsOrderContext,itemSearchHelper.getItemSearchOnlineResponse(),authenticationResponse));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(ITEM_RESOLUTION_RECEIVING.getUrl(), null, payload,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
