package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.AuthenticationResponse;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.OpenPutawayResponse;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import static com.lenskart.cs.endpoints.CsEndpoints.OPEN_PUTAWAYS;

@Slf4j
@SuperBuilder
@Getter
public class OpenPutawaysHelper extends CsBaseHelper implements ServiceHelper {

    private String userId;
    private OrderContext orderContext;
    private CsOrderContext csOrderContext;
    private Response response;
    private AuthenticationResponse authenticationResponse;
    private Map<String, String> customHeaders;

    @Override
    public ServiceHelper init() {
        statusCode=200;
        headers = getHeadersWithSessionToken(orderContext,csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(OPEN_PUTAWAYS.getUrl(Map.of("userId",authenticationResponse.getUserId())), headers, null, statusCode);
        List<OpenPutawayResponse> responseList = response.jsonPath().getList("", OpenPutawayResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}