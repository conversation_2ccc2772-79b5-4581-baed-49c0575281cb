package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CancellationInvoiceResponse;
import com.lenskart.cs.model.CancellationReasonRefundMethodsResponse;
import com.lenskart.cs.model.CancellationRequest;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.cs.validators.CancellationReasonRefundMethodsValidator;
import com.lenskart.cs.validators.CancellationValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.lenskart.cs.endpoints.CsEndpoints.CANCELLATION_REASON_REFUND_METHODS;
import static com.lenskart.cs.endpoints.CsEndpoints.CANCEL_INVOICE;


@SuperBuilder
@Slf4j
public class CancellationReasonsRefundMethodsHelper extends CsBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    CancellationReasonRefundMethodsResponse cancellationReasonRefundMethodsResponse;
    CsOrderContext csOrderContext;


    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(CANCELLATION_REASON_REFUND_METHODS.getUrl(Map.of("orderID",String.valueOf(orderContext.getOrderId()),"client",csOrderContext.getClient())), null, null,200);
        cancellationReasonRefundMethodsResponse = response.as(CancellationReasonRefundMethodsResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        CancellationReasonRefundMethodsValidator validator = CancellationReasonRefundMethodsValidator.builder().csOrderContext(csOrderContext).orderContext(orderContext).cancellationReasonRefundMethodsResponse(cancellationReasonRefundMethodsResponse).build();
        validator.validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
