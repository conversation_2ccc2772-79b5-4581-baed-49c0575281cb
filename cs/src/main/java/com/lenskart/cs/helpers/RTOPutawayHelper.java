package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.RTOPutawayResponse;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.cs.endpoints.CsEndpoints.RTO_PUTAWAY;

@SuperBuilder
@Getter
public class RTOPutawayHelper extends CsBaseHelper implements ServiceHelper {
    String payload;
    OrderContext orderContext;
    Response response;
    CsOrderContext csOrderContext;
    CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass;
    RTOPutawayResponse rtoPutawayResponse;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext, csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(
            RTO_PUTAWAY.getUrl(Map.of("userId", csOrderContextReceivingGatePass.getUserId())),
            headers,
            null,
            200
        );
        
        // Parse the response into our custom class
        rtoPutawayResponse = response.as(RTOPutawayResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}