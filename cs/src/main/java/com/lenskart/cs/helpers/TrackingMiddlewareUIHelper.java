package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.TrackingMiddlewareUIResponse;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.trackingmiddleware.models.response.TrackingRawScanLogsRespone;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.List;

import static com.lenskart.cs.endpoints.CsEndpoints.MIDDLEWARE_FUSION_TRACKING;

@SuperBuilder
public class TrackingMiddlewareUIHelper extends CsBaseHelper implements ServiceHelper {
    String payload;
    String awbNo;
    OrderContext orderContext;
    List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList;
    CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails;
    Response response;
    TrackingMiddlewareUIResponse trackingMiddlewareUIResponse;


    @Override
    public ServiceHelper init() {
        headers = getPushScansHeader(trackingMiddlewareDetails.getX_api_key());
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.getEventsByAWB(trackingMiddlewareDetails,awbNo));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(MIDDLEWARE_FUSION_TRACKING.getUrl(),null,payload,200);
        trackingMiddlewareUIResponse = response.as(TrackingMiddlewareUIResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
