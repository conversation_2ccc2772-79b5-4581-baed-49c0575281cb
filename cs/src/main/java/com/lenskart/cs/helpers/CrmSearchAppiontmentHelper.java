package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.config.CsConfigRegistry;
import com.lenskart.cs.model.CrmSearchAppointmentResponse;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.CRM_SEARCH_APPOINTMENT;

@SuperBuilder
public class CrmSearchAppiontmentHelper extends CsBaseHelper implements ServiceHelper {

    CsOrderContext.CrmSearchAppointmentContext crmSearchAppointmentContext;
    Response response;
    String payload;
    CrmSearchAppointmentResponse crmSearchAppointmentResponse;

    @Override
    public ServiceHelper init() {
        headers = getCrmStoreLocatorHeaders(CsConfigRegistry.getInstance().getTrackingMiddlewareConfig("xAuthToken"));
        payload= JsonUtils.convertObjectToJsonString(CsRequestBuilders.getCrmSearchAppointmentRequest(crmSearchAppointmentContext));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response= RestUtils.post(CRM_SEARCH_APPOINTMENT.getUrl(),
                headers,
                payload,
                200);
        crmSearchAppointmentResponse= response.as(CrmSearchAppointmentResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
