package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class CsReturnCancellationHelper extends CsBaseHelper implements ExecutionHelper {


    OrderContext orderContext;
    JSONObject payload;
    Response response;
    CsOrderContext csOrderContext;
    NexsOrderContext nexsOrderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        ReturnHelper returnHelper = ReturnHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build();

        returnHelper.test();

        ReturnCancellationHelper.builder()
                .orderContext(orderContext)
                .build().test();
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
