package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.config.CsConfigRegistry;
import com.lenskart.cs.model.CrmInitiateCancellationResponse;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.CRM_INITIATE_CANCELLATION;

@Slf4j
@SuperBuilder
public class CrmInitiateCancellationHelper extends CsBaseHelper implements ServiceHelper {

    String payload;
    Response response;
    CsOrderContext csOrderContext;
    OrderContext orderContext;
    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    CrmInitiateCancellationResponse crmInitiateCancellationResponse;


    @Override
    public ServiceHelper init() {
        statusCode=200;
        headers = getCrmHeaders(CsConfigRegistry.getInstance().getTrackingMiddlewareConfig("authToken"));
        payload= JsonUtils.convertObjectToJsonString(CsRequestBuilders.getCrmCancellationRequest(orderContext,csOrderContext));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(CRM_INITIATE_CANCELLATION.getUrl(), headers, payload, statusCode);
        crmInitiateCancellationResponse = response.as(CrmInitiateCancellationResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
