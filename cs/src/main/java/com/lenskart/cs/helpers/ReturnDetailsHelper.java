package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnDetailsResponse;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.RETURN_DETAILS_V2;

@SuperBuilder
public class ReturnDetailsHelper extends CsBaseHelper implements ServiceHelper {

    String payload;
    OrderContext orderContext;
    CsOrderContext csOrderContext;
    Response response;
    ReturnDetailsResponse returnDetailsResponse;

    @Override
    public ServiceHelper init() {
        statusCode=200;
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.getReturnDetailsRequest(csOrderContext));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response= RestUtils.post(RETURN_DETAILS_V2.getUrl(),
                null,
                payload,
                statusCode);
        returnDetailsResponse = response.as(ReturnDetailsResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
