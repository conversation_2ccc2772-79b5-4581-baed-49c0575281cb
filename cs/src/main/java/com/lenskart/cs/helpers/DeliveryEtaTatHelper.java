package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.DELIVERY_ETA;


@SuperBuilder
public class DeliveryEtaTatHelper extends CsBaseHelper implements ServiceHelper {


    String payload;
    OrderContext orderContext;
    Response response;
    CsOrderContext.ShippingEstimate csOrderContext;


    @Override
    public ServiceHelper init() {
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.getDeliveryEtaModel(csOrderContext));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(DELIVERY_ETA.getUrl(), null, payload,202);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
