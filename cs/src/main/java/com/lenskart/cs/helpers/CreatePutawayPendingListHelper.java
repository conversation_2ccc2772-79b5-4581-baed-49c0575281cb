package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CreatePendingPutawayResponse;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ItemStatusReceivedResponse;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;
import static com.lenskart.cs.endpoints.CsEndpoints.RECEIVING_CREATE_PUTAWAY_LIST_PENDING;

@SuperBuilder
public class CreatePutawayPendingListHelper extends CsBaseHelper implements ServiceHelper {
    String payload;
    OrderContext orderContext;
    Response response;
    CsOrderContext csOrderContext;
    CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass;
    CreatePendingPutawayResponse createPendingPutawayResponse;
    ItemStatusReceivedResponse itemStatusReceivedResponse;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext,csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {

        // Verify the request payload format
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.getPutawayPendingListRequest(itemStatusReceivedResponse.getResolution().split(":")[1].trim()));

        response = RestUtils.post(
            RECEIVING_CREATE_PUTAWAY_LIST_PENDING.getUrl(Map.of("status", csOrderContextReceivingGatePass.getStatus(),
                    "facility",CsRequestBuilders.getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getFacilityCode())),
            headers,payload,
            200
        );

        createPendingPutawayResponse = response.as(CreatePendingPutawayResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}