package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.OfflineProductSearchResponse;
import io.restassured.response.Response;
import lombok.Builder;
import lombok.experimental.SuperBuilder;
import java.util.Map;

import static com.lenskart.cs.endpoints.CsEndpoints.ITEM_SEARCH_OFFLINE_PRODUCT;

@SuperBuilder
@Builder
public class OfflineProductSearchHelper extends CsBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    CsOrderContext csOrderContext;
    Response response;
    OfflineProductSearchResponse offlineProductSearchResponse;

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext,csOrderContext);
        queryParams = getOfflineProductSearchQueryParams(csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(
            ITEM_SEARCH_OFFLINE_PRODUCT.getUrl(Map.of("productId",csOrderContext.getProductIDToBeReturned())),
            headers, 
            queryParams, 
            statusCode
        );
        offlineProductSearchResponse = response.as(OfflineProductSearchResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}