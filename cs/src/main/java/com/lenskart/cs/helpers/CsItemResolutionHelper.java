package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class CsItemResolutionHelper extends CsBaseHelper implements ExecutionHelper {


    OrderContext orderContext;
    JSONObject payload;
    Response response;
    CsOrderContext csOrderContext;
    CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass;
    NexsOrderContext nexsOrderContext;


    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        AuthenticationHelper authenticationHelper=AuthenticationHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build();

        authenticationHelper.test();

        ItemSearchByOrderFacilityHelper itemSearchHelper =ItemSearchByOrderFacilityHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build();

        itemSearchHelper.test();

        ItemResolutionHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .nexsOrderContext(nexsOrderContext)
                .authenticationResponse(authenticationHelper.authenticationResponse)
                .itemSearchHelper(itemSearchHelper)
                .build()
                .test();

        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
