package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.config.CsConfigRegistry;
import com.lenskart.cs.model.CrmStoreLocatorResponse;
import com.lenskart.cs.model.CsOrderContext;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.core.model.ItemResolutionFlat_.log;
import static com.lenskart.cs.endpoints.CsEndpoints.CRM_STORE_LOCATOR;

@SuperBuilder
@Slf4j
public class CrmStoreLocatorHelper extends CsBaseHelper implements ServiceHelper {

    CsOrderContext.ShippingEstimate shippingEstimate;
    Response response;
    CrmStoreLocatorResponse crmStoreLocatorResponse;

    @Override
    public ServiceHelper init() {
        statusCode=200;
        headers = getCrmStoreLocatorHeaders(CsConfigRegistry.getInstance().getTrackingMiddlewareConfig("xAuthToken"));
        queryParams= getCrmStoreLocatorQueryParams(shippingEstimate);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response= RestUtils.get(CRM_STORE_LOCATOR.getUrl(),
                headers,
                queryParams,
                200);
        crmStoreLocatorResponse = parseResponse(response.asPrettyString(), CrmStoreLocatorResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
