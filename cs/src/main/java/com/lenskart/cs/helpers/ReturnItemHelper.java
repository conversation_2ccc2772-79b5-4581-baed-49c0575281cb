package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnOrderRequestDTO;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.RETURN_ITEMS;


@SuperBuilder
public class ReturnItemHelper extends CsBaseHelper implements ServiceHelper {

    ReturnOrderRequestDTO payload;
    OrderContext orderContext;
    Response response;
    CsOrderContext csOrderContext;
    ReturnHelper returnHelper;
    NexsOrderContext nexsOrderContext;


    @Override
    public ServiceHelper init() {
        statusCode=200;
        payload = CsRequestBuilders.createPayloadForReturnItems(orderContext,csOrderContext,returnHelper.returnResponse,nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        /* Initiate Refund */
        response = RestUtils.post(RETURN_ITEMS.getUrl(), null, JsonUtils.convertObjectToJsonString(payload), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
