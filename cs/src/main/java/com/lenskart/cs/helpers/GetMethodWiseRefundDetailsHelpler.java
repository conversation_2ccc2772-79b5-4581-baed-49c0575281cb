package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.GATE_PASS_CLOSUREOFFLINE;
import static com.lenskart.cs.endpoints.CsEndpoints.METHOD_WISE_REFUND_DETAILS;


@SuperBuilder
public class GetMethodWiseRefundDetailsHelpler extends CsBaseHelper implements ServiceHelper {


    String payload;
    OrderContext orderContext;
    Response response;
    CsOrderContext.GetMethodWiseRefundDetails getMethodWiseRefundDetails;


    @Override
    public ServiceHelper init() {
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.getMethodWiseRefundDetailsModel(getMethodWiseRefundDetails));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(METHOD_WISE_REFUND_DETAILS.getUrl(), null, payload,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
