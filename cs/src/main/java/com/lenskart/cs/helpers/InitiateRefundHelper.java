package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import static com.lenskart.cs.endpoints.CsEndpoints.INITIATE_REFUND;

@SuperBuilder
public class InitiateRefundHelper extends CsBaseHelper implements ServiceHelper {

    String payload;
    OrderContext orderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        payload = CsRequestBuilders.createPayloadForRefund(orderContext.getOrderId());
        headers = getHeaders(payload);
        return this;
    }

    @Override
    public ServiceHelper process() {
        /* Initiate Refund */
        response = RestUtils.post(INITIATE_REFUND.getUrl(), headers, payload, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
