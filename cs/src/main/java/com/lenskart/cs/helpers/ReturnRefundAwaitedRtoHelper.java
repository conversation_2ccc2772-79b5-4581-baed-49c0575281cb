package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnOrderRequestDTO;
import com.lenskart.cs.model.ReturnRefundAwaitedRtoResponse;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.RETURN_REFUND_AWAITED_RTO;

@SuperBuilder
public class ReturnRefundAwaitedRtoHelper extends CsBaseHelper implements ServiceHelper {

    private OrderContext orderContext;
    private CsOrderContext csOrderContext;
    private Response response;
    private String payload;
    private ReturnResponse returnResponse;
    private ReturnRefundAwaitedRtoResponse returnRefundAwaitedRtoResponse;
    private NexsOrderContext nexsOrderContext;

    @Override
    public ServiceHelper init() {
        statusCode=200;
        payload=JsonUtils.convertObjectToJsonString(CsRequestBuilders.createPayloadForReturnItems(orderContext,csOrderContext,returnResponse,nexsOrderContext));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response= RestUtils.post(RETURN_REFUND_AWAITED_RTO.getUrl(),
                null,
                payload,
                statusCode);
        returnRefundAwaitedRtoResponse = response.as(ReturnRefundAwaitedRtoResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}
