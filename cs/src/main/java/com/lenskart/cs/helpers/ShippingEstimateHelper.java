package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.cs.validators.CancellationValidator;
import com.lenskart.cs.validators.ShippingEstimateValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.INITIATE_REFUND;
import static com.lenskart.cs.endpoints.CsEndpoints.SHIPPING_ESTIMATE;

@SuperBuilder
public class ShippingEstimateHelper extends CsBaseHelper implements ServiceHelper {

    String payload;
    OrderContext orderContext;
    CsOrderContext.ShippingEstimate csOrderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        queryParams=getQueryParamsEstimateAPI(orderContext,csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(SHIPPING_ESTIMATE.getUrl(), null, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        ShippingEstimateValidator validator = ShippingEstimateValidator.builder().csOrderContext(csOrderContext).build();
        validator.validateNode();
        validator.validateDBEntities();

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
