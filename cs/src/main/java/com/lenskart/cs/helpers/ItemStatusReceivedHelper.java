package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.*;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static com.lenskart.cs.endpoints.CsEndpoints.*;

@Slf4j
@SuperBuilder
@Getter
public class ItemStatusReceivedHelper extends CsBaseHelper implements ServiceHelper {

    Response response;
    OrderContext orderContext;
    CsOrderContext csOrderContext;
    ItemSearchOnlineResponse itemSearchOnlineResponse;
    ItemStatusReceivedResponse itemStatusReceivedResponse;

    @Override
    public ServiceHelper init() {
        statusCode=200;
        headers = getHeadersWithSessionToken(orderContext,csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(ITEM_STATUS_RECEIVED.getUrl(Map.of("uwItemId",String.valueOf(itemSearchOnlineResponse.getItemDetailsList().getFirst().getUwItemId()),"barcode",String.valueOf(itemSearchOnlineResponse.getItemDetailsList().getFirst().getBarcode()))), headers, null, statusCode);
        itemStatusReceivedResponse=response.as(ItemStatusReceivedResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}