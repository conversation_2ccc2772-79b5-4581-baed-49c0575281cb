package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class CsReturnHelper extends CsBaseHelper implements ExecutionHelper {


    OrderContext orderContext;
    JSONObject payload;
    Response response;
    CsOrderContext csOrderContext;
    NexsOrderContext nexsOrderContext;
    CsOrderContext.ReceivingGatePass receivingGatePass;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        ReturnHelper returnHelper = ReturnHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build();

        returnHelper.test();

        ReversePickupInfoHelper.builder()
                .returnHelper(returnHelper)
                .orderContext(orderContext)
                .build().test();

        ReturnItemHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .nexsOrderContext(nexsOrderContext)
                .returnHelper(returnHelper)
                .build().test();

        AwaitUtils.sleepSeconds(15);

        AuthenticationHelper authenticationHelper=AuthenticationHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build();

        authenticationHelper.test();

        ItemSearchByOrderFacilityHelper itemSearchHelper =ItemSearchByOrderFacilityHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build();

        itemSearchHelper.test();

        ItemResolutionHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .nexsOrderContext(nexsOrderContext)
                .authenticationResponse(authenticationHelper.authenticationResponse)
                .itemSearchHelper(itemSearchHelper)
                .build()
                .test();
        // Get open putaways
        OpenPutawaysHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .authenticationResponse(authenticationHelper.authenticationResponse)
                .build()
                .test();

        ItemStatusReceivingHelper itemStatusReceivingHelper=ItemStatusReceivingHelper.builder()
                .itemSearchOnlineResponse(itemSearchHelper.itemSearchOnlineResponse)
                .build();
        itemStatusReceivingHelper.test();

        ItemStatusReceivedHelper itemStatusReceivedHelper=ItemStatusReceivedHelper.builder()
                .itemSearchOnlineResponse(itemSearchHelper.itemSearchOnlineResponse)
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build();
        itemStatusReceivedHelper.test();

        AwaitUtils.sleepSeconds(15);

        CreatePutawayPendingListHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .itemStatusReceivedResponse(itemStatusReceivedHelper.getItemStatusReceivedResponse())
                .build()
                .test();



        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
