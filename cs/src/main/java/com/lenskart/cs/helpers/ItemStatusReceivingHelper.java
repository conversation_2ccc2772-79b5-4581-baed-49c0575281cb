package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.*;
import com.lenskart.cs.validators.ItemStatusReceivingValidator;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Map;

import static com.lenskart.cs.endpoints.CsEndpoints.ITEM_STATUS_RECEIVING;


@Slf4j
@SuperBuilder
@Getter
public class ItemStatusReceivingHelper extends CsBaseHelper implements ServiceHelper {

    Response response;
    ItemSearchOnlineResponse itemSearchOnlineResponse;
    ItemStatusReceivingResponse itemStatusReceivingResponse;
    boolean itemStatusReceiving;

    @Override
    public ServiceHelper init() {
        statusCode=200;
        return this;
    }

    @Override
    public ServiceHelper process() {
        itemStatusReceiving = AwaitUtils.retryOperation(
                () -> {
                    try {
                        response = RestUtils.get(ITEM_STATUS_RECEIVING.getUrl(Map.of("uwItemId",String.valueOf(itemSearchOnlineResponse.getItemDetailsList().getFirst().getUwItemId()),"barcode",String.valueOf(itemSearchOnlineResponse.getItemDetailsList().getFirst().getBarcode()))), null, null, 200);
                        itemStatusReceivingResponse=response.as(ItemStatusReceivingResponse.class);
                        if ("Return receiving completed".equals(itemStatusReceivingResponse.getMessage())) {
                            return true;
                        }
                    } catch (Exception e) {
                        log.warn("Exception during Item Status Receiving : {}", e.getMessage());
                        return false;
                    }
                    return false;
                },
                "Item Status Receiving",
                4, // max attempts
                Duration.ofSeconds(20) // 20 second poll interval
        );
        return this;
    }

    @Override
    public ServiceHelper validate() {
        ItemStatusReceivingValidator itemStatusReceivingValidator=ItemStatusReceivingValidator.builder().itemReceivingStatus(itemStatusReceiving).build();
        itemStatusReceivingValidator.validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}