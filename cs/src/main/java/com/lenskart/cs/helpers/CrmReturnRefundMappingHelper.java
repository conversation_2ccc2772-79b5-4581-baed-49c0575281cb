package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CrmReturnRefundMappingResponse;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;


import java.util.Map;


import static com.lenskart.cs.endpoints.CsEndpoints.CRM_RETURN_REFUND_MAPPING;

@SuperBuilder
public class CrmReturnRefundMappingHelper extends CsBaseHelper implements ServiceHelper {


    Response response;
    OrderContext orderContext;
    CsOrderContext csOrderContext;
    CrmReturnRefundMappingResponse crmReturnRefundMappingResponse;

    @Override
    public ServiceHelper init() {
        statusCode=200;
        return this;

    }

    @Override
    public ServiceHelper process() {

        response = RestUtils.get(CRM_RETURN_REFUND_MAPPING.getUrl(Map.of("order_id", String.valueOf(orderContext.getOrderId()))),null,null,statusCode);
        crmReturnRefundMappingResponse = response.as(CrmReturnRefundMappingResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
