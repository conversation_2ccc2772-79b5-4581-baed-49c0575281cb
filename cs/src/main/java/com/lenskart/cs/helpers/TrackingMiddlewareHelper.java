package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.config.CsConfigRegistry;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.TrackingRawScanWrapperResponse;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.cs.validators.TrackingMiddlewareScansValidator;
import com.lenskart.trackingmiddleware.models.response.TrackingRawScanLogsRespone;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import static com.lenskart.cs.endpoints.CsEndpoints.MIDDLEWARE_PUSH_SCANS;

@Slf4j
@SuperBuilder
public class TrackingMiddlewareHelper extends CsBaseHelper implements ServiceHelper {
    String payload;
    OrderContext orderContext;
    List<CsOrderContext.TrackingMiddlewareDetails> trackingMiddlewareDetailsList;
    CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails;
    Response response;
    TrackingRawScanWrapperResponse wrapperResponse;
    String awbNo;
    TrackingRawScanLogsRespone trackingRawScanLogsRespone;
    int index;

    @Override
    public ServiceHelper init() {

        headers = getPushScansHeader(CsConfigRegistry.getInstance().getTrackingMiddlewareConfig("x_api_key_"+trackingMiddlewareDetails.getCourierName()));
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.buildPayload(trackingMiddlewareDetails.getCourierName(),trackingMiddlewareDetailsList.get(index),awbNo));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(MIDDLEWARE_PUSH_SCANS.getUrl(),headers,payload,200);
        AwaitUtils.sleepSeconds(15);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        wrapperResponse = response.as(TrackingRawScanWrapperResponse.class);
        TrackingMiddlewareScansValidator validator = TrackingMiddlewareScansValidator.builder()
                .response(wrapperResponse)
                .build();

        validator.validateNode();         // (Optional) Validates if `success=true`
        validator.validateDBEntities();
        return this;
    }

    @Override
    public ServiceHelper test() {
        for (int i=0;i<trackingMiddlewareDetailsList.size();i++){
            index=i;
            init();
            process();
            validate();
        }
        return this;
    }
}
