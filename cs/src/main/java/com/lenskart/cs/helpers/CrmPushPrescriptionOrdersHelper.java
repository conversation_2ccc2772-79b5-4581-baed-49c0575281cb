package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.config.CsConfigRegistry;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.cs.endpoints.CsEndpoints.CRM_PUSH_PRESCRIPTION_ORDERS;

@SuperBuilder
public class CrmPushPrescriptionOrdersHelper extends CsBaseHelper implements ServiceHelper {

    Response response;
    JSONObject payload;
    OrderContext orderContext;
    CsOrderContext.PFUContext pfuContext;

    @Override
    public ServiceHelper init() {
        headers = getCrmStoreLocatorHeaders(CsConfigRegistry.getInstance().getTrackingMiddlewareConfig("xAuthToken"));
        payload= CsRequestBuilders.getCrmPushPrescriptionOrdersRequest(orderContext,pfuContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response= RestUtils.post(CRM_PUSH_PRESCRIPTION_ORDERS.getUrl(Map.of("order_id",String.valueOf(orderContext.getOrderId()),"item_id",String.valueOf(orderContext.getProductLists().getFirst().getItemId()))),
                headers,
                payload.toString(),
                200);
        return this;
    }

    @Override
    public ServiceHelper validate() {

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
