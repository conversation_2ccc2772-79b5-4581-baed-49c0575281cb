package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.*;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.cs.endpoints.CsEndpoints.RTO_RECEIVING;

@SuperBuilder
public class RtoHelper extends CsBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    RTOItemReceivingRequest request;
    ItemSearchHelper itemSearchHelper;
    RTOReceivingMetadata rtoReceivingMetadata;
    CsOrderContext csOrderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext,csOrderContext);
        queryParams = getQueryParams(orderContext,csOrderContext);
        request = CsRequestBuilders.getRTOItemReceivingRequest(orderContext, itemSearchHelper.getItemSearchResponse(),csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(RTO_RECEIVING.getUrl(), headers, JsonUtils.convertObjectToJsonString(request), 200);
        rtoReceivingMetadata = response.as(RTOReceivingMetadata.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
