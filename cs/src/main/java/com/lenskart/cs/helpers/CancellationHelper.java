package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;

import com.lenskart.cs.model.CancellationInvoiceResponse;
import com.lenskart.cs.model.CancellationRequest;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.cs.validators.CancellationValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;

import static com.lenskart.cs.endpoints.CsEndpoints.CANCEL_INVOICE;


@SuperBuilder
@Slf4j
public class CancellationHelper extends CsBaseHelper implements ServiceHelper {


    String payload;
    OrderContext orderContext;
    Response response;
    CancellationInvoiceResponse cancellationInvoiceResponse;
    CancellationRequest cancellationRequest;
    CsOrderContext csOrderContext;


    @Override
    public ServiceHelper init() {
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.getCancellationRequest(csOrderContext,orderContext));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(CANCEL_INVOICE.getUrl(Map.of("orderID",String.valueOf(orderContext.getOrderId()))), null, payload,202);
        cancellationInvoiceResponse = response.as(CancellationInvoiceResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        if(csOrderContext.isSkipCancellationReasonsRefundMethod()) {
            CancellationValidator validator = CancellationValidator.builder().csOrderContext(csOrderContext).orderContext(orderContext).cancellationInvoiceResponse(cancellationInvoiceResponse).build();
            validator.validateNode();
            validator.validateDBEntities();
        }
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
