package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.ReturnIReversePickInfoRequest;
import com.lenskart.cs.model.ReturnRequest;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.cs.model.ReturnReversePickInfoResponse;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.cs.endpoints.CsEndpoints.RETURN;
import static com.lenskart.cs.endpoints.CsEndpoints.REVERSE_PICKUP_INFO_UPDATE;

@SuperBuilder
public class ReversePickupInfoHelper extends CsBaseHelper implements ServiceHelper {


    JSONObject payload;
    OrderContext orderContext;
    Response response;
    ReturnHelper returnHelper;
    ReturnIReversePickInfoRequest request;
    ReturnReversePickInfoResponse returnReversePickInfoResponse;
    ReturnIReversePickInfoRequest returnIReversePickInfo;



    @Override
    public ServiceHelper init() {
        statusCode=200;
        request = CsRequestBuilders.getReturnReversePickInfoRequest(returnHelper.returnResponse);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(REVERSE_PICKUP_INFO_UPDATE.getUrl(), null, JsonUtils.convertObjectToJsonString(request), 200);
        returnReversePickInfoResponse = response.as(ReturnReversePickInfoResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
