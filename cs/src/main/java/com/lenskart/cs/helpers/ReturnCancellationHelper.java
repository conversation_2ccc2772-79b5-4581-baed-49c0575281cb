package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.*;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.cs.validators.ReturnValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.cs.endpoints.CsEndpoints.RETURN;

@SuperBuilder
public class ReturnCancellationHelper extends CsBaseHelper implements ServiceHelper {


    JSONObject payload;
    OrderContext orderContext;
    Response response;
    ReturnCancellationRequest request;
    CsOrderContext csOrderContext;
    ReturnCancellationResponse returnCancellationResponse;
    ReturnIReversePickInfoRequest returnIReversePickInfo;
    ReversePickupInfoHelper reversePickupInfoHelper;
    ReturnHelper returnHelper;

    @Override
    public ServiceHelper init() {
        statusCode=200;
        request = CsRequestBuilders.getReturnCancellationRequestDetails(csOrderContext,returnHelper);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(RETURN.getUrl(), null, JsonUtils.convertObjectToJsonString(request), 200);
        returnCancellationResponse = response.as(ReturnCancellationResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
    return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
