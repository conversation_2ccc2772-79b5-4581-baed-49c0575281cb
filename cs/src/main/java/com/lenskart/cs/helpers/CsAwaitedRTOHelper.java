package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.scm.helpers.orderinterceptor.TrackingHelper;
import com.lenskart.scm.model.ScmOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class CsAwaitedRTOHelper extends CsBaseHelper implements ExecutionHelper {


    OrderContext orderContext;
    JSONObject payload;
    Response response;
    String incrementID;
    String trackingNo;
    String carrier;
    String unicomOrderCode;
    ScmOrderContext scmOrderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        TrackingHelper.builder()
                .scmOrderContext(scmOrderContext)
                .trackingNo(trackingNo)
                .carrier(carrier)
                .incrementID(incrementID)
                .unicomOrderCode(unicomOrderCode)
                .build()
                .test();

        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}
