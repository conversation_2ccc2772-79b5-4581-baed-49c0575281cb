package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.cs.endpoints.CsEndpoints.*;

@SuperBuilder
@Slf4j
public class PinCodeEligibilityHelper  extends CsBaseHelper implements ServiceHelper {
    String payload;
    OrderContext orderContext;
    Response response;
    CsOrderContext.ShippingEstimate csOrderContextShippingEstimate;

    @Override
    public ServiceHelper init() {
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.getPincodeEligibility(csOrderContextShippingEstimate));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PINCODE_ELIGIBILITY.getUrl(),null,payload,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
