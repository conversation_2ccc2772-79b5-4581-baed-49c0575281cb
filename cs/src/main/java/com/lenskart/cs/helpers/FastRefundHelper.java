package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.cs.endpoints.CsEndpoints.PROCESS_FAST_REFUND;

@SuperBuilder
public class FastRefundHelper extends CsBaseHelper implements ServiceHelper {

    String payload;
    OrderContext orderContext;
    CsOrderContext csOrderContext;
    NexsOrderContext nexsOrderContext;

    Response response;

    @Override
    public ServiceHelper init() {
        payload = CsRequestBuilders.getProcessFastRefundRequest(orderContext,nexsOrderContext,csOrderContext);
        headers = getHeaders(payload);
        return this;
    }

    @Override
    public ServiceHelper process() {
        /* Initiate Refund */
        response = RestUtils.post(PROCESS_FAST_REFUND.getUrl(), headers, payload, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
