package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnOrderRequestDTO;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;


import static com.lenskart.cs.endpoints.CsEndpoints.CREATE_RETURN_DIRECT_RECEIVED;
@SuperBuilder
public class ReturnDirectedReceivedHelper extends CsBaseHelper implements ServiceHelper {

    private ReturnOrderRequestDTO request;
    private OrderContext orderContext;
    private CsOrderContext csOrderContext;
    private ReturnResponse returnResponse;
    private Response response;
    private String payload;
    private NexsOrderContext nexsOrderContext;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext, csOrderContext);
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.createPayloadForReturnItems(orderContext,csOrderContext,returnResponse,nexsOrderContext));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(CREATE_RETURN_DIRECT_RECEIVED.getUrl(), headers, payload, statusCode);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}

