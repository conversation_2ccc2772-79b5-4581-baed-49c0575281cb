package com.lenskart.cs.validators;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.OrderState;
import com.lenskart.cs.database.TrackingMiddlewareDBUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.TrackingRawScanWrapperResponse;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.trackingmiddleware.models.response.TrackingRawScanLogsRespone;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;

@Slf4j
@Builder
public class PFUStatusValidator implements IValidator {
    private OrderContext orderContext;
    private CsOrderContext csOrderContext;
    private final String status;


    @Override
    public void validateNode() {

    }

    @Override
    public void validateDBEntities() {
        List<Map<String, Object>> orderStatus = ScmDbUtils.getOrderStatus(String.valueOf(orderContext.getOrderId()), orderContext.getProductLists().getFirst().getProductId());
        for (Map<String, Object> map : orderStatus) {
            Assert.assertEquals(status.toString(), map.get("shipment_status"));
        }
    }
}
