package com.lenskart.cs.validators;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.TestType;
import com.lenskart.cs.model.CancellationInvoiceResponse;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.scm.database.ScmDbUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;


@Builder
@Slf4j
public class ItemStatusReceivingValidator implements IValidator {

    boolean itemReceivingStatus;

    @Override
    public void validateNode() {
        Assert.assertTrue(itemReceivingStatus);
    }

    @Override
    public void validateDBEntities() {

    }
}
