package com.lenskart.cs.validators;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.TestType;
import com.lenskart.cs.model.CancellationInvoiceResponse;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.scm.database.ScmDbUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;


@Builder
@Slf4j
public class CancellationValidator implements IValidator {
    CsOrderContext csOrderContext;
    CancellationInvoiceResponse cancellationInvoiceResponse;
    OrderContext orderContext;
    @Override
    public void validateNode() {
        if (csOrderContext.getTestType().equals(TestType.NEGATIVE))
        {
            Assert.assertEquals(cancellationInvoiceResponse.getMessage(), csOrderContext.getMessage());
            Assert.assertEquals(cancellationInvoiceResponse.getCode(), csOrderContext.getCode());
        }
    }

    @Override
    public void validateDBEntities() {
        List<Map<String, Object>> orderStatus = ScmDbUtils.getOrderStatus(String.valueOf(orderContext.getOrderId()), csOrderContext.getProductIDToBeCancelled());
        for (Map<String, Object> map : orderStatus) {
            Assert.assertEquals(map.get("shipment_status"), csOrderContext.getCancelledOrderShipmentStatus());
        }
    }
}
