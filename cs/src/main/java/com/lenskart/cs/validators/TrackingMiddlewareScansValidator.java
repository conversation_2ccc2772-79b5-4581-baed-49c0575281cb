package com.lenskart.cs.validators;

import com.lenskart.commons.base.IValidator;
import com.lenskart.cs.database.TrackingMiddlewareDBUtils;
import com.lenskart.cs.model.TrackingRawScanWrapperResponse;
import com.lenskart.trackingmiddleware.models.response.TrackingRawScanLogsRespone;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;

@Slf4j
@Builder
public class TrackingMiddlewareScansValidator implements IValidator {

    private final TrackingRawScanWrapperResponse response;
    private TrackingRawScanLogsRespone data;

    /**
     * ✅ API Response Validation
     *
     * 🎯 Purpose:
     * This method validates the response received after calling the
     * `/middleware/push-scans` API. This API is responsible for accepting
     * tracking scan events (e.g., Picked Up, In Transit, Delivered, etc.)
     * and returning a wrapper response containing metadata and scan ID.
     *
     * 🧪 What It Validates:
     * - Response success flag is `true`
     * - Data section is present and not null
     * - Logs key details like message and scan ID for traceability
     *
     * 🛠 Why It's Important:
     * Ensures the API executed successfully and the server returned a proper
     * payload for further DB validation.
     */
    @Override
    public void validateNode() {
        data = response.getData();
        Assert.assertTrue(response.isSuccess(), "❌ Response success flag is false");
        Assert.assertNotNull(data, "❌ Data section is null in response");

        log.info("✅ Middleware scan validation passed. Message: {}, Scan ID: {}",
                data.getMessage(), data.getTrackingRawScanLogId());
    }

    /**
     * ✅ Database Validation
     *
     * 🎯 Purpose:
     * Verifies whether the scan pushed via `/middleware/push-scans` API
     * was successfully persisted in the backend database (typically
     * in the `tracking_raw_scan_logs` table).
     *
     * 🧪 What It Validates:
     * - A row exists in the DB for the scan ID returned by the API
     * - The status of the scan is `1` (usually indicates 'successful' or 'valid')
     *
     * 🛠 Why It's Important:
     * Validates backend data integrity. Just because the API returns success
     * doesn’t mean the data is saved properly in DB.
     * This step ensures that the middleware pipeline is working end-to-end.
     */
    @Override
    public void validateDBEntities() {
        if (data == null || data.getTrackingRawScanLogId() == null) {
            throw new RuntimeException("❌ trackingRawScanLogId is missing in response");
        }

        int scanId = data.getTrackingRawScanLogId();

        List<Map<String, Object>> resultList = (List<Map<String, Object>>)
                TrackingMiddlewareDBUtils.getEventStatusFromTrackingRawScanLogs(scanId);

        if (resultList == null || resultList.isEmpty()) {
            throw new RuntimeException("❌ No DB entry found for scan ID: " + scanId);
        }

        int status = Integer.parseInt(resultList.get(0).get("status").toString());

        log.info("✅ DB status for scan ID {} is: {}", scanId, status);
        Assert.assertEquals(status, 1, "❌ DB status is not 1 for scan ID: " + scanId);
    }
}
