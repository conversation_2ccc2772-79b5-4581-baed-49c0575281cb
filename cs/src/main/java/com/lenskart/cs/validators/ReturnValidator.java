package com.lenskart.cs.validators;

import com.lenskart.commons.base.IValidator;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.cs.database.ReturnDbUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.Map;


@Builder
@Slf4j
public class ReturnValidator implements IValidator {
    CsOrderContext csOrderContext;
    ReturnResponse returnResponse;
    OrderContext orderContext;

    @Override
    public void validateNode() {
        // Add validation logic for PickingCompletionHelper
    }

    @Override
    public void validateDBEntities() {
        Map<String, Object> returnDetails=ReturnDbUtils.getReturnDetails(returnResponse,orderContext,csOrderContext);
        Assert.assertEquals(returnDetails.get("increment_id"),returnResponse.getReversePickUpDetails().getIncrementId());
    }
}
