package com.lenskart.cs.validators;

import com.lenskart.commons.base.IValidator;
import com.lenskart.cs.database.ReturnDbUtils;
import com.lenskart.cs.database.ShippingDbUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnResponse;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.Map;


@Builder
@Slf4j
public class ShippingEstimateValidator implements IValidator {
    CsOrderContext.ShippingEstimate csOrderContext;
    @Override
    public void validateNode() {

    }

    @Override
    public void validateDBEntities() {
        Map<String, Object> shippingDetails= ShippingDbUtils.getPincodeLevelDeliveryTatDetails(csOrderContext);
    }
}
