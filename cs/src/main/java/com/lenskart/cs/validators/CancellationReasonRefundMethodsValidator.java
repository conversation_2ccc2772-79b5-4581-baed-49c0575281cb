package com.lenskart.cs.validators;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CancellationReasonRefundMethodsResponse;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.scm.database.ScmDbUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;


@Builder
@Slf4j
public class CancellationReasonRefundMethodsValidator implements IValidator {
    CsOrderContext csOrderContext;
    CancellationReasonRefundMethodsResponse cancellationReasonRefundMethodsResponse;
    OrderContext orderContext;

    @Override
    public void validateNode() {
        Assert.assertEquals(cancellationReasonRefundMethodsResponse.isCancellable(),csOrderContext.isCancellable());
    }

    @Override
    public void validateDBEntities() {

    }
}
