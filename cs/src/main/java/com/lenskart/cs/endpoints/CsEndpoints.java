package com.lenskart.cs.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum CsEndpoints implements BaseEndpoint {

    AUTH_LOGIN("/auth/login", "reverseService"),
    ITEM_SEARCH_RTO("/item-search/rto", "reverseService"),
    RTO_RECEIVING("/receiving/rto-receiving", "reverseService"),
    SCM_TRACKING_ACTION("/test/produce/scm_tracking_action", "reverseService"),
    RETURN("/return", "orderOpsService"),
    REVERSE_PICKUP_INFO_UPDATE("/return/reverse-pickup-info/update", "orderOpsService"),
    INITIATE_REFUND("/v1.0/initiate-exchange-order-refund", "refundService"),
    RETURN_ITEMS("/reverse/return-items", "orderOpsService"),
    ITEM_SEARCH_OFFLINE_PRODUCT("/item-search/offline-product/{$productId}", "reverseService"),
    CANCEL_INVOICE("/{$orderID}/cancel-invoice", "orderOpsService"),
    SHIPPING_ESTIMATE("/estimate", "shippingService"),
    ITEM_SEARCH_BY_ORDER_AND_FACILITY("/item-search/{$orderId}/{$facilityCode}", "reverseService"),
    ITEM_SEARCH_BY_GATEPASSID_AND_FACILITY("/item-search/gatepass-item-page/{$gatePass-id}/{$facility}", "reverseService"),
    PINCODE_ELIGIBILITY("/v1/pincode/eligibility","shippingService"),
    DELIVERY_ETA("/v1/delivery-eta", "shippingService"),
    LOCAL_FITTING_GATE_PASS_RECEIVING("/receiving/gatepass/local-fitting", "reverseService"),
    ITEM_RESOLUTION_RECEIVING("/receiving/item-resolution", "reverseService"),
    CANCELLATION_REASON_REFUND_METHODS("/cancel-reasons-refund-methods/{$orderID}/{$client}", "orderOpsService"),
    EMPTY_BOX_RECEIVING("/receiving/empty-box-received", "reverseService"),
    FETCH_RECEIVING_REASON("/reason/receiving/fetch", "reverseService"),
    GATE_PASS_CLOSURE("/gatePassClosure/checkState", "reverseService"),
    GATE_PASS_CLOSUREOFFLINE("/gatePassClosure/offline/", "reverseService"),
    TRACKING_NUMBER_VALIDATOR("/trackingNumber/validate/{$id}/{$trackingNumber}/{$userId}/{$returnType}/{$facilityCode}", "reverseService"),
    REASON_RECEIVING_SAVE("/reason/receiving/save", "reverseService"),
    OPEN_PUTAWAYS("/putaway/receiving/open-putaways/{$userId}", "reverseService"),
    ITEM_STATUS_RECEIVING("/status/receiving/{$uwItemId}/{$barcode}", "reverseService"),
    ITEM_STATUS_RECEIVED("/status/received/{$uwItemId}/{$barcode}", "reverseService"),
    METHOD_WISE_REFUND_DETAILS("get-method-wise-refund-details", "refundService"),
    RTO_PUTAWAY("/putaway/rto-receiving/open-putaways/{$userId}", "reverseService"),
    RETURN_DETAILS_V2("/return/details/v2.0/get", "returnService"),
    RECEIVING_QC_ACTION("/qc/action", "reverseService"),
    CREATE_RETURN_DIRECT_RECEIVED("/return/v1.0/create-return/direct-received", "returnService"),
    RETURN_REFUND_AWAITED_RTO("/return/v1.0/awaited-rto/return-refund", "returnService"),
    UPDATE_RETURN_REFUND_METHOD("/return/v1.0/update/order-status-refund", "returnService"),
    PROCESS_FAST_REFUND("/v1.0/process-fast-refund", "refundService"),
    MIDDLEWARE_PUSH_SCANS("/pushScan/sendData", "trackingMiddlewareService"),
    MIDDLEWARE_FUSION_TRACKING("/fusion-tracking", "trackingMiddlewareConsumerUI"),
    CRM_INITIATE_CANCELLATION("/sprinklr/order/initiate-cancellation","crmService"),
    CRM_RETURN_REFUND_MAPPING("/v1/orders/{$order_id}/return-refund-mapping","crmService"),
    CRM_STORE_LOCATOR("/pfu/store/customer/nearest-store","crmService"),
    CRM_SEARCH_APPOINTMENT("/pfu/store/search-appointment-slots","crmService"),
    CRM_PUSH_PRESCRIPTION_ORDERS("/pfu/push-prescription/orders/{$order_id}/items/{$item_id}","crmService"),
    RECEIVING_CREATE_PUTAWAY_LIST_PENDING("/item-search/returns/create-putaway-list-pending/{$status}/{$facility}","reverseService");
    private final String endpoint;
    private final String serviceName;

    CsEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    @Override
    public String getUrl() {
        return CsEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return CsEndpointManager.getEndpointUrl(this, pathParams);
    }

}
