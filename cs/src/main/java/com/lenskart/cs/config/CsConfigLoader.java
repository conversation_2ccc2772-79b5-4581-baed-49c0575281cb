package com.lenskart.cs.config;

import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility class to load Juno configurations from YAML files
 */
@Slf4j
public class CsConfigLoader {
    // Configuration cache
    private static final Map<String, CsConfig> configCache = new ConcurrentHashMap<>();

    // Default values
    private static final String DEFAULT_CONFIG_PATH = "cs.yml";
    private static final String DEFAULT_ENVIRONMENT = "preprod";

    /**
     * Loads the Juno configuration from the default YAML file
     *
     * @return JunoConfig object with configuration details
     */
    public static CsConfig loadConfig() {
        return loadConfig(DEFAULT_CONFIG_PATH);
    }

    /**
     * Loads the Juno configuration from a specified YAML file
     *
     * @param configPath Path to the YAML file
     * @return JunoConfig object with configuration details
     */
    public static CsConfig loadConfig(String configPath) {
        // Return cached config if available
        if (configCache.containsKey(configPath)) {
            return configCache.get(configPath);
        }

        try {
            Yaml yaml = new Yaml();

            // Try to load from classpath first
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(configPath);

            // If not found in classpath, try as a file path
            if (inputStream == null) {
                try {
                    inputStream = new FileInputStream(configPath);
                } catch (IOException e) {
                    log.warn("Could not find configuration file: {}", configPath);
                    CsConfig defaultConfig = createDefaultConfig();
                    configCache.put(configPath, defaultConfig);
                    return defaultConfig;
                }
            }

            // Load the YAML file as a map
            Map<String, Object> yamlMap = yaml.load(inputStream);
            inputStream.close();

            if (yamlMap == null || yamlMap.isEmpty()) {
                log.warn("Empty or invalid configuration file: {}", configPath);
                CsConfig defaultConfig = createDefaultConfig();
                configCache.put(configPath, defaultConfig);
                return defaultConfig;
            }

            // Create a new CseConfig object
            CsConfig config = new CsConfig();

            // Process each environment in the YAML file
            for (Map.Entry<String, Object> entry : yamlMap.entrySet()) {
                String envName = entry.getKey();

                if (entry.getValue() instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> envConfig = (Map<String, Object>) entry.getValue();

                    // Create a new environment configuration
                    CsConfig.EnvironmentConfig environmentConfig = new CsConfig.EnvironmentConfig();

                    // Process base URLs if available
                    if (envConfig.containsKey("baseUrls") && envConfig.get("baseUrls") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, String> baseUrls = (Map<String, String>) envConfig.get("baseUrls");
                        environmentConfig.setBaseUrls(baseUrls);
                    }

                    // Process auth configuration if available
                    if (envConfig.containsKey("refunds") && envConfig.get("refunds") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, String> refunds = (Map<String, String>) envConfig.get("refunds");
                        environmentConfig.setRefunds(refunds);
                        log.info("Loaded auth configuration: {}", refunds);
                    }

                    // Process auth configuration if available
                    if (envConfig.containsKey("trackingMiddleware") && envConfig.get("trackingMiddleware") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, String> trackingMiddleware = (Map<String, String>) envConfig.get("trackingMiddleware");
                        environmentConfig.setTrackingMiddleware(trackingMiddleware);
                        log.info("Loaded auth configuration: {}", trackingMiddleware);
                    }

                    // Add the environment configuration to the CseConfig
                    config.getEnvironments().put(envName, environmentConfig);
                }
            }

            // Cache the config
            configCache.put(configPath, config);

            log.info("Loaded Cse configuration from: {}", configPath);
            return config;

        } catch (Exception e) {
            log.error("Failed to load Cse configuration: {}", e.getMessage(), e);
            CsConfig defaultConfig = createDefaultConfig();
            configCache.put(configPath, defaultConfig);
            return defaultConfig;
        }
    }

    /**
     * Creates a default Cse configuration
     *
     * @return Default CseConfig
     */
    private static CsConfig createDefaultConfig() {
        CsConfig config = new CsConfig();

        // Create default environment configuration
        CsConfig.EnvironmentConfig environmentConfig = new CsConfig.EnvironmentConfig();

        // Add default base URLs
        Map<String, String> baseUrls = new HashMap<>();
        baseUrls.put("sessionService", "https://api-gateway.cse.preprod.lenskart.com");
        baseUrls.put("testService", "https://jsonplaceholder.typicode.com");
        environmentConfig.setBaseUrls(baseUrls);

        // Add the environment configuration to the CseConfig
        config.getEnvironments().put(DEFAULT_ENVIRONMENT, environmentConfig);

        return config;
    }

    /**
     * Clears the configuration cache
     */
    public static void clearCache() {
        configCache.clear();
        log.info("Configuration cache cleared");
    }

    /**
     * Get the default environment name
     *
     * @return Default environment name
     */
    public static String getDefaultEnvironment() {
        return DEFAULT_ENVIRONMENT;
    }

    /**
     * Get the default configuration path
     *
     * @return Default configuration path
     */
    public static String getDefaultConfigPath() {
        return DEFAULT_CONFIG_PATH;
    }
}
