package com.lenskart.cs.requestbuilders;

import com.google.gson.Gson;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.MiddlewareCouriers;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.cs.helpers.ReturnHelper;
import com.lenskart.cs.model.*;
import com.lenskart.cs.model.ItemSearchRtoResponse;
import com.lenskart.cs.model.RTOItemReceivingRequest;
import com.lenskart.cs.model.ReturnOrderRequestDTO;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.refundcommon.model.dto.RefundAmount;
import com.lenskart.refundcommon.model.enums.IdentifierType;
import com.lenskart.refundcommon.model.request.ProcessFastRefundRequest;
import com.lenskart.returncommon.model.dto.DualRefundRequest;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.lenskart.commons.model.MiddlewareCouriers.*;
import static com.lenskart.refundcommon.model.enums.RefundReason.FAST_REFUND;

public class CsRequestBuilders {

    public static RTOItemReceivingRequest getRTOItemReceivingRequest(OrderContext orderContext, ItemSearchRtoResponse itemSearchResponse, CsOrderContext csOrderContext) {
        return RTOItemReceivingRequest.builder()
                .facility(getProduct(orderContext, csOrderContext.getProductIDToBeReturned()).getFacilityCode())
                .identifierId("shippingId")
                .identifierType(getProduct(orderContext, csOrderContext.getProductIDToBeReturned()).getShippingPackageId())
                .itemDetails(itemSearchResponse.getItemDetailsList())
                .returnType("shippingId")
                .shipmentId(getProduct(orderContext, csOrderContext.getProductIDToBeReturned()).getShippingPackageId())
                .userId("17722")
                .build();
    }

    public static OrderContext.ProductList getProduct(OrderContext orderContext, String productID) {
        List<OrderContext.ProductList> productList = orderContext.getProductLists();
        for (OrderContext.ProductList product : productList) {
            if (productID.equals(product.getProductId())) {
                return product;
            }
        }
        return null;
    }

    private static ReturnPickupAddress getReturnPickUpAddress() {

        return ReturnPickupAddress.builder()
                .city("NEW DELHI")
                .email("<EMAIL>")
                .state("DELHI")
                .country("India")
                .pincode(110059)
                .first_name("MEENU")
                .last_name("MEENU")
                .street_1("A1/157,2ND FLOOR,FRONT SIDE")
                .telephone("+91-**********")
                .street_2("UTTAM NAGAR,NEW DELHI").build();
    }


    private static List<ReturnItemReasonDetails> getReturnReasonDetailsList() {
        ReturnItemReasonDetails reasonDetails = ReturnItemReasonDetails.builder()
                .primary_reason_id(2003)
                .type("RETURN")
                .additional_comments("Return")
                .secondary_reason_id(211)
                .build();
        return Collections.singletonList(reasonDetails);
    }

    private static List<ReturnItemDetails> getReturnItemDetails(OrderContext orderContext, CsOrderContext csOrderContext) {
        ReturnItemDetails returnItemDetails = ReturnItemDetails.builder()
                .reasons(getReturnReasonDetailsList())
                .magento_item_id(getProduct(orderContext, csOrderContext.getProductIDToBeReturned()).getItemId())
                .uw_item_id(null)
                .claim_insurance(false)
                .do_refund(csOrderContext.isDoRefund())
                .need_approval(csOrderContext.isNeedApproval())
                .qc_status("Pass")
                .refund_method(csOrderContext.getRefundMethod().toString())
                .refund_method_request(csOrderContext.getRefundMethodRequest())
                .build();
        return Collections.singletonList(returnItemDetails);
    }

    public static ReturnRequest getReturnRequestDetails(OrderContext orderContext, CsOrderContext csOrderContext) {
        return ReturnRequest.builder()
                .items(getReturnItemDetails(orderContext, csOrderContext))
                .exchangeAddress(null)
                .pickupAddress(getReturnPickUpAddress())
                .storeEmail(null)
                .isCourierReassigned(null)
                .storeFacilityCode(null)
                .salesmanNumber("**********")
                .salesmanName("Ankit Singh")
                .returnMethod("RPU")
                .returnSource(csOrderContext.getSource())
                .incrementId(null)
                .newCourier(null)
                .oldCourier(null)
                .initiatedBy(null)
                .facilityCode(getProduct(orderContext, csOrderContext.getProductIDToBeReturned()).getFacilityCode())
                .callbackRequiredToSalesman(false)
                .productIdsMap(null)
                .entity(null)
                .build();

    }

    public static ReturnCancellationRequest getReturnCancellationRequestDetails(CsOrderContext csOrderContext, ReturnHelper returnHelper) {
        return ReturnCancellationRequest.builder()
                .returnId(returnHelper.returnResponse.getResult().getReturns().getFirst().getReturnId())
                .status(csOrderContext.getReturnStatus())
                .comments(csOrderContext.getComment())
                .build();

    }

    public static ReturnIReversePickInfoRequest getReturnReversePickInfoRequest(ReturnResponse returnResponse) {
        return ReturnIReversePickInfoRequest.builder()
                .awb(returnResponse.getReversePickUpDetails().getAwbNumber())
                .courier(returnResponse.getReversePickUpDetails().getCourier())
                .pickupId(returnResponse.getResult().getGroup_id())
                .referenceId(String.valueOf(returnResponse.getReversePickUpDetails().getIncrementId()) + returnResponse.getResult().getGroup_id())
                .userId(0)
                .userName("Ankit")
                .build();
    }

    public static String createPayloadForRefund(int orderId) {
        JSONObject payload = new JSONObject();
        payload.put("exchangeOrderId", 0);
        payload.put("masterOrderId", orderId);
        return payload.toString();
    }


    public static ReturnOrderRequestDTO createPayloadForReturnItems(OrderContext orderContext, CsOrderContext csOrderContext, ReturnResponse returnResponse, NexsOrderContext nexsOrderContext) {

        ReturnOrderRequestDTO returnOrderRequestDTO = ReturnOrderRequestDTO.builder()
                .incrementId(orderContext.getOrderId())
                .items(getReturnItemRequestDetails(returnResponse, orderContext, csOrderContext))
                .source("web")
                .referenceOrderCode(String.valueOf(nexsOrderContext.getUnicomOrderCode()))
                .doRefund(true)
                .isDualCo(false)
                .facility(getProduct(orderContext, csOrderContext.getProductIDToBeReturned()).getFacilityCode())
                .raiseRPUatUnicom(false)
                .raiseRPUatNexs(true)
                .facility(getProduct(orderContext, csOrderContext.getProductIDToBeReturned()).getFacilityCode())
                .rtoItem(false)
                .awaitedRtoItem(false)
                .groupId(returnResponse.getResult().getGroup_id())
                .reasonDetail("Customer Request")
                .refundMethod("Credit Card")
                .shippingPackageId(getProduct(orderContext, csOrderContext.getProductIDToBeReturned()).getShippingPackageId())
                .uwOrderDTOs(List.of())
                .orderDTOs(List.of())
                .ordersHeaderDTO(null)
                .userId("user123")
                .build();
        return returnOrderRequestDTO;
    }

    public static CancellationRequest getCancellationRequest(CsOrderContext csOrderContext, OrderContext orderContext) {
        return CancellationRequest.builder()
                .initiatedBy(csOrderContext.getCancelledBy())
                .source(csOrderContext.getCancellationSource())
                .paymentMethod(csOrderContext.getRefundMethod().toString())
                .reasonDetail(csOrderContext.getCancellationReason())
                .reasonID(csOrderContext.getCancellationReasonID())
                .cancellationType(csOrderContext.getCancellationType().getValue())
                .magentoItemID(getProduct(orderContext, csOrderContext.getProductIDToBeCancelled()).getItemId())
                .build();
    }

    public static PinCodeEligibility getPincodeEligibility(CsOrderContext.ShippingEstimate csOrderContextShippingEstimate) {
        return PinCodeEligibility.builder()
                .pincode(String.valueOf(csOrderContextShippingEstimate.getPincode()))
                .source(csOrderContextShippingEstimate.getSipping_facility_source())
                .promise_type(csOrderContextShippingEstimate.getPromiseType())
                .country_code(csOrderContextShippingEstimate.getShipping_country())
                .facility_code(csOrderContextShippingEstimate.getSource_facility())
                .build();
    }

    public static DeliveryETAModel getDeliveryEtaModel(CsOrderContext.ShippingEstimate csOrderContextShippingEstimate) {
        return DeliveryETAModel.builder()
                .pincodeModel(new DeliveryETAModel.PincodeModel(csOrderContextShippingEstimate.getPincode()))
                .dispatchDays(csOrderContextShippingEstimate.getDispatchDays())
                .platformModel(new DeliveryETAModel.PlatformModel(csOrderContextShippingEstimate.getPlatform()))
                .sourceFacility(csOrderContextShippingEstimate.getSource_facility())
                .destinationCountry(csOrderContextShippingEstimate.getShipping_country())
                .shipToStore(csOrderContextShippingEstimate.getShip_to_store())
                .build();
    }

    public static LocalFittingGatepassRequest localFittingGatepassRequest(CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass, OrderContext orderContext, CsOrderContext csOrderContext) {
        return LocalFittingGatepassRequest.builder()
                .gatepassNo(csOrderContext.getGatepassNo())
                .incrementId(orderContext.getOrderId())
                .gatepassItemId(csOrderContextReceivingGatePass.getGatepassItemId())
                .uwItemId(csOrderContext.getUwItemId())
                .facilityCode(getProduct(orderContext, csOrderContextReceivingGatePass.getProductIDNeedsToBeReceived()).getFacilityCode())
                .build();
    }


    public static ItemResolution itemResolutionModel(OrderContext orderContext, CsOrderContext csOrderContext, NexsOrderContext nexsOrderContext, ItemSearchOnlineResponse itemSearchResponse, AuthenticationResponse authenticationResponse) {
        ItemDetails itemDetails = itemSearchResponse.getItemDetailsList().getFirst();
        if (csOrderContext.isNotReceivedYN()) {
            itemDetails.setNotReceivedYN(true);
        }
        itemDetails.setQcStatus(csOrderContext.getQcStatus());
        return ItemResolution.builder()
                .facility(nexsOrderContext.getFacilityCode())
                .gatePassCode(String.valueOf(orderContext.getOrderId()))
                .identifierType(csOrderContext.getIdentifierType())
                .identifierId(String.valueOf(orderContext.getOrderId()))
                .psuedoGatepass(csOrderContext.getPsuedoGatepass())
                .returnType(csOrderContext.getReturnType())
                .rtoItems(csOrderContext.getRtoItems())
                .shipmentId(nexsOrderContext.getShippingId())
                .productBadRecall(csOrderContext.isProductBadRecall())
                .qcFailParams(getQCFailParams(csOrderContext))
                .userId(authenticationResponse.getUserId())
                .itemDetails(itemDetails)
                .build();
    }

    public static EmptyBoxReceivingRequest emptyBoxReceiving(OrderContext orderContext, CsOrderContext csOrderContext, NexsOrderContext nexsOrderContext, ItemSearchOnlineResponse itemSearchResponse) {
        return EmptyBoxReceivingRequest.builder()
                .returnType(csOrderContext.getReturnType())
                .itemDetailsList(itemSearchResponse.getItemDetailsList())
                .build();
    }

    public static GatepassClosureOfflineModel gatepassClosureofflineModel(CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass) {
        return GatepassClosureOfflineModel.builder()
                .userName(csOrderContextReceivingGatePass.getUserName())
                .gatepassId(csOrderContextReceivingGatePass.getGatepassId())
                .build();
    }

    public static ReceivingQCActionModule getQCActionModule(CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass, OrderContext orderContext, CsOrderContext csOrderContext) {
        return ReceivingQCActionModule.builder()
                .barcode(csOrderContextReceivingGatePass.getBarcode())
                .facilityCode(getProduct(orderContext, csOrderContextReceivingGatePass.getProductIDNeedsToBeReceived()).getFacilityCode())
                .gatepassItemId(csOrderContextReceivingGatePass.getGatepassItemId())
                .gatepassNo(csOrderContextReceivingGatePass.getGatepassId())
                .incrementId(orderContext.getOrderId())
                .productId(Integer.parseInt(csOrderContextReceivingGatePass.getProductIDNeedsToBeReceived()))
                .role(csOrderContextReceivingGatePass.getRole())
                .userName(csOrderContextReceivingGatePass.getUserName())
                .uwItemId(csOrderContext.getUwItemId())
                .build();
    }

    public static ReasonReceivingRequest getReasonReceivingRequest(CsOrderContext csOrderContext) {
        return ReasonReceivingRequest.builder()
                .barcode(csOrderContext.getBarcode())
                .comment(csOrderContext.getComment())
                .createdAt(csOrderContext.getCreatedAt())
                .gatepassItemId(csOrderContext.getId())
                .id(csOrderContext.getId())
                .primaryReason(csOrderContext.getPrimaryReason())
                .secondaryReason(csOrderContext.getSecondaryReason())
                .source(csOrderContext.getSource())
                .uwItemId(csOrderContext.getUwItemId())
                .build();
    }


    public static GetMethodWiseRefundDetailsModel getMethodWiseRefundDetailsModel(CsOrderContext.GetMethodWiseRefundDetails getMethodWiseRefundDetails) {
        return GetMethodWiseRefundDetailsModel.builder()
                .orderId(getMethodWiseRefundDetails.getOrderId())
                .identifierType(getMethodWiseRefundDetails.getIdentifierType())
                .identifierValues(getMethodWiseRefundDetails.getIdentifierValues())
                .build();

    }

    public static ReturnDetailsRequest getReturnDetailsRequest(CsOrderContext csOrderContext) {
        return ReturnDetailsRequest.builder()
                .identifierType("UW_ITEM_ID")
                .identifierValues(List.of(String.valueOf(csOrderContext.getUwItemId())))
                .build();
    }

    public static DualRefundRequest updateReturnRefundRequest(CsOrderContext csOrderContext) {
        DualRefundRequest dualRefundRequest = new DualRefundRequest();
        dualRefundRequest.setIncrementId(Long.valueOf(csOrderContext.getReceivingGatePass().getIncrementId()));
        dualRefundRequest.setRefundMethod(csOrderContext.getRefundMethod().toString());
        dualRefundRequest.setSource(csOrderContext.getSource());
        dualRefundRequest.setStatus(csOrderContext.getRefundStatus());
        dualRefundRequest.setReturnId(Long.valueOf(csOrderContext.getReturnId()));
        dualRefundRequest.setTransactionType(csOrderContext.getTransactionType());
        return dualRefundRequest;
    }

    private static List<ReturnItemRequestDTO> getReturnItemRequestDetails(ReturnResponse returnResponse, OrderContext orderContext, CsOrderContext csOrderContext) {
        return Collections.singletonList(ReturnItemRequestDTO.builder()
                .itemId(getUWItemID(returnResponse, orderContext, csOrderContext))
                .qcStatus(csOrderContext.getReceivingGatePass().getQcStatus()).build());
    }

    public static String getReturnID(ReturnResponse returnResponse, OrderContext orderContext, CsOrderContext csOrderContext) {
        for (Returns item : returnResponse.getResult().getReturns()) {
            if (item.getMagentoItemId() == CsRequestBuilders.getProduct(orderContext, csOrderContext.getProductIDToBeReturned()).getItemId()) {
                return String.valueOf(item.getReturnId());
            }
        }
        return "";
    }

    public static int getUWItemID(ReturnResponse returnResponse, OrderContext orderContext, CsOrderContext csOrderContext) {
        for (Returns item : returnResponse.getResult().getReturns()) {
            if (item.getMagentoItemId() == CsRequestBuilders.getProduct(orderContext, csOrderContext.getProductIDToBeReturned()).getItemId()) {
                return item.getUwItemId();
            }
        }
        return 0;
    }

    public static String getProcessFastRefundRequest(OrderContext orderContext, NexsOrderContext nexsOrderContext, CsOrderContext csOrderContext) {
        List<ProcessFastRefundRequest.RefundReq> refundReqList = new ArrayList<>();
        ProcessFastRefundRequest.RefundReq refundReq = new ProcessFastRefundRequest.RefundReq();
        refundReq.setIdentifierType(IdentifierType.UW_ITEM_ID);
        refundReq.setIdentifierValue(nexsOrderContext.getUwItemId());
        refundReq.setRefundAmount(RefundAmount.rupees(orderContext.getFinalOrderAmount()));
        refundReq.setRefundReason(FAST_REFUND);
        refundReqList.add(refundReq);
        ProcessFastRefundRequest processFastRefundRequest = new ProcessFastRefundRequest();
        processFastRefundRequest.setOrderId((long) orderContext.getOrderId());
        processFastRefundRequest.setRefundReq(refundReqList);
        processFastRefundRequest.setRefundMethod(csOrderContext.getRefundMethod().toString());
        processFastRefundRequest.setRemarks(csOrderContext.getRemarks());
        processFastRefundRequest.setClient(csOrderContext.getClient());
        processFastRefundRequest.setUserId(csOrderContext.getFastRefunduserId());
        processFastRefundRequest.setRefundTriggerPoint(csOrderContext.getRefundIntiation());
        return JsonUtils.convertObjectToJsonString(processFastRefundRequest);
    }

    public static ItemResolution.QcFailParams getQCFailParams(CsOrderContext csOrderContext) {

        if (csOrderContext.getQcFailParams() == null) {
            return new ItemResolution.QcFailParams();
        } else {
            return ItemResolution.QcFailParams.builder()
                    .primaryReason(csOrderContext.getPrimaryReason())
                    .secondaryReason(csOrderContext.getSecondaryReason())
                    .imageList(new ArrayList<>())
                    .build();
        }
    }

    public static List<String> getPutawayPendingListRequest(String putawayNo) {
        return List.of(putawayNo);

    }

    public static PurpleDroneRequest getPurpleDroneScanRequest(CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails, String awbNo) {
        return PurpleDroneRequest.builder()
                .courierComment(trackingMiddlewareDetails.getCourierComment())
                .statusTime(trackingMiddlewareDetails.getStatusTime())
                .waybillNo(awbNo)
                .userId(trackingMiddlewareDetails.getUserId())
                .estimatedDeliveryDate(trackingMiddlewareDetails.getEstimatedDeliveryDate())
                .statusDate(trackingMiddlewareDetails.getStatusDate())
                .password(trackingMiddlewareDetails.getPassword())
                .currentLocation(trackingMiddlewareDetails.getCurrentLocation())
                .shipmentMode(trackingMiddlewareDetails.getShipmentMode())
                .currentCity(trackingMiddlewareDetails.getCurrentCity())
                .currentState(trackingMiddlewareDetails.getCurrentState())
                .statusCode(trackingMiddlewareDetails.getStatusCode())
                .comments(trackingMiddlewareDetails.getComments())
                .build();
    }


    public static BluedartMiddlewareRequest getBlueDartScanRequest(CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        // Build ScanDetail
        BluedartMiddlewareRequest.ScanDetail scanDetail =
                BluedartMiddlewareRequest.ScanDetail.builder()
                        .scan(trackingMiddlewareDetails.getScan())
                        .scanCode(trackingMiddlewareDetails.getScanCode())
                        .scanGroupType(trackingMiddlewareDetails.getScanGroupType())
                        .scanDate(trackingMiddlewareDetails.getScanDate())
                        .scanTime(trackingMiddlewareDetails.getScanTime())
                        .scannedLocation(trackingMiddlewareDetails.getScannedLocation())
                        .scanType(trackingMiddlewareDetails.getScanType())
                        .comments(trackingMiddlewareDetails.getComments())
                        .scannedLocationCode(trackingMiddlewareDetails.getScannedLocationCode())
                        .scannedLocationCity(trackingMiddlewareDetails.getScannedLocationCity())
                        .scannedLocationStateCode(trackingMiddlewareDetails.getScannedLocationStateCode())
                        .statusTimeZone(trackingMiddlewareDetails.getStatusTimeZone())
                        .statusLatitude(trackingMiddlewareDetails.getStatusLatitude())
                        .statusLongitude(trackingMiddlewareDetails.getStatusLongitude())
                        .sorryCardNumber(trackingMiddlewareDetails.getSorryCardNumber())
                        .reachedDestinationLocation(trackingMiddlewareDetails.getReachedDestinationLocation())
                        .secureCode(trackingMiddlewareDetails.getSecureCode())
                        .build();

        // Build Scans
        BluedartMiddlewareRequest.Scans scans = BluedartMiddlewareRequest.Scans.builder()
                .scanDetail(Collections.singletonList(scanDetail))
                .deliveryDetails(BluedartMiddlewareRequest.DeliveryDetails.builder()
                        .receivedBy(trackingMiddlewareDetails.getReceivedBy())
                        .build())
                .reweigh(BluedartMiddlewareRequest.Reweigh.builder().build())
                .build();

        // Build Shipment
        BluedartMiddlewareRequest.Shipment shipment = BluedartMiddlewareRequest.Shipment.builder()
                .senderID(trackingMiddlewareDetails.getSenderID())
                .receiverID(trackingMiddlewareDetails.getReceiverID())
                .waybillNo(trackingMiddlewareDetails.getWaybillNo())
                .origin(trackingMiddlewareDetails.getOrigin())
                .originAreaCode(trackingMiddlewareDetails.getOriginAreaCode())
                .destination(trackingMiddlewareDetails.getDestination())
                .destinationAreaCode(trackingMiddlewareDetails.getDestinationAreaCode())
                .pickUpDate(trackingMiddlewareDetails.getPickUpDate())
                .pickUpTime(trackingMiddlewareDetails.getPickUpTime())
                .shipmentMode(trackingMiddlewareDetails.getShipmentMode())
                .expectedDeliveryDate(trackingMiddlewareDetails.getEstimatedDeliveryDate())
                .feature(trackingMiddlewareDetails.getFeature())
                .refNo(trackingMiddlewareDetails.getRefNo())
                .prodCode(trackingMiddlewareDetails.getProdCode())
                .subProductCode(trackingMiddlewareDetails.getSubProductCode())
                .weight(trackingMiddlewareDetails.getWeight())
                .dynamicExpectedDeliveryDate(trackingMiddlewareDetails.getDynamicExpectedDeliveryDate())
                .customerCode(trackingMiddlewareDetails.getCustomerCode())
                .scans(scans)
                .build();

        // Wrap Shipment in StatusTracking
        BluedartMiddlewareRequest.StatusTracking statusTracking = BluedartMiddlewareRequest.StatusTracking.builder()
                .shipment(shipment)
                .build();

        // Build and return final request
        return BluedartMiddlewareRequest.builder()
                .statustracking(Collections.singletonList(statusTracking))
                .build();
    }

    public static CommonMiddlewareRequest getPicoExpressScanRequest(CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        return CommonMiddlewareRequest.builder()
                .waybillNo(trackingMiddlewareDetails.getWaybillNo())
                .origin(trackingMiddlewareDetails.getOrigin())
                .originAreaCode(trackingMiddlewareDetails.getOriginAreaCode())
                .destination(trackingMiddlewareDetails.getDestination())
                .destinationAreaCode(trackingMiddlewareDetails.getDestinationAreaCode())
                .pickUpDate(trackingMiddlewareDetails.getPickUpDate())
                .shipmentMode(trackingMiddlewareDetails.getShipmentMode())
                .expectedDeliveryDate(trackingMiddlewareDetails.getDynamicExpectedDeliveryDate())
                .referenceNo(trackingMiddlewareDetails.getRefNo())
                .scanCode(trackingMiddlewareDetails.getScanCode())
                .scanDateTime(trackingMiddlewareDetails.getScanDate()) // ISO format preferred
                .currentLocation(trackingMiddlewareDetails.getCurrentLocation())
                .description(trackingMiddlewareDetails.getComments())
                .currentCity(trackingMiddlewareDetails.getCurrentCity())
                .currentState(trackingMiddlewareDetails.getCurrentState())
                .latitude(trackingMiddlewareDetails.getStatusLatitude())
                .longitude(trackingMiddlewareDetails.getStatusLongitude())
                .receivedBy(trackingMiddlewareDetails.getReceivedBy())
                .timeZone(trackingMiddlewareDetails.getStatusTimeZone())
                .build();
    }

    public static CommonMiddlewareRequest getCriticalLogScanRequest(CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        return CommonMiddlewareRequest.builder()
                .waybillNo(trackingMiddlewareDetails.getWaybillNo())
                .origin(trackingMiddlewareDetails.getOrigin())
                .originAreaCode(trackingMiddlewareDetails.getOriginAreaCode())
                .destination(trackingMiddlewareDetails.getDestination())
                .destinationAreaCode(trackingMiddlewareDetails.getDestinationAreaCode())
                .pickUpDate(trackingMiddlewareDetails.getPickUpDate())
                .shipmentMode(trackingMiddlewareDetails.getShipmentMode())
                .expectedDeliveryDate(trackingMiddlewareDetails.getDynamicExpectedDeliveryDate())
                .referenceNo(trackingMiddlewareDetails.getRefNo())
                .scanCode(trackingMiddlewareDetails.getScanCode())
                .scanDateTime(trackingMiddlewareDetails.getScanDate())
                .currentLocation(trackingMiddlewareDetails.getCurrentLocation())
                .description(trackingMiddlewareDetails.getComments())
                .currentCity(trackingMiddlewareDetails.getCurrentCity())
                .currentState(trackingMiddlewareDetails.getCurrentState())
                .latitude(trackingMiddlewareDetails.getStatusLatitude())
                .longitude(trackingMiddlewareDetails.getStatusLongitude())
                .receivedBy(trackingMiddlewareDetails.getReceivedBy())
                .timeZone(trackingMiddlewareDetails.getStatusTimeZone())
                .build();
    }

    public static TrackingMiddlewareUIRequest getEventsByAWB(CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails, String awbNo) {
        return TrackingMiddlewareUIRequest.builder()
                .identifierType(trackingMiddlewareDetails.getIdentifierType())
                .identifierValues(List.of(awbNo))
                .build();
    }

    public static DOTMiddlewareRequest getDotScanRequest(CsOrderContext.TrackingMiddlewareDetails trackingMiddlewareDetails) {
        return DOTMiddlewareRequest.builder()
                .orderUpdateEventType(
                        DOTMiddlewareRequest.OrderUpdateEventType.builder()
                                .type(trackingMiddlewareDetails.getOrderUpdateEventType())
                                .build())
                .timestamp(trackingMiddlewareDetails.getTimestamp())
                .order(
                        DOTMiddlewareRequest.Order.builder()
                                .id(trackingMiddlewareDetails.getWaybillNo())
                                .type(trackingMiddlewareDetails.getOrderType())
                                .date(trackingMiddlewareDetails.getDate())
                                .slot(
                                        DOTMiddlewareRequest.Slot.builder()
                                                .start(trackingMiddlewareDetails.getSlotStart())
                                                .end(trackingMiddlewareDetails.getSlotEnd())
                                                .build()
                                )
                                .channel(trackingMiddlewareDetails.getChannel())
                                .orderStatus(trackingMiddlewareDetails.getOrderStatus())
                                .orderSubStatus(trackingMiddlewareDetails.getOrderSubStatus())
                                .actor(
                                        DOTMiddlewareRequest.Actor.builder()
                                                .id(trackingMiddlewareDetails.getActorId())
                                                .build()
                                )
                                .triggerTime(trackingMiddlewareDetails.getTriggerTime())
                                .teamId(trackingMiddlewareDetails.getTeamId())
                                .planIteration(trackingMiddlewareDetails.getPlanIteration())
                                .tourDetail(
                                        DOTMiddlewareRequest.TourDetail.builder()
                                                .tourId(trackingMiddlewareDetails.getTourId())
                                                .sequence(trackingMiddlewareDetails.getSequence())
                                                .riderId(trackingMiddlewareDetails.getRiderId())
                                                .riderName(trackingMiddlewareDetails.getRiderName())
                                                .riderNumber(trackingMiddlewareDetails.getRiderNumber())
                                                .transporterId(trackingMiddlewareDetails.getTransporterId())
                                                .transporterName(trackingMiddlewareDetails.getTransporterName())
                                                .vehicleModelId(trackingMiddlewareDetails.getVehicleModelId())
                                                .vehicleModelName(trackingMiddlewareDetails.getVehicleModelName())
                                                .vehicleId(trackingMiddlewareDetails.getVehicleId())
                                                .vehicleName(trackingMiddlewareDetails.getVehicleName())
                                                .tourStartTime(trackingMiddlewareDetails.getTourStartTime())
                                                .isSortedForTour(trackingMiddlewareDetails.isSortedForTour())
                                                .customProperties(trackingMiddlewareDetails.getCustomProperties())
                                                .build()
                                )
                                .initialEta(trackingMiddlewareDetails.getInitialEta())
                                .initialEtd(trackingMiddlewareDetails.getInitialEtd())
                                .currentEta(trackingMiddlewareDetails.getCurrentEta())
                                .slaStatus(trackingMiddlewareDetails.getSlaStatus())
                                .orchestrationMetadata(
                                        DOTMiddlewareRequest.OrchestrationMetadata.builder()
                                                .waybills(null)
                                                .build()
                                )
                                .amountTransaction(
                                        DOTMiddlewareRequest.AmountTransaction.builder()
                                                .refId(trackingMiddlewareDetails.getRefId())
                                                .amount(
                                                        DOTMiddlewareRequest.Amount.builder()
                                                                .amount(trackingMiddlewareDetails.getAmount())
                                                                .currency(trackingMiddlewareDetails.getCurrency())
                                                                .symbol(trackingMiddlewareDetails.getSymbol())
                                                                .build()
                                                )
                                                .net(
                                                        DOTMiddlewareRequest.AmountOnly.builder()
                                                                .amount(trackingMiddlewareDetails.getAmount())
                                                                .build()
                                                )
                                                .gross(
                                                        DOTMiddlewareRequest.AmountOnly.builder()
                                                                .amount(trackingMiddlewareDetails.getAmount())
                                                                .build()
                                                )
                                                .exchangeType(trackingMiddlewareDetails.getExchangeType())
                                                .build()
                                )
                                .orderAmountInformation(
                                        DOTMiddlewareRequest.OrderAmountInformation.builder()
                                                .amount(
                                                        DOTMiddlewareRequest.OrderAmount.builder()
                                                                .id(trackingMiddlewareDetails.getId())
                                                                .grossAmount(
                                                                        DOTMiddlewareRequest.Amount.builder()
                                                                                .amount(trackingMiddlewareDetails.getAmount())
                                                                                .currency(trackingMiddlewareDetails.getCurrency())
                                                                                .build()
                                                                )
                                                                .netAmount(
                                                                        DOTMiddlewareRequest.Amount.builder()
                                                                                .amount(trackingMiddlewareDetails.getAmount())
                                                                                .currency(trackingMiddlewareDetails.getCurrency())
                                                                                .build()
                                                                )
                                                                .amountToBeCollected(
                                                                        DOTMiddlewareRequest.Amount.builder()
                                                                                .amount(trackingMiddlewareDetails.getAmount())
                                                                                .currency(trackingMiddlewareDetails.getCurrency())
                                                                                .build()
                                                                )
                                                                .build()
                                                )
                                                .pendingAmount(
                                                        DOTMiddlewareRequest.PendingAmount.builder()
                                                                .amount(trackingMiddlewareDetails.getAmount())
                                                                .currency(trackingMiddlewareDetails.getCurrency())
                                                                .build()
                                                )
                                                .paymentStatus(trackingMiddlewareDetails.getPaymentStatus())
                                                .paymentHistory(trackingMiddlewareDetails.getPaymentHistory())
                                                .build()
                                )
                                .checklist(trackingMiddlewareDetails.getChecklist())
                                .lineItems((List<Object>) trackingMiddlewareDetails.getLineItems())
                                .lineItemTransactionStatuses(trackingMiddlewareDetails.getLineItemTransactionStatuses())
                                .quantities(trackingMiddlewareDetails.getQuantities())
                                .trackingInfo(trackingMiddlewareDetails.getTrackingInfo())
                                .homebaseProofOfCompletion(trackingMiddlewareDetails.getHomebaseProofOfCompletion())
                                .proofOfSkipPaymentCollection(
                                        DOTMiddlewareRequest.ProofOfSkipPaymentCollection.builder()
                                                .checklistValues(Collections.singletonList(trackingMiddlewareDetails.getChecklistValues()))
                                                .build()
                                )
                                .build()
                )
                .build();
    }

    public static Object buildPayload(String courierName, CsOrderContext.TrackingMiddlewareDetails details, String awbNo) {
        MiddlewareCouriers courier = MiddlewareCouriers.getByCourierName(courierName);
        switch (courier) {
            case PURPLEDRONE:
                return getPurpleDroneScanRequest(details, awbNo);
            case BLUEDART:
                return getBlueDartScanRequest(details);
            case PICOXPRESS:
                return getPicoExpressScanRequest(details);
            case CRITICALOG:
                return getCriticalLogScanRequest(details);
            case DOT:
                return getDotScanRequest(details);
            default:
                throw new IllegalArgumentException("Unsupported courier: " + courierName);
        }
    }

    public static CrmSearchAppointmentRequest getCrmSearchAppointmentRequest(CsOrderContext.CrmSearchAppointmentContext context) {
        return CrmSearchAppointmentRequest.builder()
                .storeCode(context.getStoreCode())
                .build();
    }

    public static JSONObject getCrmPushPrescriptionOrdersRequest(OrderContext orderContext, CsOrderContext.PFUContext pfuContext) {
        JSONObject payload = new JSONObject();
        payload.put("powerType", orderContext.getProductLists().getFirst().getPowerType().getDisplayName().toUpperCase());
        payload.put("left", leftEye(orderContext.getProductLists().getFirst().getPowerType().getDisplayName(), orderContext));
        payload.put("right", rightEye(orderContext.getProductLists().getFirst().getPowerType().getDisplayName(), orderContext));
        payload.put("userName", "Automation User");
        payload.put("updatedBy", pfuContext.getPrescriptionUpdatedBy());
        return payload;
    }

    public static JSONObject leftEye(String powerType, OrderContext orderContext) {
        JSONObject left = new JSONObject();
        if (PowerTypes.BIFOCAL.getDisplayName().equalsIgnoreCase(powerType)) {
            if (orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()) {
                left.put("sph", "0.50");
            } else {
                left.put("sph", "-0.50");
            }

            left.put("cyl", "-2.00");
            left.put("axis", "100");
            left.put("pd", "29");
            left.put("ap", "+1.25");
            left.put("lensHeight", "35 mm");
            left.put("lensWidth", "51 mm");
            left.put("ed", "10");
            left.put("dbl", "10");
            left.put("bottomDistance", "1");
            left.put("topDistance", "10");
            left.put("effectiveDia", "10");
        } else if (PowerTypes.SINGLE_VISION.getDisplayName().equalsIgnoreCase(powerType)) {
            if (orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()) {
                left.put("sph", "-1.00");
            } else {
                left.put("sph", "+1.00");
            }
            left.put("cyl", "*****");
            left.put("axis", "6");
            left.put("pd", "30.5");
        } else if (PowerTypes.SUNGLASSES.getDisplayName().equalsIgnoreCase(powerType) && Countries.IN.name().equalsIgnoreCase(orderContext.getCountryCodeMapper().getCountry().name())) {
            if (orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()) {
                left.put("sph", "0.50");
            } else {
                left.put("sph", "0.00");
            }
            left.put("cyl", "*****");
            left.put("axis", "166");
        } else if (PowerTypes.SUNGLASSES.getDisplayName().equalsIgnoreCase(powerType)) {
            if (orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()) {
                left.put("sph", "0.50");
            } else {
                left.put("sph", "0.00");
            }
            left.put("cyl", "0.00");
            left.put("axis", "166");
        }
        return left;
    }


    public static JSONObject rightEye(String powerType, OrderContext orderContext) {
        JSONObject right = new JSONObject();
        if (PowerTypes.BIFOCAL.getDisplayName().equalsIgnoreCase(powerType)) {
            if (orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()) {
                right.put("sph", "0.50");
            } else {
                right.put("sph", "-0.50");
            }
            right.put("cyl", "-2.00");
            right.put("axis", "100");
            right.put("pd", "29");
            right.put("ap", "+1.25");
            right.put("lensHeight", "35 mm");
            right.put("lensWidth", "51 mm");
            right.put("ed", "10");
            right.put("dbl", "10");
            right.put("bottomDistance", "1");
            right.put("topDistance", "10");
            right.put("effectiveDia", "10");
        } else if (PowerTypes.SINGLE_VISION.getDisplayName().equalsIgnoreCase(powerType)) {
            if (orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()) {
                right.put("sph", "-1.00");
            } else {
                right.put("sph", "+1.00");
            }
            right.put("cyl", "*****");
            right.put("axis", "6");
            right.put("pd", "30.5");
        } else if (PowerTypes.SUNGLASSES.getDisplayName().equalsIgnoreCase(powerType) && Countries.IN.name().equalsIgnoreCase(orderContext.getCountryCodeMapper().getCountry().name())) {
            if (orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()) {
                right.put("sph", "0.50");
            } else {
                right.put("sph", "0.00");
            }

            right.put("cyl", "*****");
            right.put("axis", "166");
        } else if (PowerTypes.SUNGLASSES.getDisplayName().equalsIgnoreCase(powerType)) {
            if (orderContext.getIsUpdatePrescriptionWhileOrderProcessingInWH()) {
                right.put("sph", "0.50");
            } else {
                right.put("sph", "0.00");
            }
            right.put("cyl", "0.00");
            right.put("axis", "166");
        }
        return right;
    }


    public static CrmInitiateCancellationRequest getCrmCancellationRequest(OrderContext orderContext, CsOrderContext csOrderContext) {
        return CrmInitiateCancellationRequest.builder()
                .action(csOrderContext.getComment())
                .orderId(String.valueOf(orderContext.getOrderId()))
                .paymentMethod(csOrderContext.getRefundMethod())
                .reasonDetail(csOrderContext.getCancellationReason())
                .reasonId(csOrderContext.getCancellationReasonID().toString())
                .build();
    }
}

