package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CrmSearchAppointmentResponse {

    private int status;
    private String message;
    private String error;
    private List<SlotDateData> data;

    @JsonProperty("store_data")
    private StoreData storeData;

    @JsonProperty("date_slots")
    private Map<String, String> dateSlots;

    @JsonProperty("appointmnet_type")
    private List<AppointmentType> appointmentType;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class SlotDateData {
        private String date;
        private String day;

        @JsonProperty("is_available")
        private int isAvailable;

        @JsonProperty("time_slot")
        private List<TimeSlot> timeSlot;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TimeSlot {
        private String label;
        private String type;

        @JsonProperty("slot_id")
        private int slotId;

        @JsonProperty("is_available")
        private int isAvailable;

        @JsonProperty("start_time")
        private String startTime;

        @JsonProperty("end_time")
        private String endTime;

        @JsonProperty("slot_group")
        private String slotGroup;

        @JsonProperty("busy_status")
        private String busyStatus;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class StoreData {

        @JsonProperty("store_open_status")
        private int storeOpenStatus;

        @JsonProperty("address_country")
        private String addressCountry;

        @JsonProperty("alt_store_name_catch")
        private String altStoreNameCatch;

        @JsonProperty("lk_store_id")
        private String lkStoreId;

        @JsonProperty("address_full")
        private String addressFull;

        @JsonProperty("address_landmark")
        private String addressLandmark;

        @JsonProperty("address_pin_code")
        private String addressPinCode;

        @JsonProperty("store_virtual_number")
        private String storeVirtualNumber;

        @JsonProperty("business_hours_openi")
        private String businessHoursOpen;

        @JsonProperty("business_hours_closi")
        private String businessHoursClose;

        @JsonProperty("map_short_url")
        private String mapShortUrl;

        @JsonProperty("appointment_url")
        private String appointmentUrl;

        private Map<String, String> serviceOptions;

        private RatingAndReviewCount ratingAndReviewCount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RatingAndReviewCount {
        private String averageRating;
        private int totalReviews;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AppointmentType {
        private int id;

        @JsonProperty("labelText")
        private String labelText;
    }
}
