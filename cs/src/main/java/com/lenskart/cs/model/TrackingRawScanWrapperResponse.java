package com.lenskart.cs.model;

import com.lenskart.trackingmiddleware.models.response.TrackingRawScanLogsRespone;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingRawScanWrapperResponse {
    private boolean success;
    private String code;
    private String error;
    private TrackingRawScanLogsRespone data;
}

