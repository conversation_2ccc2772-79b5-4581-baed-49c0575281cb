package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import lombok.Builder;
import lombok.Data;

import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@Builder
public class PurpleDroneRequest {


    @JsonProperty("CourierComment")
    private String courierComment;

    @JsonProperty("StatusTime")
    private String statusTime;

    @JsonProperty("WaybillNo")
    private String waybillNo;

    @JsonProperty("userId")
    private String userId;

    @JsonProperty("estimated_delivery_date")
    private String estimatedDeliveryDate;

    @JsonProperty("StatusDate")
    private String statusDate;

    @JsonProperty("password")
    private String password;

    @JsonProperty("CurrentLocation")
    private String currentLocation;

    @JsonProperty("shipmentMode")
    private String shipmentMode;

    @JsonProperty("currentCity")
    private String currentCity;

    @JsonProperty("currentState")
    private String currentState;

    @JsonProperty("StatusCode")
    private String statusCode;

    @JsonProperty("comments")
    private String comments;
}

