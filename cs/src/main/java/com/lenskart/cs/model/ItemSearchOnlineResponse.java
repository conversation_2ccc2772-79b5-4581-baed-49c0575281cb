package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ItemSearchOnlineResponse {
    private List<ItemDetails> itemDetailsList;
    private int totalCount;
    private String gatepassCreatedAt;
    private String storeAddress;
    private String country;
    private Object extraItemsPresentInThisGatepassIds;
    private Object receivedAsExtraItemInOtherGatepassIds;
    private Object extraItemOffset;
    private boolean nexsRtoAtUnicom;

    @Builder
    @Data
    public static class Product {
        private String splOrderFlag;
        private String fulfillable;
        private boolean isMultiple;
        private String colorCode;
        private int subProductId;
    }
}