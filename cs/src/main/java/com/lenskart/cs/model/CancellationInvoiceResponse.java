package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;


@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CancellationInvoiceResponse {
    @JsonProperty("estimated_refund_date")
    private String estimatedRefundDate;
    @JsonProperty("code")
    private String code;
    @JsonProperty("increment_id")
    private String incrementId;
    @JsonProperty("refund_amount")
    private String refundAmount;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("category")
    private String category;
    @JsonProperty("message")
    private String message;
    @JsonProperty("brand")
    private String brand;
}
