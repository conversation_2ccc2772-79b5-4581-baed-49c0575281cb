package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CrmReturnRefundMappingResponse {
    private List<Object> returnRefundMapping;
    private List<AdditionalRefund> additionalRefunds;
    private List<Object> refunds;
    private Double totalRefund;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AdditionalRefund {
        private String whenNdWhereSpectDamaged;
        private Long id;
        private Long returnId;
        private Long orderId;
        private Double refundAmount;
        private String payeeName;
        private String chequeNo;
        private String chequeAwb;
        private String chequeCourier;
        private String onlineTransactionId;
        private String neftCsAccountName;
        private String neftCsAccountNumber;
        private String neftCsAccountType;
        private String neftUtr;
        private String neftTransferAcName;
        private String neftTransferAcNo;
        private Integer neftEmailSent;
        private Integer cholaNeftEmailSent;
        private String bankIfsc;
        private String bankName;
        private String bankBranchName;
        private String refundMethod;
        private String status;
        private String orderRefundReason;
        private String createdAt;
        private String updatedAt;
        private String neftDetailsDate;
        private String bankTransactionDate;
        private Double refundToFranchisecustomer;
        private String neftFailReason;
        private Integer impsRefundTries;
        private String type;
        private String remarks;
        private String stateCirDamage;

        @JsonProperty("WhenNdWhereSpectDamaged") // Duplicate with different case
        private String whenNdWhereSpectDamagedAlt;

        private Integer webStatus;
        private Integer retryCount;
        private String webStatusLog;
        private String refRefundId;
        private String gatewayName;
        private String userName;
        private String additionalComment;
        private String pgRefundReason;
        private Integer enableRefund;
    }
}
