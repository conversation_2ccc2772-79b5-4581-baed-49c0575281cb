package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.ArrayList;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CreatePendingPutawayResponse extends ArrayList<CreatePendingPutawayResponse.PutawayItem> {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PutawayItem {
        private Integer putawayCode;
        private String putawayType;
        private String status;
        private Date createdAt;
        private String createdBy;
        private String updatedBy;
        private Date updatedAt;
        private String facilityCode;
        private Integer numberOfItems;
        private String referencePutawayCode;
        private Integer productClassification;
        private Integer locationId;
    }
}