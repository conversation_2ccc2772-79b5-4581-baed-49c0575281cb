package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CrmStoreLocatorResponse {

    private int status;
    private String message;
    private String error;
    private StoreLocatorData data;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StoreLocatorData {
        @JsonProperty("current_page")
        private int currentPage;

        private List<StoreDetail> data;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StoreDetail {
        private int id;

        @JsonProperty("lk_store_id")
        private String lkStoreId;

        @JsonProperty("alt_store_name_catch")
        private String altStoreNameCatch;

        @JsonProperty("store_open_status")
        private int storeOpenStatus;

        @JsonProperty("store_pickup_available")
        private boolean storePickupAvailable;

        @JsonProperty("location_type_labels")
        private String locationTypeLabels;

        @JsonProperty("address_full")
        private String addressFull;

        @JsonProperty("address_line_1")
        private String addressLine1;

        @JsonProperty("address_line_2")
        private String addressLine2;

        @JsonProperty("address_locality")
        private String addressLocality;

        @JsonProperty("address_landmark")
        private String addressLandmark;

        @JsonProperty("address_city")
        private String addressCity;

        @JsonProperty("address_state")
        private String addressState;

        @JsonProperty("address_pin_code")
        private String addressPinCode;

        @JsonProperty("address_country")
        private String addressCountry;

        private String latitude;
        private String longitude;

        @JsonProperty("appointment_url")
        private String appointmentUrl;

        @JsonProperty("store_email")
        private String storeEmail;

        @JsonProperty("store_virtual_number")
        private String storeVirtualNumber;

        @JsonProperty("store_phone")
        private String storePhone;

        @JsonProperty("business_hours_openi")
        private String businessHoursOpeni;

        @JsonProperty("business_hours_closi")
        private String businessHoursClosi;

        @JsonProperty("store_manager_name")
        private String storeManagerName;

        @JsonProperty("store_manager_mobile")
        private String storeManagerMobile;

        @JsonProperty("parking_options")
        private String parkingOptions;

        @JsonProperty("google_maps_url")
        private String googleMapsUrl;

        @JsonProperty("google_search_url")
        private String googleSearchUrl;

        @JsonProperty("google_review_url")
        private String googleReviewUrl;

        @JsonProperty("review_short_url")
        private String reviewShortUrl;

        @JsonProperty("map_short_url")
        private String mapShortUrl;

        @JsonProperty("store_front_image")
        private String storeFrontImage;

        private double distance;

        @JsonProperty("serviceOptions")
        private Map<String, String> serviceOptions;

        @JsonProperty("ratingAndReviewCount")
        private RatingAndReview ratingAndReviewCount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RatingAndReview {
        @JsonProperty("averageRating")
        private String averageRating;

        @JsonProperty("totalReviews")
        private int totalReviews;
    }

}
