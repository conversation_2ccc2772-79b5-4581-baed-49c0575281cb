package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.commons.model.Countries;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryETAModel {
    @JsonProperty("pincode")
    private PincodeModel pincodeModel;
    private Integer dispatchDays;
    @JsonProperty("platform")
    private PlatformModel platformModel;
    private String sourceFacility;
    private Countries destinationCountry;
    private Boolean shipToStore;

    @Data
    @AllArgsConstructor
    public static class PincodeModel {
        Integer pincode;
    }
    @Data
    @AllArgsConstructor
    public static class PlatformModel {
        private String name;
    }
}