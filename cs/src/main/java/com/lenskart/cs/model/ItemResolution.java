package com.lenskart.cs.model;



import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
public class ItemResolution {

    private String facility;
    private String gatePassCode;
    private String identifierType;
    private String identifierId;
    private String psuedoGatepass;
    private String returnType;
    private String rtoItems;
    private String shipmentId;
    private boolean productBadRecall;
    private QcFailParams qcFailParams;
    private String userId;
    private ItemDetails itemDetails;

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    @Builder
    public static class QcFailParams {
        private String primaryReason;
        private String secondaryReason;
        private List<String> imageList;
    }
}
