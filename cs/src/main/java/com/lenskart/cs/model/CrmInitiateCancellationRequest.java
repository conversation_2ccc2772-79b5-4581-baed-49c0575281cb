package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class CrmInitiateCancellationRequest {

    @JsonProperty("action")
    private String action;
    @JsonProperty("order_id")
    private String orderId;
    @JsonProperty("payment_method")
    private String paymentMethod;
    @JsonProperty("reason_detail")
    private String reasonDetail;
    @JsonProperty("reason_id")
    private String reasonId;
}
