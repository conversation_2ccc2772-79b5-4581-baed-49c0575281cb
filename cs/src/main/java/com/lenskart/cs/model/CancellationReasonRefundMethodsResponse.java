package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CancellationReasonRefundMethodsResponse {

    @JsonProperty("refund_methods")
    private List<String> refundMethods;
    @JsonProperty("is_cancellable")
    private boolean isCancellable;
    @JsonProperty("cancel_reasons")
    private List<CancelReasons> cancelReasons;
    @JsonProperty("is_gv_applicable")
    private boolean isGvApplicable;
    @JsonProperty("is_lk_lkplus_applicable")
    private boolean isLkLkPlusApplicable;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CancelReasons {

        @JsonProperty("id")
        private Integer id;
        @JsonProperty("reason")
        private String reason;
        @JsonProperty("customerReason")
        private Integer customerReason;
    }
}