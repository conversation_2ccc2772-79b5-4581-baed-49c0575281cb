package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;


@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuthenticationResponse {
    @JsonProperty("userId")
    private String userId;
    @JsonProperty("userName")
    private String userName;
    @JsonProperty("userRole")
    private String userRole;
    @JsonProperty("tokenName")
    private String tokenName;
    @JsonProperty("tokenValue")
    private String tokenValue;
    @JsonProperty("roleBasedConfig")
    private RoleBasedConfig roleBasedConfig;


    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RoleBasedConfig {
        @JsonProperty("qc_approve")
        private String qcApprove;
    }

}
