package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class DOTMiddlewareRequest {

    @JsonProperty("orderUpdateEventType")
    private OrderUpdateEventType orderUpdateEventType;

    @JsonProperty("order")
    private Order order;

    @JsonProperty("timestamp")
    private String timestamp;

    @Data
    @Builder
    public static class OrderUpdateEventType {
        @JsonProperty("type")
        private String type;
    }

    @Data
    @Builder
    public static class Order {
        @JsonProperty("id")
        private String id;

        @JsonProperty("type")
        private String type;

        @JsonProperty("date")
        private String date;

        @JsonProperty("slot")
        private Slot slot;

        @JsonProperty("channel")
        private String channel;

        @JsonProperty("orderStatus")
        private String orderStatus;

        @JsonProperty("orderSubStatus")
        private String orderSubStatus;

        @JsonProperty("checklist")
        private Map<String, Object> checklist;

        @JsonProperty("actor")
        private Actor actor;

        @JsonProperty("triggerTime")
        private String triggerTime;

        @JsonProperty("lineItems")
        private List<Object> lineItems; // Empty array, can specify a class if needed

        @JsonProperty("lineItemTransactionStatuses")
        private List<LineItemTransactionStatus> lineItemTransactionStatuses;

        @JsonProperty("teamId")
        private String teamId;

        @JsonProperty("quantities")
        private List<Quantity> quantities;

        @JsonProperty("homebaseId")
        private String homebaseId;

        @JsonProperty("homebaseEta")
        private String homebaseEta;

        @JsonProperty("homebaseEtd")
        private String homebaseEtd;

        @JsonProperty("homebaseCompleteOtp")
        private String homebaseCompleteOtp;

        @JsonProperty("customerCompleteOtp")
        private String customerCompleteOtp;

        @JsonProperty("cancellationOtp")
        private String cancellationOtp;

        @JsonProperty("customerReturnOtp")
        private String customerReturnOtp;

        @JsonProperty("planIteration")
        private int planIteration;

        @JsonProperty("tourDetail")
        private TourDetail tourDetail;

        @JsonProperty("initialEta")
        private String initialEta;

        @JsonProperty("initialEtd")
        private String initialEtd;

        @JsonProperty("currentEta")
        private String currentEta;

        @JsonProperty("slaStatus")
        private String slaStatus;

        @JsonProperty("trackingInfo")
        private Map<String, Object> trackingInfo;

        @JsonProperty("orchestrationMetadata")
        private OrchestrationMetadata orchestrationMetadata;

        @JsonProperty("amountTransaction")
        private AmountTransaction amountTransaction;

        @JsonProperty("homebaseProofOfCompletion")
        private Map<String, Object> homebaseProofOfCompletion;

        @JsonProperty("proofOfSkipPaymentCollection")
        private ProofOfSkipPaymentCollection proofOfSkipPaymentCollection;

        @JsonProperty("orderAmountInformation")
        private OrderAmountInformation orderAmountInformation;
    }

    @Data
    @Builder
    public static class Slot {
        @JsonProperty("start")
        private String start;

        @JsonProperty("end")
        private String end;
    }

    @Data
    @Builder
    public static class Actor {
        @JsonProperty("id")
        private String id;
    }

    @Data
    @Builder
    public static class LineItemTransactionStatus {
        @JsonProperty("homebaseTransactionStatus")
        private HomebaseTransactionStatus homebaseTransactionStatus;

        @JsonProperty("id")
        private String id;

        @JsonProperty("reconcileQuantity")
        private int reconcileQuantity;

        @JsonProperty("handlingUnit")
        private String handlingUnit;
    }

    @Data
    @Builder
    public static class HomebaseTransactionStatus {
        @JsonProperty("orderedQuantity")
        private int orderedQuantity;

        @JsonProperty("transactedQuantity")
        private int transactedQuantity;

        @JsonProperty("status")
        private String status;
    }

    @Data
    @Builder
    public static class Quantity {
        @JsonProperty("value")
        private int value;

        @JsonProperty("unit")
        private String unit;
    }

    @Data
    @Builder
    public static class TourDetail {
        @JsonProperty("tourId")
        private String tourId;

        @JsonProperty("sequence")
        private int sequence;

        @JsonProperty("riderId")
        private String riderId;

        @JsonProperty("riderName")
        private String riderName;

        @JsonProperty("riderNumber")
        private String riderNumber;

        @JsonProperty("transporterId")
        private String transporterId;

        @JsonProperty("transporterName")
        private String transporterName;

        @JsonProperty("vehicleModelId")
        private String vehicleModelId;

        @JsonProperty("vehicleModelName")
        private String vehicleModelName;

        @JsonProperty("vehicleId")
        private String vehicleId;

        @JsonProperty("vehicleName")
        private String vehicleName;

        @JsonProperty("tourStartTime")
        private String tourStartTime;

        @JsonProperty("isSortedForTour")
        private boolean isSortedForTour;

        @JsonProperty("customProperties")
        private Map<String, Object> customProperties;
    }

    @Data
    @Builder
    public static class OrchestrationMetadata {
        @JsonProperty("waybills")
        private List<Object> waybills; // empty array
    }

    @Data
    @Builder
    public static class AmountTransaction {
        @JsonProperty("refId")
        private String refId;

        @JsonProperty("amount")
        private Amount amount;

        @JsonProperty("net")
        private AmountOnly net;

        @JsonProperty("gross")
        private AmountOnly gross;

        @JsonProperty("exchangeType")
        private String exchangeType;
    }

    @Data
    @Builder
    public static class Amount {
        @JsonProperty("amount")
        private double amount;

        @JsonProperty("currency")
        private String currency;

        @JsonProperty("symbol")
        private String symbol;
    }

    @Data
    @Builder
    public static class AmountOnly {
        @JsonProperty("amount")
        private double amount;
    }

    @Data
    @Builder
    public static class ProofOfSkipPaymentCollection {
        @JsonProperty("checklistValues")
        private List<Object> checklistValues; // empty list
    }

    @Data
    @Builder
    public static class OrderAmountInformation {
        @JsonProperty("amount")
        private OrderAmount amount;

        @JsonProperty("pendingAmount")
        private PendingAmount pendingAmount;

        @JsonProperty("paymentStatus")
        private String paymentStatus;

        @JsonProperty("paymentHistory")
        private List<Object> paymentHistory; // empty list
    }

    @Data
    @Builder
    public static class OrderAmount {
        @JsonProperty("id")
        private String id;

        @JsonProperty("grossAmount")
        private Amount grossAmount;

        @JsonProperty("netAmount")
        private Amount netAmount;

        @JsonProperty("amountToBeCollected")
        private Amount amountToBeCollected;
    }

    @Data
    @Builder
    public static class PendingAmount {
        @JsonProperty("amount")
        private double amount;

        @JsonProperty("currency")
        private String currency;
    }
}
