package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class LocalFittingGatepassRequest {

    @JsonProperty("gatepass_no")
    private String gatepassNo;
    @JsonProperty("increment_id")
    private Integer incrementId;
    @JsonProperty("gatepass_item_id")
    private Integer gatepassItemId;
    @JsonProperty("uw_item_id")
    private Integer uwItemId;
    @JsonProperty("facilityCode")
    private String facilityCode;
}