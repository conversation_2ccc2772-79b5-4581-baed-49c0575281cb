package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CrmInitiateCancellationResponse {
    private String code;
    private String message;
    private String category;
    private String brand;
    @JsonProperty("refund_methods")
    private String refundMethods;
    @JsonProperty("increment_id")
    private String incrementId;
    @JsonProperty("is_cancellable")
    private String isCancellable;
    @JsonProperty("is_gv_applicable")
    private String isGvApplicable;
    @JsonProperty("is_rpu_canceled")
    private String isRpuCanceled;
    @JsonProperty("is_lk_lkplus_applicable")
    private String isLkLkplusApplicable;
    @JsonProperty("cancel_reasons")
    private String cancelReasons;
    @JsonProperty("refund_methods_response")
    private String refundMethodsResponse;
}
