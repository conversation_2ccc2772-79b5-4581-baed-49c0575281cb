package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingMiddlewareUIResponse {
    private boolean success;
    private String code;
    private String error;
    private List<ShipmentData> data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShipmentData {
        private Integer id;
        private Integer courierId;
        private String courierName;
        private String aggregatorCourierName;
        private String awb;
        private Long orderNumber;
        private Long shipmentId;
        private String magentoId;
        private String returnId;
        private String unicomOrderCode;
        private String shipmentType;
        private String origin;
        private String originAreaCode;
        private String destination;
        private String destinationAreaCode;
        private Double weight;
        private Long pickUpDate;
        private String receivedBy;
        private boolean indexed;
        private String timeZone;
        private Long createdAt;
        private Long updatedAt;
        private List<TrackingOrderEvent> trackingOrdersEvents;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TrackingOrderEvent {
        private Integer id;
        private Long trackingEventRawScanId;
        private String awb;
        private String courierStatusCode;
        private String courierStatusDescription;
        private String lkStatusCode;
        private String displayLabel;
        private String expectedDeliveryDate;
        private Long scanDateTime;
        private String state;
        private String city;
        private Double latitude;
        private Double longitude;
        private boolean reachedDestinationLocation;
        private boolean indexed;
        private Long createdAt;
        private Long updatedAt;
    }
}

