package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class BluedartMiddlewareRequest {

    @JsonProperty("statustracking")
    private List<StatusTracking> statustracking;

    @Data
    @Builder
    public static class StatusTracking {
        @JsonProperty("Shipment")
        private Shipment shipment;
    }

    @Data
    @Builder
    public static class Shipment {
        @JsonProperty("SenderID")
        private String senderID;

        @JsonProperty("ReceiverID")
        private String receiverID;

        @JsonProperty("WaybillNo")
        private String waybillNo;

        @JsonProperty("ExpectedDeliveryDate")
        private String expectedDeliveryDate;

        @JsonProperty("Origin")
        private String origin;

        @JsonProperty("OriginAreaCode")
        private String originAreaCode;

        @JsonProperty("Destination")
        private String destination;

        @JsonProperty("DestinationAreaCode")
        private String destinationAreaCode;

        @JsonProperty("PickUpDate")
        private String pickUpDate;

        @JsonProperty("PickUpTime")
        private String pickUpTime;

        @JsonProperty("ShipmentMode")
        private String shipmentMode;

        @JsonProperty("Feature")
        private String feature;

        @JsonProperty("RefNo")
        private String refNo;

        @JsonProperty("ProdCode")
        private String prodCode;

        @JsonProperty("SubProductCode")
        private String subProductCode;

        @JsonProperty("Weight")
        private String weight;

        @JsonProperty("DynamicExpectedDeliveryDate")
        private String dynamicExpectedDeliveryDate;

        @JsonProperty("CustomerCode")
        private String customerCode;

        @JsonProperty("Scans")
        private Scans scans;
    }

    @Data
    @Builder
    public static class Scans {
        @JsonProperty("ScanDetail")
        private List<ScanDetail> scanDetail;

        @JsonProperty("DeliveryDetails")
        private DeliveryDetails deliveryDetails;

        @JsonProperty("Reweigh")
        private Reweigh reweigh;
    }

    @Data
    @Builder
    public static class ScanDetail {
        @JsonProperty("Scan")
        private String scan;

        @JsonProperty("ScanCode")
        private String scanCode;

        @JsonProperty("ScanGroupType")
        private String scanGroupType;

        @JsonProperty("ScanDate")
        private String scanDate;

        @JsonProperty("ScanTime")
        private String scanTime;

        @JsonProperty("ScannedLocation")
        private String scannedLocation;

        @JsonProperty("ScanType")
        private String scanType;

        @JsonProperty("Comments")
        private String comments;

        @JsonProperty("ScannedLocationCode")
        private String scannedLocationCode;

        @JsonProperty("ScannedLocationCity")
        private String scannedLocationCity;

        @JsonProperty("ScannedLocationStateCode")
        private String scannedLocationStateCode;

        @JsonProperty("StatusTimeZone")
        private String statusTimeZone;

        @JsonProperty("StatusLatitude")
        private String statusLatitude;

        @JsonProperty("StatusLongitude")
        private String statusLongitude;

        @JsonProperty("SorryCardNumber")
        private String sorryCardNumber;

        @JsonProperty("ReachedDestinationLocation")
        private String reachedDestinationLocation;

        @JsonProperty("SecureCode")
        private String secureCode;
    }

    @Data
    @Builder
    public static class DeliveryDetails {
        @JsonProperty("ReceivedBy")
        private String receivedBy;
    }

    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class Reweigh {
        // Empty class as per JSON structure
    }
}
