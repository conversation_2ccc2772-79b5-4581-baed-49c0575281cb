package com.lenskart.cs.model;

import com.lenskart.commons.model.*;
import lombok.Builder;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Builder
@Data
public class CsOrderContext {

        private String productIDToBeReturned;
        private String qcStatus;
        private String message;
        private String code;
        private TestType testType=TestType.POSITIVE;
        private boolean notReceivedYN;
        private ItemResolution.QcFailParams qcFailParams;
        private String psuedoGatepass;
        private String rtoItems;
        private boolean productBadRecall;
        private String identifierType;
        private String returnType;
        private boolean needApproval;
        private String refundMethodRequest;
        private boolean doRefund;
        private String gatepassNo;
        private boolean isCancellable;
        private CancellationType cancellationType;
        private String cancelledBy;
        private String cancellationSource;
        private String cancellationReason;
        private Integer cancellationReasonID;
        private String productIDToBeCancelled;
        private String cancelledOrderShipmentStatus;
        private Integer uwItemId;
        private String gatepassItemId;
        private String barcode;
        private String source;
        private String comment;
        private String createdAt;
        private Integer id;
        private String primaryReason;
        private String secondaryReason;
        private String userId;
        private ReceivingGatePass receivingGatePass;
        private String refundMethod;
        private String refundStatus;
        private String returnStatus;
        private String returnId;
        private String transactionType;
        private String remarks;
        private String client;
        private String fastRefunduserId;
        private String refundIntiation;
        private BigDecimal refundAmount;
        @Builder.Default
        private boolean skipCancellationReasonsRefundMethod=true;


    @Builder
    @Data
    public static class ShippingEstimate {
        private Integer pincode;
        private ProductTypes classification;
        private FrameTypes frame_type;
        private PowerTypes powerTypes;
        private Boolean in_days;
        private Boolean dispach_date_req;
        private String packages;
        private String brand;
        private String lens_type;
        private String platform;
        private String r_sph;
        private String l_sph;
        private String r_cyl;
        private String l_cyl;
        private String r_axi;
        private String l_axi;
        private Boolean last_piece;
        private Countries lk_country;
        private Countries shipping_country;
        private Integer cart_item_id;
        private Boolean localFittingRequired;
        private String source_facility;
        private Boolean ship_to_store;
        private Boolean lens_only;
        private Countries source_country;
        private String coating_id;
        private String store_facility;
        private Boolean true_last_piece;
        private Boolean use_new;
        private String screen_name;
        private Integer dispatchDays;
        private String sipping_facility_source;
        private String promiseType;
    }

    @Builder
    @Data
    public static class ReceivingGatePass {
      private String productIDNeedsToBeReceived;
      private Integer gatepassItemId;
      private String userName;
      private String trackingNo;
      private String returnType;
      private String barcode;
      private String facilityCode;
      private Integer incrementId;
      private Integer productId;
      private String qcStatus;
      private String role;
      private String gatepassId;
      private String userId;
      private String status;
      private String putawayNo;
    }

    @Builder
    @Data
    public static class GetMethodWiseRefundDetails {
        private Integer orderId;
        private String identifierType;
        private  List<String> identifierValues;
    }

    @Builder
    @Data
    public static class TrackingMiddlewareDetails {
        private String courierComment;
        private String courierName;
        private String statusTime;
        private String waybillNo;
        private String userId;
        private String statusDate;
        private String password;
        private String currentLocation;
        private String statusCode;
        private String orderStatus;
        private String toMobileNumber;
        private String toEmail;
        private String toName;
        private String client;
        private String currentCity;
        private String currentState;
        private String x_api_key;
        private String identifierType;
        private String shipmentMode;
        private String estimatedDeliveryDate;
        private String senderID;
        private String receiverID;
        private String origin;
        private String originAreaCode;
        private String destination;
        private String destinationAreaCode;
        private String pickUpDate;
        private String pickUpTime;
        private String feature;
        private String refNo;
        private String prodCode;
        private String subProductCode;
        private String weight;
        private String dynamicExpectedDeliveryDate;
        private String customerCode;
        private String scan;
        private String scanCode;
        private String scanGroupType;
        private String scanDate;
        private String scanTime;
        private String scannedLocation;
        private String scanType;
        private String scannedLocationCode;
        private String scannedLocationCity;
        private String scannedLocationStateCode;
        private String statusTimeZone;
        private String statusLatitude;
        private String statusLongitude;
        private String sorryCardNumber;
        private String reachedDestinationLocation;
        private String secureCode;
        private String comments;
        private String receivedBy;
        private List<String> identifierValue;
//DOT Courier
        private String orderUpdateEventType;
        private String type;
        private String id;
        private String orderType;
        private String date;
        private String slot;
        private String slotStart;
        private String slotEnd;
        private String channel;
        private String orderSubStatus;
        private Map<String,Object> checklist;
        private String actor;
        private String actorId;
        private String triggerTime;
        private Map<String,Object> lineItems;
        private List<DOTMiddlewareRequest.LineItemTransactionStatus> lineItemTransactionStatuses;
        private String homebaseTransactionStatus;
        private String orderedQuantity;
        private String transactedQuantity;
        private String status;
        private String reconcileQuantity;
        private String handlingUnit;
        private String teamId;
        private List<DOTMiddlewareRequest.Quantity> quantities;
        private String value;
        private String unit;
        private String homebaseId;
        private String homebaseEta;
        private String homebaseEtd;
        private String homebaseCompleteOtp;
        private String customerCompleteOtp;
        private String cancellationOtp;
        private String customerReturnOtp;
        private int planIteration;
        private String tourDetail;
        private String tourId;
        private int sequence;
        private String riderId;
        private String riderName;
        private String riderNumber;
        private String transporterId;
        private String transporterName;
        private String vehicleModelId;
        private String vehicleModelName;
        private String vehicleId;
        private String vehicleName;
        private String tourStartTime;
        private boolean isSortedForTour;
        private Map<String,Object> customProperties;
        private String initialEta;
        private String initialEtd;
        private String currentEta;
        private String slaStatus;
        private Map<String,Object> trackingInfo;
        private String orchestrationMetadata;
        private String amountTransaction;
        private String refId;
        private double amount;
        private String currency;
        private String symbol;
        private String exchangeType;
        private Map<String,Object> homebaseProofOfCompletion;
        private List<Object> proofOfSkipPaymentCollection;
        private String checklistValues;
        private String orderAmountInformation;
        private String pendingAmount;
        private String paymentStatus;
        private List<Object> paymentHistory;
        private String timestamp;

    }

    @Builder
    @Data
    public static class CrmSearchAppointmentContext {
        private String storeCode;

    }

    @Builder
    @Data
    public static class PFUContext {
        private String prescriptionUpdatedBy;
        private int prescriptionAdditionCount;
        private boolean medibuddyApproveStatus;
        private boolean isInsuranceOrder;
        private String initialOrderState;
        private String finalOrderState;
        private boolean initialPowerGiven;
        private boolean isPaymentCompleted;

    }

}