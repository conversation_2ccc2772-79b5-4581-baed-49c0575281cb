package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReturnDetailsResponse {

    private boolean success;
    private String status;
    private String error;
    private String message;
    private List<ReturnOrder> returnOrders;
    private List<ReturnOrderItem> returnOrderItems;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReturnOrder {
        private Long id;
        private String requestId;
        private Long incrementId;
        private String status;
        private String unicomOrderCode;
        private Long groupId;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date returnCreateDatetime;

        private String returnType;
        private String returnMethod;
        private Boolean isInsurance;
        private Boolean isQcAtDoorstep;
        private Boolean isAutoCancelEnable;
        private String receivingFlag;
        private String source;
        private String facilityCode;
        private String bulkType;
        private Integer reversePuFollowupCnt;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date lastFollowupDatetime;

        private String agentEmail;
        private String reverseCourier;
        private String overrideComment;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date lastUpdateDatetime;

        private String reversePickupReferenceId;
        private String reverseAwb;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReturnOrderItem {
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date lastFollowupDatetime;

        private String agentEmail;
        private String reverseCourier;
        private String overrideComment;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date lastUpdateDatetime;

        private String reversePickupReferenceId;
        private String reverseAwb;

        private Boolean isFranchise;
        private String classification;
        private String productDeliveryType;
        private String method;
        private String channel;
        private String reasonForReturn;
        private String qcFailReason;
        private String tbybPrescription;
        private Integer itemSelectedFlag;
        private Integer csohUpdatedFlag;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date updatedAt;

        private String wfi;
        private String barcodeId;
    }


    }
