package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Data
public class ReturnOrderRequestDTO {

    @JsonProperty("increment_id")
    private Integer incrementId;
    @JsonProperty("items")
    private List<ReturnItemRequestDTO> items;
    @JsonProperty("source")
    public String source;
    @JsonProperty("reference_order_code")
    public String referenceOrderCode;
    @JsonProperty("do_refund")
    private Boolean doRefund;
    @JsonProperty("is_dualco")
    private Boolean isDualCo;
    @JsonProperty("facility")
    private String facility;
    @JsonProperty("raiseRPUatUnicom")
    private Boolean raiseRPUatUnicom;
    @JsonProperty("rtoItem")
    private Boolean rtoItem;
    @JsonProperty("awaitedRtoItem")
    private Boolean awaitedRtoItem;
    @JsonProperty("NewFlowFlag")
    private Integer newFlowFlag;
    @JsonProperty("groupId")
    private Long groupId;
    @JsonProperty("raiseRPUatNexs")
    private Boolean raiseRPUatNexs;
    @JsonProperty("reasonDetail")
    private String reasonDetail;
    @JsonProperty("refundMethod")
    private String refundMethod;
    @JsonProperty("shipping_package_id")
    public String shippingPackageId;
    @JsonProperty("uw_orders_list")
    public List<Object> uwOrderDTOs;
    @JsonProperty("orders_list")
    public List<Object> orderDTOs;
    @JsonProperty("orders_header")
    public Object ordersHeaderDTO;
    @JsonProperty("user_id")
    private String userId;
}
