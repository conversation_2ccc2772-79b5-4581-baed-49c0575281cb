package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OfflineProductSearchResponse {
    List<com.lenskart.core.model.ItemDetails> itemDetailsList;
    Integer totalCount;
    Date gatepassCreatedAt;
    String storeAddress;
    String country;
    Map<Integer, String> extraItemsPresentInThisGatepassIds;
    Map<Integer, String> receivedAsExtraItemInOtherGatepassIds;
    Integer extraItemOffset;
    boolean isNexsRtoAtUnicom;
}