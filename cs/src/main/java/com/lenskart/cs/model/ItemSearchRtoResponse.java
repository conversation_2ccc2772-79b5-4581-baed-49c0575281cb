package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;


@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ItemSearchRtoResponse {

    List<ItemDetails> itemDetailsList;
    Integer totalCount;
    String gatepassCreatedAt;
    String storeAddress;
    String country;
    Boolean extraItemsPresentInThisGatepassIds;
    Boolean receivedAsExtraItemInOtherGatepassIds;
    Integer extraItemOffset;
    boolean nexsRtoAtUnicom;
}
