package com.lenskart.cs.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpenPutawayResponse {

    private PutawayMaster putawayMaster;
    private String displayName;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PutawayMaster {
        private Integer putawayCode;
        private String putawayType;
        private String status;
        private String createdAt;
        private String createdBy;
        private String updatedBy;
        private String updatedAt;
        private String facilityCode;
        private Integer numberOfItems;
        private String referencePutawayCode;
        private Integer productClassification;
        private Integer locationId;
    }
}