package com.lenskart.cs.model;

import lombok.Builder;
import lombok.Data;

/**
 * Request payload for pushing shipment scan data through middleware,
 * used for PicoExpress courier and critical log requests.
 */

@Builder
@Data
public class CommonMiddlewareRequest {
    private String waybillNo;
    private String origin;
    private String originAreaCode;
    private String destination;
    private String destinationAreaCode;
    private String pickUpDate;
    private String shipmentMode;
    private String expectedDeliveryDate;
    private String referenceNo;
    private String scanCode;
    private String scanDateTime;
    private String currentLocation;
    private String description;
    private String currentCity;
    private String currentState;
    private String latitude;
    private String longitude;
    private String receivedBy;
    private String timeZone;
}
