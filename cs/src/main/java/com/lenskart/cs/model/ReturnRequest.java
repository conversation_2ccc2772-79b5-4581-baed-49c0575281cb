package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class ReturnRequest {

    @JsonProperty("entity")
    private String entity;
    @JsonProperty("facility_code")
    private String facilityCode;
    @JsonProperty("return_source")
    private String returnSource;
    @JsonProperty("initiated_by")
    private String initiatedBy;
    @JsonProperty("return_method")
    private String returnMethod;
    @JsonProperty("productIdsMap")
    private String productIdsMap;
    @JsonProperty("incrementId")
    private Integer incrementId;
    @JsonProperty("storeEmail")
    private String storeEmail;
    @JsonProperty("is_courier_reassigned")
    private String isCourierReassigned;
    @JsonProperty("newCourier")
    private String newCourier;
    @JsonProperty("oldCourier")
    private String oldCourier;
    @JsonProperty("salesman_name")
    private String salesmanName;
    @JsonProperty("salesman_number")
    private String salesmanNumber;
    @JsonProperty("callback_required_to_salesman")
    private boolean callbackRequiredToSalesman;
    @JsonProperty("store_facility_code")
    private String storeFacilityCode;
    @JsonProperty("pickup_address")
    private ReturnPickupAddress pickupAddress;
    @JsonProperty("items")
    private List<ReturnItemDetails> items;
    @JsonProperty("exchange_address")
    private String exchangeAddress;
}
