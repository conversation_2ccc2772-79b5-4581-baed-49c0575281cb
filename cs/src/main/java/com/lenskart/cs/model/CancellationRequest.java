package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.commons.model.CancellationType;
import com.lenskart.commons.model.PaymentMethod;
import com.lenskart.cs.constants.Constant;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class CancellationRequest {

    @JsonProperty("initiated_by")
    private String initiatedBy;
    @JsonProperty("source")
    private String source;
    @JsonProperty("payment_method")
    private String paymentMethod;
    @JsonProperty("reason_detail")
    private String reasonDetail;
    @JsonProperty("reason_id")
    private int reasonID;
    @JsonProperty("cancellation_type")
    private String cancellationType;
    @JsonProperty("magento_item_id")
    private int magentoItemID;
}
