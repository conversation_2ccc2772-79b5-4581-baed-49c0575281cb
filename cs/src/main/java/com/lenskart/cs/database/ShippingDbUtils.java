package com.lenskart.cs.database;


import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.cs.constants.Constant;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnResponse;

import java.util.Map;

public class ShippingDbUtils {

    public static Map<String, Object> getPincodeLevelDeliveryTatDetails(CsOrderContext.ShippingEstimate csOrderContext) {
        Map<String, Object> shippingDetails = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), Constant.SHIPPING_DB,
                        ShippingQueries.PINCODE_LEVEL_DELIVERY_TAT_DETAIL,
                        csOrderContext.getSource_facility(),csOrderContext.getPincode(),csOrderContext.getShipping_country()).getFirst();
            return shippingDetails;
    }
}
