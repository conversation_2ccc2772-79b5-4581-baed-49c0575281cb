package com.lenskart.cs.database;

import com.lenskart.cs.model.CsOrderContext;
import lombok.Builder;
import lombok.Data;

public class MiddlewareQueries {
    // Deletes test data from raw scan logs table where the request matches a pattern
    public static final String CLEAR_TRACKING_RAW_SCAN_LOGS = "DELETE FROM tracking_events_raw_scan_logs WHERE request LIKE ?";

    // Deletes test data from tracking orders based on AWB
    public static final String CLEAR_TRACKING_ORDERS = "DELETE FROM tracking_orders WHERE awb = ?";

    // Deletes test data from tracking order events based on AWB
    public static final String CLEAR_TRACKING_ORDER_EVENTS = "DELETE FROM tracking_orders_events WHERE awb = ?";

    // Query to fetch the status of a scan event from the 'tracking_events_raw_scan_logs' table
    public static String GET_EVENT_STATUS = "SELECT status FROM tracking_events_raw_scan_logs WHERE id = ?";

}
