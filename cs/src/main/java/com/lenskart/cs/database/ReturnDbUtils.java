package com.lenskart.cs.database;


import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.constants.Constant;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.cs.model.Returns;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;


import java.util.Map;

public class ReturnDbUtils {

    public static Map<String, Object> getReturnDetails(ReturnResponse returnResponse,OrderContext orderContext,CsOrderContext csOrderContext) {
        Map<String, Object> returnDetails = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), Constant.RETURN_DB,
                        ReturnQueries.RETURN_DETAIL,
                        CsRequestBuilders.getReturnID(returnResponse,orderContext,csOrderContext)).getFirst();
            return returnDetails;
    }
}
