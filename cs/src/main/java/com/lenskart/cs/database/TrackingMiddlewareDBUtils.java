package com.lenskart.cs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.model.MiddlewareCouriers;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.constants.Constant;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.cs.model.TrackingMiddlewareUIResponse;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;

import java.util.Map;

public class TrackingMiddlewareDBUtils {

    public static Object getEventStatusFromTrackingRawScanLogs(int id) {
        return MySQLQueryExecutor.executeQuery(
                Cluster.TRACKING_MIDDLEWARE_CLUSTER.getClusterName(),
                Constant.TRACKING_MIDDLEWARE_DB,
                MiddlewareQueries.GET_EVENT_STATUS,
                id
        );
    }
}
