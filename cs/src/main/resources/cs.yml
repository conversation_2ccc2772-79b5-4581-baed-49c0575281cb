# Configuration file for the cse module

# QA environment configuration
preprod:
  # Base URLs for different services
  baseUrls:
    reverseService: https://reversereceiving.scm.preprod.lenskart.com
    orderOpsService: https://order-ops.scm.preprod.lenskart.com
    refundService: https://refund-api.scm.preprod.lenskart.com
    shippingService: https://shipping.scm.preprod.lenskart.com
    returnService: https://return-api.scm.preprod.lenskart.com
    trackingMiddlewareService: https://tracking-middleware-api.scm.preprod.lenskart.com
    trackingMiddlewareConsumerUI: https://tracking-middleware-ui-api.scm.preprod.lenskart.com
    crmService: https://crm.scm.preprod.lenskart.com

  refunds:
    secretKey: PobQ3xczRWIvORNZBRx/jw==
    NXS2UserName: <EMAIL>
    NXS2Password: NDQ3Mjk=
    QNXS2UserName: 120401
    QNSX2Password: VWRpdEBQYXNzMw==

  trackingMiddleware:
    x_api_key_purpledrone: XdcB59AKrMdFD8TBxfOUVRgqKQpl0Nz4_1
    x_api_key_criticallog: R0ewcisiJnPcabHc3ttMEOMZGJ2JM1ue_1
    x_api_key_bluedart: pykdsbQa8T54RL1DWNb8JPF5YLRXspzu_1
    x_api_key_picoexpress: 9AaJ05cNmo0V7rMW0myM25tv6ko8XaPE_1
    x_api_key_dot: gW8CxqPshtSJ4H2lSVcDLyo4p2nnLS0c_1
    authToken: 610ae8a3df5f760c5caf3e99f9246cb4bf964e4095be71c51c44d9ec9f3996a1
    xAuthToken: WHFdGYPz7rq%r9aNrU9f5NvhMINo
