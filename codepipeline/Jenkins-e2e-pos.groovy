def testsExecuted = false

/**
 * Determines the email recipients based on build trigger type
 * @return String of comma-separated email addresses
 */
def getEmailRecipients() {
    // Check if build was triggered manually
    def isManualTrigger = currentBuild.getBuildCauses('hudson.model.Cause$UserIdCause').size() > 0

    if (isManualTrigger) {
        // Manual trigger - send to limited recipients
        echo "📧 Manual trigger detected - sending email to limited recipients"
        return '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>,<EMAIL>, <EMAIL>'
    } else {
        // Scheduled trigger - send to all stakeholders
        echo "📧 Scheduled trigger detected - sending email to all stakeholders"
        return '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
    }
}

pipeline {
    agent any
    tools {
            maven 'Maven'
            jdk 'JDK-21'
        }

    triggers {
        cron('H 3 * * *')
    }

    // Define parameters for the pipeline
    parameters {
        choice(name: 'ENVIRONMENT', choices: ['preprod', 'prod'], description: 'Select the environment to run tests against')
        choice(name: 'SUITE_TYPE', choices: ['e2e'], description: 'Select the test suite to run')
        string(name: 'TESTNG_XML_PATH', defaultValue: 'pos-testng.xml', description: 'Enter the path to the TestNG XML file, leave blank for default value')
        choice(name: 'TEST_CATEGORY', choices: ['SANITY', 'REGRESSION', 'E2E', 'ALL'], description: 'Select the test category to run')
    }

    // Define environment variables
    environment {
        // Set Maven options
        MAVEN_OPTS = '-Xmx512m -Xms256m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC'
        // Set the environment property for tests
        TEST_ENV = "${params.ENVIRONMENT}"
        MODULE_NAME = "${params.SUITE_TYPE}"
        DEFAULT_ENVIRONMENT = 'preprod'
        DEFAULT_TEST_CATEGORY = 'ALL'
    }

    stages {

        // Stage to checkout code from Git
        stage('Checkout') {
            steps {
                // Checkout code from Git repository
                checkout scm

                script {
                    def environment = params.ENVIRONMENT ?: env.DEFAULT_ENVIRONMENT
                    def testCategory = params.TEST_CATEGORY ?: env.DEFAULT_TEST_CATEGORY
                    currentBuild.displayName = "#${env.BUILD_NUMBER} - ${env.MODULE_NAME} - ${environment}"
                    currentBuild.description = "Module: ${env.MODULE_NAME} | Environment: ${environment} | Category: ${testCategory}"
                       }
            }
        }

        // Stage to build the project without running tests
        stage('Build') {
            steps {
                        echo "🔨 Building ${env.MODULE_NAME} module..."

                        // Build the project without running tests
                        sh "mvn clean install -DskipTests -q -s ${env.WORKSPACE}/settings.xml"

                        script {
                           echo "✅ Build completed successfully for ${env.MODULE_NAME}"
                        }

            }
        }

        // Stage to run tests based on selected parameters
        stage('Test') {
            steps {
                script {
                    // Set up test command based on selected suite type
                    def testCommand = 'mvn test'

                     // Add module selection based on SUITE_TYPE parameter
                     if (params.SUITE_TYPE != 'all') {
                        testCommand += " -pl ${params.SUITE_TYPE}"
                     }

                      // Add xml file selection based on TESTNG_XML_PATH parameter
                      if (params.TESTNG_XML_PATH != 'ALL') {
                        testCommand += " -Dsurefire.suiteXmlFiles=src/test/resources/${params.SUITE_TYPE}-${params.TESTNG_XML_PATH}"
                      }

                    // Add environment parameter
                    testCommand += " -Denvironment=${params.ENVIRONMENT}"


                    // Add test category parameter if not ALL
                    if (params.TEST_CATEGORY != 'ALL') {
                        testCommand += " -DtestCategory=${params.TEST_CATEGORY}"
                    }

                    // Add additional Maven parameters for test execution
                    testCommand += ' -Dmaven.test.failure.ignore=true'

                    // Add skipTests parameter
                    testCommand += ' -DskipTests=false'

                    testCommand += ' -s'
                    testCommand += " ${env.WORKSPACE}/settings.xml"

                     echo "**************** Run Command ****************"
                     echo "Test Command: ${testCommand}"

                    // Execute the test command
                    sh testCommand
                }
            }
        }
    }

    post {
        always {
            script {
                // Archive test results only if they exist
                def testReportsExist = sh(script: "find . -name '*.xml' -path '*/target/surefire-reports/*' -newer codepipeline/Jenkins-e2e-pos.groovy | head -1", returnStdout: true).trim()

                if (testReportsExist) {
                    try {
                        junit '**/target/surefire-reports/*.xml'
                    } catch (Exception e) {
                        echo "Failed to parse test results: ${e.message}"
                    }
                } else {
                    echo "No recent test results found - tests may not have run due to build failure"
                }

                // Determine the module for which tests were run
                def testModule = params.SUITE_TYPE == 'all' ? 'all-modules' : params.SUITE_TYPE

                // Archive extent reports for the specific module
                def extentReportPath = params.SUITE_TYPE == 'all' ?
                    '**/test-output/extent-reports/extent-report.html' :
                    "${testModule}/test-output/extent-reports/extent-report.html"

                archiveArtifacts artifacts: extentReportPath, allowEmptyArchive: true, fingerprint: false

                // Archive complete test-output directory for the module
                def testOutputPath = params.SUITE_TYPE == 'all' ?
                    '**/test-output/**' :
                    "${testModule}/test-output/**"

                archiveArtifacts artifacts: testOutputPath, allowEmptyArchive: true

                // Archive centralized log files at root level
                archiveArtifacts artifacts: 'automation.log', allowEmptyArchive: true, fingerprint: false
                archiveArtifacts artifacts: 'http-requests.log', allowEmptyArchive: true, fingerprint: false

                // Collect test statistics
                def testResults = [:]
                def totalTests = 0
                def passedTests = 0
                def failedTests = 0
                def skippedTests = 0
                def testDuration = currentBuild.duration ?: 0
                def emailSent = false

                // Parse test results from JUnit XML files
                try {
                    def testResultAction = currentBuild.rawBuild.getAction(hudson.tasks.junit.TestResultAction.class)
                    if (testResultAction != null) {
                        totalTests = testResultAction.totalCount
                        passedTests = testResultAction.totalCount - testResultAction.failCount - testResultAction.skipCount
                        failedTests = testResultAction.failCount
                        skippedTests = testResultAction.skipCount
                        testsExecuted = true
                        echo "Total tests count: ${totalTests}"
                        echo "Passed tests count: ${passedTests}"
                        echo "Failed tests count: ${failedTests}"
                        echo "Skipped tests count: ${skippedTests}"
                    } else {
                        echo "No test results found - tests may not have been executed"
                        testsExecuted = false
                    }
                } catch (Exception e) {
                    echo "Failed to parse test results: ${e.message}"
                    testsExecuted = false
                }

                // Determine build status icon and color
                def statusIcon = ""
                def statusColor = ""
                def buildStatus = currentBuild.result ?: 'SUCCESS'

                switch(buildStatus) {
                    case 'SUCCESS':
                        statusIcon = "✅"
                        statusColor = "#28a745"
                        break
                    case 'FAILURE':
                        statusIcon = "❌"
                        statusColor = "#dc3545"
                        break
                    case 'UNSTABLE':
                        statusIcon = "⚠️"
                        statusColor = "#ffc107"
                        break
                    default:
                        statusIcon = "ℹ️"
                        statusColor = "#17a2b8"
                }

                // Calculate test execution time
                def executionTime = testDuration > 0 ?
                    String.format("%.2f minutes", testDuration / 60000.0) :
                    "N/A"

                // Get current timestamp in IST
                def timestamp = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("IST"))

                // Determine extent report attachment
                def extentReportExists = false
                def extentReportFile = ""

                try {
                    if (params.SUITE_TYPE == 'all') {
                        // For 'all' modules, find any extent report
                            def reportFiles = sh(script: "find . -name 'extent-report.html' -path '*/test-output/extent-reports/*' | head -1", returnStdout: true).trim()
                        if (reportFiles) {
                            extentReportFile = reportFiles
                            extentReportExists = true
                        }
                    } else {
                        // For specific module
                        extentReportFile = "${testModule}/test-output/extent-reports/extent-report.html"
                        extentReportExists = fileExists(extentReportFile)
                    }
                } catch (Exception e) {
                    echo "Error checking extent report existence: ${e.message}"
                }

                // Check for automation.log file
                def automationLogExists = fileExists('automation.log')
                def httpLogExists = fileExists('http-requests.log')

                // Build attachments pattern
                def attachmentsList = []
                if (extentReportExists) {
                    attachmentsList.add(extentReportFile)
                }
                if (automationLogExists) {
//                    attachmentsList.add('automation.log')
                }
                if (httpLogExists) {
//                    attachmentsList.add('http-requests.log')
                }
                def attachmentsPattern = attachmentsList.join(',')

                echo "Email attachments: ${attachmentsPattern}"

                // Send rich email notification only if tests were executed
                // If tests weren't executed due to build failure, let the failure block handle it
                if (testsExecuted) {
                    emailSent = true
                    emailext (
                    subject: "${statusIcon} ${buildStatus}:Pre-Prod ${env.JOB_NAME} [${env.BUILD_NUMBER}] - ${testModule} Tests",
                    mimeType: 'text/html',
                    body: """
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                            .container { max-width: 800px; margin: 0 auto; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                            .header { background-color: ${statusColor}; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
                            .content { padding: 20px; }
                            .section { margin-bottom: 20px; }
                            .section h3 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 5px; }
                            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 15px 0; }
                            .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; border-left: 4px solid ${statusColor}; }
                            .stat-number { font-size: 24px; font-weight: bold; color: ${statusColor}; }
                            .stat-label { font-size: 12px; color: #666; text-transform: uppercase; }
                            .info-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                            .info-table th, .info-table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #eee; }
                            .info-table th { background-color: #f8f9fa; font-weight: bold; }
                            .button { display: inline-block; padding: 10px 20px; background-color: ${statusColor}; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
                            .footer { background-color: #f8f9fa; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 12px; color: #666; }
                            .success { color: #28a745; }
                            .failure { color: #dc3545; }
                            .warning { color: #ffc107; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h1>${statusIcon} Test Execution Report</h1>
                                <h2> Backend Automation [Build #${env.BUILD_NUMBER}]</h2>
                                <p>Status: <strong>${buildStatus}</strong></p>
                            </div>

                            <div class="content">
                                <div class="section">
                                    <h3>📊 Test Statistics</h3>
                                    ${testsExecuted ? """
                                    <div class="stats-grid">
                                        <div class="stat-card">
                                            <div class="stat-number">${totalTests}</div>
                                            <div class="stat-label">Total Tests</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-number success">${passedTests}</div>
                                            <div class="stat-label">Passed</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-number failure">${failedTests}</div>
                                            <div class="stat-label">Failed</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-number warning">${skippedTests}</div>
                                            <div class="stat-label">Skipped</div>
                                        </div>
                                    </div>
                                    """ : """
                                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 10px 0;">
                                        <h4 style="color: #856404; margin: 0 0 10px 0;">⚠️ No Tests Executed</h4>
                                        <p style="color: #856404; margin: 0;">Tests were not executed due to build failure during compilation or dependency resolution phase.</p>
                                        <p style="color: #856404; margin: 5px 0 0 0;"><strong>Please check the console output for build errors.</strong></p>
                                    </div>
                                    """}
                                </div>

                                <div class="section">
                                    <h3>🔧 Build Information</h3>
                                    <table class="info-table">
                                        <tr><th>Environment</th><td>${params.ENVIRONMENT}</td></tr>
                                        <tr><th>Test Suite</th><td>${params.SUITE_TYPE}</td></tr>
                                        <tr><th>Testng File </th><td>${params.SUITE_TYPE}-${params.TESTNG_XML_PATH}</td></tr>
                                        <tr><th>Test Category</th><td>${params.TEST_CATEGORY}</td></tr>
                                        <tr><th>Execution Time</th><td>${executionTime}</td></tr>
                                        <tr><th>Build Number</th><td>#${env.BUILD_NUMBER}</td></tr>
                                        <tr><th>Timestamp</th><td>${timestamp}</td></tr>
                                        <tr><th>Node</th><td>${env.NODE_NAME ?: 'Unknown'}</td></tr>
                                    </table>
                                </div>

                                <div class="section">
                                    <h3>📈 Test Results Summary</h3>
                                    ${testsExecuted ? """
                                    <p><strong>Pass Rate:</strong> ${totalTests > 0 ? String.format("%.1f%%", (passedTests * 100.0 / totalTests)) : "N/A"}</p>
                                    <p><strong>Failure Rate:</strong> ${totalTests > 0 ? String.format("%.1f%%", (failedTests * 100.0 / totalTests)) : "N/A"}</p>
                                    ${failedTests > 0 ? "<p style='color: #dc3545;'><strong>⚠️ ${failedTests} test(s) failed. Please review the detailed report.</strong></p>" : ""}
                                    ${skippedTests > 0 ? "<p style='color: #ffc107;'><strong>ℹ️ ${skippedTests} test(s) were skipped.</strong></p>" : ""}
                                    """ : """
                                    <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 10px 0;">
                                        <h4 style="color: #721c24; margin: 0 0 10px 0;">❌ Build Failed</h4>
                                        <p style="color: #721c24; margin: 0;">The build failed before tests could be executed. This could be due to:</p>
                                        <ul style="color: #721c24; margin: 10px 0 0 20px;">
                                            <li>Compilation errors in the source code</li>
                                            <li>Missing or incompatible dependencies</li>
                                            <li>Configuration issues</li>
                                            <li>Infrastructure problems</li>
                                        </ul>
                                        <p style="color: #721c24; margin: 10px 0 0 0;"><strong>Action Required:</strong> Check the console output and fix the build issues before running tests.</p>
                                    </div>
                                    """}
                                </div>

                                <div class="section">
                                    <h3>🔗 Quick Links</h3>
                                    <a href="${env.BUILD_URL}" class="button">View Build Details</a>
                                    <a href="${env.BUILD_URL}console" class="button">Console Output</a>
                                    <a href="${env.BUILD_URL}testReport" class="button">Test Results</a>
                                    <a href="${env.BUILD_URL}artifact/" class="button">Download Artifacts</a>
                                </div>

                                ${extentReportExists || automationLogExists ? """
                                <div class="section">
                                    <h3>📋 Test Reports & Logs</h3>
                                    ${extentReportExists ? """
                                    <p><strong>📊 Extent Report:</strong> Detailed test execution report with interactive dashboard</p>
                                    <a href="${env.BUILD_URL}artifact/${extentReportFile.replace('\\', '/')}" class="button">Download Extent Report</a>
                                    """ : ""}
                                    ${automationLogExists ? """
                                    <p><strong>📝 Automation Logs:</strong> Complete test execution logs with detailed debugging information</p>
                                    <a href="${env.BUILD_URL}artifact/automation.log" class="button">Download Automation Logs</a>
                                    """ : ""}
                                    ${httpLogExists ? """
                                    <p><strong>🌐 HTTP Logs:</strong> API requests and responses for debugging</p>
                                    <a href="${env.BUILD_URL}artifact/http-requests.log" class="button">Download HTTP Logs</a>
                                    """ : ""}
                                    <p style="margin-top: 15px;"><em>All reports and logs are attached to this email and available in build artifacts.</em></p>
                                </div>
                                """ : ""}

                                <div class="section">
                                    <h3>📝 Additional Information</h3>
                                    <ul>
                                        <li><strong>Git Branch:</strong> ${env.GIT_BRANCH ?: 'Unknown'}</li>
                                        <li><strong>Git Commit:</strong> ${env.GIT_COMMIT?.take(8) ?: 'Unknown'}</li>
                                        <li><strong>Triggered By:</strong> ${env.BUILD_USER ?: 'System'}</li>
                                        <li><strong>Jenkins URL:</strong> <a href="${env.JENKINS_URL}">${env.JENKINS_URL}</a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="footer">
                                <p>This is an automated message from Jenkins CI/CD Pipeline</p>
                                <p>Lenskart Automation Framework | Generated on ${timestamp}</p>
                            </div>
                        </div>
                    </body>
                    </html>
                    """,
                    attachmentsPattern: attachmentsPattern,
                    to: getEmailRecipients()
                    )
                } else {
                    echo "Skipping main email notification - tests were not executed due to build failure"
                    echo "Build failure email will be sent from the failure post condition"
                }
            }
        }

        success {
            echo 'Build and tests completed successfully!'
            script {
                echo "✅ All tests passed for ${params.SUITE_TYPE} module in ${params.ENVIRONMENT} environment"
                echo "📊 Test execution completed successfully"
            }
        }

        failure {
            script {
                echo 'Build or tests failed!'

                // Only send failure email if the main email wasn't sent (i.e., tests weren't executed)
                if (!testsExecuted) {
                    echo "Sending build failure notification - tests were not executed"
                    try {
                        // Send a simplified failure notification for build failures
                    def buildStatus = currentBuild.result ?: 'FAILURE'
                    def statusIcon = "❌"
                    def statusColor = "#dc3545"
                    def testModule = params.SUITE_TYPE == 'all' ? 'all-modules' : params.SUITE_TYPE
                    def timestamp = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("IST"))
                    def executionTime = currentBuild.duration > 0 ?
                        String.format("%.2f minutes", currentBuild.duration / 60000.0) : "N/A"

                    // Check for log files for failure email
                    def failureAutomationLogExists = fileExists('automation.log')
                    def failureAttachments = failureAutomationLogExists ? 'automation.log' : ''

                    emailext (
                        subject: "${statusIcon} BUILD FAILED:Pre-Prod ${env.JOB_NAME} [${env.BUILD_NUMBER}] - ${testModule} Module",
                        mimeType: 'text/html',
                        body: """
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <style>
                                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                                .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                                .header { background-color: ${statusColor}; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
                                .content { padding: 20px; }
                                .failure-box { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 15px 0; }
                                .info-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                                .info-table th, .info-table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #eee; }
                                .info-table th { background-color: #f8f9fa; font-weight: bold; }
                                .button { display: inline-block; padding: 10px 20px; background-color: ${statusColor}; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
                                .footer { background-color: #f8f9fa; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 12px; color: #666; }
                            </style>
                        </head>
                        <body>
                            <div class="container">
                                <div class="header">
                                    <h1>${statusIcon} Build Failed</h1>
                                    <h2>Backend Automation [Build #${env.BUILD_NUMBER}]</h2>
                                    <p>Status: <strong>FAILURE</strong></p>
                                </div>

                                <div class="content">
                                    <div class="failure-box">
                                        <h3 style="color: #721c24; margin: 0 0 10px 0;">❌ Build Failure</h3>
                                        <p style="color: #721c24; margin: 0;">The build failed before tests could be executed. This could be due to:</p>
                                        <ul style="color: #721c24; margin: 10px 0 0 20px;">
                                            <li>Compilation errors in the source code</li>
                                            <li>Missing or incompatible dependencies</li>
                                            <li>Configuration issues</li>
                                            <li>Infrastructure problems</li>
                                        </ul>
                                        <p style="color: #721c24; margin: 10px 0 0 0;"><strong>Action Required:</strong> Check the console output and fix the build issues.</p>
                                    </div>

                                    <h3>🔧 Build Information</h3>
                                    <table class="info-table">
                                        <tr><th>Environment</th><td>${params.ENVIRONMENT}</td></tr>
                                        <tr><th>Test Suite</th><td>${params.SUITE_TYPE}</td></tr>
                                        <tr><th>Test Category</th><td>${params.TEST_CATEGORY}</td></tr>
                                        <tr><th>Execution Time</th><td>${executionTime}</td></tr>
                                        <tr><th>Build Number</th><td>#${env.BUILD_NUMBER}</td></tr>
                                        <tr><th>Timestamp</th><td>${timestamp}</td></tr>
                                        <tr><th>Node</th><td>${env.NODE_NAME ?: 'Unknown'}</td></tr>
                                    </table>

                                    <h3>🔗 Quick Links</h3>
                                    <a href="${env.BUILD_URL}" class="button">View Build Details</a>
                                    <a href="${env.BUILD_URL}console" class="button">Console Output</a>
                                    ${failureAutomationLogExists ? """<a href="${env.BUILD_URL}artifact/automation.log" class="button">Download Build Logs</a>""" : ""}

                                    <h3>📝 Additional Information</h3>
                                    <ul>
                                        <li><strong>Git Branch:</strong> ${env.GIT_BRANCH ?: 'Unknown'}</li>
                                        <li><strong>Git Commit:</strong> ${env.GIT_COMMIT?.take(8) ?: 'Unknown'}</li>
                                        <li><strong>Triggered By:</strong> ${env.BUILD_USER ?: 'System'}</li>
                                        <li><strong>Jenkins URL:</strong> <a href="${env.JENKINS_URL}">${env.JENKINS_URL}</a></li>
                                    </ul>
                                </div>

                                <div class="footer">
                                    <p>This is an automated message from Jenkins CI/CD Pipeline</p>
                                    <p>Lenskart Automation Framework | Generated on ${timestamp}</p>
                                </div>
                            </div>
                        </body>
                        </html>
                        """,
                        attachmentsPattern: failureAttachments,
                        to: getEmailRecipients()
                        )
                    } catch (Exception e) {
                        echo "Failed to send failure notification email: ${e.message}"
                    }
                } else {
                    echo "Skipping build failure email - tests were executed and main email was already sent"
                }
            }
        }

        unstable {
            echo 'Build is unstable (some tests failed)!'
        }
    }
}
