pipeline {
    agent any

    tools {
        maven 'Maven'
        jdk 'JDK-21'
    }

    triggers {
        cron('45 1,13 * * *')
    }

    environment {
        MAVEN_OPTS = '-Xmx512m -Xms256m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC'
        DEFAULT_ENVIRONMENT = 'preprod'
    }

    stages {
        stage('Build') {
            steps {
                script {
                    echo "🔨 Starting build stage..."
                }
                sh """
                    mvn clean install -DskipTests -s ${env.WORKSPACE}/settings.xml
                """
                script {
                    echo "✅ Build completed successfully"
                }
            }
        }

        stage('Test') {
            steps {
                script {
                    echo "🧪 Starting test stage..."
                    def testCommand = """
                        mvn test \
                          -pl nexs \
                          -Dsurefire.suiteXmlFiles=src/test/resources/nexs-EnsureMinimumInventory.xml \
                          -Denvironment=${env.DEFAULT_ENVIRONMENT} \
                          -Dmaven.test.failure.ignore=true \
                          -DskipTests=false \
                          -s ${env.WORKSPACE}/settings.xml
                    """.stripIndent()
                    echo "**************** Run Command ****************"
                    echo testCommand

                    sh testCommand
                }
            }
        }
    }

    post {
        success {
            echo '🎉 Build and tests completed successfully!'
            script {
                echo "✅ All tests passed"
                echo "📊 Test execution completed successfully"
            }
        }
        failure {
            echo '❌ Build or tests failed!'
        }
        unstable {
            echo '⚠️ Build is unstable (some tests failed)!'
        }
    }
}
