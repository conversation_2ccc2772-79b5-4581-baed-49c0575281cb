# NEXS Module Documentation

## Overview

The NEXS (Next-Generation eXecution System) module is a comprehensive order state management system designed to handle the complete lifecycle of orders from creation to dispatch. It implements a robust state machine pattern with dedicated helper classes for each state transition, ensuring reliable and traceable order processing.

## Architecture

### Core Components

1. **Order State Management**: Centralized state machine with 8 distinct order states
2. **State Transition Helpers**: Dedicated helper classes for each state transition
3. **Configuration Management**: Environment-specific configuration loading
4. **API Integration**: RESTful API calls for state transitions
5. **Context Management**: Comprehensive order context tracking

## Order States

The NEXS system manages orders through the following sequential states:

| State | Display Name | Description | Sequence |
|-------|-------------|-------------|----------|
| `CREATED` | Created | Order has been created and is awaiting processing | 1 |
| `PROCESSING` | Processing | Order is being processed by the system | 2 |
| `IN_PICKING` | In Picking | Order items are being picked from warehouse | 3 |
| `PICKED` | Picked | All order items have been picked successfully | 4 |
| `IN_QC` | In QC | Order is undergoing quality control checks | 5 |
| `QC_DONE` | QC Done | Quality control has been completed successfully | 6 |
| `INVOICED` | Invoiced | Invoice has been generated for the order | 7 |
| `DISPATCHED` | Dispatched | Order has been dispatched for delivery | 8 |

### State Transition Rules

- Orders can only move to the next sequential state
- Backward transitions are not allowed
- Each transition requires specific business logic validation
- State history is maintained for audit purposes

## Key Classes and Interfaces

### Core Models

#### `NexsOrderContext`
Central context object containing all order-related information:

```java
@Data
@Builder
public class NexsOrderContext {
    // Basic order information
    private String orderId;
    private String customerId;
    private String storeId;
    private String warehouseId;
    
    // Current state information
    private OrderState currentState;
    private LocalDateTime lastStateChange;
    
    // State transition history
    private List<StateTransitionRecord> stateHistory;
    
    // Processing, picking, QC, invoice, and dispatch information
    // ... (detailed fields for each stage)
    
    // Authentication headers
    private Headers headers;
}
```

#### `OrderState` Enum
Defines all possible order states with utility methods:

```java
public enum OrderState {
    CREATED("Created", "Order has been created and is awaiting processing", 1),
    // ... other states
    
    // Utility methods
    public Optional<OrderState> getNextState()
    public boolean canTransitionTo(OrderState targetState)
    public boolean isInitialState()
    public boolean isFinalState()
}
```

### State Transition Management

#### `OrderStateTransitionManager`
Central manager for handling all state transitions:

- Maps state transitions to helper classes
- Validates transition rules
- Executes transitions through appropriate helpers
- Maintains transition history

#### `StateTransitionHelper` Interface
Base interface for all state transition helpers:

```java
public interface StateTransitionHelper {
    void executeTransition();
    NexsOrderContext getOrderContext();
}
```

#### `BaseStateTransitionHelper`
Abstract base class providing common functionality:

- Validation logic
- Logging utilities
- Common error handling
- State transition validation

### State Transition Helpers

Each state transition has a dedicated helper class following the ServiceHelper pattern:

1. **`ProcessingHelper`**: CREATED → PROCESSING
2. **`PickingInitiationHelper`**: PROCESSING → IN_PICKING
3. **`PickingCompletionHelper`**: IN_PICKING → PICKED
4. **`QualityControlInitiationHelper`**: PICKED → IN_QC
5. **`QualityControlCompletionHelper`**: IN_QC → QC_DONE
6. **`InvoiceGenerationHelper`**: QC_DONE → INVOICED
7. **`DispatchHelper`**: INVOICED → DISPATCHED

Each helper implements the standard pattern:
- `init()`: Initialize headers and payload
- `process()`: Execute API calls
- `validate()`: Validate responses
- `test()`: Execute complete flow

### Order State Manager

#### `NexsOrderStateManager`
High-level orchestrator for order state management:

```java
@SuperBuilder
public class NexsOrderStateManager extends NexsBaseHelper implements ServiceHelper {
    
    // Single state transition
    public NexsOrderStateManager transitionToNextState()
    
    // Direct state transition
    public NexsOrderStateManager transitionToState(OrderState state)
    
    // Sequential transitions to final state
    public NexsOrderStateManager transitionToFinalState(OrderState finalState)
    
    // Check current state
    public boolean isInState(OrderState state)
}
```

## Configuration Management

### Configuration Structure

The module uses YAML-based configuration with environment-specific settings:

```yaml
# nexs.yml
preprod:
  baseUrls:
    nexsService: https://api-gateway.nexs.preprod.lenskart.com
    testService: https://jsonplaceholder.typicode.com
```

### Configuration Classes

- **`NexsConfig`**: Main configuration class
- **`NexsConfigLoader`**: Loads configuration from YAML files
- **`NexsConfigRegistry`**: Singleton registry for configuration access

## Usage Examples

### Basic Single State Transition

```java
// Create order context
NexsOrderContext orderContext = createSampleOrderContext("ORD-001", OrderState.CREATED);

// Transition to next state
NexsOrderStateManager stateManager = NexsOrderStateManager.builder()
    .orderContext(orderContext)
    .build()
    .transitionToNextState();
```

### Sequential Transitions to Final State

```java
// Create order context
NexsOrderContext orderContext = createSampleOrderContext("ORD-003", OrderState.CREATED);

// Transition through all states to reach DISPATCHED
NexsOrderStateManager stateManager = NexsOrderStateManager.builder()
    .orderContext(orderContext)
    .sequentialTransition(true)
    .build()
    .transitionToFinalState(OrderState.DISPATCHED);
```

### Orchestrated Flow with ServiceHelper Pattern

```java
// Create order context
NexsOrderContext orderContext = createSampleOrderContext("ORD-004", OrderState.CREATED);

// Use orchestration flow
NexsOrderStateManager stateManager = NexsOrderStateManager.builder()
    .orderContext(orderContext)
    .targetState(OrderState.INVOICED)
    .sequentialTransition(true)
    .build();

// Execute complete flow: init() → process() → validate()
stateManager.test();
```

## API Integration

### Endpoints
The module integrates with NEXS APIs through the `NexsEndpoints` enum (currently empty but designed for future endpoint definitions).

### Service Layer
- **`NexsService`**: Handles API communication
- **`NexsBaseHelper`**: Provides common header management and authentication

## Exception Handling

### Exception States
Defined in `NexsExceptionStates` enum:
- `X_DEVICE_HEADER_NOT_PASSED`
- `X_USER_ID_HEADER_NOT_PASSED`
- `X_MERCHANT_ID_HEADER_NOT_PASSED`

### Error Handling Strategy
- Comprehensive validation at each transition
- Detailed logging for debugging
- Graceful error recovery mechanisms
- State rollback capabilities (where applicable)

## Testing and Examples

### Example Classes
- **`NexsOrderStateManagerExamples`**: Comprehensive examples demonstrating various usage patterns
- Includes examples for single transitions, sequential transitions, orchestrated flows, and batch processing

### Test Categories
Tests are categorized using the `@TestCategory` annotation:
- `SANITY`: Basic functionality tests
- `REGRESSION`: Comprehensive regression tests
- `E2E`: End-to-end integration tests

## Dependencies

### Maven Dependencies
The module depends on:
- Commons module (shared utilities)
- RestAssured (API testing)
- Lombok (code generation)
- SLF4J (logging)
- Jackson (JSON processing)
- SnakeYAML (configuration parsing)

## Best Practices

### State Management
1. Always validate order context before transitions
2. Maintain comprehensive state history
3. Use sequential transitions for complete workflows
4. Implement proper error handling and rollback

### Configuration
1. Use environment-specific configurations
2. Cache configuration for performance
3. Implement configuration refresh mechanisms
4. Validate configuration on startup

### Testing
1. Test each state transition independently
2. Validate complete order workflows
3. Test error scenarios and edge cases
4. Use meaningful test data and assertions

## Future Enhancements

1. **Parallel Processing**: Support for concurrent order processing
2. **State Persistence**: Database integration for state persistence
3. **Event Sourcing**: Event-driven architecture for state changes
4. **Monitoring**: Enhanced monitoring and alerting capabilities
5. **API Expansion**: Complete API endpoint definitions and implementations

## Troubleshooting

### Common Issues
1. **State Transition Failures**: Check order context validation and API connectivity
2. **Configuration Issues**: Verify YAML syntax and environment settings
3. **Authentication Errors**: Ensure proper headers and tokens are set
4. **Validation Failures**: Review business rule implementations in helpers

### Debugging Tips
1. Enable debug logging for detailed execution traces
2. Use state history to track transition patterns
3. Validate API responses and payloads
4. Check configuration loading and caching

---

*This documentation provides a comprehensive overview of the NEXS module architecture, usage patterns, and best practices. For specific implementation details, refer to the source code and example classes.*
