# Test Categories

This document explains how to use test categories in the automation framework.

## Overview

Tests in the automation framework can be categorized into three types:

1. **SANITY** - Basic smoke tests that verify core functionality
2. **REGRESSION** - More comprehensive tests that verify all functionality
3. **E2E** - End-to-end tests that verify complete user flows

## How to Use Test Categories

### Annotating Tests

You can annotate your test methods or classes with the `@TestCategory` annotation:

```java
import com.lenskart.commons.annotations.TestCategory;
import org.testng.annotations.Test;

// Apply to a class - all methods inherit this category
@TestCategory(TestCategory.Category.REGRESSION)
public class MyTestClass {

    // This test inherits REGRESSION category from the class
    @Test
    public void testMethod1() {
        // Test code
    }
    
    // Override the class-level category for a specific method
    @Test
    @TestCategory(TestCategory.Category.SANITY)
    public void testMethod2() {
        // Test code
    }
}
```

### Running Tests by Category

#### From Maven Command Line

To run tests of a specific category:

```bash
mvn test -DtestCategory=SANITY
```

Valid values for `testCategory` are:
- `SANITY`
- `REGRESSION`
- `E2E`

If no category is specified, all tests will be run.

#### From Jenkins Pipeline

In the Jenkins pipeline, select the desired test category from the dropdown when triggering the build.

## Best Practices

1. **Class-Level Annotation**: Use class-level annotations for the default category of tests in that class.
2. **Method-Level Override**: Override the category at the method level when needed.
3. **Naming Convention**: Consider using a naming convention that reflects the category (e.g., `testSanityLogin()`, `testRegressionUserProfile()`).
4. **Documentation**: Document the purpose of each test category in your project.

## Example

```java
@TestCategory(TestCategory.Category.REGRESSION)
public class UserTests {

    @Test
    public void testUserLogin() {
        // This is a REGRESSION test
    }
    
    @Test
    @TestCategory(TestCategory.Category.SANITY)
    public void testBasicUserRegistration() {
        // This is a SANITY test
    }
    
    @Test
    @TestCategory(TestCategory.Category.E2E)
    public void testCompleteUserJourney() {
        // This is an E2E test
    }
}
```
