# NEXS Module

## Overview

The **NEXS (Next-Generation Execution System) Module** provides comprehensive automation testing capabilities for Lenskart's warehouse management and order fulfillment system, including order state management, picking operations, packing processes, quality control, and complete order lifecycle tracking.

## 🏗️ **Architecture**

### **Core Components**

- **Order State Management**: Complete order state transition automation
- **Picking Operations**: Warehouse picking workflow automation
- **Packing Processes**: Order packing and completion workflows
- **Quality Control**: QC operations and validation
- **Consolidation**: Order consolidation and shipment management
- **IMS Integration**: Inventory Management System integration
- **WMS Operations**: Warehouse Management System operations

## 📁 **Package Structure**

```
com.lenskart.nexs/
├── api/                 # External service API interfaces
├── base/                # Base classes for state transitions
├── config/              # NEXS-specific configuration management
├── constants/           # NEXS-specific constants and enums
├── database/            # Database queries and WMS operations
├── endpoints/           # NEXS API endpoints and endpoint manager
├── examples/            # Usage examples for NEXS operations
├── exceptions/          # NEXS-specific exception states
├── helpers/             # Service helper classes for NEXS operations
│   └── state/           # State-specific helper classes
├── manager/             # Order state transition managers
├── model/               # NEXS-specific data models
│   ├── fitting/         # Fitting-related models
│   ├── ims/             # IMS integration models
│   ├── orderqc/         # Order QC models
│   ├── packing/         # Packing operation models
│   └── picking/         # Picking operation models
├── requestBuilder/      # Request builders for NEXS API calls
└── requestbuilders/     # Additional request builders
```

## 🔧 **Key Features**

### **1. Order State Management**

#### **Complete Order State Transitions**
```
OrderStateTransitionManager stateManager = new OrderStateTransitionManager();

// Transition order through states
String orderId = "ORD123456";

// CREATED -> PROCESSING
stateManager.transitionToProcessing(orderId);

// PROCESSING -> IN_PICKING
stateManager.transitionToInPicking(orderId);

// IN_PICKING -> PICKED
stateManager.transitionToPicked(orderId);

// PICKED -> IN_QC
stateManager.transitionToInQc(orderId);

// IN_QC -> QC_DONE
stateManager.transitionToQcDone(orderId);

// QC_DONE -> INVOICED
stateManager.transitionToInvoiced(orderId);

// INVOICED -> DISPATCHED
stateManager.transitionToDispatched(orderId);
```

#### **State-Specific Operations**
```java
// Get current order state
NexsOrderStateHelper stateHelper = new NexsOrderStateHelper();
NexsOrderState currentState = stateHelper.getCurrentState(orderId);

// Validate state transition
boolean canTransition = stateHelper.canTransitionTo(orderId, NexsOrderState.IN_PICKING);

// Get order state history
List<StateTransition> history = stateHelper.getStateHistory(orderId);
```

### **2. Picking Operations**

#### **Picking Workflow**
```java
// Get picking details
PackingDetailsHelper packingHelper = new PackingDetailsHelper();
PickingDetails pickingDetails = packingHelper.getPickingDetails(orderId);

// Complete picking process
boolean pickingCompleted = packingHelper.completePickingProcess(orderId, pickingDetails);

// Validate picked items
boolean itemsValid = packingHelper.validatePickedItems(orderId, pickedItems);
```

### **3. Packing Operations**

#### **Packing Workflow**
```
PackingCompletionHelper packingHelper = new PackingCompletionHelper();

// Start packing process
PackingSession session = packingHelper.startPackingSession(orderId);

// Add items to package
packingHelper.addItemToPackage(session.getSessionId(), itemId, quantity);

// Complete packing
boolean packingCompleted = packingHelper.completePackingProcess(orderId, packingDetails);

// Generate shipping label
String shippingLabel = packingHelper.generateShippingLabel(orderId);
```

### **4. Order Completion**

#### **Order Completion Workflow**
```java
NexsOrderCompletionHelper completionHelper = new NexsOrderCompletionHelper();

// Complete order processing
boolean orderCompleted = completionHelper.completeOrder(orderId);

// Generate invoice
String invoiceId = completionHelper.generateInvoice(orderId);

// Prepare for dispatch
boolean readyForDispatch = completionHelper.prepareForDispatch(orderId);

// Mark as dispatched
boolean dispatched = completionHelper.markAsDispatched(orderId, trackingNumber);
```

### **5. Consolidation Operations**

#### **Order Consolidation**
```java
ConsolidationHelpers consolidationHelper = new ConsolidationHelpers();

// Get consolidation opportunities
List<String> consolidatableOrders = consolidationHelper.getConsolidatableOrders(customerId);

// Create consolidated shipment
String consolidationId = consolidationHelper.createConsolidatedShipment(orderIds);

// Get consolidation status
ConsolidationStatus status = consolidationHelper.getConsolidationStatus(consolidationId);
```

### **6. Shipment Management**

#### **Shipment Operations**
```java
GetShipmentsHelper shipmentHelper = new GetShipmentsHelper();

// Get shipment details
ShipmentDetails shipment = shipmentHelper.getShipmentDetails(shipmentId);

// Get shipments for order
List<ShipmentDetails> shipments = shipmentHelper.getShipmentsForOrder(orderId);

// Track shipment
TrackingInfo tracking = shipmentHelper.trackShipment(trackingNumber);
```

## 🌐 **API Endpoints**

### **NEXS Order Processing Endpoints**
```java
public enum NexsOrderProcessingEndpoints implements BaseEndpoint {
    // Order State Management
    GET_ORDER_STATE("/api/v1/orders/{orderId}/state", "nexsService", "GET", 
                   "Get current order state"),
    UPDATE_ORDER_STATE("/api/v1/orders/{orderId}/state", "nexsService", "PUT", 
                      "Update order state"),
    
    // Picking Operations
    GET_PICKING_DETAILS("/api/v1/orders/{orderId}/picking", "nexsService", "GET", 
                       "Get order picking details"),
    COMPLETE_PICKING("/api/v1/orders/{orderId}/picking/complete", "nexsService", "POST", 
                    "Complete picking process"),
    
    // Packing Operations
    START_PACKING("/api/v1/orders/{orderId}/packing/start", "nexsService", "POST", 
                 "Start packing process"),
    COMPLETE_PACKING("/api/v1/orders/{orderId}/packing/complete", "nexsService", "POST", 
                    "Complete packing process"),
    
    // Quality Control
    START_QC("/api/v1/orders/{orderId}/qc/start", "nexsService", "POST", 
            "Start quality control process"),
    COMPLETE_QC("/api/v1/orders/{orderId}/qc/complete", "nexsService", "POST", 
               "Complete quality control process"),
    
    // Order Completion
    GENERATE_INVOICE("/api/v1/orders/{orderId}/invoice", "nexsService", "POST", 
                    "Generate order invoice"),
    DISPATCH_ORDER("/api/v1/orders/{orderId}/dispatch", "nexsService", "POST", 
                  "Dispatch completed order"),
    
    // Consolidation
    GET_CONSOLIDATION_OPTIONS("/api/v1/consolidation/{customerId}", "nexsService", "GET", 
                             "Get order consolidation options"),
    CREATE_CONSOLIDATION("/api/v1/consolidation/create", "nexsService", "POST", 
                        "Create consolidated shipment");
}
```

## 🔧 **Configuration**

### **nexs.yml Configuration**
```yaml
nexs:
  baseUrls:
    nexsService: https://nexs.preprod.lenskart.com
    imsService: https://ims.preprod.lenskart.com
    wmsService: https://wms.preprod.lenskart.com
    
  authentication:
    nexsUser: "nexs_automation"
    nexsPassword: "NexsPass123!"
    sessionTimeout: 3600
    
  orderStates:
    supportedStates:
      - CREATED
      - PROCESSING
      - IN_PICKING
      - PICKED
      - IN_QC
      - QC_DONE
      - INVOICED
      - DISPATCHED
      - DELIVERED
      - CANCELLED
      - RETURNED
      
    skippableStates:
      - IN_QC  # QC can be skipped for certain order types
      
    stateTransitionTimeouts:
      PROCESSING: 300000      # 5 minutes
      IN_PICKING: 1800000     # 30 minutes
      PICKED: 600000          # 10 minutes
      IN_QC: 900000           # 15 minutes
      QC_DONE: 300000         # 5 minutes
      
  warehouse:
    defaultWarehouse: "WH001"
    pickingZones:
      - "ZONE_A"
      - "ZONE_B"
      - "ZONE_C"
    packingStations:
      - "PACK_01"
      - "PACK_02"
      - "PACK_03"
      
  qc:
    enableQcForPrescription: true
    enableQcForHighValue: true
    highValueThreshold: 15000
    qcMandatoryCategories:
      - "PRESCRIPTION_GLASSES"
      - "CONTACT_LENSES"
      - "SUNGLASSES_PREMIUM"
      
  consolidation:
    enableConsolidation: true
    maxConsolidationTime: 86400000  # 24 hours
    maxOrdersPerConsolidation: 5
    
  timeouts:
    apiTimeout: 30000
    pickingTimeout: 1800000
    packingTimeout: 900000
    qcTimeout: 900000
```

## 🧪 **Testing**

### **Test Categories**

#### **Sanity Tests**
```java
@Test
@TestCategory(TestCategory.Category.SANITY)
public void testOrderStateTransition() {
    NexsOrderStateHelper stateHelper = new NexsOrderStateHelper();
    
    String orderId = "ORD123456";
    NexsOrderState currentState = stateHelper.getCurrentState(orderId);
    
    // Test state transition
    boolean canTransition = stateHelper.canTransitionTo(orderId, NexsOrderState.IN_PICKING);
    assert canTransition;
}

@Test
@TestCategory(TestCategory.Category.SANITY)
public void testNexsAuthentication() {
    NexsAuthHelper authHelper = new NexsAuthHelper();
    String token = authHelper.authenticate();
    assert token != null && !token.isEmpty();
}
```

#### **Regression Tests**
```java
@Test
@TestCategory(TestCategory.Category.REGRESSION)
public void testCompleteOrderWorkflow() {
    OrderStateTransitionManager stateManager = new OrderStateTransitionManager();
    String orderId = "ORD123456";
    
    // Test complete order workflow
    stateManager.transitionToProcessing(orderId);
    stateManager.transitionToInPicking(orderId);
    stateManager.transitionToPicked(orderId);
    stateManager.transitionToInQc(orderId);
    stateManager.transitionToQcDone(orderId);
    stateManager.transitionToInvoiced(orderId);
    stateManager.transitionToDispatched(orderId);
    
    // Validate final state
    NexsOrderStateHelper stateHelper = new NexsOrderStateHelper();
    NexsOrderState finalState = stateHelper.getCurrentState(orderId);
    assert finalState == NexsOrderState.DISPATCHED;
}
```

### **Running NEXS Tests**
```bash
# Run all NEXS tests
mvn test -pl nexs

# Run specific test categories
mvn test -pl nexs -DtestCategory=SANITY

# Run with specific environment
mvn test -pl nexs -Denvironment=preprod

# Run specific test class
mvn test -pl nexs -Dtest=OrderStateTransitionTest
```

## 📊 **Data Models**

### **NEXS-Specific Models**
```java
// NEXS Order Context
@Data
public class NexsOrderContext {
    private String orderId;
    private NexsOrderState currentState;
    private String warehouseId;
    private String pickingZone;
    private String packingStation;
    private boolean qcRequired;
    private List<OrderItem> items;
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;
}

// Picking Models
@Data
public class PickingDetails {
    private String orderId;
    private String pickingZone;
    private String assignedPicker;
    private List<PickingItem> items;
    private PickingStatus status;
    private LocalDateTime startTime;
    private LocalDateTime completionTime;
}

// Packing Models
@Data
public class PackingDetails {
    private String orderId;
    private String packingStation;
    private String assignedPacker;
    private List<PackingItem> items;
    private PackingStatus status;
    private String packageId;
    private double packageWeight;
    private String shippingLabel;
}
```

## 🛠️ **Request Builders**

### **NEXS Request Builder**
```java
NexsRequestBuilder builder = new NexsRequestBuilder();

// Build state transition request
Map<String, Object> stateRequest = builder
    .withOrderId("ORD123456")
    .withTargetState(NexsOrderState.IN_PICKING)
    .withUserId("USER123")
    .buildStateTransitionRequest();

// Build picking completion request
Map<String, Object> pickingRequest = builder
    .withOrderId("ORD123456")
    .withPickedItems(pickedItems)
    .withPickingZone("ZONE_A")
    .buildPickingCompletionRequest();
```

## 🗄️ **Database Integration**

### **WMS Database Operations**
```java
WMSDbUtils wmsUtils = new WMSDbUtils();

// Get order details from WMS
Map<String, Object> orderData = wmsUtils.getOrderDetails("ORD123456");

// Get picking information
PickingInfo pickingInfo = wmsUtils.getPickingInfo("ORD123456");

// Get inventory status
InventoryStatus inventory = wmsUtils.getInventoryStatus("PROD123", "WH001");

// Update order state in database
boolean updated = wmsUtils.updateOrderState("ORD123456", NexsOrderState.PICKED);
```

### **Picking Queries**
```java
PickingQueries pickingQueries = new PickingQueries();

// Get pending picking orders
List<String> pendingOrders = pickingQueries.getPendingPickingOrders("ZONE_A");

// Get picker workload
int workload = pickingQueries.getPickerWorkload("PICKER123");

// Get picking performance metrics
PickingMetrics metrics = pickingQueries.getPickingMetrics("ZONE_A", date);
```

## 🔍 **Exception Handling**

### **NEXS Exception States**
```java
public enum NexsExceptionStates {
    ORDER_NOT_FOUND("NEXS_001", "Order not found in NEXS system"),
    INVALID_STATE_TRANSITION("NEXS_002", "Invalid order state transition"),
    PICKING_FAILED("NEXS_003", "Order picking process failed"),
    PACKING_FAILED("NEXS_004", "Order packing process failed"),
    QC_FAILED("NEXS_005", "Quality control process failed"),
    CONSOLIDATION_FAILED("NEXS_006", "Order consolidation failed"),
    WAREHOUSE_UNAVAILABLE("NEXS_007", "Warehouse system unavailable");
}
```

## 📈 **Performance Features**

- **State Transition Optimization**: Efficient state management with minimal database calls
- **Batch Processing**: Support for batch operations on multiple orders
- **Async Operations**: Asynchronous processing for long-running operations
- **Connection Pooling**: Optimized database connection management
- **Caching**: Cache frequently accessed order and inventory data

## 🔗 **Integration Points**

### **With Other Modules**
- **Commons**: Uses shared database connections and utilities
- **Juno**: Receives orders for fulfillment processing
- **SCM**: Integrates for supply chain coordination
- **Cosmos**: Provides order state updates for tracking

### **External Services**
- **IMS (Inventory Management System)**: Real-time inventory updates
- **WMS (Warehouse Management System)**: Warehouse operations integration
- **Shipping Partners**: Integration with logistics providers
- **Analytics Service**: Order fulfillment performance analytics

## 🚀 **Getting Started**

### **1. Add NEXS Dependency**
```xml
<dependency>
    <groupId>com.lenskart</groupId>
    <artifactId>nexs</artifactId>
    <version>1.0.0</version>
</dependency>
```

### **2. Configure NEXS Service**
Update `nexs.yml` with your NEXS service and warehouse configuration.

### **3. Create Your First Test**
```java
public class MyNexsTest {
    @Test
    @TestCategory(TestCategory.Category.SANITY)
    public void testOrderStateRetrieval() {
        NexsOrderStateHelper helper = new NexsOrderStateHelper();
        NexsOrderState state = helper.getCurrentState("ORD123456");
        assert state != null;
    }
}
```

## 📚 **Examples**

The `examples` package contains comprehensive usage examples:
- **NexsOrderStateManagerExamples**: Complete order state management workflows
- **Picking Operations**: Warehouse picking process examples
- **Packing Workflows**: Order packing and completion examples
- **Quality Control**: QC process automation examples
- **Consolidation**: Order consolidation and shipment examples

The NEXS module provides comprehensive automation capabilities for testing Lenskart's warehouse management and order fulfillment operations, ensuring efficient and reliable order processing from picking to dispatch.
