# POS Country-Specific Configuration Documentation

## Overview

The POS module now supports country-specific configurations for multiple POS users, enabling different authentication credentials and user management across various countries. This system allows each country to have multiple POS terminals with unique usernames, passwords, and store-specific configurations.

## Architecture

### Core Components

1. **Enhanced Configuration Models**: Extended `PosConfig` to support country-specific settings
2. **Country POS Configuration**: `CountryPosConfig` class for country-level settings
3. **POS User Management**: `PosUser` class for individual user configurations
4. **Configuration Registry**: Enhanced `PosConfigRegistry` for country-specific lookups
5. **Authentication Helper**: `PosCountryAuthHelper` for country-specific authentication
6. **User Management Utilities**: `PosUserManager` for advanced user operations

## Configuration Structure

### YAML Configuration Format

```yaml
preprod:
  baseUrls:
    sessionService: https://webservice.pos.preprod.lenskart.com
    webService: https://puno-webservice.juno.preprod.lenskart.com/puno-webservice
  
  countries:
    IN:  # India
      countryCode: "IN"
      countryName: "India"
      defaultCurrency: "INR"
      defaultLanguage: "en"
      defaultTimezone: "Asia/Kolkata"
      posUsers:
        - username: "pos_mumbai_001"
          password: "Mumbai@123"
          userId: "USR_IN_001"
          storeId: "STR_MUM_001"
          storeName: "Mumbai Central Store"
          storeLocation: "Mumbai, Maharashtra"
          firstName: "Rajesh"
          lastName: "Kumar"
          email: "<EMAIL>"
          phoneNumber: "+91-9876543210"
          role: "POS_OPERATOR"
          permissions:
            - "SALES_TRANSACTION"
            - "INVENTORY_VIEW"
            - "CUSTOMER_MANAGEMENT"
          active: true
          department: "Sales"
          region: "West"
          requiresPasswordChange: false
          maxConcurrentSessions: 2
```

### Supported Countries

Currently configured countries:
- **India (IN)**: 4 POS users across Mumbai, Delhi, Bangalore, Chennai
- **Singapore (SG)**: 2 POS users across Orchard and Marina Bay
- **United States (US)**: 2 POS users across New York and Los Angeles

## Configuration Classes

### PosConfig.CountryPosConfig

Country-level configuration containing:
- Country information (code, name, currency, language, timezone)
- List of POS users for the country
- Utility methods for user lookup

<augment_code_snippet path="pos/src/main/java/com/lenskart/pos/config/PosConfig.java" mode="EXCERPT">
````java
public static class CountryPosConfig {
    private String countryCode;
    private String countryName;
    private String defaultCurrency;
    private String defaultLanguage;
    private String defaultTimezone;
    private List<PosUser> posUsers;
    
    public PosUser getPosUser(String username)
    public PosUser getPosUserByStoreId(String storeId)
    public List<PosUser> getActivePosUsers()
}
````
</augment_code_snippet>

### PosConfig.PosUser

Individual POS user configuration containing:
- User identification (username, password, userId)
- Store information (storeId, storeName, storeLocation)
- User details (name, email, phone)
- Role and permissions
- Status and authentication settings

<augment_code_snippet path="pos/src/main/java/com/lenskart/pos/config/PosConfig.java" mode="EXCERPT">
````java
public static class PosUser {
    private String username;
    private String password;
    private String userId;
    private String storeId;
    private String storeName;
    private String role;
    private List<String> permissions;
    private boolean active = true;
    
    public boolean hasPermission(String permission)
    public boolean isValidForAuth()
    public String getFullName()
}
````
</augment_code_snippet>

## Usage Examples

### Basic Authentication by Country and Username

```java
// Authenticate specific user in India
PosCountryAuthHelper authHelper = PosCountryAuthHelper.forCountryAndUser(
    Countries.IN, 
    "pos_mumbai_001"
);

authHelper.test();
PosCountryAuthHelper.PosAuthResult result = authHelper.getAuthResult();
```

### Authentication by Store ID

```java
// Authenticate user by store ID in Singapore
PosCountryAuthHelper authHelper = PosCountryAuthHelper.forCountryAndStore(
    Countries.SG, 
    "STR_SG_001"
);

authHelper.test();
```

### First Available User Authentication

```java
// Authenticate with first available user in US
PosCountryAuthHelper authHelper = PosCountryAuthHelper.forCountry(Countries.US);
authHelper.test();
```

### User Management Operations

```java
// Get all active users for a country
List<PosConfig.PosUser> users = PosConfigRegistry.getInstance()
    .getActivePosUsers("IN");

// Find user by email across all countries
PosUserManager.UserSearchResult result = PosUserManager.findUserByEmail(
    "<EMAIL>"
);

// Get users by role across all countries
Map<String, List<PosConfig.PosUser>> supervisors = PosUserManager
    .getUsersByRole("POS_SUPERVISOR");

// Get recommended user for a country
PosConfig.PosUser recommended = PosUserManager.getRecommendedUser("IN");
```

## User Roles and Permissions

### Available Roles
- **POS_MANAGER**: Full access with user management capabilities
- **POS_SUPERVISOR**: Extended access with inventory management
- **POS_OPERATOR**: Basic sales and customer management access

### Available Permissions
- `SALES_TRANSACTION`: Process sales transactions
- `INVENTORY_VIEW`: View inventory information
- `INVENTORY_MANAGEMENT`: Manage inventory
- `CUSTOMER_MANAGEMENT`: Manage customer information
- `REPORTS_VIEW`: Access reports
- `USER_MANAGEMENT`: Manage other users

## Authentication Result

The authentication process returns a comprehensive result object:

```java
public static class PosAuthResult {
    private String sessionToken;
    private String userId;
    private String username;
    private String storeId;
    private String storeName;
    private String role;
    private String countryCode;
    private String countryName;
    private String currency;
    private String timezone;
    private List<String> permissions;
    private long authenticationTime;
    
    public boolean hasPermission(String permission)
    public String getFormattedAuthTime()
}
```

## Configuration Management

### Loading Configurations

The system automatically loads configurations from `pos.yml`:

```java
// Get country configuration
PosConfig.CountryPosConfig countryConfig = PosConfigRegistry.getInstance()
    .getCountryConfig("IN");

// Get specific user
PosConfig.PosUser user = PosConfigRegistry.getInstance()
    .getPosUser("IN", "pos_mumbai_001");
```

### Configuration Validation

```java
// Validate all user configurations
PosUserManager.ValidationReport report = PosUserManager.validateAllUsers();

if (report.isAllValid()) {
    log.info("All configurations are valid");
} else {
    log.warn("Found {} invalid configurations", report.invalidUsers);
}
```

## Advanced Features

### User Statistics

```java
// Get user statistics by country
Map<String, PosUserManager.UserStatistics> stats = PosUserManager.getUserStatistics();

for (Map.Entry<String, PosUserManager.UserStatistics> entry : stats.entrySet()) {
    log.info("Country {}: {}", entry.getKey(), entry.getValue());
}
```

### Regional and Departmental Filtering

```java
// Get users by region
List<PosConfig.PosUser> westUsers = PosUserManager.getUsersByRegion("IN", "West");

// Get users by department
List<PosConfig.PosUser> salesUsers = PosUserManager.getUsersByDepartment("IN", "Sales");
```

### Permission-Based Selection

```java
// Get users with specific permissions
Map<String, List<PosConfig.PosUser>> inventoryUsers = PosUserManager
    .getUsersByPermission("INVENTORY_MANAGEMENT");
```

## Best Practices

### Security
1. Use strong passwords for all POS users
2. Regularly rotate passwords
3. Limit concurrent sessions per user
4. Assign minimal required permissions

### Configuration Management
1. Keep country-specific settings consistent
2. Use meaningful usernames and store IDs
3. Maintain accurate user contact information
4. Regular validation of configurations

### Testing
1. Test authentication for each country
2. Validate permission-based access
3. Test user lookup operations
4. Verify configuration loading

## Error Handling

The system provides comprehensive error handling:

```java
try {
    PosCountryAuthHelper authHelper = PosCountryAuthHelper.forCountryAndUser(
        Countries.IN, "invalid_user"
    );
    authHelper.test();
} catch (IllegalStateException e) {
    log.error("Authentication failed: {}", e.getMessage());
}
```

## Integration with Existing Systems

The country-specific configuration integrates seamlessly with:
- Existing POS endpoints and services
- Commons module country definitions
- Base helper classes and authentication patterns
- Configuration registry patterns

## Future Enhancements

1. **Dynamic User Management**: Runtime user creation and modification
2. **Role-Based Access Control**: More granular permission system
3. **Multi-Store Support**: Users with access to multiple stores
4. **Session Management**: Advanced session tracking and management
5. **Audit Logging**: Comprehensive authentication and access logging

---

*This documentation provides a complete guide to implementing and using country-specific POS configurations. For specific implementation details, refer to the example classes and source code.*
