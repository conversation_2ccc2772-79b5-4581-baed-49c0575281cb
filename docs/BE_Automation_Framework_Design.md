# Detailed Design Document: BE Automation Framework

## 1. Executive Summary

The BE Automation Framework is a comprehensive, modular Java-based testing framework designed for service-level and end-to-end automated functional testing of backend services. The framework follows a multi-layered architecture with a strong emphasis on reusability, maintainability, and extensibility. It leverages industry-standard tools and libraries while implementing several design patterns to create a robust testing solution.

This document outlines the architecture, design patterns, module structure, and key features of the framework, highlighting how it can be efficiently used for testing complex distributed systems.

## 2. Architecture Overview

### 2.1 High-Level Architecture

The framework follows a modular architecture with the following key components:

1. **Commons Module**: Core reusable components, utilities, and base classes
2. **Service-Specific Modules**: Specialized modules for testing individual services (Juno, SCM, NEXS, CSE, POS)
3. **E2E Module**: End-to-end test orchestration across multiple services
4. **Example Module**: Reference implementation demonstrating framework usage

### 2.2 Key Architectural Principles

- **Separation of Concerns**: Clear separation between test data, test execution, and validation
- **Modularity**: Independent modules that can be used separately or together
- **Reusability**: Common components extracted into a shared module
- **Configurability**: Externalized configuration for different environments
- **Extensibility**: Easy to extend for new services or test scenarios

## 3. Module Structure and Functionality

### 3.1 Commons Module

The Commons module serves as the foundation of the framework, providing reusable components and utilities that are used across all other modules.

#### 3.1.1 Configuration Management

- **ConfigLoader**: Loads configuration from YAML files with environment-specific settings
- **ConfigRegistry**: Singleton registry that centralizes access to all configurations
- **Database Configurations**: Support for MySQL, MongoDB, Redis, and Elasticsearch
- **SSH Tunneling**: Secure connection to databases via SSH tunneling

#### 3.1.2 Database Connectivity

- **DatabaseConnectionManager**: Manages MySQL connections with connection pooling
- **MongoDBConnectionManager**: Manages MongoDB connections
- **RedisConnectionManager**: Manages Redis connections
- **ElasticsearchConnectionManager**: Manages Elasticsearch connections
- **QueryExecutor**: Executes SQL queries with type-safe result mapping
- **MongoDBQueryExecutor**: Executes MongoDB queries
- **RedisExecutor**: Executes Redis commands
- **ElasticsearchExecutor**: Executes Elasticsearch operations

#### 3.1.3 Base Classes and Interfaces

- **BaseHelper**: Abstract base class for service helpers
- **ServiceHelper**: Interface defining the test execution flow
- **ExecutionHelper**: Interface for orchestrating complex test flows
- **IValidator**: Interface for validation components
- **IRequestBuilder**: Interface for request building components

#### 3.1.4 Utilities

- **RestUtils**: Utilities for REST API testing using RestAssured
- **JsonUtils**: JSON parsing and manipulation utilities
- **LoggerUtils**: Logging utilities
- **SSHSessionUtil**: SSH tunneling utilities

#### 3.1.5 Reporting

- **BaseTestListener**: Base TestNG listener for test execution logging
- **ExtentReportListener**: Generates Extent Reports
- **AllureReportListener**: Generates Allure Reports

### 3.2 Service-Specific Modules

Each service module (Juno, SCM, NEXS, CSE, POS) follows a consistent structure:

#### 3.2.1 Configuration

- **ServiceConfigRegistry**: Service-specific configuration registry
- **Endpoints**: Enum of service endpoints with URL construction

#### 3.2.2 API Layer

- **ServiceAPI**: Classes for making API calls to the service

#### 3.2.3 Models

- **Request/Response Models**: POJO classes for API requests and responses
- **Context Models**: Test context classes for maintaining test state

#### 3.2.4 Helpers

- **ServiceHelpers**: Classes that orchestrate test flows for specific features

#### 3.2.5 Validators

- **Validators**: Classes for validating API responses and database state

#### 3.2.6 Request Builders

- **RequestBuilders**: Classes for building API request payloads

### 3.3 E2E Module

The E2E module orchestrates end-to-end tests across multiple services:

- **E2EHelpers**: Orchestrates flows across multiple services
- **E2EValidator**: Validates the end-to-end flow results
- **DataProviders**: Provides test data for E2E tests

### 3.4 Example Module

The Example module provides a reference implementation demonstrating how to use the framework:

- **GetDataHelper**: Example service helper
- **DiscoveryServiceAPI**: Example API client
- **GetDataValidator**: Example validator
- **CreateDataRequestHelper**: Example request builder
- **GetDiscoveryTest**: Example test class

## 4. Design Patterns Used

The framework implements several design patterns to promote good software engineering practices:

### 4.1 Singleton Pattern

Used in configuration management to ensure a single instance of configuration registries:

- **ConfigRegistry**: Singleton for centralized configuration access
- **Service-specific ConfigRegistries**: Singletons for service configurations

### 4.2 Builder Pattern

Used extensively with Lombok's `@Builder` and `@SuperBuilder` annotations:

- **BaseHelper and its subclasses**: For constructing helper objects
- **Request/Response models**: For creating request objects
- **Configuration objects**: For creating configuration instances

### 4.3 Factory Pattern

Used for creating database connections and other resources:

- **DatabaseConnectionManager**: Creates database connections
- **MongoDBConnectionManager**: Creates MongoDB clients
- **RedisConnectionManager**: Creates Redis connections

### 4.4 Template Method Pattern

Used in the test execution flow:

- **ServiceHelper interface**: Defines the template methods (`init()`, `process()`, `validate()`, `test()`)
- **BaseHelper**: Provides common implementation
- **Service-specific helpers**: Implement the specific steps

### 4.5 Strategy Pattern

Used for different validation strategies:

- **IValidator interface**: Defines the validation contract
- **Service-specific validators**: Implement different validation strategies

### 4.6 Facade Pattern

Used to simplify complex subsystems:

- **DatabaseConfigLoader**: Simplifies database configuration loading
- **MongoDBConfigLoader**: Simplifies MongoDB configuration loading
- **QueryExecutor**: Simplifies database query execution

### 4.7 Registry Pattern

Used for configuration management:

- **ConfigRegistry**: Central registry for all configurations
- **Service-specific registries**: Registries for service-specific configurations

## 5. Commons Module Reusability

The Commons module is designed for maximum reusability across different test modules:

### 5.1 Reusable Components

1. **Configuration Management**:
   - Unified configuration loading from YAML files
   - Environment-specific configuration support
   - Configuration caching for performance

2. **Database Connectivity**:
   - Connection pooling for efficient database access
   - SSH tunneling for secure database connections
   - Support for multiple database types (MySQL, MongoDB, Redis, Elasticsearch)

3. **Base Classes and Interfaces**:
   - Common test execution flow through ServiceHelper interface
   - Standardized validation through IValidator interface
   - Consistent request building through IRequestBuilder interface

4. **Utilities**:
   - REST API testing utilities with logging
   - JSON parsing and manipulation
   - Logging utilities for consistent logging

5. **Reporting**:
   - Integrated test reporting with Extent Reports and Allure

### 5.2 Reuse Mechanisms

1. **Dependency Injection**:
   - Service modules include Commons as a dependency
   - Helper classes extend BaseHelper for common functionality

2. **Interface Implementation**:
   - Service-specific components implement common interfaces
   - Ensures consistent behavior across modules

3. **Configuration Inheritance**:
   - Service-specific configurations extend base configurations
   - Allows for service-specific customization while maintaining consistency

4. **Utility Class Usage**:
   - Static utility methods for common operations
   - Consistent error handling and logging

## 6. Maintainability and Modularity

### 6.1 Maintainability Features

1. **Clear Separation of Concerns**:
   - API layer separate from test logic
   - Validation separate from test execution
   - Configuration separate from implementation

2. **Consistent Structure**:
   - All service modules follow the same structure
   - Common naming conventions across modules
   - Standardized package organization

3. **Comprehensive Logging**:
   - Detailed logging of test execution
   - Request/response logging for API calls
   - Error logging with stack traces

4. **Centralized Configuration**:
   - Configuration changes in one place
   - Environment-specific configurations
   - Easy to update for new environments

5. **Automated Reporting**:
   - Test results automatically captured
   - Visual reports for test execution
   - Failure analysis through detailed logs

### 6.2 Modularity Features

1. **Independent Modules**:
   - Each service module can be used independently
   - E2E module orchestrates across services
   - Commons module provides shared functionality

2. **Pluggable Components**:
   - New validators can be added without changing existing code
   - New request builders can be added for new API endpoints
   - New helpers can be added for new test flows

3. **Configurable Database Connections**:
   - Database connections configured through external configuration
   - Support for multiple database instances
   - Easy to add new database connections

4. **Flexible Test Execution**:
   - Tests can be run individually or as part of a suite
   - Environment-specific test execution
   - Parameterized tests through data providers

## 7. Implementation Advantages

### 7.1 Technical Advantages

1. **Robust Database Connectivity**:
   - Connection pooling for performance
   - SSH tunneling for security
   - Support for multiple database types

2. **Flexible Configuration**:
   - Environment-specific configuration
   - Runtime configuration changes
   - Centralized configuration management

3. **Comprehensive Reporting**:
   - Multiple reporting formats (Extent, Allure)
   - Detailed test execution logs
   - Visual representation of test results

4. **Type-Safe API Testing**:
   - Strongly-typed request/response models
   - Automatic JSON serialization/deserialization
   - Validation against expected types

5. **Efficient Resource Management**:
   - Connection pooling for database connections
   - Resource cleanup after test execution
   - Memory-efficient test execution

### 7.2 Business Advantages

1. **Reduced Test Maintenance**:
   - Changes to APIs require updates in fewer places
   - Common functionality maintained in one place
   - Consistent test structure across services

2. **Faster Test Development**:
   - Reusable components for common operations
   - Standardized test structure
   - Reference implementations for guidance

3. **Improved Test Coverage**:
   - End-to-end testing across services
   - Service-level testing for individual components
   - Database validation for data integrity

4. **Better Quality Assurance**:
   - Consistent validation across tests
   - Detailed reporting of test results
   - Comprehensive logging for troubleshooting

5. **Environment Flexibility**:
   - Tests can run against different environments
   - Configuration-driven environment selection
   - No code changes needed for environment switching

## 8. Framework Usage

### 8.1 Creating a New Test

1. **Define Test Context**:
   - Create a context class to hold test state
   - Define test parameters and expected results

2. **Create Request Builder**:
   - Implement IRequestBuilder interface
   - Build API request payload based on test context

3. **Create Service Helper**:
   - Extend BaseHelper and implement ServiceHelper
   - Implement test flow (init, process, validate)

4. **Create Validator**:
   - Implement IValidator interface
   - Validate API responses and database state

5. **Create Test Class**:
   - Use TestNG annotations for test definition
   - Use data providers for parameterized testing
   - Instantiate helper with builder pattern

### 8.2 Creating E2E Tests

1. **Define Order Context**:
   - Create a context class to hold order state
   - Track state changes across services

2. **Create E2E Helper**:
   - Orchestrate calls to service-specific helpers
   - Maintain test state across service boundaries

3. **Create E2E Validator**:
   - Validate end-to-end flow results
   - Check database state across services

4. **Create E2E Test Class**:
   - Define test flow using TestNG
   - Use data providers for different test scenarios

### 8.3 Running Tests

1. **Individual Service Tests**:
   - Run tests for a specific service module
   - Use TestNG XML files for test selection

2. **E2E Tests**:
   - Run end-to-end tests across services
   - Use TestNG XML files for test selection

3. **Docker Execution**:
   - Run tests in Docker container
   - Pass parameters for test selection
   - Generate reports in mounted volumes

## 9. Conclusion

The BE Automation Framework provides a comprehensive solution for testing backend services at both the service level and end-to-end. Its modular design, reusable components, and implementation of best practices make it a powerful tool for ensuring the quality of distributed systems.

The framework's key strengths include:

1. **Modularity**: Independent modules that can be used separately or together
2. **Reusability**: Common components extracted into a shared module
3. **Maintainability**: Clear structure and separation of concerns
4. **Extensibility**: Easy to extend for new services or test scenarios
5. **Configurability**: Externalized configuration for different environments

By leveraging this framework, testing teams can develop and maintain automated tests more efficiently, leading to better test coverage, faster test development, and improved software quality.
