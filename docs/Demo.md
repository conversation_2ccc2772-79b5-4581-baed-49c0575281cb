**🚀 Using OrderContext and NexsOrderContext in Automation Framework**

This document explains how to use `OrderContext` and `NexsOrderContext` in 🧪 test cases for 📦 order placement and ✅ order completion. These context objects act as holders for test data, enabling 🔁 flexibility, 👓 readability, and ♻️ reusability.

---

## 1️⃣ End-to-End Test with Data Provider

This test uses a 📊 data provider to run multiple 🛍️ order scenarios dynamically.

### 💡 Example:

```java
@Test(dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
public void createOrder(OrderContext orderContext) {
    log.info("Order context: {}", convertObjectToJsonString(orderContext));

    E2EHelpers.builder()
        .orderContext(orderContext)
        .build()
        .test();
}
```

### 🎯 Purpose:

- Runs full end-to-end 🧪 tests using various order data
- Fetches test data from `E2EDataProvider`

📝 Note: If this test fails in the `Nexs` part, use `nexsOrderCompletion()` to manually ✅ complete the order.

---



## 2️⃣ Order Placement using `OrderContext`

The `OrderContext` is used to simulate placing an order (e.g., via 📱 Juno).

### 💡 Example:

```java
@Test
public void junoOrderPlacement() {
    OrderContext orderContext = OrderContext.builder()
        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
            .country(Countries.IN)
            .pinCode("121004")
            .build())
        .headers(OrderContext.Headers.builder()
            .client(Client.ANDROID)
            .build())
        .phoneNumber("**********")
        .productLists(List.of(
            OrderContext.ProductList.builder()
                .productId("148248")
                .powerType(ZERO_POWER)
                .finalState(NexsOrderState.DISPATCHED)
                .build()))
        .paymentMethod(PaymentMethod.COD)
        .build();

    JunoOrderCreationHelper orderCreationHelper = JunoOrderCreationHelper.builder()
        .orderContext(orderContext)
        .build();

    orderCreationHelper.test();
}
```

### 🎯 Purpose:

- Simulates placing an order via the 📱 Juno app
- Customizes key parameters:
  - 🌍 Country and 📍 Pin Code
  - 💻 Client type (e.g., ANDROID)
  - 🧾 Product list
  - 💳 Payment method

### 📝 Notes:

- Makes it easy to plug in new test cases
- Uses `JunoOrderCreationHelper` to encapsulate the full 🛒 order placement process

---



## 3️⃣ Order Completion using `NexsOrderContext`

The `NexsOrderContext` is used to simulate the final 📦 delivery flow.

### 💡 Example:

```java
@Test(enabled = true)
public void nexsOrderCompletion() {
    NexsOrderContext nexsOrderContext = NexsOrderContext.builder()
        .shippingId("SNXS2260000004443208")
        // Optional: .incrementId("1930753269")
        .headers(NexsOrderContext.Headers.builder().build())
        .build();

    NexsOrderCompletionHelper.builder()
        .nexsOrderContext(nexsOrderContext)
        .build()
        .test();
}
```

### 🎯 Purpose:

- Simulates completion of an existing 🧾 order
- Accepts:
  - Either 📦 Shipping ID or 🧾 Increment ID (at least one is required)

### 📝 Notes:

- Handles state transitions like 🧺 picking → 📦 packing → 🚚 dispatch → 📬 delivery
- Great for 🔁 regression testing

---

## ✅ Best Practices

- Use the `builder()` pattern for clean and readable code
- Avoid 🔢 hardcoded values – use ⚙️ config files or 📊 data providers
- Reuse 🔄 utility methods for common logic

---

Using `OrderContext` and `NexsOrderContext` empowers your automation suite to simulate realistic order flows with enhanced clarity and flexibility. Happy testing! 🎉

