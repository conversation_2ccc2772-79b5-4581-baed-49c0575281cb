package com.lenskart.commons.examples;

import com.lenskart.commons.utils.AwaitUtils;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.Duration;

/**
 * Examples demonstrating how to use AwaitUtils in real automation scenarios
 */
@Slf4j
public class AwaitUtilsExamples {

    /**
     * Example 1: Waiting for API endpoint to become available
     */
    public static void waitForApiEndpoint() {
        log.info("=== Example: Waiting for API Endpoint ===");
        
        String apiUrl = "https://api.example.com/health";
        
        boolean isAvailable = AwaitUtils.waitForApiSuccess(
            () -> {
                try {
                    Response response = RestAssured.get(apiUrl);
                    return response.getStatusCode();
                } catch (Exception e) {
                    log.debug("API call failed: {}", e.getMessage());
                    return 500; // Return error status on exception
                }
            },
            "API endpoint to become available"
        );
        
        if (isAvailable) {
            log.info("✅ API endpoint is now available");
        } else {
            log.error("❌ API endpoint did not become available within timeout");
        }
    }

    /**
     * Example 2: Waiting for database record to be created
     */
    public static void waitForDatabaseRecord(Connection connection, String orderId) {
        log.info("=== Example: Waiting for Database Record ===");
        
        boolean recordExists = AwaitUtils.waitForDatabaseRecord(
            () -> {
                try (PreparedStatement stmt = connection.prepareStatement(
                    "SELECT COUNT(*) FROM orders WHERE order_id = ?")) {
                    
                    stmt.setString(1, orderId);
                    ResultSet rs = stmt.executeQuery();
                    
                    if (rs.next()) {
                        return rs.getInt(1) > 0;
                    }
                    return false;
                    
                } catch (Exception e) {
                    log.debug("Database check failed: {}", e.getMessage());
                    return false;
                }
            },
            "order record with ID: " + orderId
        );
        
        if (recordExists) {
            log.info("✅ Order record found in database");
        } else {
            log.error("❌ Order record not found within timeout");
        }
    }

    /**
     * Example 3: Waiting for order status to change
     */
    public static void waitForOrderStatusChange(Connection connection, String orderId, String expectedStatus) {
        log.info("=== Example: Waiting for Order Status Change ===");
        
        boolean statusUpdated = AwaitUtils.waitForDatabaseUpdate(
            () -> {
                try (PreparedStatement stmt = connection.prepareStatement(
                    "SELECT status FROM orders WHERE order_id = ?")) {
                    
                    stmt.setString(1, orderId);
                    ResultSet rs = stmt.executeQuery();
                    
                    if (rs.next()) {
                        return rs.getString("status");
                    }
                    return null;
                    
                } catch (Exception e) {
                    log.debug("Status check failed: {}", e.getMessage());
                    return null;
                }
            },
            expectedStatus,
            "order status for ID: " + orderId
        );
        
        if (statusUpdated) {
            log.info("✅ Order status updated to: {}", expectedStatus);
        } else {
            log.error("❌ Order status did not update to: {} within timeout", expectedStatus);
        }
    }

    /**
     * Example 4: Retry API call with exponential backoff
     */
    public static boolean retryApiCallWithBackoff(String apiUrl, String payload) {
        log.info("=== Example: Retry API Call with Backoff ===");
        
        return AwaitUtils.retryWithBackoff(
            () -> {
                try {
                    Response response = RestAssured.given()
                        .contentType("application/json")
                        .body(payload)
                        .post(apiUrl);
                    
                    int statusCode = response.getStatusCode();
                    log.debug("API call returned status: {}", statusCode);
                    
                    // Consider 2xx as success
                    return statusCode >= 200 && statusCode < 300;
                    
                } catch (Exception e) {
                    log.debug("API call failed: {}", e.getMessage());
                    return false;
                }
            },
            "API call to " + apiUrl,
            5, // max attempts
            Duration.ofSeconds(1), // initial delay
            Duration.ofSeconds(10) // max delay
        );
    }

    /**
     * Example 5: Wait for file processing to complete
     */
    public static void waitForFileProcessing(String inputFilePath, String outputFilePath) {
        log.info("=== Example: Waiting for File Processing ===");
        
        // First, wait for the input file to exist
        boolean inputExists = AwaitUtils.waitForFileToExist(inputFilePath);
        if (!inputExists) {
            log.error("❌ Input file not found: {}", inputFilePath);
            return;
        }
        
        log.info("✅ Input file found, waiting for processing to complete...");
        
        // Then wait for the output file to be created
        boolean outputCreated = AwaitUtils.waitForFileToExist(outputFilePath);
        if (outputCreated) {
            log.info("✅ File processing completed, output file created: {}", outputFilePath);
        } else {
            log.error("❌ File processing did not complete within timeout");
        }
    }

    /**
     * Example 6: Complex polling scenario with multiple conditions
     */
    public static void complexPollingScenario(String orderId) {
        log.info("=== Example: Complex Polling Scenario ===");
        
        // Step 1: Wait for order to be created
        boolean orderCreated = AwaitUtils.waitUntil(
            () -> checkOrderExists(orderId),
            "order to be created",
            Duration.ofMinutes(2)
        );
        
        if (!orderCreated) {
            log.error("❌ Order creation failed");
            return;
        }
        
        // Step 2: Wait for payment processing
        boolean paymentProcessed = AwaitUtils.waitUntil(
            () -> "PAYMENT_CONFIRMED".equals(getOrderStatus(orderId)),
            "payment to be processed",
            Duration.ofMinutes(5)
        );
        
        if (!paymentProcessed) {
            log.error("❌ Payment processing failed");
            return;
        }
        
        // Step 3: Wait for inventory allocation
        boolean inventoryAllocated = AwaitUtils.waitUntil(
            () -> "INVENTORY_ALLOCATED".equals(getOrderStatus(orderId)),
            "inventory to be allocated",
            Duration.ofMinutes(3)
        );
        
        if (!inventoryAllocated) {
            log.error("❌ Inventory allocation failed");
            return;
        }
        
        // Step 4: Wait for order fulfillment
        boolean orderFulfilled = AwaitUtils.waitUntilLong(
            () -> "FULFILLED".equals(getOrderStatus(orderId)),
            "order to be fulfilled"
        );
        
        if (orderFulfilled) {
            log.info("✅ Order processing completed successfully");
        } else {
            log.error("❌ Order fulfillment failed");
        }
    }

    /**
     * Example 7: Waiting with custom poll intervals for different scenarios
     */
    public static void customPollingIntervals() {
        log.info("=== Example: Custom Polling Intervals ===");
        
        // Fast polling for quick operations (every 100ms)
        boolean quickResult = AwaitUtils.waitUntil(
            () -> checkQuickOperation(),
            "quick operation to complete",
            Duration.ofSeconds(10),
            Duration.ofMillis(100)
        );
        
        // Slow polling for long-running operations (every 5 seconds)
        boolean slowResult = AwaitUtils.waitUntil(
            () -> checkLongRunningOperation(),
            "long-running operation to complete",
            Duration.ofMinutes(30),
            Duration.ofSeconds(5)
        );
        
        log.info("Quick operation result: {}", quickResult);
        log.info("Slow operation result: {}", slowResult);
    }

    /**
     * Example 8: Using AwaitUtils for test synchronization
     */
    public static void testSynchronizationExample() {
        log.info("=== Example: Test Synchronization ===");
        
        // Wait for test data to be set up
        AwaitUtils.waitFor(Duration.ofSeconds(2), "test data setup");
        
        // Perform test action
        performTestAction();
        
        // Wait for system to process the action
        boolean processed = AwaitUtils.waitUntil(
            () -> isActionProcessed(),
            "test action to be processed",
            Duration.ofSeconds(30)
        );
        
        if (processed) {
            // Verify results
            verifyTestResults();
        } else {
            log.error("❌ Test action was not processed within timeout");
        }
        
        // Clean up with a small delay
        AwaitUtils.waitFor(Duration.ofMillis(500), "cleanup delay");
    }

    // Helper methods (these would be implemented based on your specific use case)
    
    private static boolean checkOrderExists(String orderId) {
        // Implementation would check if order exists in database/API
        log.debug("Checking if order exists: {}", orderId);
        return true; // Placeholder
    }
    
    private static String getOrderStatus(String orderId) {
        // Implementation would fetch order status from database/API
        log.debug("Getting order status for: {}", orderId);
        return "PAYMENT_CONFIRMED"; // Placeholder
    }
    
    private static boolean checkQuickOperation() {
        // Implementation for quick operation check
        return true; // Placeholder
    }
    
    private static boolean checkLongRunningOperation() {
        // Implementation for long-running operation check
        return true; // Placeholder
    }
    
    private static void performTestAction() {
        log.info("Performing test action...");
        // Implementation for test action
    }
    
    private static boolean isActionProcessed() {
        // Implementation to check if action is processed
        return true; // Placeholder
    }
    
    private static void verifyTestResults() {
        log.info("Verifying test results...");
        // Implementation for result verification
    }

    /**
     * Main method to run examples
     */
    public static void main(String[] args) {
        log.info("🚀 Running AwaitUtils Examples");
        
        // Run examples (commented out to avoid actual API calls)
        // waitForApiEndpoint();
        // complexPollingScenario("ORDER-12345");
        customPollingIntervals();
        testSynchronizationExample();
        
        log.info("✅ AwaitUtils Examples completed");
    }
}
