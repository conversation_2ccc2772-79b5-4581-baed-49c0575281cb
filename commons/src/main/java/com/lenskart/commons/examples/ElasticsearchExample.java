package com.lenskart.commons.examples;

import com.lenskart.commons.database.elasticsearch.ElasticsearchExecutor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.xcontent.XContentType;

/**
 * Example class demonstrating how to use the Elasticsearch utilities
 */
@Slf4j
public class ElasticsearchExample {
    
    /**
     * Example of creating an index with mappings and settings
     * 
     * @param esName Elasticsearch instance name
     * @return true if index was created successfully
     */
    public static boolean createProductIndex(String esName) {
        String indexName = "products";
        
        // Define index settings
        String settings = "{\n" +
                "  \"number_of_shards\": 3,\n" +
                "  \"number_of_replicas\": 1,\n" +
                "  \"analysis\": {\n" +
                "    \"analyzer\": {\n" +
                "      \"custom_analyzer\": {\n" +
                "        \"type\": \"custom\",\n" +
                "        \"tokenizer\": \"standard\",\n" +
                "        \"filter\": [\"lowercase\", \"asciifolding\"]\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
        
        // Define index mappings
        String mappings = "{\n" +
                "  \"properties\": {\n" +
                "    \"id\": { \"type\": \"keyword\" },\n" +
                "    \"name\": { \"type\": \"text\", \"analyzer\": \"custom_analyzer\" },\n" +
                "    \"description\": { \"type\": \"text\", \"analyzer\": \"custom_analyzer\" },\n" +
                "    \"price\": { \"type\": \"float\" },\n" +
                "    \"category\": { \"type\": \"keyword\" },\n" +
                "    \"tags\": { \"type\": \"keyword\" },\n" +
                "    \"created_at\": { \"type\": \"date\" }\n" +
                "  }\n" +
                "}";
        
        return ElasticsearchExecutor.createIndex(esName, indexName, settings, mappings);
    }
    
    /**
     * Example of indexing a document
     * 
     * @param esName Elasticsearch instance name
     * @return ID of the indexed document
     */
    public static String indexProduct(String esName) {
        String indexName = "products";
        String typeName = "_doc";
        
        Map<String, Object> product = new HashMap<>();
        product.put("name", "Stylish Sunglasses");
        product.put("description", "UV protection sunglasses with polarized lenses");
        product.put("price", 129.99);
        product.put("category", "eyewear");
        product.put("tags", List.of("sunglasses", "polarized", "UV protection"));
        product.put("created_at", System.currentTimeMillis());
        
        return ElasticsearchExecutor.index(esName, indexName, typeName, null, product);
    }
    
    /**
     * Example of getting a document by ID
     * 
     * @param esName Elasticsearch instance name
     * @param productId Product ID
     * @return Product document as JSON string
     */
    public static String getProduct(String esName, String productId) {
        return ElasticsearchExecutor.get(esName, "products", "_doc", productId);
    }
    
    /**
     * Example of updating a document
     * 
     * @param esName Elasticsearch instance name
     * @param productId Product ID
     * @return true if document was updated successfully
     */
    public static boolean updateProduct(String esName, String productId) {
        Map<String, Object> updates = new HashMap<>();
        updates.put("price", 99.99);
        updates.put("tags", List.of("sunglasses", "polarized", "UV protection", "sale"));
        
        return ElasticsearchExecutor.update(esName, "products", "_doc", productId, updates);
    }
    
    /**
     * Example of searching for documents
     * 
     * @param esName Elasticsearch instance name
     * @param keyword Search keyword
     * @return Search results as JSON string
     */
    public static String searchProducts(String esName, String keyword) {
        String query = "{\n" +
                "  \"multi_match\": {\n" +
                "    \"query\": \"" + keyword + "\",\n" +
                "    \"fields\": [\"name^3\", \"description\"]\n" +
                "  }\n" +
                "}";
        
        return ElasticsearchExecutor.search(esName, "products", "_doc", query);
    }
    
    /**
     * Example of filtering products by category
     * 
     * @param esName Elasticsearch instance name
     * @param category Product category
     * @return Count of products in the category
     */
    public static long countProductsByCategory(String esName, String category) {
        String query = "{\n" +
                "  \"term\": {\n" +
                "    \"category\": \"" + category + "\"\n" +
                "  }\n" +
                "}";
        
        return ElasticsearchExecutor.count(esName, "products", "_doc", query);
    }
    
    /**
     * Example of deleting a document
     * 
     * @param esName Elasticsearch instance name
     * @param productId Product ID
     * @return true if document was deleted successfully
     */
    public static boolean deleteProduct(String esName, String productId) {
        return ElasticsearchExecutor.delete(esName, "products", "_doc", productId);
    }
    
    /**
     * Example of bulk operations
     * 
     * @param esName Elasticsearch instance name
     * @return true if bulk operation was successful
     */
    public static boolean bulkIndexProducts(String esName) {
        List<Object> actions = new ArrayList<>();
        
        // Create first product
        Map<String, Object> product1 = new HashMap<>();
        product1.put("name", "Reading Glasses");
        product1.put("description", "Comfortable reading glasses with blue light filter");
        product1.put("price", 79.99);
        product1.put("category", "eyewear");
        product1.put("tags", List.of("reading", "blue light", "comfort"));
        product1.put("created_at", System.currentTimeMillis());
        
        IndexRequest indexRequest1 = new IndexRequest("products")
                .type("_doc")
                .id("bulk-1")
                .source(product1);
        actions.add(indexRequest1);
        
        // Create second product
        Map<String, Object> product2 = new HashMap<>();
        product2.put("name", "Contact Lenses");
        product2.put("description", "Monthly disposable contact lenses");
        product2.put("price", 45.99);
        product2.put("category", "contacts");
        product2.put("tags", List.of("contacts", "monthly", "disposable"));
        product2.put("created_at", System.currentTimeMillis());
        
        IndexRequest indexRequest2 = new IndexRequest("products")
                .type("_doc")
                .id("bulk-2")
                .source(product2);
        actions.add(indexRequest2);
        
        // Delete a product (if it exists)
        DeleteRequest deleteRequest = new DeleteRequest("products", "_doc", "bulk-old");
        actions.add(deleteRequest);
        
        return ElasticsearchExecutor.bulk(esName, actions);
    }
    
    /**
     * Example of checking if an index exists and deleting it
     * 
     * @param esName Elasticsearch instance name
     */
    public static void cleanupIndex(String esName) {
        String indexName = "products";
        
        if (ElasticsearchExecutor.indexExists(esName, indexName)) {
            log.info("Index {} exists, deleting it", indexName);
            boolean deleted = ElasticsearchExecutor.deleteIndex(esName, indexName);
            log.info("Index deletion result: {}", deleted);
        } else {
            log.info("Index {} does not exist", indexName);
        }
    }
    
    /**
     * Run a complete example workflow
     * 
     * @param esName Elasticsearch instance name
     */
    public static void runCompleteExample(String esName) {
        try {
            // Clean up any existing index
            cleanupIndex(esName);
            
            // Create a new index
            log.info("Creating index: {}", createProductIndex(esName));
            
            // Index a document
            String productId = indexProduct(esName);
            log.info("Indexed product with ID: {}", productId);
            
            // Get the document
            String product = getProduct(esName, productId);
            log.info("Retrieved product: {}", product);
            
            // Update the document
            log.info("Updated product: {}", updateProduct(esName, productId));
            
            // Search for products
            log.info("Search results: {}", searchProducts(esName, "sunglasses"));
            
            // Count products by category
            log.info("Eyewear products count: {}", countProductsByCategory(esName, "eyewear"));
            
            // Bulk index products
            log.info("Bulk index result: {}", bulkIndexProducts(esName));
            
            // Delete a product
            log.info("Deleted product: {}", deleteProduct(esName, productId));
            
        } catch (Exception e) {
            log.error("Error in Elasticsearch example: {}", e.getMessage(), e);
        }
    }
}