package com.lenskart.commons.examples;

import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Example demonstrating how to use RestUtils multipart file upload functionality
 */
@Slf4j
public class RestUtilsMultipartExample {

    /**
     * Example 1: Basic file upload matching the provided curl command
     */
    public static void basicFileUploadExample() {
        log.info("=== Example 1: Basic File Upload ===");
        
        // Create headers matching the curl command
        Map<String, String> headers = new HashMap<>();
        headers.put("x-api-client", "pos_web");
        headers.put("x-lenskart-api-key", "valyoo123");
        headers.put("x-lenskart-app-id", "connect");
        
        // URL from the curl command
        String url = "https://webservice.pos.preprod.lenskart.com/v1/bulk";
        
        // File path (you would replace this with your actual file path)
        String filePath = "/path/to/your/bulk_1749192132170.csv";
        
        try {
            // Basic file upload - matches the provided curl command exactly
            Response response = RestUtils.postWithMultipartFile(
                url,
                headers,
                filePath,
                "file"  // Field name for the file
            );
            
            log.info("Upload successful! Status: {}", response.getStatusCode());
            log.info("Response: {}", response.getBody().asString());
            
        } catch (Exception e) {
            log.error("Upload failed: {}", e.getMessage());
        }
        
        log.info("✅ Basic file upload example completed");
    }

    /**
     * Example 2: File upload with additional query parameters
     */
    public static void fileUploadWithQueryParamsExample() {
        log.info("=== Example 2: File Upload with Query Parameters ===");
        
        Map<String, String> headers = new HashMap<>();
        headers.put("x-api-client", "pos_web");
        headers.put("x-lenskart-api-key", "valyoo123");
        headers.put("x-lenskart-app-id", "connect");
        
        // Add query parameters
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("batch_id", "BATCH_" + System.currentTimeMillis());
        queryParams.put("source", "automation");
        queryParams.put("validate_only", "false");
        
        String url = "https://webservice.pos.preprod.lenskart.com/v1/bulk";
        String filePath = "/path/to/your/bulk_data.csv";
        
        try {
            Response response = RestUtils.postWithMultipartFile(
                url,
                headers,
                queryParams,
                filePath,
                "file",
                null  // No additional form data
            );
            
            log.info("Upload with query params successful! Status: {}", response.getStatusCode());
            
        } catch (Exception e) {
            log.error("Upload with query params failed: {}", e.getMessage());
        }
        
        log.info("✅ File upload with query parameters example completed");
    }

    /**
     * Example 3: File upload with additional form data
     */
    public static void fileUploadWithFormDataExample() {
        log.info("=== Example 3: File Upload with Additional Form Data ===");
        
        Map<String, String> headers = new HashMap<>();
        headers.put("x-api-client", "pos_web");
        headers.put("x-lenskart-api-key", "valyoo123");
        headers.put("x-lenskart-app-id", "connect");
        
        // Add additional form data
        Map<String, Object> formData = new HashMap<>();
        formData.put("description", "Bulk product upload from automation");
        formData.put("category", "electronics");
        formData.put("priority", "high");
        formData.put("notify_on_completion", "true");
        
        String url = "https://webservice.pos.preprod.lenskart.com/v1/bulk";
        String filePath = "/path/to/your/products.csv";
        
        try {
            Response response = RestUtils.postWithMultipartFile(
                url,
                headers,
                null,  // No query parameters
                filePath,
                "file",
                formData
            );
            
            log.info("Upload with form data successful! Status: {}", response.getStatusCode());
            
        } catch (Exception e) {
            log.error("Upload with form data failed: {}", e.getMessage());
        }
        
        log.info("✅ File upload with form data example completed");
    }

    /**
     * Example 4: File upload with expected status code validation
     */
    public static void fileUploadWithStatusValidationExample() {
        log.info("=== Example 4: File Upload with Status Code Validation ===");
        
        Map<String, String> headers = new HashMap<>();
        headers.put("x-api-client", "pos_web");
        headers.put("x-lenskart-api-key", "valyoo123");
        headers.put("x-lenskart-app-id", "connect");
        
        String url = "https://webservice.pos.preprod.lenskart.com/v1/bulk";
        String filePath = "/path/to/your/validated_data.csv";
        
        try {
            // Upload with expected status code - will throw exception if status doesn't match
            Response response = RestUtils.postWithMultipartFile(
                url,
                headers,
                filePath,
                "file",
                200  // Expected status code
            );
            
            log.info("Upload successful with expected status! Status: {}", response.getStatusCode());
            
        } catch (RuntimeException e) {
            log.error("Upload failed - unexpected status code: {}", e.getMessage());
        } catch (Exception e) {
            log.error("Upload failed: {}", e.getMessage());
        }
        
        log.info("✅ File upload with status validation example completed");
    }

    /**
     * Example 5: Complete file upload with all parameters
     */
    public static void completeFileUploadExample() {
        log.info("=== Example 5: Complete File Upload with All Parameters ===");
        
        Map<String, String> headers = new HashMap<>();
        headers.put("x-api-client", "pos_web");
        headers.put("x-lenskart-api-key", "valyoo123");
        headers.put("x-lenskart-app-id", "connect");
        headers.put("Content-Type", "multipart/form-data");  // Optional - RestAssured handles this
        
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("batch_id", "COMPLETE_BATCH_" + System.currentTimeMillis());
        queryParams.put("async", "true");
        
        Map<String, Object> formData = new HashMap<>();
        formData.put("description", "Complete bulk upload example");
        formData.put("category", "test_data");
        formData.put("created_by", "automation_user");
        formData.put("timestamp", System.currentTimeMillis());
        
        String url = "https://webservice.pos.preprod.lenskart.com/v1/bulk";
        String filePath = "/path/to/your/complete_data.csv";
        
        try {
            Response response = RestUtils.postWithMultipartFile(
                url,
                headers,
                queryParams,
                filePath,
                "file",
                formData,
                201  // Expected status code for creation
            );
            
            log.info("Complete upload successful! Status: {}", response.getStatusCode());
            log.info("Response headers: {}", response.getHeaders());
            log.info("Response body: {}", response.getBody().asString());
            
            // Extract useful information from response
            String batchId = response.jsonPath().getString("batch_id");
            String status = response.jsonPath().getString("status");
            
            log.info("Batch ID: {}, Status: {}", batchId, status);
            
        } catch (Exception e) {
            log.error("Complete upload failed: {}", e.getMessage());
        }
        
        log.info("✅ Complete file upload example completed");
    }

    /**
     * Example 6: Creating and uploading a CSV file dynamically
     */
    public static void dynamicCsvUploadExample() {
        log.info("=== Example 6: Dynamic CSV Creation and Upload ===");
        
        try {
            // Create a CSV file dynamically
            String csvFilePath = createSampleCsvFile();
            
            Map<String, String> headers = new HashMap<>();
            headers.put("x-api-client", "pos_web");
            headers.put("x-lenskart-api-key", "valyoo123");
            headers.put("x-lenskart-app-id", "connect");
            
            String url = "https://webservice.pos.preprod.lenskart.com/v1/bulk";
            
            Response response = RestUtils.postWithMultipartFile(
                url,
                headers,
                csvFilePath,
                "file"
            );
            
            log.info("Dynamic CSV upload successful! Status: {}", response.getStatusCode());
            
            // Clean up the temporary file
            new File(csvFilePath).delete();
            log.info("Temporary CSV file cleaned up");
            
        } catch (Exception e) {
            log.error("Dynamic CSV upload failed: {}", e.getMessage());
        }
        
        log.info("✅ Dynamic CSV upload example completed");
    }

    /**
     * Helper method to create a sample CSV file
     */
    private static String createSampleCsvFile() throws IOException {
        File tempFile = File.createTempFile("bulk_upload_", ".csv");
        String filePath = tempFile.getAbsolutePath();
        
        try (FileWriter writer = new FileWriter(tempFile)) {
            // Write CSV header
            writer.write("product_id,product_name,category,price,stock_quantity\n");
            
            // Write sample data
            for (int i = 1; i <= 10; i++) {
                writer.write(String.format("%d,Product %d,Category %d,%.2f,%d\n", 
                    i, i, (i % 3) + 1, 99.99 + i, 100 + i));
            }
        }
        
        log.info("Created sample CSV file: {} (size: {} bytes)", filePath, tempFile.length());
        return filePath;
    }

    /**
     * Example 7: Error handling and retry logic
     */
    public static void errorHandlingExample() {
        log.info("=== Example 7: Error Handling and Retry Logic ===");
        
        Map<String, String> headers = new HashMap<>();
        headers.put("x-api-client", "pos_web");
        headers.put("x-lenskart-api-key", "valyoo123");
        headers.put("x-lenskart-app-id", "connect");
        
        String url = "https://webservice.pos.preprod.lenskart.com/v1/bulk";
        String filePath = "/path/to/your/data.csv";
        
        int maxRetries = 3;
        int retryCount = 0;
        
        while (retryCount < maxRetries) {
            try {
                log.info("Upload attempt {} of {}", retryCount + 1, maxRetries);
                
                Response response = RestUtils.postWithMultipartFile(
                    url,
                    headers,
                    filePath,
                    "file",
                    200
                );
                
                log.info("Upload successful on attempt {}", retryCount + 1);
                break;
                
            } catch (RuntimeException e) {
                retryCount++;
                log.warn("Upload attempt {} failed: {}", retryCount, e.getMessage());
                
                if (retryCount >= maxRetries) {
                    log.error("Upload failed after {} attempts", maxRetries);
                    throw e;
                }
                
                // Wait before retry
                try {
                    Thread.sleep(2000 * retryCount); // Exponential backoff
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        log.info("✅ Error handling example completed");
    }

    /**
     * Main method to run all examples
     */
    public static void main(String[] args) {
        log.info("🚀 Running RestUtils Multipart File Upload Examples");
        
        // Note: These examples will fail without actual files and valid credentials
        // but demonstrate the API usage patterns
        
        basicFileUploadExample();
        fileUploadWithQueryParamsExample();
        fileUploadWithFormDataExample();
        fileUploadWithStatusValidationExample();
        completeFileUploadExample();
        dynamicCsvUploadExample();
        errorHandlingExample();
        
        log.info("✅ RestUtils Multipart File Upload Examples completed");
    }
}
