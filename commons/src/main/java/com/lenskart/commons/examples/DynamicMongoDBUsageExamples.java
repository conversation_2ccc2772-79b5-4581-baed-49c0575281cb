package com.lenskart.commons.examples;

import com.lenskart.commons.database.mongodb.MongoDBConnectionManager;
import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import com.mongodb.client.result.InsertOneResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.Arrays;
import java.util.List;

/**
 * Examples demonstrating how to use the Dynamic MongoDB multi-cluster functionality
 */
@Slf4j
public class DynamicMongoDBUsageExamples {

    /**
     * Example 1: Basic document operations across multiple clusters
     */
    public static void basicDocumentOperationsExample() {
        log.info("=== Example 1: Basic Document Operations ===");
        
        try {
            // Find user documents from user cluster
            List<Document> users = MongoDBQueryExecutor.find(
                "user_cluster", 
                "userdb", 
                "users", 
                new Document("status", "active")
            );
            log.info("Found {} active users", users.size());
            
            // Find inventory items from inventory cluster
            List<Document> products = MongoDBQueryExecutor.find(
                "inventory_cluster", 
                "inventory", 
                "products", 
                new Document("category", "eyewear").append("stock", new Document("$gt", 0))
            );
            log.info("Found {} eyewear products in stock", products.size());
            
            // Get analytics data from analytics cluster
            Document salesSummary = MongoDBQueryExecutor.findOne(
                "analytics_cluster", 
                "analytics", 
                "daily_sales", 
                new Document("date", "2024-01-15")
            );
            if (salesSummary != null) {
                log.info("Sales summary for 2024-01-15: {}", salesSummary.toJson());
            }
            
        } catch (Exception e) {
            log.error("Error in basic document operations example: {}", e.getMessage());
        }
        
        log.info("✅ Basic document operations example completed");
    }

    /**
     * Example 2: Document insertion across clusters
     */
    public static void documentInsertionExample() {
        log.info("=== Example 2: Document Insertion ===");
        
        try {
            // Insert new user in user cluster
            Document newUser = new Document("userId", "USER12345")
                    .append("email", "<EMAIL>")
                    .append("firstName", "John")
                    .append("lastName", "Doe")
                    .append("status", "active")
                    .append("createdAt", new java.util.Date());
            
            InsertOneResult userResult = MongoDBQueryExecutor.insertOne(
                "user_cluster", 
                "userdb", 
                "users", 
                newUser
            );
            log.info("Inserted user with ID: {}", userResult.getInsertedId());
            
            // Insert order in orders cluster
            Document newOrder = new Document("orderId", "ORD67890")
                    .append("userId", "USER12345")
                    .append("items", Arrays.asList(
                        new Document("productId", "PROD001").append("quantity", 2),
                        new Document("productId", "PROD002").append("quantity", 1)
                    ))
                    .append("totalAmount", 299.99)
                    .append("status", "pending")
                    .append("createdAt", new java.util.Date());
            
            InsertOneResult orderResult = MongoDBQueryExecutor.insertOne(
                "orders_cluster", 
                "orders", 
                "orders", 
                newOrder
            );
            log.info("Inserted order with ID: {}", orderResult.getInsertedId());
            
            // Insert multiple inventory updates
            List<Document> inventoryUpdates = Arrays.asList(
                new Document("productId", "PROD001")
                    .append("action", "stock_update")
                    .append("quantity", -2)
                    .append("timestamp", new java.util.Date()),
                new Document("productId", "PROD002")
                    .append("action", "stock_update")
                    .append("quantity", -1)
                    .append("timestamp", new java.util.Date())
            );
            
            MongoDBQueryExecutor.insertMany(
                "inventory_cluster", 
                "inventory", 
                "stock_movements", 
                inventoryUpdates
            );
            log.info("Inserted {} inventory updates", inventoryUpdates.size());
            
        } catch (Exception e) {
            log.error("Error in document insertion example: {}", e.getMessage());
        }
        
        log.info("✅ Document insertion example completed");
    }

    /**
     * Example 3: Document updates across clusters
     */
    public static void documentUpdateExample() {
        log.info("=== Example 3: Document Updates ===");
        
        try {
            // Update user profile in user cluster
            Document userFilter = new Document("userId", "USER12345");
            Document userUpdate = new Document("$set", 
                new Document("lastLoginAt", new java.util.Date())
                    .append("loginCount", new Document("$inc", 1))
            );
            
            UpdateResult userUpdateResult = MongoDBQueryExecutor.updateOne(
                "user_cluster", 
                "userdb", 
                "users", 
                userFilter, 
                userUpdate
            );
            log.info("Updated user. Matched: {}, Modified: {}", 
                    userUpdateResult.getMatchedCount(), userUpdateResult.getModifiedCount());
            
            // Update order status in orders cluster
            Document orderFilter = new Document("orderId", "ORD67890");
            Document orderUpdate = new Document("$set", 
                new Document("status", "processing")
                    .append("updatedAt", new java.util.Date())
            );
            
            UpdateResult orderUpdateResult = MongoDBQueryExecutor.updateOne(
                "orders_cluster", 
                "orders", 
                "orders", 
                orderFilter, 
                orderUpdate
            );
            log.info("Updated order. Matched: {}, Modified: {}", 
                    orderUpdateResult.getMatchedCount(), orderUpdateResult.getModifiedCount());
            
            // Update inventory stock in inventory cluster
            Document inventoryFilter = new Document("productId", "PROD001");
            Document inventoryUpdate = new Document("$inc", 
                new Document("stockQuantity", -2)
            ).append("$set", 
                new Document("lastUpdated", new java.util.Date())
            );
            
            UpdateResult inventoryUpdateResult = MongoDBQueryExecutor.updateOne(
                "inventory_cluster", 
                "inventory", 
                "products", 
                inventoryFilter, 
                inventoryUpdate
            );
            log.info("Updated inventory. Matched: {}, Modified: {}", 
                    inventoryUpdateResult.getMatchedCount(), inventoryUpdateResult.getModifiedCount());
            
        } catch (Exception e) {
            log.error("Error in document update example: {}", e.getMessage());
        }
        
        log.info("✅ Document update example completed");
    }

    /**
     * Example 4: Aggregation operations across clusters
     */
    public static void aggregationExample() {
        log.info("=== Example 4: Aggregation Operations ===");
        
        try {
            // User analytics aggregation
            List<Document> userPipeline = Arrays.asList(
                new Document("$match", new Document("status", "active")),
                new Document("$group", new Document("_id", "$country")
                    .append("userCount", new Document("$sum", 1))
                    .append("avgAge", new Document("$avg", "$age"))
                ),
                new Document("$sort", new Document("userCount", -1))
            );
            
            List<Document> userStats = MongoDBQueryExecutor.aggregate(
                "user_cluster", 
                "userdb", 
                "users", 
                userPipeline
            );
            log.info("User statistics by country: {}", userStats.size());
            for (Document stat : userStats) {
                log.info("Country: {}, Users: {}, Avg Age: {}", 
                        stat.getString("_id"), 
                        stat.getInteger("userCount"), 
                        stat.getDouble("avgAge"));
            }
            
            // Sales analytics aggregation
            List<Document> salesPipeline = Arrays.asList(
                new Document("$match", new Document("date", 
                    new Document("$gte", "2024-01-01").append("$lt", "2024-02-01")
                )),
                new Document("$group", new Document("_id", "$category")
                    .append("totalSales", new Document("$sum", "$amount"))
                    .append("orderCount", new Document("$sum", 1))
                ),
                new Document("$sort", new Document("totalSales", -1))
            );
            
            List<Document> salesStats = MongoDBQueryExecutor.aggregate(
                "analytics_cluster", 
                "analytics", 
                "sales", 
                salesPipeline
            );
            log.info("Sales statistics by category: {}", salesStats.size());
            for (Document stat : salesStats) {
                log.info("Category: {}, Total Sales: {}, Orders: {}", 
                        stat.getString("_id"), 
                        stat.getDouble("totalSales"), 
                        stat.getInteger("orderCount"));
            }
            
        } catch (Exception e) {
            log.error("Error in aggregation example: {}", e.getMessage());
        }
        
        log.info("✅ Aggregation example completed");
    }

    /**
     * Example 5: Cross-cluster data analysis
     */
    public static void crossClusterAnalysisExample() {
        log.info("=== Example 5: Cross-Cluster Data Analysis ===");
        
        try {
            // Get user count from user cluster
            long userCount = MongoDBQueryExecutor.count(
                "user_cluster", 
                "userdb", 
                "users", 
                new Document("status", "active")
            );
            
            // Get order count from orders cluster
            long orderCount = MongoDBQueryExecutor.count(
                "orders_cluster", 
                "orders", 
                "orders", 
                new Document("status", new Document("$in", Arrays.asList("pending", "processing")))
            );
            
            // Get product count from inventory cluster
            long productCount = MongoDBQueryExecutor.count(
                "inventory_cluster", 
                "inventory", 
                "products", 
                new Document("stock", new Document("$gt", 0))
            );
            
            // Get catalog count from catalog cluster
            long catalogCount = MongoDBQueryExecutor.count(
                "catalog_cluster", 
                "catalog", 
                "products", 
                new Document("status", "published")
            );
            
            log.info("=== Cross-Cluster Summary ===");
            log.info("Active Users: {}", userCount);
            log.info("Pending/Processing Orders: {}", orderCount);
            log.info("Products in Stock: {}", productCount);
            log.info("Published Catalog Items: {}", catalogCount);
            
            // Calculate some business metrics
            if (userCount > 0) {
                double ordersPerUser = (double) orderCount / userCount;
                log.info("Orders per User: {:.2f}", ordersPerUser);
            }
            
        } catch (Exception e) {
            log.error("Error in cross-cluster analysis example: {}", e.getMessage());
        }
        
        log.info("✅ Cross-cluster analysis example completed");
    }

    /**
     * Example 6: Connection validation and management
     */
    public static void connectionManagementExample() {
        log.info("=== Example 6: Connection Management ===");
        
        try {
            // Validate connections to all clusters
            String[] clusters = {"user_cluster", "inventory_cluster", "analytics_cluster", "orders_cluster", "catalog_cluster"};
            String[] databases = {"userdb", "inventory", "analytics", "orders", "catalog"};
            
            for (int i = 0; i < clusters.length; i++) {
                boolean isValid = MongoDBConnectionManager.validateConnection(clusters[i], databases[i]);
                log.info("Connection to {}.{}: {}", clusters[i], databases[i], isValid ? "✅ Valid" : "❌ Invalid");
            }
            
            // Get connection statistics
            var stats = MongoDBConnectionManager.getConnectionStatistics();
            log.info("Connection Statistics: {}", stats);
            
            // Validate specific collections
            boolean userCollectionValid = MongoDBQueryExecutor.validateCollection(
                "user_cluster", "userdb", "users"
            );
            log.info("User collection validation: {}", userCollectionValid ? "✅ Valid" : "❌ Invalid");
            
            boolean orderCollectionValid = MongoDBQueryExecutor.validateCollection(
                "orders_cluster", "orders", "orders"
            );
            log.info("Order collection validation: {}", orderCollectionValid ? "✅ Valid" : "❌ Invalid");
            
        } catch (Exception e) {
            log.error("Error in connection management example: {}", e.getMessage());
        }
        
        log.info("✅ Connection management example completed");
    }

    /**
     * Main method to run all examples
     */
    public static void main(String[] args) {
        log.info("🚀 Running Dynamic MongoDB Multi-Cluster Usage Examples");
        log.info("Demonstrating operations across user, inventory, analytics, orders, and catalog clusters");
        log.info("");
        
        basicDocumentOperationsExample();
        documentInsertionExample();
        documentUpdateExample();
        aggregationExample();
        crossClusterAnalysisExample();
        connectionManagementExample();
        
        log.info("✅ All Dynamic MongoDB Multi-Cluster Usage Examples completed!");
        
        // Clean up connections
        try {
            MongoDBConnectionManager.closeAllMongoClients();
            log.info("🧹 Cleaned up all MongoDB connections");
        } catch (Exception e) {
            log.error("Error cleaning up connections: {}", e.getMessage());
        }
    }
}
