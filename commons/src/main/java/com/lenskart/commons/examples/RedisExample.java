package com.lenskart.commons.examples;

import com.lenskart.commons.database.redis.RedisConnectionManager;
import com.lenskart.commons.database.redis.RedisExecutor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Example class demonstrating how to use the Redis utilities
 */
@Slf4j
public class RedisExample {
    
    /**
     * Example of basic Redis operations
     * 
     * @param redisName Redis instance name
     */
    public static void basicOperations(String redisName) {
        // Set a key
        RedisExecutor.set(redisName, "greeting", "Hello, Redis!");
        log.info("Set greeting: {}", RedisExecutor.get(redisName, "greeting"));
        
        // Set with expiration
        RedisExecutor.setex(redisName, "temp", "This will expire", 60);
        log.info("Set temp with 60s expiration: {}", RedisExecutor.get(redisName, "temp"));
        log.info("TTL for temp: {} seconds", RedisExecutor.ttl(redisName, "temp"));
        
        // Increment a counter
        RedisExecutor.set(redisName, "counter", "10");
        RedisExecutor.incr(redisName, "counter");
        log.info("Counter after increment: {}", RedisExecutor.get(redisName, "counter"));
        
        // Delete a key
        RedisExecutor.del(redisName, "greeting");
        log.info("Greeting after delete: {}", RedisExecutor.get(redisName, "greeting"));
    }
    
    /**
     * Example of Redis hash operations
     * 
     * @param redisName Redis instance name
     */
    public static void hashOperations(String redisName) {
        // Set hash fields individually
        RedisExecutor.hset(redisName, "user:1", "name", "John Doe");
        RedisExecutor.hset(redisName, "user:1", "email", "<EMAIL>");
        RedisExecutor.hset(redisName, "user:1", "age", "30");
        
        // Get a hash field
        log.info("User name: {}", RedisExecutor.hget(redisName, "user:1", "name"));
        
        // Get all hash fields
        Map<String, String> user = RedisExecutor.hgetAll(redisName, "user:1");
        log.info("User details: {}", user);
        
        // Set multiple hash fields at once
        Map<String, String> userData = new HashMap<>();
        userData.put("name", "Jane Smith");
        userData.put("email", "<EMAIL>");
        userData.put("age", "25");
        RedisExecutor.hset(redisName, "user:2", userData);
        
        // Get all hash fields
        Map<String, String> user2 = RedisExecutor.hgetAll(redisName, "user:2");
        log.info("User 2 details: {}", user2);
    }
    
    /**
     * Example of Redis list operations
     * 
     * @param redisName Redis instance name
     */
    public static void listOperations(String redisName) {
        // Push items to a list
        RedisExecutor.lpush(redisName, "tasks", "Task 1", "Task 2", "Task 3");
        
        // Get list length
        log.info("Tasks count: {}", RedisExecutor.llen(redisName, "tasks"));
        
        // Get all items in the list
        List<String> tasks = RedisExecutor.lrange(redisName, "tasks", 0, -1);
        log.info("All tasks: {}", tasks);
        
        // Pop an item from the list
        String task = RedisExecutor.lpop(redisName, "tasks");
        log.info("Popped task: {}", task);
        
        // Get remaining items
        tasks = RedisExecutor.lrange(redisName, "tasks", 0, -1);
        log.info("Remaining tasks: {}", tasks);
    }
    
    /**
     * Example of Redis set operations
     * 
     * @param redisName Redis instance name
     */
    public static void setOperations(String redisName) {
        // Add members to a set
        RedisExecutor.sadd(redisName, "tags", "java", "redis", "database", "nosql");
        
        // Get all members
        Set<String> tags = RedisExecutor.smembers(redisName, "tags");
        log.info("All tags: {}", tags);
        
        // Check if a member exists
        boolean hasJava = RedisExecutor.sismember(redisName, "tags", "java");
        log.info("Has Java tag: {}", hasJava);
        
        // Remove a member
        RedisExecutor.srem(redisName, "tags", "nosql");
        
        // Get updated members
        tags = RedisExecutor.smembers(redisName, "tags");
        log.info("Tags after removal: {}", tags);
    }
    
    /**
     * Main method to run the examples
     * 
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        String redisName = "default";
        
        try {
            // Run examples
            basicOperations(redisName);
            hashOperations(redisName);
            listOperations(redisName);
            setOperations(redisName);
        } finally {
            // Close connections
            RedisConnectionManager.closeAllConnections();
        }
    }
}
