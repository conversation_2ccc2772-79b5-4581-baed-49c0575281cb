package com.lenskart.commons.base;

public interface ServiceHelper {

    // method to help in creating payload, setting up the headers
    ServiceHelper init();

    // calls api using the service class present under the respective api package
    ServiceHelper process();

    // uses validator class for validation
    ServiceHelper validate();

    // orchestrator for executing the flows
    ServiceHelper test();
}
