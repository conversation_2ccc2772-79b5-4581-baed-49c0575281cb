package com.lenskart.commons.base;

import com.lenskart.commons.utils.JsonUtils;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@SuperBuilder
@Getter
public abstract class BaseHelper<P, Q> {
    protected Integer statusCode;
    protected Map<String, String> headers;
    protected Map<String, Object> queryParams;
    protected String appException;
    protected P validationState;
    protected Q requestBuilders;

    public void getAppException(String response) {
        appException = response;
    }

    protected <T> T parseResponse(String response, Class<T> valueType) {
        if (statusCode == 200) {
            return JsonUtils.parseJsonString(response, valueType);
        } else getAppException(response);
        return null;
    }

    protected <T> T parseResponse(Object response, Class<T> valueType) {
        if (statusCode == 200 || statusCode == 201) {
            return JsonUtils.parseObject(response, valueType);
        }
        return null;
    }

    protected <T> T parseResponse(String response, Class<T> valueType, int expectedStatusCode) {
        if (expectedStatusCode == 200 || expectedStatusCode == 204) {
            return JsonUtils.parseJsonString(response, valueType);
        } else getAppException(response);
        return null;
    }
}