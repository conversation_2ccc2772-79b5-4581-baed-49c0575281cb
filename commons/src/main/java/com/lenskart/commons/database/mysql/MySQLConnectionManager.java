package com.lenskart.commons.database.mysql;

import com.lenskart.commons.config.DatabaseConfig;
import com.lenskart.commons.config.MySQLMultiClusterConfig;
import com.lenskart.commons.config.SSHConfig;
import com.lenskart.commons.loader.ConfigRegistry;
import com.lenskart.commons.loader.SSHConfigLoader;
import com.lenskart.commons.utils.SSHSessionUtil;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.io.IOException;
import java.net.ServerSocket;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Dynamic MySQL connection manager that creates connections on-demand
 * for any cluster and database combination
 */
@Slf4j
public class MySQLConnectionManager {

    // Cache for DataSources: "clusterName_databaseName" -> DataSource
    private static final ConcurrentHashMap<String, DataSource> dataSources = new ConcurrentHashMap<>();
    
    // Cache for SSH tunnels: "clusterName" -> local port
    private static final ConcurrentHashMap<String, Integer> sshTunnels = new ConcurrentHashMap<>();

    /**
     * Gets a connection for the specified cluster and database
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return Database connection
     * @throws SQLException if connection cannot be established
     */
    public static Connection getConnection(String clusterName, String databaseName) throws SQLException {
        DataSource dataSource = getDataSource(clusterName, databaseName);
        return dataSource.getConnection();
    }

    /**
     * Gets a DataSource for the specified cluster and database
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return DataSource object
     */
    public static DataSource getDataSource(String clusterName, String databaseName) {
        String key = createDataSourceKey(clusterName, databaseName);
        
        if (!dataSources.containsKey(key)) {
            synchronized (MySQLConnectionManager.class) {
                if (!dataSources.containsKey(key)) {
                    DataSource dataSource = createDataSource(clusterName, databaseName);
                    dataSources.put(key, dataSource);
                    log.info("Created new DataSource for cluster: {}, database: {}", clusterName, databaseName);
                }
            }
        }
        
        return dataSources.get(key);
    }

    /**
     * Creates a new DataSource for the specified cluster and database
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return New DataSource
     */
    private static DataSource createDataSource(String clusterName, String databaseName) {
        log.info("Creating DataSource for cluster: {}, database: {}", clusterName, databaseName);
        
        // Get multi-cluster configuration
        MySQLMultiClusterConfig multiClusterConfig = ConfigRegistry.getInstance().getMySQLMultiClusterConfig();
        if (multiClusterConfig == null) {
            throw new IllegalStateException("MySQL multi-cluster configuration not found");
        }
        
        // Create database configuration
        DatabaseConfig dbConfig = multiClusterConfig.createDatabaseConfig(clusterName, databaseName);
        
        // Handle SSH tunneling if enabled
       String finalJdbcUrl = handleSSHTunneling(clusterName, dbConfig);
        
        // Create HikariCP configuration
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(finalJdbcUrl);
        hikariConfig.setUsername(dbConfig.getUsername());
        hikariConfig.setPassword(dbConfig.getPassword());
        hikariConfig.setDriverClassName(dbConfig.getDriverClassName());
        
        // Connection pool settings
        hikariConfig.setMaximumPoolSize(dbConfig.getMaxPoolSize());
        hikariConfig.setMinimumIdle(dbConfig.getMinIdle());
        hikariConfig.setConnectionTimeout(dbConfig.getConnectionTimeout());
        hikariConfig.setIdleTimeout(dbConfig.getIdleTimeout());
        hikariConfig.setMaxLifetime(dbConfig.getMaxLifetime());
        
        // Pool name for identification
        hikariConfig.setPoolName(clusterName + "_" + databaseName + "_pool");
        
        // Additional settings
        hikariConfig.setLeakDetectionThreshold(60000); // 60 seconds
        hikariConfig.setConnectionTestQuery("SELECT 1");
        
        log.info("DataSource configuration: cluster={}, database={}, url={}, maxPoolSize={}", 
                clusterName, databaseName, finalJdbcUrl, dbConfig.getMaxPoolSize());
        
        return new HikariDataSource(hikariConfig);
    }

    /**
     * Handles SSH tunneling for the cluster if enabled
     *
     * @param clusterName Name of the cluster
     * @param dbConfig Database configuration
     * @return Final JDBC URL (tunneled or original)
     */
    private static String handleSSHTunneling(String clusterName, DatabaseConfig dbConfig) {
        SSHConfig sshConfig = SSHConfigLoader.loadConfig();
        
        if (sshConfig == null || !sshConfig.isEnabled()) {
            log.debug("SSH tunneling disabled for cluster: {}", clusterName);
            return dbConfig.getUrl();
        }
        
        log.info("SSH tunneling enabled for cluster: {}", clusterName);
        
        // Check if tunnel already exists for this cluster
        Integer localPort = sshTunnels.get(clusterName);
        if (localPort == null) {
            synchronized (MySQLConnectionManager.class) {
                localPort = sshTunnels.get(clusterName);
                if (localPort == null) {
                    // Create new SSH tunnel for this cluster
                    MySQLMultiClusterConfig.ClusterConfig clusterConfig =
                        ConfigRegistry.getInstance().getMySQLMultiClusterConfig().getClusterConfig(clusterName);

                    // Create SSH config for this cluster
                    SSHConfig clusterSSHConfig = SSHConfig.builder()
                            .enabled(sshConfig.isEnabled())
                            .hostname(sshConfig.getHostname())
                            .port(sshConfig.getPort())
                            .username(sshConfig.getUsername())
                            .password(sshConfig.getPassword())
                            .privateKeyPath(sshConfig.getPrivateKeyPath())
                            .remoteHost(clusterConfig.getHost())
                            .remotePort(clusterConfig.getPort())
                            .localPort(findAvailableLocalPort())
                            .build();

                    try {
                        localPort = SSHSessionUtil.createTunnel(clusterSSHConfig);
                    } catch (Exception e) {
                        log.error("Failed to create SSH tunnel for cluster {}: {}", clusterName, e.getMessage());
                        throw new RuntimeException("SSH tunnel creation failed for cluster: " + clusterName, e);
                    }
                    
                    sshTunnels.put(clusterName, localPort);
                    log.info("Created SSH tunnel for cluster: {} -> localhost:{}", clusterName, localPort);
                }
            }
        }
        
        // Replace host and port in JDBC URL with localhost and tunnel port
        String originalUrl = dbConfig.getUrl();
        String tunneledUrl = originalUrl.replaceFirst(
            "jdbc:mysql://[^/]+/", 
            "**********************:" + localPort + "/"
        );
        
        log.info("Using tunneled URL for cluster {}: {}", clusterName, tunneledUrl);
        return tunneledUrl;
    }

    /**
     * Creates a unique key for DataSource caching
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return Unique key
     */
    private static String createDataSourceKey(String clusterName, String databaseName) {
        return clusterName + "_" + databaseName;
    }

    /**
     * Closes a specific DataSource and removes it from cache
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     */
    public static void closeDataSource(String clusterName, String databaseName) {
        String key = createDataSourceKey(clusterName, databaseName);
        DataSource dataSource = dataSources.remove(key);
        
        if (dataSource instanceof HikariDataSource) {
            ((HikariDataSource) dataSource).close();
            log.info("Closed DataSource for cluster: {}, database: {}", clusterName, databaseName);
        }
    }

    /**
     * Closes all DataSources and SSH tunnels
     */
    public static void closeAll() {
        log.info("Closing all DataSources and SSH tunnels");
        
        // Close all DataSources
        for (DataSource dataSource : dataSources.values()) {
            if (dataSource instanceof HikariDataSource) {
                ((HikariDataSource) dataSource).close();
            }
        }
        dataSources.clear();
        
        // Close all SSH tunnels
        for (Map.Entry<String, Integer> entry : sshTunnels.entrySet()) {
            try {
                // Note: We would need to store SSH configs to properly close tunnels
                // For now, we'll use closeAllTunnels() method
                log.info("SSH tunnel cleanup needed for cluster: {}", entry.getKey());
            } catch (Exception e) {
                log.warn("Error closing SSH tunnel for cluster {}: {}", entry.getKey(), e.getMessage());
            }
        }

        // Close all SSH tunnels at once
        try {
            SSHSessionUtil.closeAllTunnels();
        } catch (Exception e) {
            log.warn("Error closing all SSH tunnels: {}", e.getMessage());
        }
        sshTunnels.clear();
        
        log.info("All connections and tunnels closed");
    }

    /**
     * Gets statistics about active connections
     *
     * @return Map containing connection statistics
     */
    public static java.util.Map<String, Object> getStatistics() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("activeDataSources", dataSources.size());
        stats.put("activeSshTunnels", sshTunnels.size());
        stats.put("dataSourceKeys", dataSources.keySet());
        stats.put("sshTunnelClusters", sshTunnels.keySet());
        
        // DataSource details
        java.util.Map<String, Object> dataSourceDetails = new java.util.HashMap<>();
        for (java.util.Map.Entry<String, DataSource> entry : dataSources.entrySet()) {
            if (entry.getValue() instanceof HikariDataSource) {
                HikariDataSource hikariDS = (HikariDataSource) entry.getValue();
                java.util.Map<String, Object> details = new java.util.HashMap<>();
                details.put("poolName", hikariDS.getPoolName());
                details.put("maximumPoolSize", hikariDS.getMaximumPoolSize());
                details.put("activeConnections", hikariDS.getHikariPoolMXBean().getActiveConnections());
                details.put("idleConnections", hikariDS.getHikariPoolMXBean().getIdleConnections());
                details.put("totalConnections", hikariDS.getHikariPoolMXBean().getTotalConnections());
                dataSourceDetails.put(entry.getKey(), details);
            }
        }
        stats.put("dataSourceDetails", dataSourceDetails);
        
        return stats;
    }

    /**
     * Validates that a cluster and database combination can be accessed
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return true if accessible, false otherwise
     */
    public static boolean validateConnection(String clusterName, String databaseName) {
        try {
            Connection connection = getConnection(clusterName, databaseName);
            connection.close();
            log.info("Connection validation successful for cluster: {}, database: {}", clusterName, databaseName);
            return true;
        } catch (Exception e) {
            log.error("Connection validation failed for cluster: {}, database: {} - {}", 
                    clusterName, databaseName, e.getMessage());
            return false;
        }
    }

    /**
     * Finds an available local port for SSH tunneling
     *
     * @return Available local port number
     */
    private static int findAvailableLocalPort() {
        try (ServerSocket socket = new ServerSocket(0)) {
            return socket.getLocalPort();
        } catch (IOException e) {
            log.warn("Error finding available port, using default: {}", e.getMessage());
            return 3307; // Default fallback port
        }
    }
}
