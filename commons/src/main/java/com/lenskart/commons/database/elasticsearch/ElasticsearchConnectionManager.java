package com.lenskart.commons.database.elasticsearch;

import com.jcraft.jsch.JSchException;
import com.lenskart.commons.config.ElasticsearchConfig;
import com.lenskart.commons.config.SSHConfig;
import com.lenskart.commons.constants.Constants;
import com.lenskart.commons.loader.ConfigRegistry;
import com.lenskart.commons.loader.ElasticsearchConfigLoader;
import com.lenskart.commons.loader.SSHConfigLoader;
import com.lenskart.commons.utils.SSHSessionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manager class for Elasticsearch connections
 */
@Slf4j
public class ElasticsearchConnectionManager {

    private static final Map<String, RestHighLevelClient> esClients = new ConcurrentHashMap<>();
    private static final Map<String, SSHConfig> sshConfigs = new ConcurrentHashMap<>();
    private static final Map<String, List<Integer>> localPorts = new ConcurrentHashMap<>();

    /**
     * Gets a RestHighLevelClient for the specified Elasticsearch instance
     *
     * @param esName Name of the Elasticsearch instance to connect to
     * @return RestHighLevelClient object
     */
    public static RestHighLevelClient getClient(String esName) {
        if (!esClients.containsKey(esName)) {
            synchronized (ElasticsearchConnectionManager.class) {
                if (!esClients.containsKey(esName)) {
                    // Use ConfigRegistry to get the Elasticsearch configuration
                    ElasticsearchConfig config = ConfigRegistry.getInstance().getElasticsearchConfig(esName);
                    if (config == null) {
                        // Fall back to ElasticsearchConfigLoader if not found in registry
                        config = ElasticsearchConfigLoader.loadConfig(esName);
                    }
                    esClients.put(esName, createElasticsearchClient(config));
                }
            }
        }
        return esClients.get(esName);
    }

    /**
     * Creates a RestHighLevelClient from an ElasticsearchConfig
     *
     * @param config Elasticsearch configuration
     * @return RestHighLevelClient configured with the provided settings
     */
    private static RestHighLevelClient createElasticsearchClient(ElasticsearchConfig config) {
        // Check if we have an SSH config or need to load one
        SSHConfig sshConfig = sshConfigs.computeIfAbsent(config.getName(), k -> {
            // Load SSH configuration from registry
            SSHConfig loadedConfig = SSHConfigLoader.loadConfig();
            if (loadedConfig != null) {
                log.info("Loaded SSH configuration for Elasticsearch instance: {}", config.getName());
            }
            return loadedConfig;
        });

        List<HttpHost> httpHosts = new ArrayList<>();
        List<Integer> tunnelPorts = new ArrayList<>();

        // If SSH config exists and is enabled, create tunnels for each host
        if (sshConfig != null && sshConfig.isEnabled()) {
            for (String host : config.getHosts()) {
                try {
                    URI uri = new URI(host);
                    String hostname = uri.getHost();
                    int port = uri.getPort() > 0 ? uri.getPort() : Constants.DEFAULT_ES_PORT;
                    
                    // Create a unique local port for each host
                    int localPort = Constants.DEFAULT_ES_LOCAL_PORT + tunnelPorts.size();
                    
                    // Create SSH tunnel
                    SSHConfig tunnelConfig = SSHConfig.builder()
                            .enabled(sshConfig.isEnabled())
                            .hostname(sshConfig.getHostname())
                            .port(sshConfig.getPort())
                            .username(sshConfig.getUsername())
                            .password(sshConfig.getPassword())
                            .privateKeyPath(sshConfig.getPrivateKeyPath())
                            .localPort(localPort)
                            .remoteHost(hostname)
                            .remotePort(port)
                            .build();

                    // Create the SSH tunnel
                    int actualLocalPort = SSHSessionUtil.createTunnel(tunnelConfig);
                    tunnelPorts.add(actualLocalPort);
                    
                    log.info("Created SSH tunnel for Elasticsearch host: {} ({}:{} -> localhost:{})",
                            config.getName(), hostname, port, actualLocalPort);
                    
                    // Add the tunneled host to the list
                    String scheme = uri.getScheme() != null ? uri.getScheme() : "http";
                    httpHosts.add(new HttpHost("localhost", actualLocalPort, scheme));
                    
                } catch (URISyntaxException | JSchException e) {
                    log.error("Failed to create SSH tunnel for Elasticsearch host: {}", host, e);
                    // Fall back to direct connection for this host
                    try {
                        URI uri = new URI(host);
                        String hostname = uri.getHost();
                        int port = uri.getPort() > 0 ? uri.getPort() : Constants.DEFAULT_ES_PORT;
                        String scheme = uri.getScheme() != null ? uri.getScheme() : "http";
                        httpHosts.add(new HttpHost(hostname, port, scheme));
                    } catch (URISyntaxException ex) {
                        log.error("Invalid URI for Elasticsearch host: {}", host, ex);
                    }
                }
            }
            
            // Store the local ports for later cleanup
            localPorts.put(config.getName(), tunnelPorts);
            
        } else {
            // No SSH tunneling, use the hosts directly
            for (String host : config.getHosts()) {
                try {
                    URI uri = new URI(host);
                    String hostname = uri.getHost();
                    int port = uri.getPort() > 0 ? uri.getPort() : Constants.DEFAULT_ES_PORT;
                    String scheme = uri.getScheme() != null ? uri.getScheme() : "http";
                    httpHosts.add(new HttpHost(hostname, port, scheme));
                } catch (URISyntaxException e) {
                    log.error("Invalid URI for Elasticsearch host: {}", host, e);
                }
            }
        }

        // Create the RestClientBuilder
        RestClientBuilder builder = RestClient.builder(httpHosts.toArray(new HttpHost[0]))
                .setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                        .setConnectTimeout(config.getConnectTimeout())
                        .setSocketTimeout(config.getReadTimeout()))
                .setHttpClientConfigCallback(httpClientBuilder -> {
                    httpClientBuilder.setMaxConnTotal(config.getMaxTotalConnections())
                            .setMaxConnPerRoute(config.getDefaultMaxTotalConnectionsPerRoute());
                    
                    // Add authentication if provided
                    if (config.getUsername() != null && !config.getUsername().isEmpty() &&
                        config.getPassword() != null && !config.getPassword().isEmpty()) {
                        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                        credentialsProvider.setCredentials(AuthScope.ANY,
                                new UsernamePasswordCredentials(config.getUsername(), config.getPassword()));
                        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                    }
                    
                    return httpClientBuilder;
                });
        
        // Create and return the RestHighLevelClient
        return new RestHighLevelClient(builder);
    }

    /**
     * Closes all Elasticsearch connections managed by this connection manager
     */
    public static void closeAllConnections() {
        for (Map.Entry<String, RestHighLevelClient> entry : esClients.entrySet()) {
            try {
                entry.getValue().close();
            } catch (IOException e) {
                log.error("Error closing Elasticsearch client: {}", e.getMessage(), e);
            }
        }
        esClients.clear();
        
        // Close all SSH tunnels
        if (!sshConfigs.isEmpty()) {
            SSHSessionUtil.closeAllTunnels();
            sshConfigs.clear();
            localPorts.clear();
        }
    }

    /**
     * Closes a specific Elasticsearch connection
     *
     * @param esName Name of the Elasticsearch instance whose connection should be closed
     */
    public static void closeConnection(String esName) {
        RestHighLevelClient client = esClients.get(esName);
        if (client != null) {
            try {
                client.close();
                esClients.remove(esName);
                
                // Close associated SSH tunnels if they exist
                SSHConfig config = sshConfigs.remove(esName);
                List<Integer> ports = localPorts.remove(esName);
                
                if (config != null && ports != null) {
                    for (Integer localPort : ports) {
                        try {
                            SSHConfig tunnelConfig = SSHConfig.builder()
                                    .enabled(config.isEnabled())
                                    .hostname(config.getHostname())
                                    .port(config.getPort())
                                    .username(config.getUsername())
                                    .password(config.getPassword())
                                    .privateKeyPath(config.getPrivateKeyPath())
                                    .localPort(localPort)
                                    .build();
                            
                            SSHSessionUtil.closeTunnel(tunnelConfig);
                            log.info("Closed SSH tunnel for Elasticsearch instance: {} (localhost:{})", 
                                    esName, localPort);
                        } catch (Exception e) {
                            log.warn("Error closing SSH tunnel for Elasticsearch instance {} (localhost:{}): {}", 
                                    esName, localPort, e.getMessage());
                        }
                    }
                }
            } catch (IOException e) {
                log.error("Error closing Elasticsearch client: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * Checks if SSH tunneling is configured and enabled for a specific Elasticsearch instance
     *
     * @param esName Name of the Elasticsearch instance
     * @return true if SSH tunneling is configured and enabled, false otherwise
     */
    public static boolean isSSHTunnelingConfigured(String esName) {
        SSHConfig config = sshConfigs.get(esName);
        return config != null && config.isEnabled();
    }

    /**
     * Refreshes the SSH configuration for all Elasticsearch instances
     * This will close all existing connections and tunnels, and recreate them with the latest configuration
     */
    public static void refreshSSHConfiguration() {
        // Close all existing connections and tunnels
        closeAllConnections();
        
        // The next time a connection is requested, the SSH configuration will be reloaded
        log.info("SSH configuration refreshed. New Elasticsearch connections will use updated configuration.");
    }
}
