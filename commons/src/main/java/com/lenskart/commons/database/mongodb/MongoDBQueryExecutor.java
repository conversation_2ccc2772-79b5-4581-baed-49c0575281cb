package com.lenskart.commons.database.mongodb;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.FindIterable;
import com.mongodb.client.result.InsertOneResult;
import com.mongodb.client.result.InsertManyResult;
import com.mongodb.client.result.UpdateResult;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Dynamic MongoDB query executor that executes queries on any cluster and database combination
 */
@Slf4j
public class MongoDBQueryExecutor {

    /**
     * Finds documents in a collection
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param filter Filter criteria as Document
     * @return List of documents
     */
    public static List<Document> find(String clusterName, String databaseName, String collectionName, Document filter) {
        log.info("Finding documents in cluster: {}, database: {}, collection: {}", clusterName, databaseName, collectionName);
        log.debug("Filter: {}", filter);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            List<Document> results = new ArrayList<>();
            FindIterable<Document> iterable = collection.find(filter);
            
            for (Document doc : iterable) {
                results.add(doc);
            }
            
            log.info("Found {} documents", results.size());
            return results;
            
        } catch (Exception e) {
            log.error("Error finding documents in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error finding documents: " + e.getMessage(), e);
        }
    }

    /**
     * Finds documents in a collection with limit
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param filter Filter criteria as Document
     * @param limit Maximum number of documents to return
     * @return List of documents
     */
    public static List<Document> find(String clusterName, String databaseName, String collectionName, 
                                     Document filter, int limit) {
        log.info("Finding documents with limit {} in cluster: {}, database: {}, collection: {}", 
                limit, clusterName, databaseName, collectionName);
        log.debug("Filter: {}", filter);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            List<Document> results = new ArrayList<>();
            FindIterable<Document> iterable = collection.find(filter).limit(limit);
            
            for (Document doc : iterable) {
                results.add(doc);
            }
            
            log.info("Found {} documents (limited to {})", results.size(), limit);
            return results;
            
        } catch (Exception e) {
            log.error("Error finding documents with limit in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error finding documents with limit: " + e.getMessage(), e);
        }
    }

    /**
     * Finds a single document in a collection
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param filter Filter criteria as Document
     * @return Single document or null if not found
     */
    public static Document findOne(String clusterName, String databaseName, String collectionName, Document filter) {
        log.info("Finding single document in cluster: {}, database: {}, collection: {}", 
                clusterName, databaseName, collectionName);
        log.debug("Filter: {}", filter);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            Document result = collection.find(filter).first();
            
            if (result != null) {
                log.info("Found single document");
            } else {
                log.info("No document found matching filter");
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("Error finding single document in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error finding single document: " + e.getMessage(), e);
        }
    }

    /**
     * Inserts a single document into a collection
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param document Document to insert
     * @return Insert result
     */
    public static InsertOneResult insertOne(String clusterName, String databaseName, String collectionName, 
                                           Document document) {
        log.info("Inserting document in cluster: {}, database: {}, collection: {}", 
                clusterName, databaseName, collectionName);
        log.debug("Document: {}", document);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            InsertOneResult result = collection.insertOne(document);
            
            log.info("Document inserted successfully. Inserted ID: {}", result.getInsertedId());
            return result;
            
        } catch (Exception e) {
            log.error("Error inserting document in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error inserting document: " + e.getMessage(), e);
        }
    }

    /**
     * Inserts multiple documents into a collection
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param documents List of documents to insert
     * @return Insert result
     */
    public static InsertManyResult insertMany(String clusterName, String databaseName, String collectionName, 
                                             List<Document> documents) {
        log.info("Inserting {} documents in cluster: {}, database: {}, collection: {}", 
                documents.size(), clusterName, databaseName, collectionName);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            InsertManyResult result = collection.insertMany(documents);
            
            log.info("Documents inserted successfully. Inserted count: {}", result.getInsertedIds().size());
            return result;
            
        } catch (Exception e) {
            log.error("Error inserting documents in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error inserting documents: " + e.getMessage(), e);
        }
    }

    /**
     * Updates a single document in a collection
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param filter Filter criteria to find the document
     * @param update Update operations
     * @return Update result
     */
    public static UpdateResult updateOne(String clusterName, String databaseName, String collectionName, 
                                        Document filter, Document update) {
        log.info("Updating single document in cluster: {}, database: {}, collection: {}", 
                clusterName, databaseName, collectionName);
        log.debug("Filter: {}, Update: {}", filter, update);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            UpdateResult result = collection.updateOne(filter, update);
            
            log.info("Update completed. Matched: {}, Modified: {}", 
                    result.getMatchedCount(), result.getModifiedCount());
            return result;
            
        } catch (Exception e) {
            log.error("Error updating document in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error updating document: " + e.getMessage(), e);
        }
    }

    /**
     * Updates multiple documents in a collection
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param filter Filter criteria to find documents
     * @param update Update operations
     * @return Update result
     */
    public static UpdateResult updateMany(String clusterName, String databaseName, String collectionName, 
                                         Document filter, Document update) {
        log.info("Updating multiple documents in cluster: {}, database: {}, collection: {}", 
                clusterName, databaseName, collectionName);
        log.debug("Filter: {}, Update: {}", filter, update);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            UpdateResult result = collection.updateMany(filter, update);
            
            log.info("Update completed. Matched: {}, Modified: {}", 
                    result.getMatchedCount(), result.getModifiedCount());
            return result;
            
        } catch (Exception e) {
            log.error("Error updating documents in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error updating documents: " + e.getMessage(), e);
        }
    }

    /**
     * Deletes a single document from a collection
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param filter Filter criteria to find the document
     * @return Delete result
     */
    public static DeleteResult deleteOne(String clusterName, String databaseName, String collectionName, 
                                        Document filter) {
        log.info("Deleting single document in cluster: {}, database: {}, collection: {}", 
                clusterName, databaseName, collectionName);
        log.debug("Filter: {}", filter);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            DeleteResult result = collection.deleteOne(filter);
            
            log.info("Delete completed. Deleted count: {}", result.getDeletedCount());
            return result;
            
        } catch (Exception e) {
            log.error("Error deleting document in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error deleting document: " + e.getMessage(), e);
        }
    }

    /**
     * Deletes multiple documents from a collection
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param filter Filter criteria to find documents
     * @return Delete result
     */
    public static DeleteResult deleteMany(String clusterName, String databaseName, String collectionName, 
                                         Document filter) {
        log.info("Deleting multiple documents in cluster: {}, database: {}, collection: {}", 
                clusterName, databaseName, collectionName);
        log.debug("Filter: {}", filter);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            DeleteResult result = collection.deleteMany(filter);
            
            log.info("Delete completed. Deleted count: {}", result.getDeletedCount());
            return result;
            
        } catch (Exception e) {
            log.error("Error deleting documents in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error deleting documents: " + e.getMessage(), e);
        }
    }

    /**
     * Counts documents in a collection
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param filter Filter criteria
     * @return Document count
     */
    public static long count(String clusterName, String databaseName, String collectionName, Document filter) {
        log.info("Counting documents in cluster: {}, database: {}, collection: {}", 
                clusterName, databaseName, collectionName);
        log.debug("Filter: {}", filter);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            long count = collection.countDocuments(filter);
            
            log.info("Document count: {}", count);
            return count;
            
        } catch (Exception e) {
            log.error("Error counting documents in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error counting documents: " + e.getMessage(), e);
        }
    }

    /**
     * Executes an aggregation pipeline
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @param pipeline Aggregation pipeline
     * @return List of aggregation results
     */
    public static List<Document> aggregate(String clusterName, String databaseName, String collectionName, 
                                          List<? extends Bson> pipeline) {
        log.info("Executing aggregation in cluster: {}, database: {}, collection: {}", 
                clusterName, databaseName, collectionName);
        log.debug("Pipeline: {}", pipeline);
        
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            List<Document> results = new ArrayList<>();
            for (Document doc : collection.aggregate(pipeline)) {
                results.add(doc);
            }
            
            log.info("Aggregation completed. Result count: {}", results.size());
            return results;
            
        } catch (Exception e) {
            log.error("Error executing aggregation in cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error executing aggregation: " + e.getMessage(), e);
        }
    }

    /**
     * Validates that a cluster, database, and collection combination can be accessed
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @return true if accessible, false otherwise
     */
    public static boolean validateCollection(String clusterName, String databaseName, String collectionName) {
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            // Try to count documents to validate access
            collection.countDocuments();
            
            log.info("Collection validation successful for cluster: {}, database: {}, collection: {}", 
                    clusterName, databaseName, collectionName);
            return true;
        } catch (Exception e) {
            log.error("Collection validation failed for cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            return false;
        }
    }

    /**
     * Gets collection statistics
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param collectionName Name of the collection
     * @return Map containing collection statistics
     */
    public static Map<String, Object> getCollectionStats(String clusterName, String databaseName, String collectionName) {
        try {
            MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(clusterName, databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            
            Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("documentCount", collection.countDocuments());
            stats.put("collectionName", collectionName);
            stats.put("databaseName", databaseName);
            stats.put("clusterName", clusterName);
            
            log.info("Retrieved collection statistics for {}.{}.{}", clusterName, databaseName, collectionName);
            return stats;
            
        } catch (Exception e) {
            log.error("Error getting collection statistics for cluster: {}, database: {}, collection: {} - {}", 
                    clusterName, databaseName, collectionName, e.getMessage());
            throw new RuntimeException("Error getting collection statistics: " + e.getMessage(), e);
        }
    }
}
