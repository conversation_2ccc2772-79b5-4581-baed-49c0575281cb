package com.lenskart.commons.database.mysql;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Interface for mapping database result set rows to objects
 *
 * @param <T> Type of object to map to
 */
@FunctionalInterface
public interface ResultSetMapper<T> {

    /**
     * Maps a result set row to an object
     *
     * @param rs Result set positioned at the row to map
     * @return Object mapped from the current result set row
     * @throws SQLException if a database access error occurs
     */
    T map(ResultSet rs) throws SQLException;
}