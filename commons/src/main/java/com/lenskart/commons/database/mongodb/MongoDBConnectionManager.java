package com.lenskart.commons.database.mongodb;

import com.lenskart.commons.config.MongoDBConfig;
import com.lenskart.commons.config.MongoDBMultiClusterConfig;
import com.lenskart.commons.config.SSHConfig;
import com.lenskart.commons.loader.ConfigRegistry;
import com.lenskart.commons.loader.SSHConfigLoader;
import com.lenskart.commons.utils.SSHSessionUtil;
import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Dynamic MongoDB connection manager that creates connections on-demand
 * for any cluster and database combination
 */
@Slf4j
public class MongoDBConnectionManager {

    // Cache for MongoClients: "clusterName_databaseName" -> MongoClient
    private static final ConcurrentHashMap<String, MongoClient> mongoClients = new ConcurrentHashMap<>();
    
    // Cache for SSH tunnels: "clusterName" -> local port
    private static final ConcurrentHashMap<String, Integer> sshTunnels = new ConcurrentHashMap<>();

    /**
     * Gets a MongoDB client for the specified cluster and database
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return MongoClient object
     */
    public static MongoClient getMongoClient(String clusterName, String databaseName) {
        String key = createClientKey(clusterName, databaseName);
        
        if (!mongoClients.containsKey(key)) {
            synchronized (MongoDBConnectionManager.class) {
                if (!mongoClients.containsKey(key)) {
                    MongoClient client = createMongoClient(clusterName, databaseName);
                    mongoClients.put(key, client);
                    log.info("Created new MongoClient for cluster: {}, database: {}", clusterName, databaseName);
                }
            }
        }
        
        return mongoClients.get(key);
    }

    /**
     * Gets a MongoDB database for the specified cluster and database
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return MongoDatabase object
     */
    public static MongoDatabase getMongoDatabase(String clusterName, String databaseName) {
        MongoClient client = getMongoClient(clusterName, databaseName);
        return client.getDatabase(databaseName);
    }

    /**
     * Creates a new MongoClient for the specified cluster and database
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return New MongoClient
     */
    private static MongoClient createMongoClient(String clusterName, String databaseName) {
        log.info("Creating MongoClient for cluster: {}, database: {}", clusterName, databaseName);
        
        // Get multi-cluster configuration
        MongoDBMultiClusterConfig multiClusterConfig = ConfigRegistry.getInstance().getMongoDBMultiClusterConfig();
        if (multiClusterConfig == null) {
            throw new IllegalStateException("MongoDB multi-cluster configuration not found");
        }
        
        // Create database configuration
        MongoDBConfig dbConfig = multiClusterConfig.createMongoDBConfig(clusterName, databaseName);
        
        // Handle SSH tunneling if enabled
        String finalMongoUri = handleSSHTunneling(clusterName, dbConfig);
        
        // Create MongoDB client settings
        MongoClientSettings.Builder settingsBuilder = MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(finalMongoUri))
                .applyToConnectionPoolSettings(builder ->
                    builder.maxSize(dbConfig.getMaxPoolSize())
                           .minSize(dbConfig.getMinPoolSize())
                           .maxConnectionIdleTime(dbConfig.getMaxIdleTimeMS(), TimeUnit.MILLISECONDS)
                           .maxConnectionLifeTime(dbConfig.getMaxLifeTimeMS(), TimeUnit.MILLISECONDS)
                )
                .applyToSocketSettings(builder ->
                    builder.connectTimeout(dbConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
                           .readTimeout(dbConfig.getSocketTimeout(), TimeUnit.MILLISECONDS)
                );
        
        log.info("MongoClient configuration: cluster={}, database={}, uri={}, maxPoolSize={}", 
                clusterName, databaseName, maskUri(finalMongoUri), dbConfig.getMaxPoolSize());
        
        return MongoClients.create(settingsBuilder.build());
    }

    /**
     * Handles SSH tunneling for the cluster if enabled
     *
     * @param clusterName Name of the cluster
     * @param dbConfig Database configuration
     * @return Final MongoDB URI (tunneled or original)
     */
    private static String handleSSHTunneling(String clusterName, MongoDBConfig dbConfig) {
        SSHConfig sshConfig = SSHConfigLoader.loadConfig();
        
        if (sshConfig == null || !sshConfig.isEnabled()) {
            log.debug("SSH tunneling disabled for cluster: {}", clusterName);
            return dbConfig.getUri();
        }
        
        log.info("SSH tunneling enabled for cluster: {}", clusterName);
        
        // Check if tunnel already exists for this cluster
        Integer localPort = sshTunnels.get(clusterName);
        if (localPort == null) {
            synchronized (MongoDBConnectionManager.class) {
                localPort = sshTunnels.get(clusterName);
                if (localPort == null) {
                    // Create new SSH tunnel for this cluster
                    MongoDBMultiClusterConfig.ClusterConfig clusterConfig =
                        ConfigRegistry.getInstance().getMongoDBMultiClusterConfig().getClusterConfig(clusterName);

                    // Create SSH config for this cluster
                    SSHConfig clusterSSHConfig = SSHConfig.builder()
                            .enabled(sshConfig.isEnabled())
                            .hostname(sshConfig.getHostname())
                            .port(sshConfig.getPort())
                            .username(sshConfig.getUsername())
                            .password(sshConfig.getPassword())
                            .privateKeyPath(sshConfig.getPrivateKeyPath())
                            .remoteHost(clusterConfig.getHost())
                            .remotePort(clusterConfig.getPort())
                            .localPort(findAvailableLocalPort())
                            .build();

                    try {
                        localPort = SSHSessionUtil.createTunnel(clusterSSHConfig);
                    } catch (Exception e) {
                        log.error("Failed to create SSH tunnel for cluster {}: {}", clusterName, e.getMessage());
                        throw new RuntimeException("SSH tunnel creation failed for cluster: " + clusterName, e);
                    }
                    
                    sshTunnels.put(clusterName, localPort);
                    log.info("Created SSH tunnel for cluster: {} -> localhost:{}", clusterName, localPort);
                }
            }
        }
        
        // Replace host and port in MongoDB URI with localhost and tunnel port
        String originalUri = dbConfig.getUri();
        try {
            URI uri = new URI(originalUri);
            String path = uri.getPath(); // Contains "/database"
            String query = uri.getQuery(); // Contains URI parameters
            
            // Create a new MongoDB URI that uses localhost and the local port
            String tunneledUri = String.format("mongodb://localhost:%d%s", localPort, path);
            if (query != null && !query.isEmpty()) {
                tunneledUri += "?" + query;
            }
            
            log.info("Using tunneled URI for cluster {}: {}", clusterName, maskUri(tunneledUri));
            return tunneledUri;
            
        } catch (URISyntaxException e) {
            log.error("Failed to parse MongoDB URI for tunneling: {}", originalUri, e);
            throw new RuntimeException("Invalid MongoDB URI: " + originalUri, e);
        }
    }

    /**
     * Creates a unique key for MongoClient caching
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return Unique key
     */
    private static String createClientKey(String clusterName, String databaseName) {
        return clusterName + "_" + databaseName;
    }

    /**
     * Finds an available local port for SSH tunneling
     *
     * @return Available port number
     */
    private static int findAvailableLocalPort() {
        try (ServerSocket socket = new ServerSocket(0)) {
            return socket.getLocalPort();
        } catch (IOException e) {
            throw new RuntimeException("Failed to find available local port", e);
        }
    }

    /**
     * Masks sensitive information in MongoDB URI for logging
     *
     * @param uri MongoDB URI
     * @return Masked URI
     */
    private static String maskUri(String uri) {
        if (uri == null) return null;
        
        // Replace password in URI with asterisks
        return uri.replaceAll("://([^:]+):([^@]+)@", "://$1:****@");
    }

    /**
     * Closes a specific MongoClient for a cluster and database
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     */
    public static void closeMongoClient(String clusterName, String databaseName) {
        String key = createClientKey(clusterName, databaseName);
        MongoClient client = mongoClients.remove(key);
        
        if (client != null) {
            try {
                client.close();
                log.info("Closed MongoClient for cluster: {}, database: {}", clusterName, databaseName);
            } catch (Exception e) {
                log.error("Error closing MongoClient for cluster: {}, database: {} - {}", 
                        clusterName, databaseName, e.getMessage());
            }
        }
    }

    /**
     * Closes all MongoClients managed by this connection manager
     */
    public static void closeAllMongoClients() {
        for (Map.Entry<String, MongoClient> entry : mongoClients.entrySet()) {
            try {
                entry.getValue().close();
                log.info("Closed MongoClient: {}", entry.getKey());
            } catch (Exception e) {
                log.error("Error closing MongoClient {}: {}", entry.getKey(), e.getMessage());
            }
        }
        mongoClients.clear();

        // Close all SSH tunnels
        if (!sshTunnels.isEmpty()) {
            SSHSessionUtil.closeAllTunnels();
            sshTunnels.clear();
            log.info("Closed all SSH tunnels");
        }
    }

    /**
     * Gets all active MongoClient keys
     *
     * @return Set of active client keys
     */
    public static java.util.Set<String> getActiveClientKeys() {
        return new java.util.HashSet<>(mongoClients.keySet());
    }

    /**
     * Gets the number of active MongoClients
     *
     * @return Number of active clients
     */
    public static int getActiveClientCount() {
        return mongoClients.size();
    }

    /**
     * Validates that a cluster and database combination can be accessed
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return true if accessible, false otherwise
     */
    public static boolean validateConnection(String clusterName, String databaseName) {
        try {
            MongoDatabase database = getMongoDatabase(clusterName, databaseName);
            // Try to execute a simple command to validate the connection
            database.runCommand(new org.bson.Document("ping", 1));
            log.info("Connection validation successful for cluster: {}, database: {}", clusterName, databaseName);
            return true;
        } catch (Exception e) {
            log.error("Connection validation failed for cluster: {}, database: {} - {}", 
                    clusterName, databaseName, e.getMessage());
            return false;
        }
    }

    /**
     * Gets connection statistics
     *
     * @return Map containing connection statistics
     */
    public static Map<String, Object> getConnectionStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("activeClients", getActiveClientCount());
        stats.put("activeClientKeys", getActiveClientKeys());
        stats.put("activeTunnels", sshTunnels.size());
        stats.put("tunnelPorts", new java.util.HashMap<>(sshTunnels));
        return stats;
    }

    /**
     * Refreshes connections for a specific cluster
     * This will close existing connections for the cluster and recreate them on next access
     *
     * @param clusterName Name of the cluster to refresh
     */
    public static void refreshClusterConnections(String clusterName) {
        // Close all clients for this cluster
        mongoClients.entrySet().removeIf(entry -> {
            if (entry.getKey().startsWith(clusterName + "_")) {
                try {
                    entry.getValue().close();
                    log.info("Closed MongoClient during refresh: {}", entry.getKey());
                    return true;
                } catch (Exception e) {
                    log.error("Error closing MongoClient during refresh {}: {}", entry.getKey(), e.getMessage());
                    return true;
                }
            }
            return false;
        });

        // Close SSH tunnel for this cluster
        Integer tunnelPort = sshTunnels.remove(clusterName);
        if (tunnelPort != null) {
            // Note: SSHSessionUtil would need a method to close specific tunnels
            log.info("Removed SSH tunnel for cluster: {} (port: {})", clusterName, tunnelPort);
        }

        log.info("Refreshed connections for cluster: {}", clusterName);
    }
}
