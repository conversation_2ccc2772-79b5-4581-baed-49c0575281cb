package com.lenskart.commons.database.elasticsearch;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Utility class for executing Elasticsearch operations
 */
@Slf4j
public class ElasticsearchExecutor {

    /**
     * Creates an index in Elasticsearch
     *
     * @param esName     Name of the Elasticsearch instance
     * @param indexName  Name of the index to create
     * @param settings   Index settings (can be null)
     * @param mappings   Index mappings (can be null)
     * @return true if index was created successfully, false otherwise
     */
    public static boolean createIndex(String esName, String indexName, String settings, String mappings) {
        RestHighLevelClient client = ElasticsearchConnectionManager.getClient(esName);
        CreateIndexRequest request = new CreateIndexRequest(indexName);
        
        if (settings != null) {
            request.settings(settings, XContentType.JSON);
        }
        
        if (mappings != null) {
            request.mapping(mappings, XContentType.JSON);
        }
        
        try {
            CreateIndexResponse response = client.indices().create(request, RequestOptions.DEFAULT);
            return response.isAcknowledged();
        } catch (IOException e) {
            log.error("Error creating index {}: {}", indexName, e.getMessage(), e);
            throw new RuntimeException("Error creating index: " + e.getMessage(), e);
        }
    }

    /**
     * Checks if an index exists
     *
     * @param esName     Name of the Elasticsearch instance
     * @param indexName  Name of the index to check
     * @return true if index exists, false otherwise
     */
    public static boolean indexExists(String esName, String indexName) {
        RestHighLevelClient client = ElasticsearchConnectionManager.getClient(esName);
        GetIndexRequest request = new GetIndexRequest(indexName);
        
        try {
            return client.indices().exists(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("Error checking if index {} exists: {}", indexName, e.getMessage(), e);
            throw new RuntimeException("Error checking if index exists: " + e.getMessage(), e);
        }
    }

    /**
     * Deletes an index
     *
     * @param esName     Name of the Elasticsearch instance
     * @param indexName  Name of the index to delete
     * @return true if index was deleted successfully, false otherwise
     */
    public static boolean deleteIndex(String esName, String indexName) {
        RestHighLevelClient client = ElasticsearchConnectionManager.getClient(esName);
        DeleteIndexRequest request = new DeleteIndexRequest(indexName);
        
        try {
            return client.indices().delete(request, RequestOptions.DEFAULT).isAcknowledged();
        } catch (IOException e) {
            log.error("Error deleting index {}: {}", indexName, e.getMessage(), e);
            throw new RuntimeException("Error deleting index: " + e.getMessage(), e);
        }
    }

    /**
     * Indexes a document
     *
     * @param esName     Name of the Elasticsearch instance
     * @param indexName  Name of the index
     * @param typeName   Type name (use "_doc" for ES 7+)
     * @param id         Document ID (can be null for auto-generation)
     * @param document   Document to index as a Map
     * @return ID of the indexed document
     */
    public static String index(String esName, String indexName, String typeName, String id, Map<String, Object> document) {
        RestHighLevelClient client = ElasticsearchConnectionManager.getClient(esName);
        IndexRequest request = new IndexRequest(indexName).type(typeName);
        
        if (id != null) {
            request.id(id);
        }
        
        request.source(document);
        
        try {
            IndexResponse response = client.index(request, RequestOptions.DEFAULT);
            return response.getId();
        } catch (IOException e) {
            log.error("Error indexing document to {}/{}: {}", indexName, typeName, e.getMessage(), e);
            throw new RuntimeException("Error indexing document: " + e.getMessage(), e);
        }
    }

    /**
     * Gets a document by ID
     *
     * @param esName     Name of the Elasticsearch instance
     * @param indexName  Name of the index
     * @param typeName   Type name (use "_doc" for ES 7+)
     * @param id         Document ID
     * @return Document as a JSON string, null if not found
     */
    public static String get(String esName, String indexName, String typeName, String id) {
        RestHighLevelClient client = ElasticsearchConnectionManager.getClient(esName);
        GetRequest request = new GetRequest(indexName, typeName, id);
        
        try {
            GetResponse response = client.get(request, RequestOptions.DEFAULT);
            if (response.isExists()) {
                return response.getSourceAsString();
            } else {
                return null;
            }
        } catch (IOException e) {
            log.error("Error getting document {}/{}/{}: {}", indexName, typeName, id, e.getMessage(), e);
            throw new RuntimeException("Error getting document: " + e.getMessage(), e);
        }
    }

    /**
     * Updates a document
     *
     * @param esName     Name of the Elasticsearch instance
     * @param indexName  Name of the index
     * @param typeName   Type name (use "_doc" for ES 7+)
     * @param id         Document ID
     * @param document   Document fields to update
     * @return true if document was updated successfully, false otherwise
     */
    public static boolean update(String esName, String indexName, String typeName, String id, Map<String, Object> document) {
        RestHighLevelClient client = ElasticsearchConnectionManager.getClient(esName);
        UpdateRequest request = new UpdateRequest(indexName, typeName, id).doc(document);
        
        try {
            UpdateResponse response = client.update(request, RequestOptions.DEFAULT);
            return response.getResult() == DocWriteResponse.Result.UPDATED;
        } catch (IOException e) {
            log.error("Error updating document {}/{}/{}: {}", indexName, typeName, id, e.getMessage(), e);
            throw new RuntimeException("Error updating document: " + e.getMessage(), e);
        }
    }

    /**
     * Deletes a document
     *
     * @param esName     Name of the Elasticsearch instance
     * @param indexName  Name of the index
     * @param typeName   Type name (use "_doc" for ES 7+)
     * @param id         Document ID
     * @return true if document was deleted successfully, false otherwise
     */
    public static boolean delete(String esName, String indexName, String typeName, String id) {
        RestHighLevelClient client = ElasticsearchConnectionManager.getClient(esName);
        DeleteRequest request = new DeleteRequest(indexName, typeName, id);
        
        try {
            DeleteResponse response = client.delete(request, RequestOptions.DEFAULT);
            return response.getResult() == DocWriteResponse.Result.DELETED;
        } catch (IOException e) {
            log.error("Error deleting document {}/{}/{}: {}", indexName, typeName, id, e.getMessage(), e);
            throw new RuntimeException("Error deleting document: " + e.getMessage(), e);
        }
    }

    /**
     * Executes a search query
     *
     * @param esName     Name of the Elasticsearch instance
     * @param indexName  Name of the index
     * @param typeName   Type name (use "_doc" for ES 7+)
     * @param query      Query as a JSON string
     * @return Search results as a JSON string
     */
    public static String search(String esName, String indexName, String typeName, String query) {
        RestHighLevelClient client = ElasticsearchConnectionManager.getClient(esName);
        SearchRequest request = new SearchRequest(indexName);
        request.types(typeName);
        
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(QueryBuilders.wrapperQuery(query));
        request.source(sourceBuilder);
        
        try {
            SearchResponse response = client.search(request, RequestOptions.DEFAULT);
            return response.toString();
        } catch (IOException e) {
            log.error("Error searching in {}/{}: {}", indexName, typeName, e.getMessage(), e);
            throw new RuntimeException("Error searching: " + e.getMessage(), e);
        }
    }

    /**
     * Counts documents matching a query
     *
     * @param esName     Name of the Elasticsearch instance
     * @param indexName  Name of the index
     * @param typeName   Type name (use "_doc" for ES 7+)
     * @param query      Query as a JSON string (can be null for match_all)
     * @return Number of matching documents
     */
    public static long count(String esName, String indexName, String typeName, String query) {
        RestHighLevelClient client = ElasticsearchConnectionManager.getClient(esName);
        SearchRequest request = new SearchRequest(indexName);
        request.types(typeName);
        
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        if (query != null) {
            sourceBuilder.query(QueryBuilders.wrapperQuery(query));
        } else {
            sourceBuilder.query(QueryBuilders.matchAllQuery());
        }
        sourceBuilder.size(0); // We only need the count, not the documents
        request.source(sourceBuilder);
        
        try {
            SearchResponse response = client.search(request, RequestOptions.DEFAULT);
            return response.getHits().getTotalHits().value;
        } catch (IOException e) {
            log.error("Error counting documents in {}/{}: {}", indexName, typeName, e.getMessage(), e);
            throw new RuntimeException("Error counting documents: " + e.getMessage(), e);
        }
    }

    /**
     * Executes a bulk operation
     *
     * @param esName     Name of the Elasticsearch instance
     * @param actions    List of actions to execute in bulk
     * @return true if bulk operation was successful, false otherwise
     */
    public static boolean bulk(String esName, List<Object> actions) {
        RestHighLevelClient client = ElasticsearchConnectionManager.getClient(esName);
        BulkRequest request = new BulkRequest();
        
        for (Object action : actions) {
            if (action instanceof IndexRequest) {
                request.add((IndexRequest) action);
            } else if (action instanceof UpdateRequest) {
                request.add((UpdateRequest) action);
            } else if (action instanceof DeleteRequest) {
                request.add((DeleteRequest) action);
            } else {
                throw new IllegalArgumentException("Unsupported action type: " + action.getClass().getName());
            }
        }
        
        try {
            BulkResponse response = client.bulk(request, RequestOptions.DEFAULT);
            return !response.hasFailures();
        } catch (IOException e) {
            log.error("Error executing bulk operation: {}", e.getMessage(), e);
            throw new RuntimeException("Error executing bulk operation: " + e.getMessage(), e);
        }
    }
}