package com.lenskart.commons.database.redis;

import com.jcraft.jsch.JSchException;
import com.lenskart.commons.config.RedisConfig;
import com.lenskart.commons.config.SSHConfig;
import com.lenskart.commons.constants.Constants;
import com.lenskart.commons.loader.ConfigRegistry;
import com.lenskart.commons.loader.RedisConfigLoader;
import com.lenskart.commons.loader.SSHConfigLoader;
import com.lenskart.commons.utils.SSHSessionUtil;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manager class for Redis connections using connection pooling
 */
@Slf4j
public class RedisConnectionManager {

    private static final Map<String, JedisPool> redisPools = new ConcurrentHashMap<>();
    private static final Map<String, SSHConfig> sshConfigs = new ConcurrentHashMap<>();

    /**
     * Gets a Jedis connection from the pool for the specified Redis instance
     *
     * @param redisName Name of the Redis instance to connect to
     * @return Jedis connection
     */
    public static Jedis getConnection(String redisName) {
        if (!redisPools.containsKey(redisName)) {
            synchronized (RedisConnectionManager.class) {
                if (!redisPools.containsKey(redisName)) {
                    // Use ConfigRegistry to get the Redis configuration
                    RedisConfig config = ConfigRegistry.getInstance().getRedisConfig(redisName);
                    if (config == null) {
                        // Fall back to RedisConfigLoader if not found in registry
                        config = RedisConfigLoader.loadConfig(redisName);
                    }
                    redisPools.put(redisName, createJedisPool(config));
                }
            }
        }
        return redisPools.get(redisName).getResource();
    }

    /**
     * Creates a JedisPool from a RedisConfig
     *
     * @param config Redis configuration
     * @return JedisPool configured with the provided settings
     */
    private static JedisPool createJedisPool(RedisConfig config) {
        // Check if we have an SSH config or need to load one
        SSHConfig sshConfig = sshConfigs.computeIfAbsent(config.getName(), k -> {
            // Load SSH configuration from registry
            SSHConfig loadedConfig = SSHConfigLoader.loadConfig();
            if (loadedConfig != null) {
                log.info("Loaded SSH configuration for Redis instance: {}", config.getName());
            }
            return loadedConfig;
        });

        // Configure the JedisPoolConfig
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(config.getMaxTotal());
        poolConfig.setMaxIdle(config.getMaxIdle());
        poolConfig.setMinIdle(config.getMinIdle());
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setMinEvictableIdleTime(Duration.ofMillis(60000));
        poolConfig.setTimeBetweenEvictionRuns(Duration.ofMillis(30000));
        poolConfig.setNumTestsPerEvictionRun(3);
        poolConfig.setBlockWhenExhausted(true);

        String host = config.getHost();
        int port = config.getPort();

        // If SSH config exists and is enabled, create tunnel
        if (sshConfig != null && sshConfig.isEnabled()) {
            try {
                // Create SSH tunnel
                sshConfig = SSHConfig.builder()
                        .enabled(sshConfig.isEnabled())
                        .hostname(sshConfig.getHostname())
                        .port(sshConfig.getPort())
                        .username(sshConfig.getUsername())
                        .password(sshConfig.getPassword())
                        .privateKeyPath(sshConfig.getPrivateKeyPath())
                        .localPort(Constants.DEFAULT_REDIS_LOCAL_PORT)
                        .remoteHost(host)
                        .remotePort(port)
                        .build();

                // Create the SSH tunnel
                int localPort = SSHSessionUtil.createTunnel(sshConfig);
                
                log.info("Created SSH tunnel for Redis instance: {} ({}:{} -> localhost:{})",
                        config.getName(), host, port, localPort);
                
                // Update host and port to use the local tunnel
                host = "localhost";
                port = localPort;
            } catch (JSchException e) {
                log.error("Failed to create SSH tunnel for Redis instance: {}", config.getName(), e);
                // Fall back to direct connection
                log.warn("Falling back to direct connection: {}:{}", config.getHost(), config.getPort());
            }
        }

        // Create and return the JedisPool
        if (config.getPassword() != null && !config.getPassword().isEmpty()) {
            return new JedisPool(poolConfig, host, port, config.getTimeout(), config.getPassword(), 
                    config.getDatabase(), config.isSsl());
        } else {
            return new JedisPool(poolConfig, host, port, config.getTimeout(), null, 
                    config.getDatabase(), config.isSsl());
        }
    }

    /**
     * Closes all Redis connections managed by this connection manager
     */
    public static void closeAllConnections() {
        for (JedisPool pool : redisPools.values()) {
            try {
                pool.close();
            } catch (Exception e) {
                log.error("Error closing Redis pool: {}", e.getMessage(), e);
            }
        }
        redisPools.clear();
        
        // Close all SSH tunnels
        if (!sshConfigs.isEmpty()) {
            SSHSessionUtil.closeAllTunnels();
            sshConfigs.clear();
        }
    }

    /**
     * Closes a specific Redis connection pool
     *
     * @param redisName Name of the Redis instance whose pool should be closed
     */
    public static void closeConnectionPool(String redisName) {
        JedisPool pool = redisPools.get(redisName);
        if (pool != null) {
            try {
                pool.close();
                redisPools.remove(redisName);
                
                // Close associated SSH tunnel if it exists
                SSHConfig config = sshConfigs.remove(redisName);
                if (config != null) {
                    try {
                        SSHSessionUtil.closeTunnel(config);
                        log.info("Closed SSH tunnel for Redis instance: {}", redisName);
                    } catch (Exception e) {
                        log.warn("Error closing SSH tunnel for Redis instance {}: {}", redisName, e.getMessage());
                    }
                }
            } catch (Exception e) {
                log.error("Error closing Redis pool: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * Checks if SSH tunneling is configured and enabled for a specific Redis instance
     *
     * @param redisName Name of the Redis instance
     * @return true if SSH tunneling is configured and enabled, false otherwise
     */
    public static boolean isSSHTunnelingConfigured(String redisName) {
        SSHConfig config = sshConfigs.get(redisName);
        return config != null && config.isEnabled();
    }

    /**
     * Refreshes the SSH configuration for all Redis instances
     * This will close all existing connections and tunnels, and recreate them with the latest configuration
     */
    public static void refreshSSHConfiguration() {
        // Close all existing connections and tunnels
        closeAllConnections();
        
        // The next time a connection is requested, the SSH configuration will be reloaded
        log.info("SSH configuration refreshed. New Redis connections will use updated configuration.");
    }
}
