package com.lenskart.commons.database.redis;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.params.SetParams;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * Utility class for executing Redis operations
 */
@Slf4j
public class RedisExecutor {

    /**
     * Executes a Redis operation with automatic resource management
     *
     * @param redisName Redis instance name
     * @param operation Operation to execute
     * @param <T>       Return type
     * @return Result of the operation
     */
    private static <T> T executeWithJedis(String redisName, Function<Jedis, T> operation) {
        try (Jedis jedis = RedisConnectionManager.getConnection(redisName)) {
            return operation.apply(jedis);
        } catch (Exception e) {
            log.error("Error executing Redis operation on {}: {}", redisName, e.getMessage(), e);
            throw new RuntimeException("Error executing Redis operation: " + e.getMessage(), e);
        }
    }

    /**
     * Gets a value from Redis
     *
     * @param redisName Redis instance name
     * @param key       Key to get
     * @return Value associated with the key, or null if the key does not exist
     */
    public static String get(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.get(key));
    }

    /**
     * Sets a value in Redis
     *
     * @param redisName Redis instance name
     * @param key       Key to set
     * @param value     Value to set
     * @return "OK" if successful
     */
    public static String set(String redisName, String key, String value) {
        return executeWithJedis(redisName, jedis -> jedis.set(key, value));
    }

    /**
     * Sets a value in Redis with expiration
     *
     * @param redisName Redis instance name
     * @param key       Key to set
     * @param value     Value to set
     * @param seconds   Expiration time in seconds
     * @return "OK" if successful
     */
    public static String setex(String redisName, String key, String value, long seconds) {
        return executeWithJedis(redisName, jedis -> jedis.setex(key, seconds, value));
    }

    /**
     * Sets a value in Redis only if the key does not exist
     *
     * @param redisName Redis instance name
     * @param key       Key to set
     * @param value     Value to set
     * @return "OK" if successful, null if the key already exists
     */
    public static String setnx(String redisName, String key, String value) {
        return executeWithJedis(redisName, jedis -> {
            SetParams params = new SetParams().nx();
            return jedis.set(key, value, params);
        });
    }

    /**
     * Deletes a key from Redis
     *
     * @param redisName Redis instance name
     * @param key       Key to delete
     * @return Number of keys deleted (0 or 1)
     */
    public static Long del(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.del(key));
    }

    /**
     * Checks if a key exists in Redis
     *
     * @param redisName Redis instance name
     * @param key       Key to check
     * @return true if the key exists, false otherwise
     */
    public static Boolean exists(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.exists(key));
    }

    /**
     * Sets the expiration time for a key
     *
     * @param redisName Redis instance name
     * @param key       Key to set expiration for
     * @param seconds   Expiration time in seconds
     * @return 1 if successful, 0 if the key does not exist
     */
    public static Long expire(String redisName, String key, long seconds) {
        return executeWithJedis(redisName, jedis -> jedis.expire(key, seconds));
    }

    /**
     * Gets the time to live for a key
     *
     * @param redisName Redis instance name
     * @param key       Key to get TTL for
     * @return TTL in seconds, -1 if the key exists but has no expiration, -2 if the key does not exist
     */
    public static Long ttl(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.ttl(key));
    }

    /**
     * Increments a key by 1
     *
     * @param redisName Redis instance name
     * @param key       Key to increment
     * @return New value after increment
     */
    public static Long incr(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.incr(key));
    }

    /**
     * Increments a key by a specific amount
     *
     * @param redisName Redis instance name
     * @param key       Key to increment
     * @param amount    Amount to increment by
     * @return New value after increment
     */
    public static Long incrBy(String redisName, String key, long amount) {
        return executeWithJedis(redisName, jedis -> jedis.incrBy(key, amount));
    }

    /**
     * Decrements a key by 1
     *
     * @param redisName Redis instance name
     * @param key       Key to decrement
     * @return New value after decrement
     */
    public static Long decr(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.decr(key));
    }

    /**
     * Decrements a key by a specific amount
     *
     * @param redisName Redis instance name
     * @param key       Key to decrement
     * @param amount    Amount to decrement by
     * @return New value after decrement
     */
    public static Long decrBy(String redisName, String key, long amount) {
        return executeWithJedis(redisName, jedis -> jedis.decrBy(key, amount));
    }

    /**
     * Adds a member to a set
     *
     * @param redisName Redis instance name
     * @param key       Set key
     * @param member    Member to add
     * @return Number of members added (0 if the member already exists)
     */
    public static Long sadd(String redisName, String key, String... members) {
        return executeWithJedis(redisName, jedis -> jedis.sadd(key, members));
    }

    /**
     * Removes a member from a set
     *
     * @param redisName Redis instance name
     * @param key       Set key
     * @param member    Member to remove
     * @return Number of members removed (0 if the member does not exist)
     */
    public static Long srem(String redisName, String key, String... members) {
        return executeWithJedis(redisName, jedis -> jedis.srem(key, members));
    }

    /**
     * Gets all members of a set
     *
     * @param redisName Redis instance name
     * @param key       Set key
     * @return Set of members
     */
    public static Set<String> smembers(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.smembers(key));
    }

    /**
     * Checks if a member exists in a set
     *
     * @param redisName Redis instance name
     * @param key       Set key
     * @param member    Member to check
     * @return true if the member exists, false otherwise
     */
    public static Boolean sismember(String redisName, String key, String member) {
        return executeWithJedis(redisName, jedis -> jedis.sismember(key, member));
    }

    /**
     * Gets the number of members in a set
     *
     * @param redisName Redis instance name
     * @param key       Set key
     * @return Number of members
     */
    public static Long scard(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.scard(key));
    }

    /**
     * Adds a member to a sorted set with a score
     *
     * @param redisName Redis instance name
     * @param key       Sorted set key
     * @param score     Score
     * @param member    Member to add
     * @return Number of members added (0 if the member already exists and the score was updated)
     */
    public static Long zadd(String redisName, String key, double score, String member) {
        return executeWithJedis(redisName, jedis -> jedis.zadd(key, score, member));
    }

    /**
     * Removes a member from a sorted set
     *
     * @param redisName Redis instance name
     * @param key       Sorted set key
     * @param member    Member to remove
     * @return Number of members removed (0 if the member does not exist)
     */
    public static Long zrem(String redisName, String key, String... members) {
        return executeWithJedis(redisName, jedis -> jedis.zrem(key, members));
    }

    /**
     * Gets all members of a sorted set with scores
     *
     * @param redisName Redis instance name
     * @param key       Sorted set key
     * @return Set of members with scores
     */
//    public static Set<Tuple> zrangeWithScores(String redisName, String key, long start, long stop) {
//        return executeWithJedis(redisName, jedis -> jedis.zrangeWithScores(key, start, stop));
//    }

    /**
     * Gets the score of a member in a sorted set
     *
     * @param redisName Redis instance name
     * @param key       Sorted set key
     * @param member    Member to get score for
     * @return Score of the member, or null if the member does not exist
     */
    public static Double zscore(String redisName, String key, String member) {
        return executeWithJedis(redisName, jedis -> jedis.zscore(key, member));
    }

    /**
     * Gets the number of members in a sorted set
     *
     * @param redisName Redis instance name
     * @param key       Sorted set key
     * @return Number of members
     */
    public static Long zcard(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.zcard(key));
    }

    /**
     * Sets a hash field
     *
     * @param redisName Redis instance name
     * @param key       Hash key
     * @param field     Field to set
     * @param value     Value to set
     * @return 1 if field is a new field and value was set, 0 if field already exists and value was updated
     */
    public static Long hset(String redisName, String key, String field, String value) {
        return executeWithJedis(redisName, jedis -> jedis.hset(key, field, value));
    }

    /**
     * Sets multiple hash fields
     *
     * @param redisName Redis instance name
     * @param key       Hash key
     * @param hash      Map of fields to values
     * @return Number of fields that were added
     */
    public static Long hset(String redisName, String key, Map<String, String> hash) {
        return executeWithJedis(redisName, jedis -> jedis.hset(key, hash));
    }

    /**
     * Gets a hash field
     *
     * @param redisName Redis instance name
     * @param key       Hash key
     * @param field     Field to get
     * @return Value of the field, or null if the field does not exist
     */
    public static String hget(String redisName, String key, String field) {
        return executeWithJedis(redisName, jedis -> jedis.hget(key, field));
    }

    /**
     * Gets all fields and values in a hash
     *
     * @param redisName Redis instance name
     * @param key       Hash key
     * @return Map of fields to values
     */
    public static Map<String, String> hgetAll(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.hgetAll(key));
    }

    /**
     * Deletes a hash field
     *
     * @param redisName Redis instance name
     * @param key       Hash key
     * @param field     Field to delete
     * @return 1 if field was deleted, 0 if field does not exist
     */
    public static Long hdel(String redisName, String key, String... fields) {
        return executeWithJedis(redisName, jedis -> jedis.hdel(key, fields));
    }

    /**
     * Checks if a hash field exists
     *
     * @param redisName Redis instance name
     * @param key       Hash key
     * @param field     Field to check
     * @return true if the field exists, false otherwise
     */
    public static Boolean hexists(String redisName, String key, String field) {
        return executeWithJedis(redisName, jedis -> jedis.hexists(key, field));
    }

    /**
     * Gets all fields in a hash
     *
     * @param redisName Redis instance name
     * @param key       Hash key
     * @return Set of fields
     */
    public static Set<String> hkeys(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.hkeys(key));
    }

    /**
     * Gets all values in a hash
     *
     * @param redisName Redis instance name
     * @param key       Hash key
     * @return List of values
     */
    public static List<String> hvals(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.hvals(key));
    }

    /**
     * Gets the number of fields in a hash
     *
     * @param redisName Redis instance name
     * @param key       Hash key
     * @return Number of fields
     */
    public static Long hlen(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.hlen(key));
    }

    /**
     * Pushes a value to the head of a list
     *
     * @param redisName Redis instance name
     * @param key       List key
     * @param value     Value to push
     * @return Length of the list after the push
     */
    public static Long lpush(String redisName, String key, String... values) {
        return executeWithJedis(redisName, jedis -> jedis.lpush(key, values));
    }

    /**
     * Pushes a value to the tail of a list
     *
     * @param redisName Redis instance name
     * @param key       List key
     * @param value     Value to push
     * @return Length of the list after the push
     */
    public static Long rpush(String redisName, String key, String... values) {
        return executeWithJedis(redisName, jedis -> jedis.rpush(key, values));
    }

    /**
     * Pops a value from the head of a list
     *
     * @param redisName Redis instance name
     * @param key       List key
     * @return Value popped, or null if the list is empty
     */
    public static String lpop(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.lpop(key));
    }

    /**
     * Pops a value from the tail of a list
     *
     * @param redisName Redis instance name
     * @param key       List key
     * @return Value popped, or null if the list is empty
     */
    public static String rpop(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.rpop(key));
    }

    /**
     * Gets a range of elements from a list
     *
     * @param redisName Redis instance name
     * @param key       List key
     * @param start     Start index (0-based, inclusive)
     * @param stop      Stop index (0-based, inclusive)
     * @return List of elements in the specified range
     */
    public static List<String> lrange(String redisName, String key, long start, long stop) {
        return executeWithJedis(redisName, jedis -> jedis.lrange(key, start, stop));
    }

    /**
     * Gets the length of a list
     *
     * @param redisName Redis instance name
     * @param key       List key
     * @return Length of the list
     */
    public static Long llen(String redisName, String key) {
        return executeWithJedis(redisName, jedis -> jedis.llen(key));
    }


    /**
     * Pings the Redis server
     *
     * @param redisName Redis instance name
     * @return "PONG" if successful
     */
    public static String ping(String redisName) {
        return executeWithJedis(redisName, Jedis::ping);
    }

    /**
     * Flushes the Redis database
     *
     * @param redisName Redis instance name
     * @return "OK" if successful
     */
    public static String flushDB(String redisName) {
        return executeWithJedis(redisName, Jedis::flushDB);
    }

    /**
     * Gets Redis server info
     *
     * @param redisName Redis instance name
     * @return Server info
     */
    public static String info(String redisName) {
        return executeWithJedis(redisName, Jedis::info);
    }
}
