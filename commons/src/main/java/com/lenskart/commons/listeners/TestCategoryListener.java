package com.lenskart.commons.listeners;

import com.lenskart.commons.annotations.TestCategory;
import org.testng.IMethodInstance;
import org.testng.IMethodInterceptor;
import org.testng.ITestContext;
import org.testng.ITestNGMethod;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * TestNG listener to filter test methods based on the test category.
 * This listener intercepts test methods and includes only those that match the specified category.
 */
public class TestCategoryListener implements IMethodInterceptor {

    private static final String CATEGORY_PROPERTY = "testCategory";

    @Override
    public List<IMethodInstance> intercept(List<IMethodInstance> methods, ITestContext context) {
        String categoryName = System.getProperty(CATEGORY_PROPERTY);
        
        // If no category is specified, run all tests
        if (categoryName == null || categoryName.isEmpty()) {
            return methods;
        }
        
        try {
            TestCategory.Category requestedCategory = TestCategory.Category.valueOf(categoryName.toUpperCase());
            List<IMethodInstance> result = new ArrayList<>();
            
            for (IMethodInstance methodInstance : methods) {
                ITestNGMethod method = methodInstance.getMethod();
                Method reflectedMethod = method.getConstructorOrMethod().getMethod();
                
                // Check if method has the annotation
                TestCategory methodAnnotation = reflectedMethod.getAnnotation(TestCategory.class);
                
                // If not, check if the class has the annotation
                if (methodAnnotation == null) {
                    TestCategory classAnnotation = reflectedMethod.getDeclaringClass().getAnnotation(TestCategory.class);
                    
                    // If neither method nor class has the annotation, skip this method
                    if (classAnnotation == null) {
                        continue;
                    }
                    
                    // Use the class annotation
                    if (classAnnotation.value() == requestedCategory) {
                        result.add(methodInstance);
                    }
                } else {
                    // Use the method annotation
                    if (methodAnnotation.value() == requestedCategory) {
                        result.add(methodInstance);
                    }
                }
            }
            
            return result;
        } catch (IllegalArgumentException e) {
            // If the category is not valid, run all tests
            System.err.println("Invalid test category: " + categoryName + ". Valid categories are: " + 
                    Arrays.toString(TestCategory.Category.values()));
            return methods;
        }
    }
}
