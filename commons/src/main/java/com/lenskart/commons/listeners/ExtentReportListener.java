package com.lenskart.commons.listeners;

import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.markuputils.ExtentColor;
import com.aventstack.extentreports.markuputils.MarkupHelper;
import com.lenskart.commons.reporting.ExtentManager;
import com.lenskart.commons.utils.ExtentReportUtils;
import org.testng.ITestContext;
import org.testng.ITestResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;

/**
 * Enhanced ExtentReport listener for comprehensive test reporting
 * Provides detailed step-by-step logging with timestamps and execution details
 */
public class ExtentReportListener extends BaseTestListener {

    private static final Logger logger = LoggerFactory.getLogger(ExtentReportListener.class);
    private static ExtentReports extent;
    private static Map<String, ExtentTest> testMap = new HashMap<>();
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void onStart(ITestContext context) {
        super.onStart(context);
        extent = ExtentManager.getInstance();
    }

    @Override
    public void onFinish(ITestContext context) {
        super.onFinish(context);
        if (extent != null) {
            extent.flush();
        }
    }

    @Override
    public void onTestStart(ITestResult result) {
        super.onTestStart(result);
        
        String testName = getTestName(result);
        ExtentTest test = extent.createTest(testName, getTestDescription(result));
        
        // Add test class as category
        test.assignCategory(result.getTestClass().getName());
        
        // Add test groups as categories
        String[] groups = result.getMethod().getGroups();
        if (groups != null && groups.length > 0) {
            for (String group : groups) {
                test.assignCategory(group);
            }
        }
        
        // Add detailed test information
        String className = result.getTestClass().getName();
        String methodName = result.getMethod().getMethodName();
        String startTime = LocalDateTime.now().format(formatter);
        
        // Add test details to extent report
        test.info("📋 <b>Test Execution Details:</b>");
        test.info("🏷️ <b>Test Class:</b> " + className);
        test.info("🔧 <b>Test Method:</b> " + methodName);
        test.info("⏰ <b>Start Time:</b> " + startTime);
        
        // Add test parameters if any
        Object[] parameters = result.getParameters();
        if (parameters != null && parameters.length > 0) {
            test.info("📊 <b>Test Parameters:</b> " + Arrays.toString(parameters));
        }
        
        // Add environment information
        String environment = System.getProperty("environment", "Unknown");
        String testCategory = System.getProperty("testCategory", "Unknown");
        test.info("🌍 <b>Environment:</b> " + environment);
        test.info("📂 <b>Test Category:</b> " + testCategory);
        
        // Add test groups information
        if (groups != null && groups.length > 0) {
            test.info("🏷️ <b>Test Groups:</b> " + Arrays.toString(groups));
        }
        
        test.info("🚀 <b>Test execution started...</b>");
        
        testMap.put(testName, test);
        
        // Set current test for ExtentReportUtils
        ExtentReportUtils.setCurrentTest(test);
        
        logger.info("Started test: {} in class: {}", methodName, className);
    }

    @Override
    public void onTestSuccess(ITestResult result) {
        super.onTestSuccess(result);
        
        String testName = getTestName(result);
        ExtentTest test = testMap.get(testName);
        
        if (test != null) {
            String endTime = LocalDateTime.now().format(formatter);
            long executionTime = result.getEndMillis() - result.getStartMillis();
            
            test.log(Status.PASS, MarkupHelper.createLabel("✅ TEST PASSED", ExtentColor.GREEN));
            test.info("⏰ <b>End Time:</b> " + endTime);
            test.info("⏱️ <b>Execution Time:</b> " + executionTime + " ms");
            test.info("🎯 <b>Test completed successfully!</b>");
            
            logger.info("Test passed: {} - Execution time: {} ms", testName, executionTime);
        }

        // Clear current test from ExtentReportUtils
        ExtentReportUtils.clearCurrentTest();
    }

    @Override
    public void onTestFailure(ITestResult result) {
        super.onTestFailure(result);
        
        String testName = getTestName(result);
        ExtentTest test = testMap.get(testName);
        
        if (test != null) {
            String endTime = LocalDateTime.now().format(formatter);
            long executionTime = result.getEndMillis() - result.getStartMillis();
            
            // Log failure details
            test.log(Status.FAIL, MarkupHelper.createLabel("❌ TEST FAILED", ExtentColor.RED));
            test.info("⏰ <b>End Time:</b> " + endTime);
            test.info("⏱️ <b>Execution Time:</b> " + executionTime + " ms");
            
            if (result.getThrowable() != null) {
                Throwable throwable = result.getThrowable();
                test.fail("🚨 <b>Error Message:</b> " + throwable.getMessage());
                test.fail("📍 <b>Exception Type:</b> " + throwable.getClass().getSimpleName());
                
                // Add stack trace
                test.fail("📋 <b>Stack Trace:</b>");
                test.fail("<pre>" + getStackTrace(throwable) + "</pre>");
                
                logger.error("Test failed: {} - Error: {}", testName, throwable.getMessage(), throwable);
            }
            
            test.fail("💥 <b>Test execution failed!</b>");
        }

        // Clear current test from ExtentReportUtils
        ExtentReportUtils.clearCurrentTest();
    }
    
    @Override
    public void onTestSkipped(ITestResult result) {
        super.onTestSkipped(result);
        
        String testName = getTestName(result);
        ExtentTest test = testMap.get(testName);
        
        if (test != null) {
            String endTime = LocalDateTime.now().format(formatter);
            
            test.log(Status.SKIP, MarkupHelper.createLabel("⏭️ TEST SKIPPED", ExtentColor.YELLOW));
            test.info("⏰ <b>End Time:</b> " + endTime);
            
            if (result.getThrowable() != null) {
                Throwable throwable = result.getThrowable();
                test.skip("⚠️ <b>Skip Reason:</b> " + throwable.getMessage());
                test.skip("📍 <b>Exception Type:</b> " + throwable.getClass().getSimpleName());
                
                logger.warn("Test skipped: {} - Reason: {}", testName, throwable.getMessage());
            } else {
                test.skip("⚠️ <b>Test was skipped (no specific reason provided)</b>");
                logger.warn("Test skipped: {} - No specific reason", testName);
            }
            
            test.skip("⏭️ <b>Test execution skipped!</b>");
        }

        // Clear current test from ExtentReportUtils
        ExtentReportUtils.clearCurrentTest();
    }

    /**
     * Gets the test name from the test result
     *
     * @param result    Test result
     * @return          Test name
     */
    protected String getTestName(ITestResult result) {
        return result.getTestClass().getName() + "." + result.getMethod().getMethodName();
    }

    /**
     * Gets the test description from the test result
     *
     * @param result    Test result
     * @return          Test description or method name if no description is available
     */
    private String getTestDescription(ITestResult result) {
        String description = result.getMethod().getDescription();
        return description != null && !description.isEmpty() ? description : result.getMethod().getMethodName();
    }
    
    /**
     * Gets the stack trace from a throwable as a formatted string
     * 
     * @param throwable The throwable to get stack trace from
     * @return          Formatted stack trace string
     */
    private String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return "No stack trace available";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(throwable.getClass().getName()).append(": ").append(throwable.getMessage()).append("\n");
        
        StackTraceElement[] elements = throwable.getStackTrace();
        int maxLines = Math.min(elements.length, 10); // Limit to first 10 lines to avoid too much noise
        
        for (int i = 0; i < maxLines; i++) {
            sb.append("    at ").append(elements[i].toString()).append("\n");
        }
        
        if (elements.length > maxLines) {
            sb.append("    ... ").append(elements.length - maxLines).append(" more lines\n");
        }
        
        // Add cause if present
        if (throwable.getCause() != null && throwable.getCause() != throwable) {
            sb.append("Caused by: ").append(throwable.getCause().getClass().getName())
              .append(": ").append(throwable.getCause().getMessage()).append("\n");
            
            StackTraceElement[] causeElements = throwable.getCause().getStackTrace();
            int maxCauseLines = Math.min(causeElements.length, 5); // Limit cause to 5 lines
            
            for (int i = 0; i < maxCauseLines; i++) {
                sb.append("    at ").append(causeElements[i].toString()).append("\n");
            }
            
            if (causeElements.length > maxCauseLines) {
                sb.append("    ... ").append(causeElements.length - maxCauseLines).append(" more lines\n");
            }
        }
        
        return sb.toString();
    }
}
