package com.lenskart.commons.constants;

import java.util.List;

public class Constants {


    public static final int DEFAULT_MYSQL_LOCAL_PORT = 3307;
    public static final int DEFAULT_MONGODB_LOCAL_PORT = 27018;
    public static final int DEFAULT_REDIS_LOCAL_PORT = 6380;
    public static final int DEFAULT_ES_PORT = 9200;
    public static final int DEFAULT_ES_LOCAL_PORT = 9201;
    public static final String RESULT = "result";
    public static final String RESULT_INDEX = "result[0]";
    public static final String PRODUCT_ID = "productID";
    public static final String CUSTOMER = "customer";
    public static final String BARCODE_ID = "barcodeID";
    public static final String CART_ID = "cartID";
    public static final String ORDER_ID = "orderID";
    public static final String ITEM_ID = "itemID";
    public static final String BEARER_TOKEN = "Bearer ";
    public static final String REFUND_INITIATION = "Refund initiation ";
    public static final String ORDER_DISPATCHED = "DISPATCHED";
    public static final String REMARKS_PREPROD_AUTOMATION = "automation preprod test ";
    public static final List<String> WAREHOUSE_FACILITIES = List.of("QNXS2", "NXS2", "UAE1", "SGNXS1", "BR01", "LKH03","LKST607");
    public static final List<String> STOCK_ADJUSTMENT_FACILITIES = List.of("0QNXS", "QNXS2", "LKH03", "NXS2");
    public static final String APPLICATION_JSON = "application/json";
    public static final List<String> LENS_ONLY_PRODUCTS = List.of("96506","96505","96504");
}
