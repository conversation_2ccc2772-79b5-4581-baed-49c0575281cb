package com.lenskart.commons.constants;


import lombok.Getter;

@Getter
public enum HeaderMapper {

    AUTHORIZATION("Authorization"),
    CONTENT_TYPE("Content-Type"),
    ACCEPT("Accept"),
    X_COUNTRY_CODE("X-Country-Code"),
    X_API_CLIENT("X-Api-Client"),
    X_SESSION_TOKEN("X-Session-Token"),
    JWT_TOKEN("jwt-token"),
    FACILITY_CODE("facility-code"),
    X_LENSKART_APP_ID("X-Lenskart-App-Id"),
    SOURCE_DOMAIN("source-domain"),
    X_LENSKART_AUTH_TOKEN("x-lenskart-auth-token"),
    DATE_TIME("date-time"),
    WORKSTATION_ID("workstation-id"),
    X_LENSKART_SESSION_TOKEN("X-Lenskart-Session-Token"),
    X_JUNO_SESSION_TOKEN("X-Juno-Session-Token"),
    X_SERIAL_NUMBER("X-Serial-Number"),
    X_DEVICE_LOCATION("X-Device-Location"),
    X_LENSKART_DISTINCT_ID("X-Lenskart-Distinct-Id"),
    X_FACILITY("x-facility"),
    X_USERNAME("x-username"),
    X_PASSWORD("x-password"),
    X_CS_COOKIE("Cookie"),
    X_AUTH_TOKEN("X-Auth-Token"),
    X_LENSKART_API_KEY("X-Lenskart-API-Key"),
    X_CLIENT_KEY("x-client-key"),
    SIGNATURE_KEY("signature"),
    X_API_KEY("x-api-key"),
    X_API_AUTH_TOKEN("X-Api-Auth-Token"),
    X_REFRESH_TOKEN("X-Refresh-Token"),
    X_CUSTOMER_ID("X-Customer-Id"),
    X_CLIENT_ORG("X-client-org");




    private final String headerName;

    HeaderMapper(String headerName) {
        this.headerName = headerName;
    }
}
