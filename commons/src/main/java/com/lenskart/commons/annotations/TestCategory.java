package com.lenskart.commons.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to categorize test methods into SANITY, REGRESSION, or E2E categories.
 * This annotation can be applied to test methods or test classes.
 * When applied to a class, all test methods in the class inherit the category.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface TestCategory {
    
    /**
     * The category of the test.
     * @return The test category
     */
    Category value();
    
    /**
     * Enum representing test categories
     */
    enum Category {
        SANITY,
        REGRESSION,
        E2E
    }
}
