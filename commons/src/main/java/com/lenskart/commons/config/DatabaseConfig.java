package com.lenskart.commons.config;

import lombok.Builder;
import lombok.Data;

/**
 * Internal database configuration class for multi-cluster system
 * This is used internally by the multi-cluster configuration system
 * and should not be used directly by application code.
 */
@Data
@Builder
public class DatabaseConfig {
    private String name;
    private String url;
    private String username;
    private String password;
    private String driverClassName;
    private int maxPoolSize;
    private int minIdle;
    private long connectionTimeout;
    private long idleTimeout;
    private long maxLifetime;
}
