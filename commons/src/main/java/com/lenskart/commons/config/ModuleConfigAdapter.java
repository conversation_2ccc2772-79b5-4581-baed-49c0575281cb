package com.lenskart.commons.config;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * Generic adapter that wraps existing module configuration registries
 * to implement the ConfigProvider interface. This allows existing registries
 * to be used without modification.
 */
@Slf4j
public class ModuleConfigAdapter implements ConfigProvider {
    
    private final Object registryInstance;
    private final String moduleName;
    private final Method getBaseUrlMethod;
    private final Method getAllBaseUrlsMethod;
    private final Method refreshMethod;
    
    /**
     * Creates an adapter for a module configuration registry
     *
     * @param registryInstance The registry instance to wrap
     * @param moduleName The name of the module for logging
     */
    public ModuleConfigAdapter(Object registryInstance, String moduleName) {
        this.registryInstance = registryInstance;
        this.moduleName = moduleName;
        
        // Cache reflection methods for performance
        this.getBaseUrlMethod = findMethod("getBaseUrl", String.class);
        this.getAllBaseUrlsMethod = findMethod("getAllBaseUrls");
        this.refreshMethod = findMethod("refresh");
        
        if (getBaseUrlMethod == null) {
            throw new IllegalArgumentException("Registry must have a getBaseUrl(String) method");
        }
        
        log.info("Created ModuleConfigAdapter for module: {}", moduleName);
    }
    
    /**
     * Factory method to create adapters for known module registries
     *
     * @param moduleName The module name (juno, nexs, pos, etc.)
     * @return ConfigProvider adapter for the module
     */
    public static ConfigProvider createForModule(String moduleName) {
        try {
            switch (moduleName.toLowerCase()) {
                case "juno":
                    return createJunoAdapter();
                case "nexs":
                    return createNexsAdapter();
                case "pos":
                    return createPosAdapter();
                case "cs":
                    return createCseAdapter();
                case "scm":
                    return createScmAdapter();
                case "cosmos":
                    return createScmAdapter();
                default:
                    log.warn("Unknown module name: {}", moduleName);
                    return null;
            }
        } catch (Exception e) {
            log.error("Failed to create adapter for module {}: {}", moduleName, e.getMessage(), e);
            return null;
        }
    }
    
    private static ConfigProvider createJunoAdapter() {
        try {
            Class<?> registryClass = Class.forName("com.lenskart.juno.config.JunoConfigRegistry");
            Method getInstanceMethod = registryClass.getMethod("getInstance");
            Object instance = getInstanceMethod.invoke(null);
            return new ModuleConfigAdapter(instance, "juno");
        } catch (Exception e) {
            log.warn("Could not create Juno config adapter: {}", e.getMessage());
            return null;
        }
    }
    
    private static ConfigProvider createNexsAdapter() {
        try {
            Class<?> registryClass = Class.forName("com.lenskart.nexs.config.NexsConfigRegistry");
            Method getInstanceMethod = registryClass.getMethod("getInstance");
            Object instance = getInstanceMethod.invoke(null);
            return new ModuleConfigAdapter(instance, "nexs");
        } catch (Exception e) {
            log.warn("Could not create Nexs config adapter: {}", e.getMessage());
            return null;
        }
    }
    
    private static ConfigProvider createPosAdapter() {
        try {
            Class<?> registryClass = Class.forName("com.lenskart.pos.config.PosConfigRegistry");
            Method getInstanceMethod = registryClass.getMethod("getInstance");
            Object instance = getInstanceMethod.invoke(null);
            return new ModuleConfigAdapter(instance, "pos");
        } catch (Exception e) {
            log.warn("Could not create Pos config adapter: {}", e.getMessage());
            return null;
        }
    }
    
    private static ConfigProvider createCseAdapter() {
        try {
            Class<?> registryClass = Class.forName("com.lenskart.cse.config.CsConfigRegistry");
            Method getInstanceMethod = registryClass.getMethod("getInstance");
            Object instance = getInstanceMethod.invoke(null);
            return new ModuleConfigAdapter(instance, "cs");
        } catch (Exception e) {
            log.warn("Could not create CSE config adapter: {}", e.getMessage());
            return null;
        }
    }
    
    private static ConfigProvider createScmAdapter() {
        try {
            Class<?> registryClass = Class.forName("com.lenskart.scm.config.ScmConfigRegistry");
            Method getInstanceMethod = registryClass.getMethod("getInstance");
            Object instance = getInstanceMethod.invoke(null);
            return new ModuleConfigAdapter(instance, "scm");
        } catch (Exception e) {
            log.warn("Could not create SCM config adapter: {}", e.getMessage());
            return null;
        }
    }

    private static ConfigProvider createCosmosAdapter() {
        try {
            Class<?> registryClass = Class.forName("com.lenskart.cosmos.config.CosmosConfigRegistry");
            Method getInstanceMethod = registryClass.getMethod("getInstance");
            Object instance = getInstanceMethod.invoke(null);
            return new ModuleConfigAdapter(instance, "cosmos");
        } catch (Exception e) {
            log.warn("Could not create Cosmos config adapter: {}", e.getMessage());
            return null;
        }
    }
    
    @Override
    public String getBaseUrl(String serviceName) {
        if (getBaseUrlMethod == null || serviceName == null) {
            return null;
        }
        
        try {
            return (String) getBaseUrlMethod.invoke(registryInstance, serviceName);
        } catch (Exception e) {
            log.error("Error getting base URL for service {} from module {}: {}", 
                serviceName, moduleName, e.getMessage());
            return null;
        }
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public Map<String, String> getAllBaseUrls() {
        if (getAllBaseUrlsMethod == null) {
            // Fallback: return empty map
            return new HashMap<>();
        }
        
        try {
            Object result = getAllBaseUrlsMethod.invoke(registryInstance);
            if (result instanceof Map) {
                return (Map<String, String>) result;
            }
        } catch (Exception e) {
            log.error("Error getting all base URLs from module {}: {}", moduleName, e.getMessage());
        }
        
        return new HashMap<>();
    }
    
    @Override
    public void refresh() {
        if (refreshMethod == null) {
            log.warn("Refresh method not available for module: {}", moduleName);
            return;
        }
        
        try {
            refreshMethod.invoke(registryInstance);
            log.debug("Refreshed configuration for module: {}", moduleName);
        } catch (Exception e) {
            log.error("Error refreshing configuration for module {}: {}", moduleName, e.getMessage());
        }
    }
    
    @Override
    public boolean isInitialized() {
        // Assume initialized if we have a registry instance
        return registryInstance != null;
    }
    
    @Override
    public String getProviderName() {
        return moduleName + "ConfigAdapter";
    }
    
    /**
     * Finds a method by name and parameter types using reflection
     *
     * @param methodName The name of the method
     * @param parameterTypes The parameter types
     * @return The method if found, null otherwise
     */
    private Method findMethod(String methodName, Class<?>... parameterTypes) {
        try {
            Method method = registryInstance.getClass().getMethod(methodName, parameterTypes);
            method.setAccessible(true);
            return method;
        } catch (NoSuchMethodException e) {
            log.debug("Method {} not found in {}", methodName, registryInstance.getClass().getSimpleName());
            return null;
        } catch (Exception e) {
            log.warn("Error finding method {} in {}: {}", 
                methodName, registryInstance.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }
    
    /**
     * Gets the wrapped registry instance
     *
     * @return The wrapped registry instance
     */
    public Object getRegistryInstance() {
        return registryInstance;
    }
    
    /**
     * Gets the module name
     *
     * @return The module name
     */
    public String getModuleName() {
        return moduleName;
    }
}
