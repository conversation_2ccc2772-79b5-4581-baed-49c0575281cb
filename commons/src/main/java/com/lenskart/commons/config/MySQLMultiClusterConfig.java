package com.lenskart.commons.config;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Configuration class for multiple MySQL clusters with dynamic database access
 */
@Data
@Builder
public class MySQLMultiClusterConfig {
    
    // Map of cluster name to cluster configuration
    private Map<String, ClusterConfig> clusters;
    
    /**
     * Configuration for a single MySQL cluster
     */
    @Data
    @Builder
    public static class ClusterConfig {
        private String clusterName;
        private String host;
        private int port;
        private String username;
        private String password;
        private String driverClassName;
        
        // Connection pool settings
        private int maxPoolSize;
        private int minIdle;
        private long connectionTimeout;
        private long idleTimeout;
        private long maxLifetime;
        
        // Additional JDBC parameters (optional)
        private Map<String, String> additionalParams;
    }
    
    /**
     * Gets cluster configuration by name
     *
     * @param clusterName Name of the cluster
     * @return ClusterConfig for the specified cluster
     * @throws IllegalArgumentException if cluster is not found
     */
    public ClusterConfig getClusterConfig(String clusterName) {
        ClusterConfig config = clusters.get(clusterName);
        if (config == null) {
            throw new IllegalArgumentException("Cluster configuration not found for: " + clusterName + 
                ". Available clusters: " + clusters.keySet());
        }
        return config;
    }
    
    /**
     * Creates a JDBC URL for a specific database in a cluster
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return Complete JDBC URL
     */
    public String createJdbcUrl(String clusterName, String databaseName) {
        ClusterConfig cluster = getClusterConfig(clusterName);
        
        StringBuilder url = new StringBuilder();
        url.append("jdbc:mysql://")
           .append(cluster.getHost())
           .append(":")
           .append(cluster.getPort())
           .append("/")
           .append(databaseName);
        
        // Add default parameters
        url.append("?useSSL=false&serverTimezone=UTC");
        
        // Add additional parameters if specified
        if (cluster.getAdditionalParams() != null) {
            for (Map.Entry<String, String> param : cluster.getAdditionalParams().entrySet()) {
                url.append("&").append(param.getKey()).append("=").append(param.getValue());
            }
        }
        
        return url.toString();
    }
    
    /**
     * Creates a DatabaseConfig for a specific database in a cluster
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return DatabaseConfig object
     */
    public DatabaseConfig createDatabaseConfig(String clusterName, String databaseName) {
        ClusterConfig cluster = getClusterConfig(clusterName);
        
        return DatabaseConfig.builder()
                .name(clusterName + "_" + databaseName)
                .url(createJdbcUrl(clusterName, databaseName))
                .username(cluster.getUsername())
                .password(cluster.getPassword())
                .driverClassName(cluster.getDriverClassName())
                .maxPoolSize(cluster.getMaxPoolSize())
                .minIdle(cluster.getMinIdle())
                .connectionTimeout(cluster.getConnectionTimeout())
                .idleTimeout(cluster.getIdleTimeout())
                .maxLifetime(cluster.getMaxLifetime())
                .build();
    }
    
    /**
     * Checks if a cluster is configured
     *
     * @param clusterName Name of the cluster to check
     * @return true if the cluster is configured, false otherwise
     */
    public boolean isClusterConfigured(String clusterName) {
        return clusters.containsKey(clusterName);
    }
    
    /**
     * Gets all configured cluster names
     *
     * @return Set of all configured cluster names
     */
    public java.util.Set<String> getConfiguredClusters() {
        return clusters.keySet();
    }
    
    /**
     * Gets the number of configured clusters
     *
     * @return Number of configured clusters
     */
    public int getClusterCount() {
        return clusters.size();
    }
    
    /**
     * Validates that all required cluster configurations are present
     *
     * @return true if all clusters are valid, false otherwise
     */
    public boolean validateClusters() {
        for (Map.Entry<String, ClusterConfig> entry : clusters.entrySet()) {
            String clusterName = entry.getKey();
            ClusterConfig config = entry.getValue();
            
            if (config.getHost() == null || config.getHost().trim().isEmpty()) {
                throw new IllegalStateException("Host is required for cluster: " + clusterName);
            }
            if (config.getUsername() == null || config.getUsername().trim().isEmpty()) {
                throw new IllegalStateException("Username is required for cluster: " + clusterName);
            }
            if (config.getPassword() == null || config.getPassword().trim().isEmpty()) {
                throw new IllegalStateException("Password is required for cluster: " + clusterName);
            }
        }
        return true;
    }
    
    /**
     * Gets cluster statistics
     *
     * @return Map containing cluster statistics
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalClusters", clusters.size());
        stats.put("clusterNames", clusters.keySet());
        
        // Cluster details
        Map<String, Map<String, Object>> clusterDetails = new java.util.HashMap<>();
        for (Map.Entry<String, ClusterConfig> entry : clusters.entrySet()) {
            String clusterName = entry.getKey();
            ClusterConfig config = entry.getValue();
            
            Map<String, Object> details = new java.util.HashMap<>();
            details.put("host", config.getHost());
            details.put("port", config.getPort());
            details.put("username", config.getUsername());
            details.put("maxPoolSize", config.getMaxPoolSize());
            details.put("minIdle", config.getMinIdle());
            
            clusterDetails.put(clusterName, details);
        }
        stats.put("clusterDetails", clusterDetails);
        
        return stats;
    }
}
