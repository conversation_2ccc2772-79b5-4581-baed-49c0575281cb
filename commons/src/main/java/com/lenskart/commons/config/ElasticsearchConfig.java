package com.lenskart.commons.config;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Configuration class for Elasticsearch connection details
 */
@Data
@Builder
public class ElasticsearchConfig {
    private String name;
    private List<String> hosts;
    private String username;
    private String password;
    private int connectTimeout;
    private int readTimeout;
    private int maxTotalConnections;
    private int defaultMaxTotalConnectionsPerRoute;
    private boolean discoveryEnabled;
    private boolean multiThreaded;
}
