package com.lenskart.commons.config;

import lombok.Builder;
import lombok.Data;

/**
 * Configuration class for Redis connection details
 */
@Data
@Builder
public class RedisConfig {
    private String name;
    private String host;
    private int port;
    private String password;
    private int database;
    private boolean ssl;
    private int timeout;
    private int maxTotal;
    private int maxIdle;
    private int minIdle;
}
