package com.lenskart.commons.config;

import java.util.Map;

/**
 * Interface for configuration providers that supply base URLs for services.
 * This allows different modules to provide their own configuration sources
 * while maintaining a consistent interface for URL resolution.
 */
public interface ConfigProvider {
    
    /**
     * Gets the base URL for a specific service
     *
     * @param serviceName The name of the service (e.g., "sessionService", "pickingService")
     * @return The base URL for the service, or null if not found
     */
    String getBaseUrl(String serviceName);
    
    /**
     * Gets all base URLs as a map
     *
     * @return Map of service names to base URLs
     */
    Map<String, String> getAllBaseUrls();
    
    /**
     * Refreshes the configuration from the underlying source
     * This method should reload configurations and clear any caches
     */
    void refresh();
    
    /**
     * Checks if the configuration provider is properly initialized
     *
     * @return true if the provider is ready to serve configurations
     */
    default boolean isInitialized() {
        return true;
    }
    
    /**
     * Gets the name of this configuration provider for logging/debugging
     *
     * @return A descriptive name for this provider
     */
    default String getProviderName() {
        return this.getClass().getSimpleName();
    }
}
