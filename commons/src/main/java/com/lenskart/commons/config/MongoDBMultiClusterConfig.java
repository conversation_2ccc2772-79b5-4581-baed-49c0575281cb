package com.lenskart.commons.config;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration class for multiple MongoDB clusters with dynamic database access
 */
@Data
@Builder
public class MongoDBMultiClusterConfig {
    
    // Map of cluster name to cluster configuration
    private Map<String, ClusterConfig> clusters;
    
    /**
     * Configuration for a single MongoDB cluster
     */
    @Data
    @Builder
    public static class ClusterConfig {
        private String clusterName;
        private String host;
        private int port;
        private String username;
        private String password;
        private String authSource;
        private String authMechanism;
        
        // Connection pool settings
        private int maxPoolSize;
        private int minPoolSize;
        private int connectTimeout;
        private int socketTimeout;
        private int maxIdleTimeMS;
        private int maxLifeTimeMS;
        
        // Additional connection parameters (optional)
        private Map<String, String> additionalParams;
    }
    
    /**
     * Gets the cluster configuration for a specific cluster
     *
     * @param clusterName Name of the cluster
     * @return Cluster configuration
     * @throws IllegalArgumentException if cluster is not found
     */
    public ClusterConfig getClusterConfig(String clusterName) {
        if (clusters == null || !clusters.containsKey(clusterName)) {
            throw new IllegalArgumentException("MongoDB cluster not found: " + clusterName);
        }
        return clusters.get(clusterName);
    }
    
    /**
     * Creates a MongoDB URI for a specific database in a cluster
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return Complete MongoDB URI
     */
    public String createMongoDBUri(String clusterName, String databaseName) {
        ClusterConfig cluster = getClusterConfig(clusterName);
        
        StringBuilder uri = new StringBuilder();
        uri.append("mongodb://");
        
        // Add credentials if provided
        if (cluster.getUsername() != null && !cluster.getUsername().trim().isEmpty()) {
            uri.append(cluster.getUsername());
            if (cluster.getPassword() != null && !cluster.getPassword().trim().isEmpty()) {
                uri.append(":").append(cluster.getPassword());
            }
            uri.append("@");
        }
        
        // Add host and port
        uri.append(cluster.getHost())
           .append(":")
           .append(cluster.getPort())
           .append("/")
           .append(databaseName);
        
        // Add authentication parameters
        StringBuilder params = new StringBuilder();
        if (cluster.getAuthSource() != null && !cluster.getAuthSource().trim().isEmpty()) {
            params.append("authSource=").append(cluster.getAuthSource());
        }
        if (cluster.getAuthMechanism() != null && !cluster.getAuthMechanism().trim().isEmpty()) {
            if (params.length() > 0) params.append("&");
            params.append("authMechanism=").append(cluster.getAuthMechanism());
        }
        
        // Add additional parameters if specified
        if (cluster.getAdditionalParams() != null) {
            for (Map.Entry<String, String> param : cluster.getAdditionalParams().entrySet()) {
                if (params.length() > 0) params.append("&");
                params.append(param.getKey()).append("=").append(param.getValue());
            }
        }
        
        if (params.length() > 0) {
            uri.append("?").append(params);
        }
        
        return uri.toString();
    }
    
    /**
     * Creates a MongoDBConfig for a specific database in a cluster
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return MongoDBConfig object
     */
    public MongoDBConfig createMongoDBConfig(String clusterName, String databaseName) {
        ClusterConfig cluster = getClusterConfig(clusterName);
        
        return MongoDBConfig.builder()
                .name(clusterName + "_" + databaseName)
                .uri(createMongoDBUri(clusterName, databaseName))
                .database(databaseName)
                .username(cluster.getUsername())
                .password(cluster.getPassword())
                .authSource(cluster.getAuthSource())
                .authMechanism(cluster.getAuthMechanism())
                .connectTimeout(cluster.getConnectTimeout())
                .socketTimeout(cluster.getSocketTimeout())
                .maxPoolSize(cluster.getMaxPoolSize())
                .minPoolSize(cluster.getMinPoolSize())
                .maxIdleTimeMS(cluster.getMaxIdleTimeMS())
                .maxLifeTimeMS(cluster.getMaxLifeTimeMS())
                .build();
    }
    
    /**
     * Checks if a cluster is configured
     *
     * @param clusterName Name of the cluster to check
     * @return true if the cluster is configured, false otherwise
     */
    public boolean isClusterConfigured(String clusterName) {
        return clusters != null && clusters.containsKey(clusterName);
    }
    
    /**
     * Gets all configured cluster names
     *
     * @return Set of cluster names
     */
    public java.util.Set<String> getClusterNames() {
        return clusters != null ? clusters.keySet() : java.util.Collections.emptySet();
    }
    
    /**
     * Gets the number of configured clusters
     *
     * @return Number of clusters
     */
    public int getClusterCount() {
        return clusters != null ? clusters.size() : 0;
    }
    
    /**
     * Validates all cluster configurations
     *
     * @return true if all configurations are valid, false otherwise
     */
    public boolean validateConfigurations() {
        if (clusters == null || clusters.isEmpty()) {
            return false;
        }
        
        for (Map.Entry<String, ClusterConfig> entry : clusters.entrySet()) {
            String clusterName = entry.getKey();
            ClusterConfig config = entry.getValue();
            
            // Validate required fields
            if (config.getHost() == null || config.getHost().trim().isEmpty()) {
                throw new IllegalStateException("Host is required for cluster: " + clusterName);
            }
            if (config.getPort() <= 0) {
                throw new IllegalStateException("Valid port is required for cluster: " + clusterName);
            }
            if (config.getUsername() == null || config.getUsername().trim().isEmpty()) {
                throw new IllegalStateException("Username is required for cluster: " + clusterName);
            }
            if (config.getPassword() == null || config.getPassword().trim().isEmpty()) {
                throw new IllegalStateException("Password is required for cluster: " + clusterName);
            }
        }
        return true;
    }
    
    /**
     * Gets cluster statistics
     *
     * @return Map containing cluster statistics
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalClusters", getClusterCount());
        stats.put("clusterNames", getClusterNames());
        
        // Cluster details
        Map<String, Map<String, Object>> clusterDetails = new HashMap<>();
        if (clusters != null) {
            for (Map.Entry<String, ClusterConfig> entry : clusters.entrySet()) {
                String clusterName = entry.getKey();
                ClusterConfig config = entry.getValue();
                
                Map<String, Object> details = new HashMap<>();
                details.put("host", config.getHost());
                details.put("port", config.getPort());
                details.put("username", config.getUsername());
                details.put("authSource", config.getAuthSource());
                details.put("authMechanism", config.getAuthMechanism());
                details.put("maxPoolSize", config.getMaxPoolSize());
                details.put("minPoolSize", config.getMinPoolSize());
                details.put("connectTimeout", config.getConnectTimeout());
                details.put("socketTimeout", config.getSocketTimeout());
                
                clusterDetails.put(clusterName, details);
            }
        }
        stats.put("clusterDetails", clusterDetails);
        
        return stats;
    }
    
    /**
     * Creates a copy of this configuration with additional parameters for a specific cluster
     *
     * @param clusterName Name of the cluster to modify
     * @param additionalParams Additional parameters to add
     * @return New configuration with additional parameters
     */
    public MongoDBMultiClusterConfig withAdditionalParams(String clusterName, Map<String, String> additionalParams) {
        if (!isClusterConfigured(clusterName)) {
            throw new IllegalArgumentException("Cluster not found: " + clusterName);
        }
        
        Map<String, ClusterConfig> newClusters = new HashMap<>(clusters);
        ClusterConfig originalConfig = clusters.get(clusterName);
        
        Map<String, String> mergedParams = new HashMap<>();
        if (originalConfig.getAdditionalParams() != null) {
            mergedParams.putAll(originalConfig.getAdditionalParams());
        }
        if (additionalParams != null) {
            mergedParams.putAll(additionalParams);
        }
        
        ClusterConfig newConfig = ClusterConfig.builder()
                .clusterName(originalConfig.getClusterName())
                .host(originalConfig.getHost())
                .port(originalConfig.getPort())
                .username(originalConfig.getUsername())
                .password(originalConfig.getPassword())
                .authSource(originalConfig.getAuthSource())
                .authMechanism(originalConfig.getAuthMechanism())
                .maxPoolSize(originalConfig.getMaxPoolSize())
                .minPoolSize(originalConfig.getMinPoolSize())
                .connectTimeout(originalConfig.getConnectTimeout())
                .socketTimeout(originalConfig.getSocketTimeout())
                .maxIdleTimeMS(originalConfig.getMaxIdleTimeMS())
                .maxLifeTimeMS(originalConfig.getMaxLifeTimeMS())
                .additionalParams(mergedParams)
                .build();
        
        newClusters.put(clusterName, newConfig);
        
        return MongoDBMultiClusterConfig.builder()
                .clusters(newClusters)
                .build();
    }
}
