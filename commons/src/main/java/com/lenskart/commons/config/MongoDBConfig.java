package com.lenskart.commons.config;

import lombok.Builder;
import lombok.Data;

/**
 * Configuration class for MongoDB connection details
 */
@Data
@Builder
public class MongoDBConfig {
    private String name;
    private String uri;
    private String database;
    private String username;
    private String password;
    private String authSource;
    private String authMechanism;
    private int connectTimeout;
    private int socketTimeout;
    private int maxPoolSize;
    private int minPoolSize;
    private int maxIdleTimeMS;
    private int maxLifeTimeMS;

    /**
     * Creates a MongoDB URI based on host, port, and database name
     *
     * @param host     MongoDB host
     * @param port     MongoDB port
     * @param database MongoDB database name
     * @return Complete MongoDB URI
     */
    public static String createMongoDBUri(String host, int port, String database) {
        return String.format("mongodb://%s:%d/%s", host, port, database);
    }
}
