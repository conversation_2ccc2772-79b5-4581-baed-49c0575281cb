package com.lenskart.commons.config;

import com.lenskart.commons.loader.ConfigRegistry;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Unified configuration registry that can delegate to module-specific registries
 * or fall back to the commons ConfigRegistry. This provides a single point of
 * configuration access for the endpoint management system.
 */
@Slf4j
public class UnifiedConfigRegistry implements ConfigProvider {
    
    // Cache for base URLs
    private final Map<String, String> baseUrlCache = new ConcurrentHashMap<>();
    
    // Map of module-specific config providers
    private final Map<String, ConfigProvider> moduleProviders = new ConcurrentHashMap<>();
    
    // Fallback to commons ConfigRegistry
    private final ConfigRegistry commonsRegistry;
    
    // Singleton instance
    private static volatile UnifiedConfigRegistry instance;
    
    // Private constructor for singleton
    private UnifiedConfigRegistry() {
        this.commonsRegistry = ConfigRegistry.getInstance();
        loadBaseUrls();
    }
    
    /**
     * Gets the singleton instance of UnifiedConfigRegistry
     *
     * @return The singleton instance
     */
    public static UnifiedConfigRegistry getInstance() {
        if (instance == null) {
            synchronized (UnifiedConfigRegistry.class) {
                if (instance == null) {
                    instance = new UnifiedConfigRegistry();
                }
            }
        }
        return instance;
    }
    
    /**
     * Registers a module-specific configuration provider
     *
     * @param moduleName The name of the module (e.g., "juno", "nexs", "pos")
     * @param provider The configuration provider for the module
     */
    public void registerModuleProvider(String moduleName, ConfigProvider provider) {
        if (moduleName == null || provider == null) {
            throw new IllegalArgumentException("Module name and provider cannot be null");
        }
        
        moduleProviders.put(moduleName.toLowerCase(), provider);
        log.info("Registered configuration provider for module: {}", moduleName);
        
        // Refresh cache to include new provider's URLs
        refreshCache();
    }
    
    /**
     * Unregisters a module-specific configuration provider
     *
     * @param moduleName The name of the module to unregister
     */
    public void unregisterModuleProvider(String moduleName) {
        if (moduleName != null) {
            ConfigProvider removed = moduleProviders.remove(moduleName.toLowerCase());
            if (removed != null) {
                log.info("Unregistered configuration provider for module: {}", moduleName);
                refreshCache();
            }
        }
    }
    
    @Override
    public String getBaseUrl(String serviceName) {
        if (serviceName == null) {
            return null;
        }
        
        // Check cache first
        String cachedUrl = baseUrlCache.get(serviceName);
        if (cachedUrl != null) {
            return cachedUrl;
        }
        
        // Try module-specific providers first
        for (ConfigProvider provider : moduleProviders.values()) {
            try {
                String url = provider.getBaseUrl(serviceName);
                if (url != null) {
                    baseUrlCache.put(serviceName, url);
                    log.debug("Found base URL for service {} from module provider: {}", serviceName, url);
                    return url;
                }
            } catch (Exception e) {
                log.warn("Error getting base URL from module provider {}: {}", 
                    provider.getProviderName(), e.getMessage());
            }
        }
        
        // Fall back to commons registry
        try {
            String url = commonsRegistry.getBaseUrl(serviceName);
            if (url != null) {
                baseUrlCache.put(serviceName, url);
                log.debug("Found base URL for service {} from commons registry: {}", serviceName, url);
                return url;
            }
        } catch (Exception e) {
            log.warn("Error getting base URL from commons registry: {}", e.getMessage());
        }
        
        log.warn("Base URL not found for service: {}", serviceName);
        return null;
    }
    
    @Override
    public Map<String, String> getAllBaseUrls() {
        Map<String, String> allUrls = new HashMap<>();
        
        // Add URLs from commons registry
        try {
            Map<String, String> commonsUrls = commonsRegistry.getAllBaseUrls();
            if (commonsUrls != null) {
                allUrls.putAll(commonsUrls);
            }
        } catch (Exception e) {
            log.warn("Error getting all base URLs from commons registry: {}", e.getMessage());
        }
        
        // Add URLs from module providers (these will override commons URLs if there are conflicts)
        for (ConfigProvider provider : moduleProviders.values()) {
            try {
                Map<String, String> moduleUrls = provider.getAllBaseUrls();
                if (moduleUrls != null) {
                    allUrls.putAll(moduleUrls);
                }
            } catch (Exception e) {
                log.warn("Error getting all base URLs from module provider {}: {}", 
                    provider.getProviderName(), e.getMessage());
            }
        }
        
        // Add cached URLs
        allUrls.putAll(baseUrlCache);
        
        return allUrls;
    }
    
    @Override
    public void refresh() {
        log.info("Refreshing unified configuration registry");
        
        // Clear cache
        baseUrlCache.clear();
        
        // Refresh commons registry
        try {
            commonsRegistry.refresh();
        } catch (Exception e) {
            log.error("Error refreshing commons registry: {}", e.getMessage(), e);
        }
        
        // Refresh all module providers
        for (Map.Entry<String, ConfigProvider> entry : moduleProviders.entrySet()) {
            try {
                entry.getValue().refresh();
                log.debug("Refreshed configuration for module: {}", entry.getKey());
            } catch (Exception e) {
                log.error("Error refreshing configuration for module {}: {}", 
                    entry.getKey(), e.getMessage(), e);
            }
        }
        
        // Reload base URLs
        loadBaseUrls();
        
        log.info("Unified configuration registry refresh completed");
    }
    
    @Override
    public boolean isInitialized() {
        // Check if commons registry is initialized
        boolean commonsInitialized = commonsRegistry != null;
        
        // Check if at least one module provider is initialized
        boolean moduleInitialized = moduleProviders.values().stream()
            .anyMatch(ConfigProvider::isInitialized);
        
        return commonsInitialized || moduleInitialized;
    }
    
    @Override
    public String getProviderName() {
        return "UnifiedConfigRegistry";
    }
    
    /**
     * Loads base URLs from all available sources into the cache
     */
    private void loadBaseUrls() {
        log.debug("Loading base URLs into cache");
        
        // Load from commons registry first
        try {
            Map<String, String> commonsUrls = commonsRegistry.getAllBaseUrls();
            if (commonsUrls != null) {
                baseUrlCache.putAll(commonsUrls);
                log.debug("Loaded {} base URLs from commons registry", commonsUrls.size());
            }
        } catch (Exception e) {
            log.warn("Error loading base URLs from commons registry: {}", e.getMessage());
        }
        
        // Load from module providers (these will override commons URLs)
        for (Map.Entry<String, ConfigProvider> entry : moduleProviders.entrySet()) {
            try {
                Map<String, String> moduleUrls = entry.getValue().getAllBaseUrls();
                if (moduleUrls != null) {
                    baseUrlCache.putAll(moduleUrls);
                    log.debug("Loaded {} base URLs from module provider: {}", 
                        moduleUrls.size(), entry.getKey());
                }
            } catch (Exception e) {
                log.warn("Error loading base URLs from module provider {}: {}", 
                    entry.getKey(), e.getMessage());
            }
        }
        
        log.info("Loaded total of {} base URLs into cache", baseUrlCache.size());
    }
    
    /**
     * Refreshes the cache by reloading all base URLs
     */
    private void refreshCache() {
        baseUrlCache.clear();
        loadBaseUrls();
    }
    
    /**
     * Gets the number of registered module providers
     *
     * @return Number of registered module providers
     */
    public int getModuleProviderCount() {
        return moduleProviders.size();
    }
    
    /**
     * Gets the names of all registered module providers
     *
     * @return Set of module provider names
     */
    public java.util.Set<String> getRegisteredModules() {
        return new java.util.HashSet<>(moduleProviders.keySet());
    }
    
    /**
     * Gets statistics about the configuration registry
     *
     * @return Map containing various statistics
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("moduleProviders", moduleProviders.size());
        stats.put("cachedUrls", baseUrlCache.size());
        stats.put("registeredModules", getRegisteredModules());
        stats.put("initialized", isInitialized());
        return stats;
    }
}
