package com.lenskart.commons.endpoints;

import java.util.Map;

/**
 * Base interface for all endpoint enums across modules.
 * Provides a contract for endpoint path, service name, and URL generation.
 */
public interface BaseEndpoint {
    
    /**
     * Gets the endpoint path (e.g., "/v2/sessions", "/nexs/order/picking")
     *
     * @return The endpoint path
     */
    String getEndpoint();
    
    /**
     * Gets the service name for configuration lookup (e.g., "sessionService", "pickingService")
     *
     * @return The service name
     */
    String getServiceName();
    
    /**
     * Gets the complete URL for this endpoint
     *
     * @return Complete URL for the endpoint
     */
    String getUrl();
    
    /**
     * Gets the complete URL for this endpoint with path parameters replaced
     *
     * @param pathParams Map of path parameters to replace in the URL
     * @return Complete URL with path parameters replaced
     */
    String getUrl(Map<String, String> pathParams);
    
    /**
     * Gets the enum name for this endpoint
     *
     * @return The enum constant name
     */
    String name();
}
