package com.lenskart.commons.loader;

import com.lenskart.commons.config.ElasticsearchConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

/**
 * Facade for loading Elasticsearch configurations
 */
@Slf4j
public class ElasticsearchConfigLoader {
    
    /**
     * Load Elasticsearch configuration
     * 
     * @param esName Elasticsearch instance name
     * @return Elasticsearch configuration
     */
    public static ElasticsearchConfig loadConfig(String esName) {
        ElasticsearchConfig config = ConfigRegistry.getInstance().getElasticsearchConfig(esName);
        
        if (config == null) {
            log.warn("Elasticsearch configuration not found for name: {}", esName);
            return createDefaultConfig(esName);
        }
        
        return config;
    }
    
    /**
     * Create default Elasticsearch configuration
     * 
     * @param esName Elasticsearch instance name
     * @return Default Elasticsearch configuration
     */
    private static ElasticsearchConfig createDefaultConfig(String esName) {
        return ElasticsearchConfig.builder()
                .name(esName)
                .hosts(Arrays.asList("http://localhost:9200"))
                .username(null)
                .password(null)
                .connectTimeout(5000)
                .readTimeout(5000)
                .maxTotalConnections(20)
                .defaultMaxTotalConnectionsPerRoute(10)
                .discoveryEnabled(false)
                .multiThreaded(true)
                .build();
    }
    
    /**
     * Clear the configuration cache
     */
    public static void clearCache() {
        ConfigRegistry.getInstance().refresh();
    }
}
