package com.lenskart.commons.loader;

import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Generic configuration loader that handles loading and caching of YAML configurations
 */
@Slf4j
public class ConfigLoader {
    // Singleton instance
    private static ConfigLoader instance;
    
    // Configuration cache
    private final Map<String, Object> configCache = new ConcurrentHashMap<>();
    
    // Default values
    private static final String DEFAULT_CONFIG_PATH = "config.yml";
    private static final String DEFAULT_ENVIRONMENT = "preprod";
    
    // Private constructor for singleton
    private ConfigLoader() {}
    
    /**
     * Get singleton instance
     * 
     * @return ConfigLoader instance
     */
    public static synchronized ConfigLoader getInstance() {
        if (instance == null) {
            instance = new ConfigLoader();
        }
        return instance;
    }
    
    /**
     * Load raw YAML configuration
     * 
     * @param configPath Path to the YAML file
     * @return Map containing the raw YAML configuration
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> loadRawConfig(String configPath) {
        String cacheKey = "raw:" + configPath;
        
        // Check if config is already cached
        if (configCache.containsKey(cacheKey)) {
            return (Map<String, Object>) configCache.get(cacheKey);
        }
        
        try {
            Yaml yaml = new Yaml();

            // Try to load from classpath first
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(configPath);

            // If not found in classpath, try as a file path
            if (inputStream == null) {
                try {
                    inputStream = new FileInputStream(configPath);
                } catch (IOException e) {
                    log.warn("Could not find configuration file: {}", configPath);
                    return new HashMap<>();
                }
            }

            // Load the YAML file
            Map<String, Object> yamlConfig = yaml.load(inputStream);
            inputStream.close();
            
            if (yamlConfig == null) {
                yamlConfig = new HashMap<>();
            }
            
            // Cache the config
            configCache.put(cacheKey, yamlConfig);
            
            return yamlConfig;
        } catch (Exception e) {
            log.error("Failed to load configuration: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * Get configuration for a specific environment
     * 
     * @param environment Environment name (e.g., "preprod", "prod")
     * @param configPath Path to the YAML file
     * @return Map containing the environment-specific configuration
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getEnvironmentConfig(String environment, String configPath) {
        String cacheKey = "env:" + environment + ":" + configPath;
        
        // Check if config is already cached
        if (configCache.containsKey(cacheKey)) {
            return (Map<String, Object>) configCache.get(cacheKey);
        }
        
        // Load the raw configuration
        Map<String, Object> rawConfig = loadRawConfig(configPath);
        
        // Check if the environment configuration exists
        if (!rawConfig.containsKey(environment)) {
            log.warn("Configuration not found for environment: {}", environment);
            return new HashMap<>();
        }
        
        // Get the environment configuration
        Map<String, Object> envConfig = (Map<String, Object>) rawConfig.get(environment);
        
        if (envConfig == null) {
            envConfig = new HashMap<>();
        }
        
        // Cache the config
        configCache.put(cacheKey, envConfig);
        
        return envConfig;
    }
    
    /**
     * Get configuration for the default environment
     * 
     * @param configPath Path to the YAML file
     * @return Map containing the environment-specific configuration
     */
    public Map<String, Object> getEnvironmentConfig(String configPath) {
        return getEnvironmentConfig(DEFAULT_ENVIRONMENT, configPath);
    }
    
    /**
     * Get configuration for the default environment from the default config file
     * 
     * @return Map containing the environment-specific configuration
     */
    public Map<String, Object> getEnvironmentConfig() {
        return getEnvironmentConfig(DEFAULT_ENVIRONMENT, DEFAULT_CONFIG_PATH);
    }
    
    /**
     * Get a section of the configuration
     * 
     * @param sectionName Name of the configuration section
     * @param environment Environment name
     * @param configPath Path to the YAML file
     * @return Map containing the section configuration
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getConfigSection(String sectionName, String environment, String configPath) {
        String cacheKey = "section:" + sectionName + ":" + environment + ":" + configPath;
        
        // Check if config is already cached
        if (configCache.containsKey(cacheKey)) {
            return (Map<String, Object>) configCache.get(cacheKey);
        }
        
        // Get the environment configuration
        Map<String, Object> envConfig = getEnvironmentConfig(environment, configPath);
        
        // Check if the section exists
        if (!envConfig.containsKey(sectionName)) {
            log.warn("Configuration section not found: {} in environment: {}", sectionName, environment);
            return new HashMap<>();
        }
        
        // Get the section configuration
        Map<String, Object> sectionConfig = (Map<String, Object>) envConfig.get(sectionName);
        
        if (sectionConfig == null) {
            sectionConfig = new HashMap<>();
        }
        
        // Cache the config
        configCache.put(cacheKey, sectionConfig);
        
        return sectionConfig;
    }
    
    /**
     * Get a section of the configuration from the default environment
     * 
     * @param sectionName Name of the configuration section
     * @param configPath Path to the YAML file
     * @return Map containing the section configuration
     */
    public Map<String, Object> getConfigSection(String sectionName, String configPath) {
        return getConfigSection(sectionName, DEFAULT_ENVIRONMENT, configPath);
    }
    
    /**
     * Get a section of the configuration from the default environment and default config file
     * 
     * @param sectionName Name of the configuration section
     * @return Map containing the section configuration
     */
    public Map<String, Object> getConfigSection(String sectionName) {
        return getConfigSection(sectionName, DEFAULT_ENVIRONMENT, DEFAULT_CONFIG_PATH);
    }
    
    /**
     * Clear the configuration cache
     */
    public void clearCache() {
        configCache.clear();
        log.info("Configuration cache cleared");
    }
    
    /**
     * Get the default environment name
     * 
     * @return Default environment name
     */
    public String getDefaultEnvironment() {
        return DEFAULT_ENVIRONMENT;
    }
    
    /**
     * Get the default configuration path
     * 
     * @return Default configuration path
     */
    public String getDefaultConfigPath() {
        return DEFAULT_CONFIG_PATH;
    }
}
