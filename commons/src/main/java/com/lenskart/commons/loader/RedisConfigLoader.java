package com.lenskart.commons.loader;

import com.lenskart.commons.config.RedisConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * Facade for loading Redis configurations
 */
@Slf4j
public class RedisConfigLoader {
    
    /**
     * Load Redis configuration
     * 
     * @param redisName Redis instance name
     * @return Redis configuration
     */
    public static RedisConfig loadConfig(String redisName) {
        RedisConfig config = ConfigRegistry.getInstance().getRedisConfig(redisName);
        
        if (config == null) {
            log.warn("Redis configuration not found for name: {}", redisName);
            return createDefaultConfig(redisName);
        }
        
        return config;
    }
    
    /**
     * Create default Redis configuration
     * 
     * @param redisName Redis instance name
     * @return Default Redis configuration
     */
    private static RedisConfig createDefaultConfig(String redisName) {
        return RedisConfig.builder()
                .name(redisName)
                .host("localhost")
                .port(6379)
                .password(null)
                .database(0)
                .ssl(false)
                .timeout(2000)
                .maxTotal(128)
                .maxIdle(128)
                .minIdle(16)
                .build();
    }
    
    /**
     * Clear the configuration cache
     */
    public static void clearCache() {
        ConfigRegistry.getInstance().refresh();
    }
}
