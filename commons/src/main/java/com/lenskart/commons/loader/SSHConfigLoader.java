package com.lenskart.commons.loader;

import com.lenskart.commons.loader.ConfigRegistry;
import com.lenskart.commons.config.SSHConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class to load SSH configurations
 */
@Slf4j
public class SSHConfigLoader {

    /**
     * Loads SSH configuration
     *
     * @return SSHConfig object with connection details
     */
    public static SSHConfig loadConfig() {
        // Get the config from the registry
        SSHConfig config = ConfigRegistry.getInstance().getSSHConfig();

        if (config == null) {
            log.debug("SSH configuration not found or disabled in registry");
        } else {
            log.debug("Loaded SSH configuration from registry");
        }

        return config;
    }

    /**
     * Check if SSH tunneling is enabled
     *
     * @return true if SSH tunneling is enabled, false otherwise
     */
    public static boolean isSSHTunnelingEnabled() {
        return ConfigRegistry.getInstance().isSSHTunnelingEnabled();
    }

    /**
     * Refreshes the SSH configuration
     */
    public static void clearCache() {
        ConfigRegistry.getInstance().refresh();
        log.info("SSH configuration refreshed");
    }
}
