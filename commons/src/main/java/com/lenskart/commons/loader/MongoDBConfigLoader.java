package com.lenskart.commons.loader;

import com.lenskart.commons.config.MongoDBConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * Facade for loading MongoDB configurations
 */
@Slf4j
public class MongoDBConfigLoader {

    /**
     * Load MongoDB configuration
     *
     * @param dbName Database name
     * @return MongoDB configuration
     */
    public static MongoDBConfig loadConfig(String dbName) {
        MongoDBConfig config = ConfigRegistry.getInstance().getMongoDBConfig(dbName);

        if (config == null) {
            log.warn("MongoDB configuration not found for name: {}", dbName);
            return createDefaultConfig(dbName);
        }

        return config;
    }

    /**
     * Create default MongoDB configuration
     *
     * @param dbName Database name
     * @return Default MongoDB configuration
     */
    private static MongoDBConfig createDefaultConfig(String dbName) {
        return MongoDBConfig.builder()
                .name(dbName)
                .uri("mongodb://localhost:27017/" + dbName)
                .database(dbName)
                .username("admin")
                .password("password")
                .authSource("admin")
                .authMechanism("SCRAM-SHA-256")
                .connectTimeout(30000)
                .socketTimeout(30000)
                .maxPoolSize(100)
                .minPoolSize(10)
                .maxIdleTimeMS(600000)
                .maxLifeTimeMS(1800000)
                .build();
    }

    /**
     * Register a MongoDB configuration
     *
     * @param dbName Database name
     * @param config MongoDB configuration
     */
    public static void registerConfig(String dbName, MongoDBConfig config) {
        // Refresh the ConfigRegistry to ensure it picks up the new configuration
        // In a real implementation, we would directly update the ConfigRegistry
        // but for now we'll just refresh it
        ConfigRegistry.getInstance().refresh();
    }

    /**
     * Clear the configuration cache
     */
    public static void clearCache() {
        ConfigRegistry.getInstance().refresh();
    }
}
