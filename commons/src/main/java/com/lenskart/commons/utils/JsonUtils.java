package com.lenskart.commons.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.json.JSONObject;
import org.testng.Assert;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class JsonUtils {

    public static <T> T parseJsonString(String json, Class<T> clazz) {
        if (json.isEmpty()) {
            return null;
        } else {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                return (T) objectMapper.readValue(json, clazz);
            } catch (JsonProcessingException e) {
                Assert.fail(String.format("Failed to parse JSON string to object: %s", e.getMessage()));
                return null;
            }
        }
    }

    public static <T> T parseObject(Object json, Class<T> clazz) {
        if (json == null) {
            return null;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.convertValue(json, clazz);
        } catch (IllegalArgumentException e) {
            Assert.fail(String.format("Failed to parse object to class %s: %s", clazz.getSimpleName(), e.getMessage()));
            return null;
        }
    }

    public static String convertObjectToJsonString(Object object) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static JSONObject convertStringToJson(String json) {
        return new JSONObject(json);
    }

    public static List<String> getKeysInJsonUsingJsonNodeFieldNames(String json, ObjectMapper mapper) {
        List<String> keys = new ArrayList<>();
        JsonNode jsonNode = null;
        try {
            jsonNode = mapper.readTree(json);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Iterator<String> iterator = jsonNode.fieldNames();
        iterator.forEachRemaining(keys::add);
        return keys;
    }

    @SneakyThrows
    public static boolean compareJson(JSONObject obj1, JSONObject obj2) {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode node1 = mapper.readTree(obj1.toString());
        JsonNode node2 = mapper.readTree(obj2.toString());
        return node1.equals(node2);
    }
}
