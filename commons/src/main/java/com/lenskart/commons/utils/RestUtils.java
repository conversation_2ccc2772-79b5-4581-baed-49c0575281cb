package com.lenskart.commons.utils;

import io.restassured.RestAssured;
import io.restassured.filter.log.RequestLoggingFilter;
import io.restassured.filter.log.ResponseLoggingFilter;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Map;

/**
 * Utility class for making REST API calls using Rest Assured
 */
@Slf4j
public class RestUtils {

    /**
     * Performs a GET request to the specified URL
     *
     * @param url         The URL to send the GET request to
     * @param headers     Map of headers to include in the request (can be null)
     * @param queryParams Map of query parameters to include in the request (can be null)
     * @return Response object containing the API response
     */
    public static Response get(String url, Map<String, String> headers, Map<String, Object> queryParams) {
        log.info("Making GET request to: {}", url);

        GenericUtils.logCurl("GET", url, headers, queryParams, null);


        RequestSpecification request = RestAssured.given()
                .filter(new RequestLoggingFilter())
                .filter(new ResponseLoggingFilter());

        // Add headers if provided
        if (headers != null && !headers.isEmpty()) {
            log.debug("Request headers: {}", headers);
            request.headers(headers);
        }

        // Add query parameters if provided
        if (queryParams != null && !queryParams.isEmpty()) {
            log.debug("Query parameters: {}", queryParams);
            request.queryParams(queryParams);
        }

        // Execute the GET request
        Response response = request.get(url);

        // Log response details
        logResponse(response);

        return response;
    }

    /**
     * Performs a GET request to the specified URL
     *
     * @param url         The URL to send the GET request to
     * @param headers     Map of headers to include in the request (can be null)
     * @param queryParams Map of query parameters to include in the request (can be null)
     * @param expectedStatusCode Status Code to match
     * @return Response object containing the API response
     */

    public static Response get(String url, Map<String, String> headers, Map<String, Object> queryParams, int expectedStatusCode) {
        Response response = get(url, headers, queryParams);
        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed : ".concat(url));
        }
        return response;
    }

    /**
     * Performs a POST request to the specified URL with a JSON payload
     *
     * @param url         The URL to send the POST request to
     * @param headers     Map of headers to include in the request (can be null)
     * @param queryParams Map of query parameters to include in the request (can be null)
     * @param payload     The object to be serialized as JSON and sent as the request body
     * @return Response object containing the API response
     */
    public static Response post(String url, Map<String, String> headers, Map<String, Object> queryParams, Object payload) {
        log.info("Making POST request to: {}", url);

        GenericUtils.logCurl("POST", url, headers, queryParams, payload);

        RequestSpecification request = RestAssured.given()
                .filter(new RequestLoggingFilter())
                .filter(new ResponseLoggingFilter());

        // Add headers if provided
        if (headers != null && !headers.isEmpty()) {
            log.debug("Request headers: {}", headers);
            request.headers(headers);
        }

        // Add query parameters if provided
        if (queryParams != null && !queryParams.isEmpty()) {
            log.debug("Query parameters: {}", queryParams);
            request.queryParams(queryParams);
        }

        // Add payload if provided
        if (payload != null) {
            log.debug("Request payload: {}", payload);
            request.contentType(ContentType.JSON);
            request.body(payload);
        }

        // Execute the POST request
        Response response = request.post(url);

        // Log response details
        logResponse(response);

        return response;
    }

    /**
     * Performs a POST request to the specified URL
     *
     * @param url         The URL to send the POST request to
     * @param headers     Map of headers to include in the request (can be null)
     * @param payload     The object to be serialized as JSON and sent as the request body
     * @return Response object containing the API response
     */
    public static Response post(String url, Map<String, String> headers, String payload) {
        return post(url, headers,null, payload);
    }

    /**
     * Performs a POST request to the specified URL
     *
     * @param url         The URL to send the GET request to
     * @param headers     Map of headers to include in the request (can be null)
     * @param payload     The object to be serialized as JSON and sent as the request body
     * @param expectedStatusCode Status Code to match
     * @return Response object containing the API response
     */

    public static Response post(String url, Map<String, String> headers,Map<String, Object> queryParams, String payload, int expectedStatusCode) {
        Response response = post( url, headers,  queryParams,  payload);
        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed : ".concat(url));
        }
        return response;
    }

    public static Response post(String url, Map<String, String> headers, String payload, int expectedStatusCode) {
        Response response = post(url, headers, payload);
        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed : ".concat(url));
        }
        return response;
    }

    /**
     * Performs a POST request with multipart form data including file upload
     *
     * @param url         The URL to send the POST request to
     * @param headers     Map of headers to include in the request (can be null)
     * @param queryParams Map of query parameters to include in the request (can be null)
     * @param filePath    Path to the file to be uploaded
     * @param fileFieldName The field name for the file in the form (e.g., "file")
     * @param formData    Map of additional form data fields (can be null)
     * @return Response object containing the API response
     */
    public static Response postWithMultipartFile(String url, Map<String, String> headers,
                                                 Map<String, Object> queryParams,
                                                 String filePath, String fileFieldName,
                                                 Map<String, Object> formData) {
        log.info("Making POST request with multipart file upload to: {}", url);
        log.info("Uploading file: {} as field: {}", filePath, fileFieldName);

        // Log curl equivalent for debugging
        logMultipartCurl(url, headers, queryParams, filePath, fileFieldName, formData);

        RequestSpecification request = RestAssured.given()
                .filter(new RequestLoggingFilter())
                .filter(new ResponseLoggingFilter());

        // Add headers if provided
        if (headers != null && !headers.isEmpty()) {
            log.debug("Request headers: {}", headers);
            request.headers(headers);
        }

        // Add query parameters if provided
        if (queryParams != null && !queryParams.isEmpty()) {
            log.debug("Query parameters: {}", queryParams);
            request.queryParams(queryParams);
        }

        // Add the file to the multipart request
        File file = new File(filePath);
        if (!file.exists()) {
            throw new RuntimeException("File not found: " + filePath);
        }

        log.debug("Adding file to multipart request: {} (size: {} bytes)", file.getName(), file.length());
        request.multiPart(fileFieldName, file);

        // Add additional form data if provided
        if (formData != null && !formData.isEmpty()) {
            log.debug("Additional form data: {}", formData);
            for (Map.Entry<String, Object> entry : formData.entrySet()) {
                Object value = entry.getValue();
                if (value != null) {
                    request.multiPart(entry.getKey(), String.valueOf(value));
                }
            }
        }

        // Execute the POST request
        Response response = request.post(url);

        // Log response details
        logResponse(response);

        return response;
    }

    /**
     * Performs a POST request with multipart form data including file upload (simplified version)
     *
     * @param url           The URL to send the POST request to
     * @param headers       Map of headers to include in the request (can be null)
     * @param filePath      Path to the file to be uploaded
     * @param fileFieldName The field name for the file in the form (e.g., "file")
     * @return Response object containing the API response
     */
    public static Response postWithMultipartFile(String url, Map<String, String> headers,
                                                 String filePath, String fileFieldName) {
        return postWithMultipartFile(url, headers, null, filePath, fileFieldName, null);
    }

    /**
     * Performs a POST request with multipart form data including file upload with expected status code
     *
     * @param url                The URL to send the POST request to
     * @param headers            Map of headers to include in the request (can be null)
     * @param queryParams        Map of query parameters to include in the request (can be null)
     * @param filePath           Path to the file to be uploaded
     * @param fileFieldName      The field name for the file in the form (e.g., "file")
     * @param formData           Map of additional form data fields (can be null)
     * @param expectedStatusCode Expected HTTP status code
     * @return Response object containing the API response
     */
    public static Response postWithMultipartFile(String url, Map<String, String> headers,
                                                 Map<String, Object> queryParams,
                                                 String filePath, String fileFieldName,
                                                 Map<String, Object> formData,
                                                 int expectedStatusCode) {
        Response response = postWithMultipartFile(url, headers, queryParams, filePath, fileFieldName, formData);
        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed with status " + response.getStatusCode() +
                    " (expected " + expectedStatusCode + ") for URL: " + url);
        }
        return response;
    }

    /**
     * Performs a POST request with multipart form data including file upload (simplified version with expected status)
     *
     * @param url                The URL to send the POST request to
     * @param headers            Map of headers to include in the request (can be null)
     * @param filePath           Path to the file to be uploaded
     * @param fileFieldName      The field name for the file in the form (e.g., "file")
     * @param expectedStatusCode Expected HTTP status code
     * @return Response object containing the API response
     */
    public static Response postWithMultipartFile(String url, Map<String, String> headers,
                                                 String filePath, String fileFieldName,
                                                 int expectedStatusCode) {
        return postWithMultipartFile(url, headers, null, filePath, fileFieldName, null, expectedStatusCode);
    }

    /**
     * Logs the curl equivalent for multipart file upload requests
     *
     * @param url           The URL
     * @param headers       Request headers
     * @param queryParams   Query parameters
     * @param filePath      File path
     * @param fileFieldName File field name
     * @param formData      Additional form data
     */
    private static void logMultipartCurl(String url, Map<String, String> headers,
                                         Map<String, Object> queryParams,
                                         String filePath, String fileFieldName,
                                         Map<String, Object> formData) {
        StringBuilder curlCommand = new StringBuilder();
        curlCommand.append("curl --location '").append(url);

        // Add query parameters to URL
        if (queryParams != null && !queryParams.isEmpty()) {
            curlCommand.append("?");
            queryParams.forEach((key, value) ->
                    curlCommand.append(key).append("=").append(value).append("&"));
            // Remove trailing &
            curlCommand.setLength(curlCommand.length() - 1);
        }

        curlCommand.append("'");

        // Add headers
        if (headers != null && !headers.isEmpty()) {
            headers.forEach((key, value) ->
                    curlCommand.append(" \\\n--header '").append(key).append(": ").append(value).append("'"));
        }

        // Add file
        curlCommand.append(" \\\n--form '").append(fileFieldName).append("=@\"").append(filePath).append("\"'");

        // Add additional form data
        if (formData != null && !formData.isEmpty()) {
            formData.forEach((key, value) ->
                    curlCommand.append(" \\\n--form '").append(key).append("=").append(value).append("'"));
        }

        log.info("Equivalent curl command:\n{}", curlCommand.toString());
    }

    /**
     * Performs a PUT request to the specified URL with a JSON payload
     *
     * @param url     The URL to send the PUT request to
     * @param headers Map of headers to include in the request (can be null)
     * @param payload The object to be serialized as JSON and sent as the request body
     * @return Response object containing the API response
     */
    public static Response put(String url, Map<String, String> headers, Object payload) {
        return  put(url, headers, null,  payload);
    }

    /**
     * Performs a PUT request to the specified URL with a JSON payload
     *
     * @param url     The URL to send the PUT request to
     * @param headers Map of headers to include in the request (can be null)
     * @param payload The object to be serialized as JSON and sent as the request body
     * @return Response object containing the API response
     */
    public static Response put(String url, Map<String, String> headers,Map<String, Object> queryParams, Object payload) {
        log.info("Making PUT request to: {}", url);
        GenericUtils.logCurl("PUT", url, headers, null, payload);
        RequestSpecification request = RestAssured.given()
                .filter(new RequestLoggingFilter())
                .filter(new ResponseLoggingFilter());

        // Add headers if provided
        if (headers != null && !headers.isEmpty()) {
            log.debug("Request headers: {}", headers);
            request.headers(headers);
        }

        // Add query parameters if provided
        if (queryParams != null && !queryParams.isEmpty()) {
            log.debug("Query parameters: {}", queryParams);
            request.queryParams(queryParams);
        }

        // Add payload if provided
        if (payload != null) {
            log.debug("Request payload: {}", payload);
            request.contentType(ContentType.JSON);
            request.body(payload);
        }

        // Execute the PUT request
        Response response = request.put(url);

        // Log response details
        logResponse(response);

        return response;
    }


    /**
     * Performs a PUT request to the specified URL with a JSON payload
     *
     * @param url     The URL to send the PUT request to
     * @param headers Map of headers to include in the request (can be null)
     * @param payload The object to be serialized as JSON and sent as the request body
     * @param expectedStatusCode Status Code to match
     * @return Response object containing the API response
     */

    public static Response put(String url, Map<String, String> headers, String payload, int expectedStatusCode) {
        Response response = put(url, headers, payload);
        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed : ".concat(url));
        }
        return response;
    }

    /**
     * Performs a DELETE request to the specified URL
     *
     * @param url     The URL to send the DELETE request to
     * @param headers Map of headers to include in the request (can be null)
     * @return Response object containing the API response
     */
    public static Response delete(String url, Map<String, String> headers) {
        log.info("Making DELETE request to: {}", url);
        GenericUtils.logCurl("PUT", url, headers, null, null);

        RequestSpecification request = RestAssured.given()
                .filter(new RequestLoggingFilter())
                .filter(new ResponseLoggingFilter());

        // Add headers if provided
        if (headers != null && !headers.isEmpty()) {
            log.debug("Request headers: {}", headers);
            request.headers(headers);
        }

        // Execute the DELETE request
        Response response = request.delete(url);

        // Log response details
        logResponse(response);

        return response;
    }


    /**
     * Performs a DELETE request to the specified URL
     *
     * @param url     The URL to send the DELETE request to
     * @param headers Map of headers to include in the request (can be null)
     * @param expectedStatusCode Status Code to match
     * @return Response object containing the API response
     */

    public static Response delete(String url, Map<String, String> headers, int expectedStatusCode) {
        Response response = delete(url, headers);
        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed : ".concat(url));
        }
        return response;
    }

    /**
     * Validates that the response status code is as expected
     *
     * @param response     The response to validate
     * @param expectedCode The expected status code
     * @return True if the status code matches, false otherwise
     */
    public static boolean validateStatusCode(Response response, int expectedCode) {
        return response.getStatusCode() == expectedCode;
    }

    /**
     * Extracts a value from the response JSON using a JSON path
     *
     * @param response The response containing JSON
     * @param jsonPath The JSON path to extract the value from
     * @return The extracted value
     */
    public static Object getValueFromResponse(Response response, String jsonPath) {
        return response.jsonPath().get(jsonPath);
    }

    /**
     * Logs details about the API response
     *
     * @param response The response to log
     */
    private static void logResponse(Response response) {
        log.info("Response Status Code: {}", response.getStatusCode());
        // Log response body if not too large
        String responseBody = response.getBody().asString();
        if (responseBody.length() > 1000) {
            log.debug("Response Body (truncated): {}", responseBody.substring(0, 1000) + "...");
        } else {
            log.debug("Response Body: {}", responseBody);
        }
    }

    /**
     * Performs a POST request with form data
     *
     * @param url         The URL to send the POST request to
     * @param headers     Map of headers to include in the request (can be null)
     * @param formData    The form data to be sent
     * @param expectedStatusCode Status Code to match
     * @return Response object containing the API response
     */
    public static Response postFormData(String url, Map<String, String> headers, String formData, int expectedStatusCode) {
        log.info("Making POST request with form data to: {}", url);

        GenericUtils.logCurl("POST", url, headers, null, formData);

        RequestSpecification request = RestAssured.given()
                .filter(new RequestLoggingFilter())
                .filter(new ResponseLoggingFilter());

        // Add headers if provided
        if (headers != null && !headers.isEmpty()) {
            log.debug("Request headers: {}", headers);
            request.headers(headers);
        }

        // Add form data
        if (formData != null) {
            log.debug("Request form data: {}", formData);
            request.contentType("application/x-www-form-urlencoded");
            request.body(formData);
        }

        // Execute the POST request
        Response response = request.post(url);

        // Log response details
        logResponse(response);

        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed : ".concat(url));
        }

        return response;
    }
}
