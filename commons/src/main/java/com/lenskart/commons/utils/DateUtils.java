package com.lenskart.commons.utils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class DateUtils {


    public static LocalDate getFirstDayOfCurrentMonth() { return LocalDate.now().withDayOfMonth(1); }

    public static LocalDate getCurrentDayOfCurrentMonth() { return LocalDate.now(); }

    public static String formatDateToString(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return date.format(formatter);
    }

    public static String formatDateToString(LocalDate date, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return date.format(formatter);
    }

    public static String getFormattedDate(String format){

        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("Asia/Kolkata"));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        return now.format(formatter);
    }

}
