package com.lenskart.commons.utils;

import com.lenskart.commons.model.FrameTypes;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.schema.product.Items;
import com.lenskart.juno.schema.product.Specifications;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * Utility class for product-related operations
 */
@Slf4j
public class ProductUtil {

    // Constants for specification and item names
    private static final String TECHNICAL_SPECIFICATION = "technical";
    private static final String FRAME_TYPE_ITEM = "Frame Type";

    /**
     * Sets the frame type for a product based on its specifications.
     * Searches for the "technical" specification and extracts the "Frame Type" item value.
     *
     * @param productList    The product list to update
     * @param specifications The list of product specifications
     * @return The updated product list with frame type set
     * @throws IllegalArgumentException if specifications are null or empty
     * @throws RuntimeException         if frame type cannot be determined
     */
    public static OrderContext.ProductList setFrameType(OrderContext.ProductList productList,
                                                        List<Specifications> specifications) {

        validateInputs(productList, specifications);

        log.info("Setting frame type for product: {}", productList.getProductId());

        try {
            Optional<FrameTypes> frameType = extractFrameTypeFromSpecifications(specifications);

            if (frameType.isPresent()) {
                productList.setFrameType(frameType.get());
                log.info("Successfully set frame type to {} for product: {}",
                        frameType.get().getDisplayName(), productList.getProductId());
            } else {
                log.warn("Frame type not found in specifications for product: {}", productList.getProductId());
                // Set a default frame type or throw exception based on business requirements
                setDefaultFrameType(productList);
            }

        } catch (Exception e) {
            log.error("Error setting frame type for product {}: {}", productList.getProductId(), e.getMessage());
            throw new RuntimeException("Failed to set frame type for product: " + productList.getProductId(), e);
        }

        return productList;
    }

    /**
     * Validates the input parameters
     *
     * @param productList    The product list to validate
     * @param specifications The specifications list to validate
     * @throws IllegalArgumentException if inputs are invalid
     */
    private static void validateInputs(OrderContext.ProductList productList, List<Specifications> specifications) {
        if (productList == null) {
            throw new IllegalArgumentException("Product list cannot be null");
        }

        if (productList.getProductId() == null || productList.getProductId().trim().isEmpty()) {
            throw new IllegalArgumentException("Product ID cannot be null or empty");
        }

        if (specifications == null || specifications.isEmpty()) {
            throw new IllegalArgumentException("No specifications found for product: " + productList.getProductId());
        }
    }

    /**
     * Extracts frame type from the specifications list
     *
     * @param specifications The list of specifications to search
     * @return Optional containing the frame type if found
     */
    private static Optional<FrameTypes> extractFrameTypeFromSpecifications(List<Specifications> specifications) {
        return specifications.stream()
                .filter(spec -> TECHNICAL_SPECIFICATION.equalsIgnoreCase(spec.getName()))
                .findFirst()
                .flatMap(ProductUtil::extractFrameTypeFromTechnicalSpec);
    }

    /**
     * Extracts frame type from a technical specification
     *
     * @param technicalSpec The technical specification to search
     * @return Optional containing the frame type if found
     */
    private static Optional<FrameTypes> extractFrameTypeFromTechnicalSpec(Specifications technicalSpec) {
        if (technicalSpec.getItems() == null || technicalSpec.getItems().isEmpty()) {
            log.warn("No items found in technical specification");
            return Optional.empty();
        }

        return technicalSpec.getItems().stream()
                .filter(item -> FRAME_TYPE_ITEM.equalsIgnoreCase(item.getName()))
                .findFirst()
                .flatMap(ProductUtil::parseFrameTypeFromItem);
    }

    /**
     * Parses frame type from an item
     *
     * @param frameTypeItem The item containing frame type information
     * @return Optional containing the parsed frame type
     */
    private static Optional<FrameTypes> parseFrameTypeFromItem(Items frameTypeItem) {
        if (frameTypeItem.getValue() == null || frameTypeItem.getValue().trim().isEmpty()) {
            log.warn("Frame type item has null or empty value");
            return Optional.empty();
        }

        try {
            String frameTypeValue = frameTypeItem.getValue().trim();
            log.debug("Parsing frame type value: {}", frameTypeValue);

            // Try to get by display name first, then by enum name
            try {
                return Optional.of(FrameTypes.getByDisplayName(frameTypeValue));
            } catch (IllegalArgumentException e) {
                log.debug("Failed to parse by display name, trying enum name: {}", frameTypeValue);
                return Optional.of(FrameTypes.getByName(frameTypeValue));
            }

        } catch (IllegalArgumentException e) {
            log.warn("Invalid frame type value: {}. Error: {}", frameTypeItem.getValue(), e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Sets a default frame type when none is found in specifications
     *
     * @param productList The product list to update with default frame type
     */
    private static void setDefaultFrameType(OrderContext.ProductList productList) {
        FrameTypes defaultFrameType = FrameTypes.FULL_RIM; // Default to FULL_RIM
        productList.setFrameType(defaultFrameType);
        log.info("Set default frame type {} for product: {}",
                defaultFrameType.getDisplayName(), productList.getProductId());
    }

    /**
     * Gets all available frame types for reference
     *
     * @return List of all available frame type names
     */
    public static List<String> getAvailableFrameTypes() {
        return FrameTypes.getAllDisplayNames();
    }
}
