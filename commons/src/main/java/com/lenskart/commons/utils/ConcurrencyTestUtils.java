package com.lenskart.commons.utils;

import io.restassured.response.Response;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

/**
 * Utility class for concurrency testing using Java 21 Virtual Threads
 * Supports testing critical APIs with concurrent requests to verify locking mechanisms
 */
@Slf4j
public class ConcurrencyTestUtils {


    /**
     * Result container for concurrent API execution
     */
    @Getter
    public static class ConcurrentExecutionResult {
        // Getters
        private final List<Response> responses;
        private final List<Exception> exceptions;
        private final Duration totalExecutionTime;
        private final int successCount;
        private final int failureCount;
        private final Map<Integer, Integer> statusCodeCounts;
        private final double averageResponseTime;

        public ConcurrentExecutionResult(List<Response> responses, List<Exception> exceptions,
                                         Duration totalExecutionTime, Map<Integer, Integer> statusCodeCounts,
                                         double averageResponseTime) {
            this.responses = responses;
            this.exceptions = exceptions;
            this.totalExecutionTime = totalExecutionTime;
            this.statusCodeCounts = statusCodeCounts;
            this.averageResponseTime = averageResponseTime;
            this.successCount = (int) responses.stream().filter(r -> r.getStatusCode() >= 200 && r.getStatusCode() < 300).count();
            this.failureCount = responses.size() - successCount + exceptions.size();
        }

        public int getTotalRequests() {
            return responses.size() + exceptions.size();
        }
    }

    /**
     * Configuration for concurrent API execution
     */
    @Data
    @Builder
    public static class ConcurrentExecutionConfig {

        private int threadCount;
        @Builder.Default
        private Duration timeout = Duration.ofMinutes(5);
        private boolean logIndividualResponses;
    }

    /**
     * Execute concurrent GET requests
     */
    public static ConcurrentExecutionResult executeConcurrentGET(String url, Map<String, String> headers,
                                                                 ConcurrentExecutionConfig config) {
        return executeConcurrentRequests(config, () -> RestUtils.get(url, headers, null));
    }

    /**
     * Execute concurrent GET requests with query parameters
     */
    public static ConcurrentExecutionResult executeConcurrentGET(String url, Map<String, String> headers,
                                                                 Map<String, Object> queryParams,
                                                                 ConcurrentExecutionConfig config) {
        return executeConcurrentRequests(config, () -> RestUtils.get(url, headers, queryParams));
    }

    /**
     * Execute concurrent POST requests
     */
    public static ConcurrentExecutionResult executeConcurrentPOST(String url, Map<String, String> headers,
                                                                  String requestBody, ConcurrentExecutionConfig config) {
        return executeConcurrentRequests(config, () -> RestUtils.post(url, headers, requestBody));
    }

    /**
     * Execute concurrent PUT requests
     */
    public static ConcurrentExecutionResult executeConcurrentPUT(String url, Map<String, String> headers,
                                                                 String requestBody, ConcurrentExecutionConfig config) {
        return executeConcurrentRequests(config, () -> RestUtils.put(url, headers, requestBody));
    }

    /**
     * Execute concurrent DELETE requests
     */
    public static ConcurrentExecutionResult executeConcurrentDELETE(String url, Map<String, String> headers,
                                                                    ConcurrentExecutionConfig config) {
        return executeConcurrentRequests(config, () -> RestUtils.delete(url, headers));
    }

    /**
     * Execute concurrent requests with custom function
     */
    public static ConcurrentExecutionResult executeConcurrentRequests(ConcurrentExecutionConfig config,
                                                                      Supplier<Response> requestFunction) {

        log.info("Starting concurrent execution with {} virtual threads", config.getThreadCount());

        // Use Virtual Thread Executor (Java 21)
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {

            List<CompletableFuture<Response>> futures = new ArrayList<>();
            List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());
            AtomicInteger requestCounter = new AtomicInteger(0);

            Instant startTime = Instant.now();

            // Submit all tasks
            for (int i = 0; i < config.getThreadCount(); i++) {
                final int requestId = i + 1;

                CompletableFuture<Response> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        int currentRequest = requestCounter.incrementAndGet();
                        log.debug("Executing request {} of {}", currentRequest, config.getThreadCount());

                        Instant requestStart = Instant.now();
                        Response response = requestFunction.get();
                        Instant requestEnd = Instant.now();

                        long responseTime = Duration.between(requestStart, requestEnd).toMillis();

                        if (config.isLogIndividualResponses()) {
                            log.info("Request {}: Status={}, ResponseTime={}ms",
                                    requestId, response.getStatusCode(), responseTime);
                        }

                        return response;

                    } catch (Exception e) {
                        log.error("Request failed with id: {} failed: {}", requestId, e.getMessage());
                        exceptions.add(e);
                        throw new RuntimeException(e);
                    }
                }, executor);

                futures.add(future);
            }

            // Wait for all requests to complete or timeout
            List<Response> responses = new ArrayList<>();
            Map<Integer, Integer> statusCodeCounts = new HashMap<>();
            List<Long> responseTimes = new ArrayList<>();

            for (int i = 0; i < futures.size(); i++) {
                try {
                    Response response = futures.get(i).get(config.getTimeout().toMillis(), TimeUnit.MILLISECONDS);
                    responses.add(response);

                    // Count status codes
                    int statusCode = response.getStatusCode();
                    statusCodeCounts.merge(statusCode, 1, Integer::sum);

                    // Track response time if available
                    long responseTime = response.getTime();
                    responseTimes.add(responseTime);

                } catch (TimeoutException e) {
                    log.error("Request {} timed out after {}", i + 1, config.getTimeout());
                    exceptions.add(e);
                } catch (Exception e) {
                    log.error("Request {} failed: {}", i + 1, e.getMessage());
                }
            }

            Instant endTime = Instant.now();
            Duration totalExecutionTime = Duration.between(startTime, endTime);

            // Calculate average response time
            double averageResponseTime = responseTimes.stream()
                    .mapToLong(Long::longValue)
                    .average()
                    .orElse(0.0);

            ConcurrentExecutionResult result = new ConcurrentExecutionResult(
                    responses, exceptions, totalExecutionTime, statusCodeCounts, averageResponseTime
            );

            // Log summary
            logExecutionSummary(result, config);

            return result;
        }
    }

    /**
     * Log execution summary
     */
    private static void logExecutionSummary(ConcurrentExecutionResult result, ConcurrentExecutionConfig config) {

        log.info("=== Concurrent Execution Summary ===");
        log.info("Total Requests: {}", result.getTotalRequests());
        log.info("Successful Requests: {}", result.getSuccessCount());
        log.info("Failed Requests: {}", result.getFailureCount());
        log.info("Total Execution Time: {}ms", result.getTotalExecutionTime().toMillis());
        log.info("Status Code Distribution: {}", result.getStatusCodeCounts());
    }

}
