package com.lenskart.commons.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import oshi.SystemInfo;
import oshi.hardware.ComputerSystem;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.OperatingSystem;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.Random;

@Slf4j
public class GenericUtils {

    public static String getSerialNumber() {
        SystemInfo systemInfo = new SystemInfo();
        OperatingSystem operatingSystem = systemInfo.getOperatingSystem();
        log.info("operatingSystem: {}", operatingSystem);
        HardwareAbstractionLayer hardwareAbstractionLayer = systemInfo.getHardware();
        ComputerSystem computerSystem = hardwareAbstractionLayer.getComputerSystem();
        log.info("SerialNumber: {}", computerSystem.getSerialNumber());
        return computerSystem.getSerialNumber();
    }

    public static String genrateRandomNumericString(int num) {
        return RandomStringUtils.randomNumeric(num);
    }

    public static String genrateRandomAlphabeticString(int num) {
        return RandomStringUtils.randomAlphabetic(num);
    }

    public static String genrateRandomNumericString() {
        long epoch = new Date().getTime() / 1000;
        return String.valueOf(epoch);
    }

    public static String currentDatePlus(long Days) {
        return LocalDate.now().plusDays(Days).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    public static String currentDateFormat() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        return dateFormat.format(new Date());
    }

    /**
     * Builds a curl command string for a REST API request with an object payload
     *
     * @param method      HTTP method (GET, POST, PUT, DELETE)
     * @param url         URL of the API
     * @param queryParams List of query parameters (can be null)
     * @param headers     List of headers (can be null)
     * @param payload     Request body as an object (can be null)
     */

    public static String curlBuilder(String method, String url, Map<String, String> headers, Map<String, Object> queryParams, Object payload) {
        StringBuilder curl = new StringBuilder("curl -X " + method + " ");
        if (url != null)
            curl.append("'").append(url).append("'");
        if (queryParams != null) {
            queryParams.forEach((key, value) -> curl.append("?").append(key).append(": ").append(value).append("'"));
        }
        if (headers != null)
            headers.forEach((key, value) -> curl.append(" -H '").append(key).append(": ").append(value).append("'"));

        if (payload != null) {
            curl.append(" -H 'Content-Type: application/json'");
            curl.append(" -d '").append(payload.toString().replace("=", ": ")).append("'");
        }
        return curl.toString();
    }

    /**
     * Logs a curl command for the given request parameters
     *
     * @param method      HTTP method (GET, POST, PUT, DELETE)
     * @param url         Request URL
     * @param headers     Map of headers to include in the request (can be null)
     * @param queryParams Map of query parameters to include in the request (can be null)
     * @param payload     Request body (can be null)
     */
    public static void logCurl(String method, String url, Map<String, String> headers, Map<String, Object> queryParams, Object payload) {
        try {
            String curlCommand = curlBuilder(method, url, headers, queryParams, payload);
            log.info("Curl Command: {}", curlCommand);

        } catch (Exception e) {
            log.info("Failed to log curl command: {}", e.getMessage());
        }
    }

    /**
     * Converts a Map of form parameters to a URL-encoded form data string.
     *
     * @param formParams Map of form parameters (key-value pairs)
     * @return URL-encoded form data string
     */
    public static String buildFormData(Map<String, String> formParams) {
        if (formParams == null || formParams.isEmpty()) {
            return "";
        }

        StringBuilder formData = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : formParams.entrySet()) {
            if (!first) {
                formData.append("&");
            }
            formData.append(entry.getKey()).append("=").append(entry.getValue());
            first = false;
        }
        return formData.toString();
    }
    
    public static String generateRandomString(int length) {
        Random random = new Random();
        random.setSeed(System.currentTimeMillis());
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(chars.length());
            sb.append(chars.charAt(randomIndex));
        }
        return sb.toString();
    }

    public static boolean isTestPackage(String packagePrefix) {
        return Arrays.stream(Thread.currentThread().getStackTrace())
                .map(StackTraceElement::getClassName)
                .anyMatch(className -> className.startsWith(packagePrefix));
    }

    public static boolean isJenkinsEnvironment() {
        // Check for Jenkins-specific environment variables
        boolean isJenkins = System.getenv("JENKINS_URL") != null ||
                System.getenv("BUILD_NUMBER") != null ||
                System.getenv("JOB_NAME") != null ||
                System.getenv("JENKINS_HOME") != null ||
                System.getenv("BUILD_ID") != null ||
                System.getProperty("jenkins") != null ||
                System.getProperty("jenkins.build") != null ||
                System.getProperty("user.name", "").equals("jenkins");

        if (isJenkins) {
            log.debug("Jenkins environment detected - indicators found:");
            if (System.getenv("JENKINS_URL") != null) log.debug("  JENKINS_URL: {}", System.getenv("JENKINS_URL"));
            if (System.getenv("BUILD_NUMBER") != null) log.debug("  BUILD_NUMBER: {}", System.getenv("BUILD_NUMBER"));
            if (System.getenv("JOB_NAME") != null) log.debug("  JOB_NAME: {}", System.getenv("JOB_NAME"));
            if (System.getenv("BUILD_ID") != null) log.debug("  BUILD_ID: {}", System.getenv("BUILD_ID"));
        }
        return isJenkins;
    }
}