package com.lenskart.commons.utils;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.lenskart.commons.config.SSHConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages SSH sessions and port forwarding for database connections
 */
@Slf4j
public class SSHSessionUtil {

    private static final Map<String, Session> sessions = new ConcurrentHashMap<>();

    /**
     * Creates an SSH tunnel using the provided configuration
     *
     * @param config SSH configuration
     * @return The local port to connect to
     * @throws JSchException if there's an error establishing the SSH connection
     */
    public static int createTunnel(SSHConfig config) throws JSchException {
        // If tunneling is not enabled, return the local port without creating a tunnel
        if (!config.isEnabled()) {
            log.debug("SSH tunneling is disabled for {}", config.getRemoteHost());
            return config.getLocalPort();
        }

        String sessionKey = generateSessionKey(config);

        if (sessions.containsKey(sessionKey) && sessions.get(sessionKey).isConnected()) {
            log.debug("Using existing SSH tunnel for {}", sessionKey);
            return config.getLocalPort();
        }

        log.info("Creating SSH tunnel to {}:{} via {}:{}",
                config.getRemoteHost(), config.getRemotePort(),
                config.getHostname(), config.getPort());

        try {
            JSch jsch = new JSch();

            // If private key is provided, use it
            if (config.getPrivateKeyPath() != null && !config.getPrivateKeyPath().isEmpty()) {
                jsch.addIdentity(config.getPrivateKeyPath());
            }

            Session session = jsch.getSession(config.getUsername(), config.getHostname(), config.getPort());

            // If password is provided, use it
            if (config.getPassword() != null && !config.getPassword().isEmpty()) {
                session.setPassword(config.getPassword());
            }

            // Disable strict host key checking
            java.util.Properties properties = new java.util.Properties();
            properties.put("StrictHostKeyChecking", "no");
            session.setConfig(properties);

            // Connect to the SSH server
            session.connect(30000);

            // Set up port forwarding
            session.setPortForwardingL(config.getLocalPort(), config.getRemoteHost(), config.getRemotePort());

            // Store the session for later use
            sessions.put(sessionKey, session);

            log.info("SSH tunnel established. Local port: {}", config.getLocalPort());
            return config.getLocalPort();
        } catch (JSchException e) {
            log.error("Failed to create SSH tunnel: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Closes an SSH tunnel
     *
     * @param config SSH configuration
     */
    public static void closeTunnel(SSHConfig config) {
        // If tunneling is not enabled, no need to close anything
        if (!config.isEnabled()) {
            return;
        }

        String sessionKey = generateSessionKey(config);
        Session session = sessions.get(sessionKey);

        if (session != null && session.isConnected()) {
            try {
                session.disconnect();
                sessions.remove(sessionKey);
                log.info("SSH tunnel closed for {}", sessionKey);
            } catch (Exception e) {
                log.error("Error closing SSH tunnel: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * Closes all SSH tunnels
     */
    public static void closeAllTunnels() {
        for (Session session : sessions.values()) {
            if (session.isConnected()) {
                try {
                    session.disconnect();
                } catch (Exception e) {
                    log.error("Error closing SSH tunnel: {}", e.getMessage(), e);
                }
            }
        }
        sessions.clear();
        log.info("All SSH tunnels closed");
    }

    /**
     * Generates a unique key for a session based on its configuration
     *
     * @param config SSH configuration
     * @return A unique session key
     */
    private static String generateSessionKey(SSHConfig config) {
        return String.format("%s@%s:%d-%s:%d",
                config.getUsername(), config.getHostname(), config.getPort(),
                config.getRemoteHost(), config.getRemotePort());
    }
}
