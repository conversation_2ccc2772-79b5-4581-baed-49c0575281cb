package com.lenskart.commons.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for logging
 */
public class LoggerUtils {
    
    /**
     * Gets a logger for the specified class
     * 
     * @param clazz Class to get logger for
     * @return      SLF4J Logger instance
     */
    public static Logger getLogger(Class<?> clazz) {
        return LoggerFactory.getLogger(clazz);
    }
    
    /**
     * Logs the start of a test
     * 
     * @param logger    Logger instance
     * @param testName  Name of the test
     */
    public static void logTestStart(Logger logger, String testName) {
        logger.info("========== Starting Test: {} ==========", testName);
    }
    
    /**
     * Logs the end of a test
     * 
     * @param logger    Logger instance
     * @param testName  Name of the test
     * @param result    Test result (PASS/FAIL)
     */
    public static void logTestEnd(Logger logger, String testName, String result) {
        logger.info("========== Test Completed: {} - Result: {} ==========", testName, result);
    }
    
    /**
     * Logs an API request
     * 
     * @param logger        Logger instance
     * @param method        HTTP method
     * @param url           Request URL
     * @param headers       Request headers
     * @param requestBody   Request body
     */
    public static void logApiRequest(Logger logger, String method, String url, Object headers, Object requestBody) {
        logger.debug("API Request: {} {}", method, url);
        logger.debug("Headers: {}", headers);
        if (requestBody != null) {
            logger.debug("Request Body: {}", requestBody);
        }
    }
    
    /**
     * Logs an API response
     * 
     * @param logger        Logger instance
     * @param statusCode    Response status code
     * @param responseBody  Response body
     */
    public static void logApiResponse(Logger logger, int statusCode, String responseBody) {
        logger.debug("API Response Status: {}", statusCode);
        logger.debug("Response Body: {}", responseBody);
    }
    
    /**
     * Logs a database query
     * 
     * @param logger    Logger instance
     * @param dbName    Database name
     * @param query     SQL query
     * @param params    Query parameters
     */
    public static void logDatabaseQuery(Logger logger, String dbName, String query, Object... params) {
        logger.debug("Database Query [{}]: {}", dbName, query);
        if (params != null && params.length > 0) {
            logger.debug("Query Parameters: {}", params);
        }
    }
    
    /**
     * Logs a database update
     * 
     * @param logger        Logger instance
     * @param dbName        Database name
     * @param query         SQL query
     * @param rowsAffected  Number of rows affected
     */
    public static void logDatabaseUpdate(Logger logger, String dbName, String query, int rowsAffected) {
        logger.debug("Database Update [{}]: {}", dbName, query);
        logger.debug("Rows Affected: {}", rowsAffected);
    }
}