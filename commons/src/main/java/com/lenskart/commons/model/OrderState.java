package com.lenskart.commons.model;

import lombok.Getter;

import java.util.Arrays;


/**
 * Enum representing different order states in the CS system..
 */
@Getter
public enum OrderState {

    CANCELLED("cancelled"),

    CANCELED("canceled"),

    ORDER_NOT_CONFIRMED("order_not_confirmed"),

    PENDING("pending"),

    PROCESSING_POWER_FOLLOWUP("processing_power_followup"),

    PROCESSING_POWER_FOLLOWUP_VERIFY("processing_power_followup_verify"),


    PROCESSING("processing");


    private final String displayName;


    /**
     * Constructor for OrderState enum
     */
    OrderState(String displayName) {
        this.displayName = displayName;

    }

    /**
     * Get order state by display name (case-insensitive)
     *
     * @param displayName The display name of the order state
     * @return The corresponding OrderState enum value
     * @throws IllegalArgumentException if the display name is not found
     */
    public static OrderState getByDisplayName(String displayName) {
        if (displayName == null || displayName.trim().isEmpty()) {
            throw new IllegalArgumentException("Display name cannot be null or empty");
        }

        return Arrays.stream(values())
                .filter(state -> state.displayName.equalsIgnoreCase(displayName.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Order state not found for display name: " + displayName));
    }

    /**
     * Get order state by enum name (case-insensitive)
     *
     * @param name The enum name
     * @return The corresponding OrderState enum value
     * @throws IllegalArgumentException if the name is not found
     */
    public static OrderState getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name cannot be null or empty");
        }

        return Arrays.stream(values())
                .filter(state -> state.name().equalsIgnoreCase(name.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Order state not found for name: " + name));
    }
}
