package com.lenskart.commons.model;
import lombok.AllArgsConstructor;
import lombok.Getter;


@AllArgsConstructor
@Getter
public enum StoreCredit {
    /**
     * Test Storecode for automation
     */
    STORECODE_IN("6706-RXGOC-CKRS", "Test Store Code for automation testing"),
    /**
     * Store code for SG
     */
    STORECODE_SG("6154-J6CQ2-RCWR", "Test Store Code for automation testing"),
    /**
     * Store code for TH
     */
    STORECODE_TH("7665-XFO1C-KXRP", "Test Store Code for automation testing"),

    /**
     * Store code for AE
     */
    STORECODE_AE("0114-5DJOQ-GTAA", "Test Store Code for AE automation testing"),
    /**
     * Store code for SA
     */
    STORECODE_SA("6918-UAFTF-KFQO", "Test Store Code for SA automation testing"),
    /**
     * Store code for US
     */
    STORECODE_US("5163-FDA6V-ARRJ", "Test Store Code for US automation testing");
    private final String code;

    /**
     * Description of the gift voucher
     */
    private final String description;

}