package com.lenskart.commons.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum representing different processing types for orders or products.
 * Each processing type has associated properties like display name and description.
 */
@AllArgsConstructor
@Getter
public enum ProcessingType {

    /**
     * FR0 processing type
     */
    FR0("FR0", "Fitting not required orders"),

    /**
     * FR1 processing type
     */
    FR1("FR1", "Fitting required orders for eyeglasses"),

    /**
     * FR2 processing type
     */
    FR2("FR2", "Fitting required orders for sunglasses");

    /**
     * Code for the processing type
     */
    private final String code;

    /**
     * Description of the processing type
     */
    private final String description;

    /**
     * Get processing type by code
     *
     * @param code The code of the processing type
     * @return The corresponding ProcessingType enum value
     * @throws IllegalArgumentException if the code is not found
     */
    public static ProcessingType getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("Code cannot be null or empty");
        }

        for (ProcessingType processingType : values()) {
            if (processingType.getCode().equalsIgnoreCase(code.trim())) {
                return processingType;
            }
        }

        throw new IllegalArgumentException("Processing type not found for code: " + code);
    }

    /**
     * Check if a processing type code is valid
     *
     * @param code The processing type code
     * @return true if the processing type code is valid, false otherwise
     */
    public static boolean isValidProcessingType(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        for (ProcessingType processingType : values()) {
            if (processingType.getCode().equalsIgnoreCase(code.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get all processing type codes as an array
     *
     * @return Array of all processing type codes
     */
    public static String[] getAllProcessingTypeCodes() {
        ProcessingType[] processingTypes = values();
        String[] codes = new String[processingTypes.length];

        for (int i = 0; i < processingTypes.length; i++) {
            codes[i] = processingTypes[i].getCode();
        }
        return codes;
    }


}