package com.lenskart.commons.model;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Enum representing different frame types available for eyeglasses and sunglasses.
 * Each frame type has associated properties like display name, description, and characteristics.
 */
@Getter
public enum FrameTypes {
    
    /**
     * Full rim frames that completely surround the lenses
     */
    FULL_RIM("Full Rim", "full_rim", "CLASSIC"),
    
    /**
     * Half rim frames that partially surround the lenses (usually top half)
     */
    HALF_RIM("Half Rim", "half_rim", "SEMI_RIMLESS"),
    
    /**
     * Rimless frames with no frame around the lenses
     */
    RIMLESS("Rimless", "rimless", "MINIMALIST");
    
    private final String displayName;
    private final String queryName;
    private final String styleCategory;
    
    /**
     * Constructor for FrameTypes enum
     */
    FrameTypes(String displayName, String description, String styleCategory) {
        this.displayName = displayName;
        this.queryName = description;
        this.styleCategory = styleCategory;
    }
    
    // ==================== FINDER METHODS ====================
    
    /**
     * Get frame type by display name (case-insensitive)
     * 
     * @param displayName The display name of the frame type
     * @return The corresponding FrameTypes enum value
     * @throws IllegalArgumentException if the display name is not found
     */
    public static FrameTypes getByDisplayName(String displayName) {
        if (displayName == null || displayName.trim().isEmpty()) {
            throw new IllegalArgumentException("Display name cannot be null or empty");
        }
        
        return Arrays.stream(values())
                .filter(frameType -> frameType.displayName.equalsIgnoreCase(displayName.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Frame type not found for display name: " + displayName));
    }
    
    /**
     * Get frame type by enum name (case-insensitive)
     * 
     * @param name The enum name
     * @return The corresponding FrameTypes enum value
     * @throws IllegalArgumentException if the name is not found
     */
    public static FrameTypes getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name cannot be null or empty");
        }
        
        return Arrays.stream(values())
                .filter(frameType -> frameType.name().equalsIgnoreCase(name.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Frame type not found for name: " + name));
    }
    
    // ==================== FILTER METHODS ====================
    
    /**
     * Get all frame types by style category
     * 
     * @param styleCategory The style category
     * @return List of FrameTypes in the specified style category
     */
    public static List<FrameTypes> getByStyleCategory(String styleCategory) {
        if (styleCategory == null || styleCategory.trim().isEmpty()) {
            throw new IllegalArgumentException("Style category cannot be null or empty");
        }
        
        return Arrays.stream(values())
                .filter(frameType -> frameType.styleCategory.equalsIgnoreCase(styleCategory.trim()))
                .collect(Collectors.toList());
    }
    
    // ==================== VALIDATION METHODS ====================
    
    /**
     * Check if a frame type name is valid
     * 
     * @param name The frame type name
     * @return true if the frame type name is valid, false otherwise
     */
    public static boolean isValidFrameType(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        return Arrays.stream(values())
                .anyMatch(frameType -> frameType.name().equalsIgnoreCase(name.trim()) || 
                                     frameType.displayName.equalsIgnoreCase(name.trim()));
    }
    
    // ==================== UTILITY METHODS ====================
    
    /**
     * Get all frame type names as a list
     * 
     * @return List of all frame type names
     */
    public static List<String> getAllFrameTypeNames() {
        return Arrays.stream(values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }
    
    /**
     * Get all display names as a list
     * 
     * @return List of all display names
     */
    public static List<String> getAllDisplayNames() {
        return Arrays.stream(values())
                .map(FrameTypes::getDisplayName)
                .collect(Collectors.toList());
    }
    
    /**
     * Get the frame type code for API calls
     * 
     * @return Frame type code in lowercase
     */
    public String getFrameTypeCode() {
        return this.name().toLowerCase();
    }
    
    // ==================== BUSINESS LOGIC METHODS ====================
    
    /**
     * Check if the frame type requires special handling during manufacturing
     * 
     * @return true if special handling is required, false otherwise
     */
    public boolean requiresSpecialHandling() {
        return switch (this) {
            case RIMLESS -> true; // Rimless frames require special drilling and mounting
            case HALF_RIM -> true; // Half rim frames require special groove cutting
            case FULL_RIM -> false; // Standard manufacturing process
            default -> false;
        };
    }
    
    /**
     * Get the durability rating of the frame type
     * 
     * @return Durability rating (1-5, where 5 is most durable)
     */
    public int getDurabilityRating() {
        return switch (this) {
            case FULL_RIM -> 5; // Most durable due to complete lens protection
            case HALF_RIM -> 3; // Moderate durability
            case RIMLESS -> 2; // Less durable due to minimal frame structure
            default -> 1;
        };
    }
    
    /**
     * Check if the frame type is suitable for sports activities
     * 
     * @return true if suitable for sports, false otherwise
     */
    public boolean isSuitableForSports() {
        return switch (this) {
            case FULL_RIM -> true; // Best protection for sports
            case HALF_RIM -> false; // Limited protection
            case RIMLESS -> false; // Fragile for sports activities
            default -> false;
        };
    }
    
    /**
     * Check if the frame type provides full lens protection
     * 
     * @return true if provides full lens protection, false otherwise
     */
    public boolean providesFullLensProtection() {
        return this == FULL_RIM;
    }
    
    /**
     * Check if the frame type is suitable for high prescription lenses
     * 
     * @return true if suitable for high prescription, false otherwise
     */
    public boolean isSuitableForHighPrescription() {
        return switch (this) {
            case FULL_RIM -> true; // Can handle thick lenses well
            case HALF_RIM -> false; // Limited support for thick lenses
            case RIMLESS -> false; // Not suitable for thick lenses
            default -> false;
        };
    }
    
    /**
     * Get the weight category of the frame type
     * 
     * @return Weight category (LIGHT, MEDIUM, HEAVY)
     */
    public String getWeightCategory() {
        return switch (this) {
            case RIMLESS -> "LIGHT";
            case HALF_RIM -> "MEDIUM";
            case FULL_RIM -> "HEAVY";
            default -> "UNKNOWN";
        };
    }
    
    /**
     * Check if the frame type is trendy/fashionable
     * 
     * @return true if trendy, false otherwise
     */
    public boolean isTrendy() {
        return switch (this) {
            case RIMLESS -> true; // Modern, minimalist look
            case HALF_RIM -> true; // Contemporary style
            case FULL_RIM -> false; // Classic but not necessarily trendy
            default -> false;
        };
    }
    
    @Override
    public String toString() {
        return String.format("%s (%s) - Durability: %d/5, Sports Suitable: %s, Weight: %s",
                displayName, styleCategory, getDurabilityRating(), 
                isSuitableForSports() ? "Yes" : "No", getWeightCategory());
    }
}
