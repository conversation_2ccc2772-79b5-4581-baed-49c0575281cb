package com.lenskart.commons.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum representing different gift voucher codes available in the system.
 * Each gift voucher has associated properties like display name and description.
 */
@AllArgsConstructor
@Getter
public enum GiftVoucher {

    /**
     * Test voucher for automation
     */
    LENS_GAGAN("LENS-GAGAN", "Test voucher for automation testing", "500"),
    /**
     * Test voucher for IN, 5%
     */
    TESTGV_IN_5PER("IN-TEST-GV-5", "Test voucher for 5 Percent for IN", "5%"),
    /**
     * Test voucher for SG, 5%
     */
    TESTGV_SG_5PER("GVSINGAPORE10", "Test voucher for 5 Percent for SG", "5%"),

    /**
     * Test voucher for AE, 5%
     */
    TESTGV_AE_5PER("GVAE10", "Test voucher for 75 Percent for AE", "5%"),
    /**
     * Test voucher for US, 5%
     */
    TESTGV_US_5PER("GVUS10", "Test voucher for 5 Percent for US", "5%"),

    /**
     * Test voucher for SA, 5%
     */
    TESTGV_SA_5PER("GVSA10", "Test voucher for 75 Percent for SA", "5%"),
    /**
     * Test voucher for TH, 5%
     */
    TESTGV_TH_5PER("GVTH10", "Test voucher for 75 Percent for TH", "5%"),
    /**
     * Standard gift voucher
     */
    LENS_GIFT("GVINDIA10", "Standard gift voucher", "1000"),

    /**
     * Premium gift voucher
     */
    LENS_PREMIUM("LENS-PREMIUM", "Premium gift voucher", "2000"),

    /**
    * Test voucher for Medibuddy Insurance flow
    */
    MEDIBUDDY_GIFTCODE("MDBY-TEST-SCRIPT", "Medibuddy gift voucher", "No limit");
    private final String code;

    /**
     * Description of the gift voucher
     */
    private final String description;

    /**
     * Value of the gift voucher
     */
    private final String value;

    /**
     * Get gift voucher by code
     *
     * @param code The code of the gift voucher
     * @return The corresponding GiftVoucher enum value
     * @throws IllegalArgumentException if the code is not found
     */
    public static GiftVoucher getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("Code cannot be null or empty");
        }

        for (GiftVoucher giftVoucher : values()) {
            if (giftVoucher.getCode().equalsIgnoreCase(code.trim())) {
                return giftVoucher;
            }
        }

        throw new IllegalArgumentException("Gift voucher not found for code: " + code);
    }

    /**
     * Check if a gift voucher code is valid
     *
     * @param code The gift voucher code
     * @return true if the gift voucher code is valid, false otherwise
     */
    public static boolean isValidGiftVoucher(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        for (GiftVoucher giftVoucher : values()) {
            if (giftVoucher.getCode().equalsIgnoreCase(code.trim())) {
                return true;
            }
        }
        return false;
    }


}