package com.lenskart.commons.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum representing different test types in the system.
 * Each test type has an associated string code.
 */
@AllArgsConstructor
@Getter
public enum TestType {


    POSITIVE("positive"),


    NEGATIVE("negative");


    private final String value;

    /**
     * Get Test by code
     *
     * @param value The string code for the cancellation type
     * @return The corresponding TESTTYPE enum value
     * @throws IllegalArgumentException if the code is not found
     */
    public static TestType getByValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Code cannot be null or empty");
        }

        for (TestType type : values()) {
            if (type.getValue().equalsIgnoreCase(value.trim())) {
                return type;
            }
        }

        throw new IllegalArgumentException("TestType not found for code: " + value);
    }
}