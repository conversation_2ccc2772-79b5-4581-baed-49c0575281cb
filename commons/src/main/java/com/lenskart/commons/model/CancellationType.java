package com.lenskart.commons.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum representing different cancellation types in the system.
 * Each cancellation type has an associated string code.
 */
@AllArgsConstructor
@Getter
public enum CancellationType {

    /**
     * Full order cancellation
     */
    FULL_CANCELLATION("full_cancellation"),

    /**
     * Partial order cancellation
     */
    PARTIAL_CANCELLATION("partial_cancellation");

    /**
     * Value for the cancellation type
     */
    private final String value;

    /**
     * Get CancellationType by code
     *
     * @param value The string code for the cancellation type
     * @return The corresponding CancellationType enum value
     * @throws IllegalArgumentException if the code is not found
     */
    public static CancellationType getByValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Code cannot be null or empty");
        }

        for (CancellationType type : values()) {
            if (type.getValue().equalsIgnoreCase(value.trim())) {
                return type;
            }
        }

        throw new IllegalArgumentException("CancellationType not found for code: " + value);
    }
}