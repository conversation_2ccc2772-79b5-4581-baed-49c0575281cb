package com.lenskart.commons.model;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Enum representing different client types/platforms in the system.
 * Each client type has associated properties like display name, platform type, and characteristics.
 */
@Getter
public enum Client {

    /**
     * Desktop web application
     */
    DESKTOP("desktop", Platform.WEB, "Desktop web browser application"),

    /**
     * iOS mobile application
     */
    IOS("ios", Platform.MOBILE, "iOS mobile application"),

    /**
     * Android mobile application
     */
    ANDROID("android", Platform.MOBILE, "Android mobile application"),

    /**
     * Mobile website
     */
    MOBILESITE("mobilesite", Platform.WEB, "Mobile optimized website"),

    /**
     * POS iOS application
     */
    POS_IOS("pos", Platform.POS, "Point of Sale iOS application for retail stores"),

    /**
     * POS Web application
     */
    POS_WEB("pos_web", Platform.POS, "Point of Sale Web application for retail stores and admin"),

    /**
     * POS iOS application
     */
    VSM("vsm", Platform.VSM, "Point of Sale VSM application for agents");

    private final String displayName;
    private final Platform platform;
    private final String description;

    /**
     * Constructor for Client enum
     */
    Client(String displayName, Platform platform, String description) {
        this.displayName = displayName;
        this.platform = platform;
        this.description = description;
    }

    /**
     * Enum for platform types
     */
    public enum Platform {
        WEB("Web Platform"),
        MOBILE("Mobile Platform"),
        POS("Point of Sale Platform"),
        VSM("VSM Platform");

        @Getter
        private final String description;

        Platform(String description) {
            this.description = description;
        }
    }

    // ==================== FINDER METHODS ====================

    /**
     * Get client by display name (case-insensitive)
     *
     * @param displayName The display name of the client
     * @return The corresponding Client enum value
     * @throws IllegalArgumentException if the display name is not found
     */
    public static Client getByDisplayName(String displayName) {
        if (displayName == null || displayName.trim().isEmpty()) {
            throw new IllegalArgumentException("Display name cannot be null or empty");
        }

        return Arrays.stream(values())
                .filter(client -> client.displayName.equalsIgnoreCase(displayName.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Client not found for display name: " + displayName));
    }

    /**
     * Get client by enum name (case-insensitive)
     *
     * @param name The enum name
     * @return The corresponding Client enum value
     * @throws IllegalArgumentException if the name is not found
     */
    public static Client getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name cannot be null or empty");
        }

        return Arrays.stream(values())
                .filter(client -> client.name().equalsIgnoreCase(name.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Client not found for name: " + name));
    }

    // ==================== FILTER METHODS ====================

    /**
     * Get all clients by platform type
     *
     * @param platform The platform type
     * @return List of Client in the specified platform
     */
    public static List<Client> getByPlatform(Platform platform) {
        if (platform == null) {
            throw new IllegalArgumentException("Platform cannot be null");
        }

        return Arrays.stream(values())
                .filter(client -> client.platform == platform)
                .collect(Collectors.toList());
    }



    /**
     * Get web-based clients
     *
     * @return List of web-based Client
     */
    public static List<Client> getWebClients() {
        return getByPlatform(Platform.WEB);
    }

    /**
     * Get mobile clients
     *
     * @return List of mobile Client
     */
    public static List<Client> getMobileClients() {
        return getByPlatform(Platform.MOBILE);
    }

    /**
     * Get POS clients
     *
     * @return List of POS Client
     */
    public static List<Client> getPOSClients() {
        return getByPlatform(Platform.POS);
    }

    /**
     * Get native mobile app clients (iOS and Android apps)
     *
     * @return List of native mobile app Client
     */
    public static List<Client> getNativeAppClients() {
        return Arrays.stream(values())
                .filter(client -> client == IOS || client == ANDROID || client == POS_IOS)
                .collect(Collectors.toList());
    }

    // ==================== VALIDATION METHODS ====================

    /**
     * Check if a client name is valid
     *
     * @param name The client name
     * @return true if the client name is valid, false otherwise
     */
    public static boolean isValidClient(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }

        return Arrays.stream(values())
                .anyMatch(client -> client.name().equalsIgnoreCase(name.trim()) ||
                                  client.displayName.equalsIgnoreCase(name.trim()));
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Get all client names as a list
     *
     * @return List of all client names
     */
    public static List<String> getAllClientNames() {
        return Arrays.stream(values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }

    /**
     * Get all display names as a list
     *
     * @return List of all display names
     */
    public static List<String> getAllDisplayNames() {
        return Arrays.stream(values())
                .map(Client::getDisplayName)
                .collect(Collectors.toList());
    }

    /**
     * Get the client code for API calls
     *
     * @return Client code in lowercase
     */
    public String getClientCode() {
        return this.name().toLowerCase();
    }

    // ==================== BUSINESS LOGIC METHODS ====================

    /**
     * Check if the client is a mobile platform
     *
     * @return true if mobile platform, false otherwise
     */
    public boolean isMobile() {
        return platform == Platform.MOBILE || this == MOBILESITE;
    }

    /**
     * Check if the client is a web-based platform
     *
     * @return true if web-based, false otherwise
     */
    public boolean isWeb() {
        return platform == Platform.WEB;
    }

    /**
     * Check if the client is a native mobile app
     *
     * @return true if native mobile app, false otherwise
     */
    public boolean isNativeApp() {
        return this == IOS || this == ANDROID || this == POS_IOS;
    }

    /**
     * Check if the client supports offline functionality
     *
     * @return true if supports offline functionality, false otherwise
     */
    public boolean supportsOffline() {
        switch (this) {
            case IOS:
            case ANDROID:
            case POS_IOS:
                return true; // Native apps can work offline
            case DESKTOP:
            case MOBILESITE:
                return false; // Web-based clients require internet
            default:
                return false;
        }
    }

    /**
     * Check if the client supports push notifications
     *
     * @return true if supports push notifications, false otherwise
     */
    public boolean supportsPushNotifications() {
        switch (this) {
            case IOS:
            case ANDROID:
            case POS_IOS:
                return true; // Native apps support push notifications
            case DESKTOP:
            case MOBILESITE:
                return false; // Web clients have limited push notification support
            default:
                return false;
        }
    }

    /**
     * Get the typical screen size category for the client
     *
     * @return Screen size category
     */
    public String getScreenSizeCategory() {
        switch (this) {
            case DESKTOP:
                return "Large";
            case IOS:
            case ANDROID:
            case MOBILESITE:
                return "Small";
            case POS_IOS:
                return "Medium";
            default:
                return "Unknown";
        }
    }

    /**
     * Check if the client requires app store distribution
     *
     * @return true if requires app store, false otherwise
     */
    public boolean requiresAppStore() {
        return isNativeApp();
    }

    @Override
    public String toString() {
        return String.format("%s (%s) - Native App: %s, Offline: %s, Push Notifications: %s",
                displayName, platform.getDescription(),
                isNativeApp() ? "Yes" : "No",
                supportsOffline() ? "Yes" : "No",
                supportsPushNotifications() ? "Yes" : "No");
    }
}
