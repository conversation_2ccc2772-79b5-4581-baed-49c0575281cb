package com.lenskart.commons.model;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Enum representing different Users available in the system.
 * Refactored for better maintainability and type safety.
 */
@Getter
public enum Users {


    ANJU("<EMAIL>",
                 "Refund went via Storecredit");


    private final String email;
    private final String description;

    /**
     * Constructor for PaymentMethod enum
     */
    Users(String email, String description) {
        this.email = email;
        this.description = description;
    }
}
