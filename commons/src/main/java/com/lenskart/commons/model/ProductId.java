package com.lenskart.commons.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ProductId {

    IN_EYEGLASSES("134828", "Eyeglasses"),
    IN_EYEGLASSES_1("131932", "Eyeglasses"),
    IN_SUNGLASSES("131315", "Sunglasses"),
    IN_SUNGLASSES_POS("131316","Sunglasses"),
    IN_CONTACT_LENS("211541", "Contact Lens"),
    IN_CONTACT_LENS_ZERO_POWER("151325", "Contact Lens"),
    IN_LOYALTY("128269", "Loyalty Services"),
    IN_ACCESSORIES("147511", "Accessories"),
    IN_CARRIAGE_BAG("208251", "Carriage Bag"),
    IN_FRAME_ONLY("140551", "Eyeglasses"),
    IN_INSURANCE("152748", "Insurance"),
    IN_CL_POWER_WISE_ID("90061940", "Contact Lens"),
    IN_CL_POWER_WISE_ID_ZERO_POWER("90221704", "Contact Lens"),
    IN_ZP_LENS("85446546", "Zero Power"),
    IN_LENS("70238508", "Power"),
    IN_LENS_ONLY_1("96505","LENS ONLY"),
    IN_LENS_ONLY_2("96506","LENS ONLY"),

    SG_EYEGLASSES_1("147098", "Eyeglasses"),
    SG_EYEGLASSES("147098", "Eyeglasses"),
    SG_SUNGLASSES("149043", "Sunglasses"),
    SG_CONTACT_LENS("145502", "Contact Lens"),
    SG_LOYALTY("128269", "Loyalty Services"),
    SG_ACCESSORIES("131936", "Accessories"), //To be added
    SG_FRAME_ONLY("147098", "Eyeglasses"),
    SG_EYEGLASSES_2("131511","Eyeglasses"),


    AE_EYEGLASSES("146024", "Eyeglasses"),
    AE_SUNGLASSES("136631", "Sunglasses"),
    AE_CONTACT_LENS("150225", "Contact Lens"),
    AE_LOYALTY("216617", "Loyalty Services"),
    AE_ACCESSORIES("131936", "Accessories"), //To be added
    AE_FRAME_ONLY("146024", "Eyeglasses"),


    SA_EYEGLASSES("147491", "Eyeglasses"),
    SA_SUNGLASSES("131315", "Sunglasses"),
    SA_CONTACT_LENS("150234", "Contact Lens"),
   // SA_LOYALTY("131935", "Loyalty Services"), No Loyalty Pid in SA
    SA_ACCESSORIES("131936", "Accessories"), // TO be added

    TH_EYEGLASSES("201194", "Eyeglasses"),
    TH_SUNGLASSES("147598", "Sunglasses"),
    TH_SUNGLASSES_POS("137153", "Sunglasses"),
    TH_CONTACT_LENS("131934", "Contact Lens"),
    TH_LOYALTY("131935", "Loyalty Services"),
    TH_ACCESSORIES("131936", "Accessories"),

    US_EYEGLASSES("136174", "Eyeglasses"),
    US_SUNGLASSES("151546", "Sunglasses"),
   // US_CONTACT_LENS("131934", "Contact Lens"), No CL in US
    //US_LOYALTY("131935", "Loyalty Services"), No Loyalty Pid in US
    US_ACCESSORIES("131936", "Accessories"); // To be added

    public static ProductId getByProductName(String productName) {
        if (productName == null) {
            throw new IllegalArgumentException("Product name cannot be null");
        }

        for (ProductId product : values()) {
            if (product.getProductName().equalsIgnoreCase(productName)) {
                return product;
            }
        }

        throw new IllegalArgumentException("Product name not found: " + productName);
    }

    public static ProductId getByProductId(String productId) {
        if (productId == null) {
            throw new IllegalArgumentException("Product ID cannot be null");
        }

        for (ProductId product : values()) {
            if (product.getProductId().equalsIgnoreCase(productId)) {
                return product;
            }
        }

        throw new IllegalArgumentException("Product ID not found: " + productId);
    }

    private final String productId;
    private final String productName;
}
