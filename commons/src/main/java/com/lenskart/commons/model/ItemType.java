package com.lenskart.commons.model;

/**
 * Enum representing different types of items in the system.
 * Used across various modules to categorize and handle different item types.
 */
public enum ItemType {
    
    /**
     * Over The Counter - Items sold directly without prescription
     */
    OTC,
    
    /**
     * Local Fitting - Items that require local fitting services
     */
    LOCAL_FITTING,
    
    /**
     * Last Piece Warehouse - Final inventory items from warehouse
     */
    LAST_PIECE_WAREHOUSE,
    
    /**
     * Lens Only - Items that are lenses without frames
     */
    LENS_ONLY,
    
    /**
     * Direct To Consumer - Items sold directly to end consumers
     */
    DTC,
    
    /**
     * Business To Business - Items sold to business customers
     */
    B2B,
    
    /**
     * Plant Fittings - Items that require plant-level fitting
     */
    PLANT_FITTINGS,
    
    /**
     * Bulk Order - Items ordered in bulk quantities
     */
    BULK_ORDER,

    CONTACT_LENS,

    LENS_ONLY_LOCAL_FITTING_IN_COCO,

    PLANT_FITTINGS_WITH_GATEPASS;
    /**
     * Get the display name for the item type
     *
     * @return Human-readable display name
     */
    public String getDisplayName() {
        return switch (this) {
            case OTC -> "Over The Counter";
            case LOCAL_FITTING -> "Local Fitting";
            case LAST_PIECE_WAREHOUSE -> "Last Piece Warehouse";
            case LENS_ONLY -> "Lens Only";
            case DTC -> "Direct To Consumer";
            case B2B -> "Business To Business";
            case PLANT_FITTINGS -> "Plant Fittings";
            case BULK_ORDER -> "Bulk Order";
            case CONTACT_LENS -> "Contact Lens";
            case PLANT_FITTINGS_WITH_GATEPASS -> "Plant Fitting with Gatepass";
            default -> this.name();
        };
    }
    
    /**
     * Get the description for the item type
     *
     * @return Description of the item type
     */
    public String getDescription() {
        return switch (this) {
            case OTC -> "Items sold directly without prescription";
            case LOCAL_FITTING -> "Items that require local fitting services";
            case LAST_PIECE_WAREHOUSE -> "Final inventory items from warehouse";
            case LENS_ONLY -> "Items that are lenses without frames";
            case DTC -> "Items sold directly to end consumers";
            case B2B -> "Items sold to business customers";
            case PLANT_FITTINGS -> "Items that require plant-level fitting";
            case BULK_ORDER -> "Items ordered in bulk quantities";
            default -> "Unknown item type";
        };
    }
    
    /**
     * Check if the item type requires fitting services
     *
     * @return true if fitting is required, false otherwise
     */
    public boolean requiresFitting() {
        return this == LOCAL_FITTING || this == PLANT_FITTINGS;
    }
    
    /**
     * Check if the item type is for business customers
     *
     * @return true if for business customers, false otherwise
     */
    public boolean isBusinessType() {
        return this == B2B || this == BULK_ORDER;
    }
    
    /**
     * Check if the item type is for consumer customers
     *
     * @return true if for consumer customers, false otherwise
     */
    public boolean isConsumerType() {
        return this == OTC || this == DTC || this == LOCAL_FITTING || this == LENS_ONLY;
    }
    
    /**
     * Check if the item type involves warehouse operations
     *
     * @return true if warehouse operations are involved, false otherwise
     */
    public boolean involvesWarehouse() {
        return this == LAST_PIECE_WAREHOUSE || this == BULK_ORDER;
    }
    
    /**
     * Get item type from string value (case-insensitive)
     *
     * @param value String value to convert
     * @return ItemType enum value
     * @throws IllegalArgumentException if value is not a valid ItemType
     */
    public static ItemType fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("ItemType value cannot be null or empty");
        }
        
        try {
            return ItemType.valueOf(value.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid ItemType value: " + value + 
                ". Valid values are: " + java.util.Arrays.toString(ItemType.values()));
        }
    }
    
    /**
     * Get all item types that require fitting
     *
     * @return Array of ItemType values that require fitting
     */
    public static ItemType[] getFittingTypes() {
        return new ItemType[]{LOCAL_FITTING, PLANT_FITTINGS};
    }
    
    /**
     * Get all business-related item types
     *
     * @return Array of ItemType values for business customers
     */
    public static ItemType[] getBusinessTypes() {
        return new ItemType[]{B2B, BULK_ORDER};
    }
    
    /**
     * Get all consumer-related item types
     *
     * @return Array of ItemType values for consumer customers
     */
    public static ItemType[] getConsumerTypes() {
        return new ItemType[]{OTC, DTC, LOCAL_FITTING, LENS_ONLY};
    }
    
    /**
     * Get all warehouse-related item types
     *
     * @return Array of ItemType values involving warehouse operations
     */
    public static ItemType[] getWarehouseTypes() {
        return new ItemType[]{LAST_PIECE_WAREHOUSE, BULK_ORDER};
    }
}
