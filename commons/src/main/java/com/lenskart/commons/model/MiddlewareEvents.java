package com.lenskart.commons.model;


import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum MiddlewareEvents {

    // Events for PurpleDrone Courier
        PURPLEDRONE_PICKED_UP("876553", "Consignment Picked Up"),
        PURPLEDRONE_IN_TRANSIT("27", "Hub in at Transit"),
        PURPLEDRONE_OUT_FOR_DELIVERY("777", "Out For Delivery"),
        PURPLEDRONE_DELIVERED("12", "Delivered"),
        PURPLEDRONE_UnDelivered("15","UnDelivered"),
        PURPLEDRONE_RTO_INITIATED("887","RTO"),
        PURPLEDRONE_RTO_DELIVERED("876552","RTO-Delivered"),
    // Events for BlueDart Courier Operations
        BLUEDART_SHIPMENT_BOOKED("PU-030-S","Online shipment booked"),
        BLUEDART_SHIPMENT_IN_TRANSIT("UD-001-S","SHIPMENT OUTSCAN"),
        BLUEDART_SHIPMENT_OUT_FOR_DELIVERY("UD-003-S","SHIPMENT OUTSCAN"),
        BLUEDART_SHIPMENT_DELIEVRED("DL-000-T","SHIPMENT DELIVERED"),
        BLUEDART_SHIPMENT_UNDELIVERED("UD-024-S","UNDELIVERED SHIPMENT HELD AT LOCATION"),
        BLUEDART_SHIPMENT_RTO_SHIPPER_REQUEST("RT-074-RT","RTO (SHIPPER REQUEST)"),
        BLUEDART_SHIPMENT_SHIPMENT_INSCAN("UD-001-RS","SHIPMENT INSCAN"),
        BLUEDART_SHIPMENT_SHIPMENT_DELIVERED_TO_RTO("DL-000-RT","SHIPMENT DELIVERED"),
    // Events for PicoExpress Courier Operations
        PICOXPRESS_PICKED("PICKED","Shipment picked by courier"),
        PICOXPRESS_IN_TRANSIT("IN_TRANSIT","In Transit"),
        PICOXPRESS_OUT_FOR_DELIVERY("OUT_FOR_DELIVERY","Out for delivery"),
        PICOXPRESS_DELIVERED("DELIVERED","Shipment Delivered"),
        PICOXPRESS_ATTEMPTED_UNDELIVERED("ATTEMPTED_UNDELIVERED","Delivery Attempt failed"),
        PICOXPRESS_RTO("RTO","RTO Initiated"),
        PICOXPRESS_RTO_DELIVERED("RTO_DELIVERED","RTO Delivered"),
    // Events for PicoExpress Courier Operations
        CRITICALLOG_PICKED("39","Consignment Picked Up"),
        CRITICALLOG_IN_TRANSIT("11","Hub in at Transit"),
        CRITICALLOG_OUT_FOR_DELIVERY("15","Out For Delivery"),
        CRITICALLOG_DELIVERED("10","Consignment delivered in good condition."),
        CRITICALLOG_ATTEMPTED_UNDELIVERED("99","UnDelivered"),
        CRITICALLOG_RTO("24","RTO Initiated"),
        CRITICALLOG_RTO_DELIVERED("25","RTO Delivered"),
    // Events for DOT Courier Operations
        DOT_PICKED("PICKUP_DONE","Consignment Picked Up"),
        DOT_OUT_FOR_DELIVERY("DROP_ENROUTE","Out For Delivery"),
        DOT_DELIVERED("COMPLETED","Consignment delivered in good condition."),

        DOT_UnDeliveredD("PARKED","UnDelivered"),
        DOT_RTO("CANCELLED","RTO Initiated"),

    //Shipment Mode
        FORWARD_SHIPMENT_MODE("F","Forward"),
        REVERSE_SHIPMENT_MODE("R","Reverse");






    private final String eventId;
    private final String eventName;

    /**
     * Retrieves a TrackingMiddlewareEvents enum by its event name (case-insensitive).
     *
     * @param eventName The name of the event to search for.
     * @return The matching TrackingMiddlewareEvents enum.
     * @throws IllegalArgumentException if the event name is not found.
     */
    public static MiddlewareEvents getByEventName(String eventName) { // Method name updated
        if (eventName == null) {
            throw new IllegalArgumentException("Event name cannot be null");
        }

        for (MiddlewareEvents event : values()) { // Enum type updated
            if (event.getEventName().equalsIgnoreCase(eventName)) {
                return event;
            }
        }

        throw new IllegalArgumentException("Event name not found: " + eventName);
    }

    /**
     * Retrieves a TrackingMiddlewareEvents enum by its event ID (case-insensitive).
     *
     * @param eventId The ID of the event to search for.
     * @return The matching TrackingMiddlewareEvents enum.
     * @throws IllegalArgumentException if the event ID is not found.
     */
    public static MiddlewareEvents getByEventId(String eventId) { // Method name updated
        if (eventId == null) {
            throw new IllegalArgumentException("Event ID cannot be null");
        }

        for (MiddlewareEvents event : values()) { // Enum type updated
            if (event.getEventId().equalsIgnoreCase(eventId)) {
                return event;
            }
        }

        throw new IllegalArgumentException("Event ID not found: " + eventId);
    }
}

