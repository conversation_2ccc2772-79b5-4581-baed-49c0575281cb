package com.lenskart.commons.model;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Enum representing different payment methods available in the system.
 * Refactored for better maintainability and type safety.
 */
@Getter
public enum RefundMethod {


    STORE_CREDIT("STORECREDIT", PaymentType.ONLINE,
            "Refund went via Storecredit", false),
    SOURCE("SOURCE", PaymentType.ONLINE,
            "Refund went via Source", false),
    CASHFREE("CASHFREE", PaymentType.ONLINE,
            "Refund went via Cashfree", false),
    EXCHANGE("EXCHANGE", PaymentType.ONLINE,
            "Return went via Exchange", false);


    private final String displayName;
    private final PaymentType type;
    private final String description;
    private final boolean requiresAuthentication;

    /**
     * Constructor for PaymentMethod enum
     */
    RefundMethod(String displayName, PaymentType type, String description, boolean requiresAuthentication) {
        this.displayName = displayName;
        this.type = type;
        this.description = description;
        this.requiresAuthentication = requiresAuthentication;
    }

    /**
     * Enum for payment types
     */
    public enum PaymentType {
        ONLINE("Online Payment"),
        OFFLINE("Offline Payment");

        @Getter
        private final String description;

        PaymentType(String description) {
            this.description = description;
        }
    }




    // ==================== FINDER METHODS ====================

    /**
     * Get payment method by display name (case-insensitive)
     *
     * @param displayName The display name of the payment method
     * @return The corresponding PaymentMethod enum value
     * @throws IllegalArgumentException if the display name is not found
     */
    public static RefundMethod getByDisplayName(String displayName) {
        if (displayName == null || displayName.trim().isEmpty()) {
            throw new IllegalArgumentException("Display name cannot be null or empty");
        }

        return Arrays.stream(values())
                .filter(method -> method.displayName.equalsIgnoreCase(displayName.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Payment method not found for display name: " + displayName));
    }
}
