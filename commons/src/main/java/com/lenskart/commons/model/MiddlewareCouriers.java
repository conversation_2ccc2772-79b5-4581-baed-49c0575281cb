package com.lenskart.commons.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum MiddlewareCouriers {

    BLUEDART("2", "bluedart"),
    CRITICALOG("28","criticallog"),
    PURPLEDRONE("10","purpledrone"),
    PICOXPRESS("27","picoexpress"),
    DOT("6","dot");

    private final String courierId;
    private final String courierName;

    /**
     * Retrieves a Courier enum by its courier ID (case-insensitive).
     *
     * @param courierId The courier ID to search for.
     * @return The matching Courier enum.
     * @throws IllegalArgumentException if the courier ID is not found.
     */
    public static MiddlewareCouriers getByCourierId(String courierId) {
        if (courierId == null) {
            throw new IllegalArgumentException("Courier ID cannot be null");
        }
        for (MiddlewareCouriers courier : values()) {
            if (courier.getCourierId().equalsIgnoreCase(courierId)) {
                return courier;
            }
        }
        throw new IllegalArgumentException("Courier ID not found: " + courierId);
    }

    /**
     * Retrieves a Courier enum by its courier name (case-insensitive).
     *
     * @param courierName The courier name to search for.
     * @return The matching Courier enum.
     * @throws IllegalArgumentException if the courier name is not found.
     */
    public static MiddlewareCouriers getByCourierName(String courierName) {
        if (courierName == null) {
            throw new IllegalArgumentException("Courier name cannot be null");
        }
        for (MiddlewareCouriers courier : values()) {
            if (courier.getCourierName().equalsIgnoreCase(courierName)) {
                return courier;
            }
        }
        throw new IllegalArgumentException("Courier name not found: " + courierName);
    }
}
