# Use official Java 17 image as the base
FROM eclipse-temurin:17-jdk

# Set working directory
WORKDIR /app

# Install Maven
RUN apt-get update && \
    apt-get install -y maven && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV MAVEN_HOME /usr/share/maven
ENV MAVEN_CONFIG /root/.m2

# Copy the Maven project files
COPY pom.xml .
COPY commons/pom.xml commons/
COPY juno/pom.xml juno/
COPY scm/pom.xml scm/
COPY nexs/pom.xml nexs/
COPY cse/pom.xml cse/
COPY pos/pom.xml pos/
COPY example/pom.xml example/
COPY e2e/pom.xml e2e/

# Download dependencies (this layer will be cached unless pom files change)
RUN mvn dependency:go-offline -B

# Copy the source code
COPY commons/src commons/src
COPY juno/src juno/src
COPY scm/src scm/src
COPY nexs/src nexs/src
COPY cse/src cse/src
COPY pos/src pos/src
COPY example/src example/src
COPY e2e/src e2e/src

# Build the project (skip tests during build)
RUN mvn clean package -DskipTests

# Create directories for test reports and logs
RUN mkdir -p test-output/extent-reports logs

# Set the entry point to run tests
ENTRYPOINT ["mvn", "test"]

# Default command to run all tests
CMD []

# Usage examples:
# Run all tests:
#   docker run --rm be-automation
#
# Run only discovery module tests:
#   docker run --rm be-automation -pl juno
#
# Run a specific test class:
#   docker run --rm be-automation -pl juno -Dtest=DummyTest
#
# Run with specific Maven options:
#   docker run --rm be-automation -DskipTests=false -Dmaven.test.failure.ignore=true